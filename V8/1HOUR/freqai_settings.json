"freqai": {
        "enabled": true,
        "identifier": "torch-lstm12", 
    	"conf_width":1,
        "train_period_days": 120,
        "fit_live_predictions_candles": 24,
        "backtest_period_days": 30,
        "expiration_hours": 4,
        "live_retrain_hours": 4,
        "purge_old_models": 6,
    	"continual_learning":true, 
    
        "save_backtest_models": true,
        "write_metrics_to_disk": true,
        "activate_tensorboard": false,
        "feature_parameters": {
            "include_corr_pairlist": [
                "BTC/USDT:USDT",
                "ETH/USDT:USDT"
            ],
            "include_timeframes": [
                "1h",
            	"2h",
            	"4h"
            ],
            "label_period_candles": 24,
            "include_shifted_candidates": 2,
            "DI_threshold": 10,
            "weight_factor": 0.5,
            "indicator_periods_candles": [
                10,
                20
            ],
            "noise_standard_deviation": 0.01,
            "buffer_train_data_candles": 20
        },
        "data_split_parameters": {
            "test_size": 0.2,
            "random_state": 42,
            "shuffle": false
        },
        "model_training_parameters": {
            "learning_rate": 3e-3,
            "trainer_kwargs": {
                "n_steps": null,
                "batch_size": 32,
                "n_epochs": 10
            },
            "model_kwargs": {
                "num_lstm_layers": 3,
                "hidden_dim": 128,
                "dropout_percent": 0.4,
                "window_size": 5
            }
        },
        "rl_config": {
            "train_cycles": 25,
            "add_state_info": true,
            "max_trade_duration_candles": 300,
            "max_training_drawdown_pct": 0.02,
            "cpu_count": 8,
            "model_type": "PPO",
            "policy_type": "MlpPolicy",
            "model_reward_parameters": {
                "rr": 1,
                "profit_aim": 0.025
            }
        }