import logging
from functools import reduce

import numpy as np
import pandas as pd
import talib.abstract as ta
from freqtrade.exchange.exchange_utils import *
from freqtrade.strategy import IStrategy, RealParameter
from pandas import DataFrame
from technical import qtpylib

logger = logging.getLogger(__name__)


class AlexStrategyFinalV8(IStrategy):
    """
    AlexStrategyFinalV8 - 基于LSTM回归模型的加密货币交易策略

    这是一个使用FreqAI框架的高级交易策略，结合了多种技术指标和机器学习模型。

    主要特点：
    1. 使用LSTM神经网络进行价格预测
    2. 多指标综合评分系统
    3. 动态权重调整机制
    4. 市场状态过滤器
    5. 波动性自适应调整
    6. 支持多空双向交易

    风险提示：
    - 本策略仅供学习和研究使用
    - 实盘交易前请充分测试和验证
    - 加密货币交易存在高风险，请谨慎使用
    """

    # ==================== 策略配置常量 ====================

    # 技术指标周期配置
    INDICATOR_PERIODS = {
        "cci_period": 20,  # CCI指标周期
        "rsi_period": 10,  # RSI指标周期
        "momentum_period": 4,  # 动量指标周期
        "sma_period": 10,  # 简单移动平均周期
        "macd_fast": 26,  # MACD快线周期
        "macd_slow": 12,  # MACD慢线周期
        "roc_period": 2,  # 变化率指标周期
        "bb_period": 20,  # 布林带周期
        "bb_std": 2.2,  # 布林带标准差倍数
        "stoch_period": 14,  # 随机指标周期
        "atr_period": 14,  # ATR周期
        "ma_long": 100,  # 长期移动平均周期
        "normalize_window": 14,  # 标准化窗口期
    }
    # ==================== 超参数优化配置 ====================

    # 买入信号超参数（通过hyperopt优化得出）
    buy_params = {
        "threshold_buy": 0.59453,  # 买入阈值
        "w0": 0.54347,  # 标准化移动平均权重
        "w1": 0.82226,  # MACD权重
        "w2": 0.56675,  # ROC权重
        "w3": 0.77918,  # RSI权重
        "w4": 0.98488,  # 布林带宽度权重
        "w5": 0.31368,  # CCI权重
        "w6": 0.75916,  # OBV权重
        "w7": 0.09226,  # ATR权重
        "w8": 0.85667,  # 随机指标权重
    }

    # 卖出信号超参数
    sell_params = {
        "threshold_sell": 0.80573,  # 卖出阈值
    }

    # ==================== 策略基础配置 ====================

    # ROI（投资回报率）表：定义不同时间段的最小收益目标
    minimal_roi = {
        "0": 0.239,  # 开仓后立即：23.9%收益
        "79": 0.058,  # 79分钟后：5.8%收益
        "231": 0.029,  # 231分钟后：2.9%收益
        "543": 0,  # 543分钟后：保本退出
    }

    # 止损设置：最大允许亏损30.5%（让模型决定何时卖出）
    stoploss = -0.305

    # 基础策略参数
    timeframe = "1h"  # 时间框架：1小时
    can_short = True  # 允许做空
    use_exit_signal = True  # 使用退出信号
    process_only_new_candles = True  # 仅处理新K线

    # FreqAI相关配置
    startup_candle_count = 0  # 启动时需要的历史K线数量
    leverage_value = 10.0  # 杠杆倍数

    # ==================== 超参数优化参数 ====================

    # 交易阈值参数
    threshold_buy = RealParameter(-1, 1, default=0, space="buy")  # 买入阈值优化范围
    threshold_sell = RealParameter(-1, 1, default=0, space="sell")  # 卖出阈值优化范围

    # 技术指标权重参数（用于计算综合评分）
    w0 = RealParameter(0, 1, default=0.10, space="buy")  # 标准化移动平均权重
    w1 = RealParameter(0, 1, default=0.15, space="buy")  # MACD权重
    w2 = RealParameter(0, 1, default=0.10, space="buy")  # ROC权重
    w3 = RealParameter(0, 1, default=0.15, space="buy")  # RSI权重
    w4 = RealParameter(0, 1, default=0.10, space="buy")  # 布林带宽度权重
    w5 = RealParameter(0, 1, default=0.10, space="buy")  # CCI权重
    w6 = RealParameter(0, 1, default=0.10, space="buy")  # OBV权重
    w7 = RealParameter(0, 1, default=0.05, space="buy")  # ATR权重
    w8 = RealParameter(0, 1, default=0.15, space="buy")  # 随机指标权重

    # ==================== 辅助方法 ====================

    def _normalize_indicator(self, series: pd.Series, window: int) -> pd.Series:
        """
        对技术指标进行Z-Score标准化

        Args:
            series: 需要标准化的数据序列
            window: 滚动窗口大小

        Returns:
            pd.Series: 标准化后的数据序列
        """
        rolling_mean = series.rolling(window=window).mean()
        rolling_std = series.rolling(window=window).std()

        # 避免除零错误
        rolling_std = rolling_std.replace(0, np.nan)

        return (series - rolling_mean) / rolling_std

    def _normalize_weights(self, weights: list) -> list:
        """
        权重归一化：确保所有权重之和为1

        Args:
            weights: 权重列表

        Returns:
            list: 归一化后的权重列表
        """
        total_weight = sum(weights)
        if total_weight == 0:
            # 如果总权重为0，返回均等权重
            return [1.0 / len(weights)] * len(weights)
        return [w / total_weight for w in weights]

    # ==================== 特征工程方法 ====================

    def feature_engineering_expand_all(
        self, dataframe: DataFrame, period: int, metadata: dict, **kwargs
    ) -> DataFrame:
        """
        扩展特征工程：计算各种技术指标

        这个方法计算用于FreqAI模型训练的技术指标特征。
        所有特征都以'%-'前缀命名，这是FreqAI的约定。

        Args:
            dataframe: 包含OHLCV数据的DataFrame
            period: 动态周期参数
            metadata: 交易对元数据
            **kwargs: 其他参数

        Returns:
            DataFrame: 添加了技术指标特征的DataFrame
        """
        # 动量指标
        dataframe["%-cci-period"] = ta.CCI(
            dataframe, timeperiod=self.INDICATOR_PERIODS["cci_period"]
        )
        dataframe["%-rsi-period"] = ta.RSI(
            dataframe, timeperiod=self.INDICATOR_PERIODS["rsi_period"]
        )
        dataframe["%-momentum-period"] = ta.MOM(
            dataframe, timeperiod=self.INDICATOR_PERIODS["momentum_period"]
        )

        # 趋势指标
        dataframe["%-ma-period"] = ta.SMA(
            dataframe, timeperiod=self.INDICATOR_PERIODS["sma_period"]
        )

        # MACD指标（趋势跟踪）
        (
            dataframe["%-macd-period"],
            dataframe["%-macdsignal-period"],
            dataframe["%-macdhist-period"],
        ) = ta.MACD(
            dataframe["close"],
            slowperiod=self.INDICATOR_PERIODS["macd_slow"],
            fastperiod=self.INDICATOR_PERIODS["macd_fast"],
        )

        # 变化率指标
        dataframe["%-roc-period"] = ta.ROC(
            dataframe, timeperiod=self.INDICATOR_PERIODS["roc_period"]
        )

        # 布林带指标（波动性和支撑阻力）
        bollinger = qtpylib.bollinger_bands(
            qtpylib.typical_price(dataframe), window=period, stds=self.INDICATOR_PERIODS["bb_std"]
        )
        dataframe["bb_lowerband-period"] = bollinger["lower"]
        dataframe["bb_middleband-period"] = bollinger["mid"]
        dataframe["bb_upperband-period"] = bollinger["upper"]

        # 布林带宽度（波动性指标）
        dataframe["%-bb_width-period"] = (
            dataframe["bb_upperband-period"] - dataframe["bb_lowerband-period"]
        ) / dataframe["bb_middleband-period"]

        # 价格相对于布林带下轨的位置
        dataframe["%-close-bb_lower-period"] = dataframe["close"] / dataframe["bb_lowerband-period"]

        return dataframe

    def feature_engineering_expand_basic(
        self, dataframe: DataFrame, metadata: dict, **kwargs
    ) -> DataFrame:
        """
        基础特征工程：计算基本的价格和成交量特征

        Args:
            dataframe: 包含OHLCV数据的DataFrame
            metadata: 交易对元数据
            **kwargs: 其他参数

        Returns:
            DataFrame: 添加了基础特征的DataFrame
        """
        # 价格变化率（动量指标）
        dataframe["%-pct-change"] = dataframe["close"].pct_change()

        # 原始成交量和价格数据
        dataframe["%-raw_volume"] = dataframe["volume"]
        dataframe["%-raw_price"] = dataframe["close"]

        return dataframe

    def feature_engineering_standard(
        self, dataframe: DataFrame, metadata: dict, **kwargs
    ) -> DataFrame:
        """
        标准特征工程：计算时间相关特征

        Args:
            dataframe: 包含OHLCV数据的DataFrame
            metadata: 交易对元数据
            **kwargs: 其他参数

        Returns:
            DataFrame: 添加了时间特征的DataFrame
        """
        # 确保日期列为datetime类型
        dataframe["date"] = pd.to_datetime(dataframe["date"])

        # 时间特征（可能影响市场行为的周期性因素）
        dataframe["%-day_of_week"] = dataframe["date"].dt.dayofweek  # 星期几（0=周一）
        dataframe["%-hour_of_day"] = dataframe["date"].dt.hour  # 小时（0-23）

        return dataframe

    # ==================== FreqAI目标设置方法 ====================

    def set_freqai_targets(self, dataframe: DataFrame, metadata: dict, **kwargs) -> DataFrame:
        """
        设置FreqAI模型的目标值

        这是策略的核心方法，计算用于机器学习模型训练的目标值。
        该方法实现了一个复合评分系统，结合多个技术指标、市场状态和波动性调整。

        评分系统组成：
        1. 技术指标综合评分 (S)
        2. 市场状态过滤器 (R, R2)
        3. 波动性调整因子 (V, V2)
        4. 最终目标 T = S * R * R2 * V * V2

        Args:
            dataframe: 包含OHLCV数据的DataFrame
            metadata: 交易对元数据
            **kwargs: 其他参数

        Returns:
            DataFrame: 添加了目标值的DataFrame
        """

        # ==================== 第一步：计算技术指标 ====================

        # 使用配置的周期参数计算指标，避免硬编码
        periods = self.INDICATOR_PERIODS

        # 趋势指标
        dataframe["ma"] = ta.SMA(dataframe, timeperiod=periods["sma_period"])
        dataframe["ma_100"] = ta.SMA(dataframe, timeperiod=periods["ma_long"])

        # 动量指标
        dataframe["roc"] = ta.ROC(dataframe, timeperiod=periods["roc_period"])
        dataframe["momentum"] = ta.MOM(dataframe, timeperiod=periods["momentum_period"])
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=periods["rsi_period"])
        dataframe["cci"] = ta.CCI(dataframe, timeperiod=periods["cci_period"])

        # MACD指标
        dataframe["macd"], dataframe["macdsignal"], dataframe["macdhist"] = ta.MACD(
            dataframe["close"], slowperiod=periods["macd_slow"], fastperiod=periods["macd_fast"]
        )

        # 布林带指标
        bollinger = ta.BBANDS(dataframe, timeperiod=periods["bb_period"])
        dataframe["bb_upperband"] = bollinger["upperband"]
        dataframe["bb_middleband"] = bollinger["middleband"]
        dataframe["bb_lowerband"] = bollinger["lowerband"]

        # 其他指标
        dataframe["stoch"] = ta.STOCH(dataframe)["slowk"]
        dataframe["atr"] = ta.ATR(dataframe, timeperiod=periods["atr_period"])
        dataframe["obv"] = ta.OBV(dataframe)

        # ==================== 第二步：指标标准化 ====================

        """
        指标标准化的目的：
        1. 使不同量纲的指标具有可比性
        2. 便于分配权重和计算综合评分
        3. 提高机器学习模型的训练效果

        标准化方法：Z-Score标准化
        公式：(X - μ) / σ
        其中：X为原始值，μ为滚动均值，σ为滚动标准差
        结果：标准化后的值以0为中心，标准差为1
        """

        # 获取标准化窗口期
        norm_window = periods["normalize_window"]

        # 使用辅助方法进行标准化，提高代码复用性
        dataframe["normalized_stoch"] = self._normalize_indicator(dataframe["stoch"], norm_window)
        dataframe["normalized_atr"] = self._normalize_indicator(dataframe["atr"], norm_window)
        dataframe["normalized_obv"] = self._normalize_indicator(dataframe["obv"], norm_window)

        # 价格相对于移动平均的标准化（趋势强度）
        dataframe["normalized_ma"] = self._normalize_indicator(
            dataframe["close"], periods["sma_period"]
        )

        # MACD标准化
        dataframe["normalized_macd"] = self._normalize_indicator(
            dataframe["macd"], periods["macd_fast"]
        )

        # ROC标准化
        dataframe["normalized_roc"] = self._normalize_indicator(
            dataframe["roc"], periods["roc_period"]
        )

        # 动量标准化
        dataframe["normalized_momentum"] = self._normalize_indicator(
            dataframe["momentum"], periods["momentum_period"]
        )

        # RSI标准化
        dataframe["normalized_rsi"] = self._normalize_indicator(
            dataframe["rsi"], periods["rsi_period"]
        )

        # 布林带宽度标准化（波动性指标）
        bb_width = dataframe["bb_upperband"] - dataframe["bb_lowerband"]
        dataframe["normalized_bb_width"] = self._normalize_indicator(bb_width, periods["bb_period"])

        # CCI标准化
        dataframe["normalized_cci"] = self._normalize_indicator(
            dataframe["cci"], periods["cci_period"]
        )

        # ==================== 第三步：动态权重调整 ====================

        """
        动态权重调整的目的：
        1. 根据市场状态调整指标权重
        2. 在强趋势市场中增加动量指标的权重
        3. 提高策略的自适应能力
        """

        # 计算趋势强度：价格与移动平均线的偏离程度
        trend_strength = abs(dataframe["ma"] - dataframe["close"])

        # 定义强趋势阈值：均值 + 1.5倍标准差
        strong_trend_threshold = (
            trend_strength.rolling(window=norm_window).mean()
            + 1.5 * trend_strength.rolling(window=norm_window).std()
        )

        # 判断是否为强趋势市场
        is_strong_trend = trend_strength > strong_trend_threshold

        # 在强趋势中增加动量指标权重（提高50%）
        dataframe["w_momentum"] = np.where(is_strong_trend, self.w3.value * 1.5, self.w3.value)

        # ==================== 第四步：计算综合评分 S ====================

        """
        综合评分计算：
        1. 收集所有权重值
        2. 进行权重归一化
        3. 计算加权平均得分
        """

        # 收集基础权重（不包括动态调整的动量权重）
        base_weights = [
            self.w0.value,  # 标准化移动平均
            self.w1.value,  # MACD
            self.w2.value,  # ROC
            self.w4.value,  # 布林带宽度
            self.w5.value,  # CCI
            self.w6.value,  # OBV
            self.w7.value,  # ATR
            self.w8.value,  # 随机指标
        ]

        # 添加动态动量权重的平均值用于归一化
        avg_momentum_weight = dataframe["w_momentum"].mean()
        all_weights = base_weights + [avg_momentum_weight]

        # 权重归一化
        normalized_weights = self._normalize_weights(all_weights)

        # 计算综合评分
        dataframe["S"] = (
            normalized_weights[0] * dataframe["normalized_ma"]
            + normalized_weights[1] * dataframe["normalized_macd"]
            + normalized_weights[2] * dataframe["normalized_roc"]
            + normalized_weights[3] * dataframe["normalized_bb_width"]
            + normalized_weights[4] * dataframe["normalized_cci"]
            + normalized_weights[5] * dataframe["normalized_obv"]
            + normalized_weights[6] * dataframe["normalized_atr"]
            + normalized_weights[7] * dataframe["normalized_stoch"]
            + (dataframe["w_momentum"] / all_weights[-1] * normalized_weights[8])
            * dataframe["normalized_momentum"]
        )

        # ==================== 第五步：市场状态过滤器 R ====================

        """
        市场状态过滤器的目的：
        1. 识别不同的市场环境（牛市、熊市、震荡市）
        2. 根据市场状态调整交易信号
        3. 提高策略在不同市场环境下的适应性
        """

        # 初始化市场状态过滤器
        dataframe["R"] = 0

        # 基于布林带的短期市场状态判断
        # 价格突破布林带上轨：强势上涨市场 (R = 1)
        dataframe.loc[
            (dataframe["close"] > dataframe["bb_middleband"])
            & (dataframe["close"] > dataframe["bb_upperband"]),
            "R",
        ] = 1

        # 价格跌破布林带下轨：强势下跌市场 (R = -1)
        dataframe.loc[
            (dataframe["close"] < dataframe["bb_middleband"])
            & (dataframe["close"] < dataframe["bb_lowerband"]),
            "R",
        ] = -1

        # 基于长期移动平均的趋势过滤器
        # 价格在长期均线之上：长期上升趋势 (R2 = 1)
        # 价格在长期均线之下：长期下降趋势 (R2 = -1)
        dataframe["R2"] = np.where(dataframe["close"] > dataframe["ma_100"], 1, -1)

        # ==================== 第六步：波动性调整因子 V ====================

        """
        波动性调整的目的：
        1. 根据市场波动性调整信号强度
        2. 在高波动性时期降低信号权重
        3. 在低波动性时期提高信号权重

        使用两个波动性指标：
        1. 布林带宽度：反映价格波动范围
        2. ATR：反映真实波动幅度
        """

        # 计算布林带宽度（相对波动性）
        bb_width = (dataframe["bb_upperband"] - dataframe["bb_lowerband"]) / dataframe[
            "bb_middleband"
        ]

        # 波动性倒数：波动性越高，调整因子越小
        # 使用小的常数避免除零错误
        epsilon = 1e-8
        dataframe["V_mean"] = 1 / (bb_width + epsilon)
        dataframe["V2_mean"] = 1 / (dataframe["atr"] + epsilon)

        # 对波动性调整因子进行标准化
        dataframe["V_norm"] = self._normalize_indicator(dataframe["V_mean"], norm_window)
        dataframe["V2_norm"] = self._normalize_indicator(dataframe["V2_mean"], norm_window)

        # 将标准化后的波动性调整因子转换为离散值
        # > 1: 低波动性，增强信号 (V = 1)
        # < -1: 高波动性，减弱信号 (V = -1)
        # 其他: 正常波动性 (V = 0)
        dataframe["V"] = np.where(
            dataframe["V_norm"] > 1, 1, np.where(dataframe["V_norm"] < -1, -1, 0)
        )
        dataframe["V2"] = np.where(
            dataframe["V2_norm"] > 1, 1, np.where(dataframe["V2_norm"] < -1, -1, 0)
        )

        # ==================== 第七步：计算最终目标值 T ====================

        """
        最终目标值计算：
        T = S × R × R2 × V × V2

        其中：
        - S: 技术指标综合评分
        - R: 短期市场状态过滤器（基于布林带）
        - R2: 长期趋势过滤器（基于长期移动平均）
        - V: 布林带波动性调整因子
        - V2: ATR波动性调整因子

        这个复合评分系统确保只有在多个条件同时满足时才产生强信号。
        """

        dataframe["T"] = (
            dataframe["S"] * dataframe["R"] * dataframe["R2"] * dataframe["V"] * dataframe["V2"]
        )

        # 设置FreqAI目标：将目标值向前移动一个周期用于预测
        target_horizon = 1  # 预测时间范围：1个时间周期
        dataframe["&-target"] = dataframe["T"].shift(-target_horizon)

        return dataframe

    # ==================== 策略核心方法 ====================

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标

        这个方法启动FreqAI模型并返回包含预测结果的DataFrame。
        FreqAI会自动调用特征工程方法和目标设置方法。

        Args:
            dataframe: 包含OHLCV数据的DataFrame
            metadata: 交易对元数据

        Returns:
            DataFrame: 包含技术指标和AI预测的DataFrame
        """
        # 获取FreqAI配置
        self.freqai_info = self.config["freqai"]

        # 启动FreqAI模型进行特征工程和预测
        dataframe = self.freqai.start(dataframe, metadata, self)

        return dataframe

    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场信号

        根据AI模型预测结果和阈值判断生成买入和卖出信号。

        Args:
            df: 包含预测结果的DataFrame
            metadata: 交易对元数据

        Returns:
            DataFrame: 包含入场信号的DataFrame
        """
        # 多头入场条件
        enter_long_conditions = [
            df["do_predict"] == 1,  # AI模型可以进行预测
            df["&-target"] > self.threshold_buy.value,  # 预测值超过买入阈值
            df["volume"] > 0,  # 有成交量
        ]

        # 空头入场条件
        enter_short_conditions = [
            df["do_predict"] == 1,  # AI模型可以进行预测
            df["&-target"] < self.threshold_sell.value,  # 预测值低于卖出阈值
            df["volume"] > 0,  # 有成交量
        ]

        # 设置多头入场信号
        df.loc[reduce(lambda x, y: x & y, enter_long_conditions), ["enter_long", "enter_tag"]] = (
            1,
            "long",
        )

        # 设置空头入场信号
        df.loc[reduce(lambda x, y: x & y, enter_short_conditions), ["enter_short", "enter_tag"]] = (
            1,
            "short",
        )

        return df

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        填充出场信号

        根据AI模型预测结果判断何时退出多头或空头仓位。

        Args:
            df: 包含预测结果的DataFrame
            metadata: 交易对元数据

        Returns:
            DataFrame: 包含出场信号的DataFrame
        """
        # 多头出场条件：预测值低于卖出阈值
        exit_long_conditions = [df["do_predict"] == 1, df["&-target"] < self.threshold_sell.value]

        # 空头出场条件：预测值高于买入阈值
        exit_short_conditions = [df["do_predict"] == 1, df["&-target"] > self.threshold_buy.value]

        # 设置多头出场信号
        if exit_long_conditions:
            df.loc[
                reduce(lambda x, y: x & y, exit_long_conditions),
                #
                ["exit_long", "exit_tag"],
            ] = (1, "exit_long")

        # 设置空头出场信号
        if exit_short_conditions:
            df.loc[
                reduce(lambda x, y: x & y, exit_short_conditions),
                #
                ["exit_short", "exit_tag"],
            ] = (1, "exit_short")

        return df

    def leverage(
        self,
        pair: str,
        current_time: str,  # 修复类型注解
        current_rate: float,
        proposed_leverage: float,
        **kwargs,
    ) -> float:
        """
        设置杠杆倍数

        Args:
            pair: 交易对
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: 建议的杠杆倍数
            **kwargs: 其他参数

        Returns:
            float: 实际使用的杠杆倍数
        """
        return self.leverage_value
