import{o as f,c as h,a as s,g as L,aN as D,b0 as I,C as R,bn as V,bo as p,b as t,w as a,x as y,h as e,e as d,F as z,K as F,s as N,a0 as $,Y as O,v as B,V as A,$ as G,W as H,bp as u,p as M,d as q,af as K,_ as W}from"./index-B2p78N-x.js";const Z={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Y=s("path",{fill:"currentColor",d:"M7.03 13.92h4V5l2.01-.03v8.95h3.99l-5 5Z"},null,-1),j=[Y];function J(m,l){return f(),h("svg",Z,[...j])}const Q={name:"mdi-arrow-down-thin",render:J},X={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},ee=s("path",{fill:"currentColor",d:"M7.03 9.97h4v8.92l2.01.03V9.97h3.99l-5-5Z"},null,-1),oe=[ee];function te(m,l){return f(),h("svg",X,[...oe])}const ne={name:"mdi-arrow-up-thin",render:te},g=m=>(M("data-v-7cd15d3b"),m=m(),q(),m),le={class:"container mt-3"},ae={class:"text-start d-flex flex-column gap-2"},se={class:"d-flex flex-column border rounded p-2 mb-2 gap-2"},ie=g(()=>s("h4",null,"UI settings",-1)),de={class:"d-flex flex-column border rounded p-2 mb-2 gap-2"},re=g(()=>s("h4",null,"Candle settings",-1)),ue={class:"d-flex"},ce={class:"me-2"},me={class:"d-flex flex-column border rounded p-2 mb-2 gap-2"},_e=g(()=>s("h4",null,"Notification Settings",-1)),pe=L({__name:"SettingsView",setup(m){const l=D(),c=I(),_=R(),b=["UTC",Intl.DateTimeFormat().resolvedOptions().timeZone],w=[{value:V.showPill,text:"Show pill in icon"},{value:V.asTitle,text:"Show in title"},{value:V.noOpenTrades,text:"Don't show open trades in header"}],v=[{value:p.GREEN_UP,text:"Green Up/Red Down"},{value:p.RED_UP,text:"Green Down/Red Up"}],C=()=>{_.resetTradingLayout(),_.resetDashboardLayout(),N("Layouts have been reset.")};return(fe,n)=>{const r=$,i=O,U=B,x=A,S=ne,k=Q,T=K,P=G,E=H;return f(),h("div",le,[t(E,{header:"FreqUI Settings"},{default:a(()=>[s("div",ae,[s("p",null,"UI Version: "+y(e(l).uiVersion),1),s("div",se,[ie,t(i,{description:"Lock dynamic layouts, so they cannot move anymore. Can also be set from the navbar at the top."},{default:a(()=>[t(r,{modelValue:e(_).layoutLocked,"onUpdate:modelValue":n[0]||(n[0]=o=>e(_).layoutLocked=o)},{default:a(()=>[d("Lock layout")]),_:1},8,["modelValue"])]),_:1}),t(i,{description:"Reset dynamic layouts to how they were."},{default:a(()=>[t(U,{size:"sm",class:"me-1",onClick:C},{default:a(()=>[d("Reset layout")]),_:1})]),_:1}),t(i,{label:"Show open trades in header",description:"Decide if open trades should be visualized"},{default:a(()=>[t(x,{modelValue:e(l).openTradesInTitle,"onUpdate:modelValue":n[1]||(n[1]=o=>e(l).openTradesInTitle=o),options:w},null,8,["modelValue"])]),_:1}),t(i,{label:"UTC Timezone",description:"Select timezone (we recommend UTC is recommended as exchanges usually work in UTC)"},{default:a(()=>[t(x,{modelValue:e(l).timezone,"onUpdate:modelValue":n[2]||(n[2]=o=>e(l).timezone=o),options:b},null,8,["modelValue"])]),_:1}),t(i,{description:"Keep background sync running while other bots are selected."},{default:a(()=>[t(r,{modelValue:e(l).backgroundSync,"onUpdate:modelValue":n[3]||(n[3]=o=>e(l).backgroundSync=o)},{default:a(()=>[d("Background sync")]),_:1},8,["modelValue"])]),_:1}),t(i,{description:"Use confirmation dialogs when force-exiting a trade."},{default:a(()=>[t(r,{modelValue:e(l).confirmDialog,"onUpdate:modelValue":n[4]||(n[4]=o=>e(l).confirmDialog=o)},{default:a(()=>[d("Show Confirm Dialog for Trade Exits")]),_:1},8,["modelValue"])]),_:1})]),s("div",de,[re,t(i,{description:"Use Heikin Ashi candles in your charts"},{default:a(()=>[t(r,{modelValue:e(l).useHeikinAshiCandles,"onUpdate:modelValue":n[5]||(n[5]=o=>e(l).useHeikinAshiCandles=o)},{default:a(()=>[d("Use Heikin Ashi candles.")]),_:1},8,["modelValue"])]),_:1}),t(i,{description:"Can reduce the transfer size for large dataframes. May require additional calls if the plot config changes."},{default:a(()=>[t(r,{modelValue:e(l).useReducedPairCalls,"onUpdate:modelValue":n[6]||(n[6]=o=>e(l).useReducedPairCalls=o)},{default:a(()=>[d("Only request necessary columns (recommended to be checked).")]),_:1},8,["modelValue"])]),_:1}),t(i,{description:"Candle Color Preference"},{default:a(()=>[t(P,{id:"settings-color-preference-radio-group",modelValue:e(c).colorPreference,"onUpdate:modelValue":n[7]||(n[7]=o=>e(c).colorPreference=o),name:"color-preference-options",onChange:e(c).updateProfitLossColor},{default:a(()=>[(f(),h(z,null,F(v,o=>t(T,{key:o.value,value:o.value},{default:a(()=>[s("div",ue,[s("span",ce,y(o.text),1),t(S,{color:o.value===e(p).GREEN_UP?e(c).colorProfit:e(c).colorLoss,class:"color-candle-arrows"},null,8,["color"]),t(k,{color:o.value===e(p).GREEN_UP?e(c).colorLoss:e(c).colorProfit,class:"color-candle-arrows"},null,8,["color"])])]),_:2},1032,["value"])),64))]),_:1},8,["modelValue","onChange"])]),_:1})]),s("div",me,[t(i,{description:"Notifications"},{default:a(()=>[_e,t(r,{modelValue:e(l).notifications[e(u).entryFill],"onUpdate:modelValue":n[8]||(n[8]=o=>e(l).notifications[e(u).entryFill]=o)},{default:a(()=>[d("Entry notifications")]),_:1},8,["modelValue"]),t(r,{modelValue:e(l).notifications[e(u).exitFill],"onUpdate:modelValue":n[9]||(n[9]=o=>e(l).notifications[e(u).exitFill]=o)},{default:a(()=>[d("Exit notifications")]),_:1},8,["modelValue"]),t(r,{modelValue:e(l).notifications[e(u).entryCancel],"onUpdate:modelValue":n[10]||(n[10]=o=>e(l).notifications[e(u).entryCancel]=o)},{default:a(()=>[d("Entry Cancel notifications")]),_:1},8,["modelValue"]),t(r,{modelValue:e(l).notifications[e(u).exitCancel],"onUpdate:modelValue":n[11]||(n[11]=o=>e(l).notifications[e(u).exitCancel]=o)},{default:a(()=>[d("Exit Cancel notifications")]),_:1},8,["modelValue"])]),_:1})])])]),_:1})])}}}),Ve=W(pe,[["__scopeId","data-v-7cd15d3b"]]);export{Ve as default};
//# sourceMappingURL=SettingsView-D4xEdT6E.js.map
