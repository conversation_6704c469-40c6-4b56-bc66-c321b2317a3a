{"version": 3, "file": "LogView-DlCFtUcf.js", "sources": ["../../src/components/ftbot/LogViewer.vue", "../../src/views/LogView.vue"], "sourcesContent": ["<template>\n  <div class=\"d-flex h-100 p-0 align-items-start\">\n    <div ref=\"scrollContainer\" class=\"border p-1 text-start pb-5 w-100 h-100 overflow-auto\">\n      <pre\n        v-for=\"(log, index) in botStore.activeBot.lastLogs\"\n        :key=\"index\"\n        class=\"m-0 overflow-visible\"\n        style=\"line-height: unset\"\n      ><span class=\"text-muted\">{{ log[0] }} <span :class=\"getLogColor(log[3])\">{{ log[3].padEnd(7, ' ') }}</span> {{ log[2] }} - </span><span class=\"text-{{ log[1] }}\">{{ log[4] }}</span\n        ></pre>\n    </div>\n    <div class=\"d-flex flex-column gap-1 ms-1\">\n      <b-button id=\"refresh-logs\" size=\"sm\" title=\"Reload Logs\" @click=\"refreshLogs\">\n        <i-mdi-refresh />\n      </b-button>\n      <b-button size=\"sm\" title=\"Scroll to bottom\" @click=\"scrollToBottom\">\n        <i-mdi-arrow-down-thick />\n      </b-button>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst botStore = useBotStore();\nconst scrollContainer = ref<HTMLElement | null>(null);\n\nonMounted(async () => {\n  refreshLogs();\n});\n\nasync function refreshLogs() {\n  await botStore.activeBot.getLogs();\n  scrollToBottom();\n}\n\nfunction getLogColor(logLevel: string) {\n  switch (logLevel) {\n    case 'WARNING':\n      return 'text-warning';\n    case 'ERROR':\n      return 'text-danger';\n    default:\n      return 'text-secondary';\n  }\n}\n\nfunction scrollToBottom() {\n  if (scrollContainer.value) {\n    scrollContainer.value.scrollTop = scrollContainer.value.scrollHeight;\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\ntextarea {\n  width: 100%;\n  min-height: 6em;\n  resize: none;\n  font-size: $fontsize-small;\n}\n</style>\n", "<template>\n  <div class=\"p-1 p-md-4 pe-md-2 h-100\">\n    <LogViewer />\n  </div>\n</template>\n\n<script setup lang=\"ts\"></script>\n\n<style scoped></style>\n"], "names": ["botStore", "useBotStore", "scrollContainer", "ref", "onMounted", "refreshLogs", "scrollToBottom", "getLogColor", "logLevel", "_openBlock", "_createElementBlock", "_hoisted_1"], "mappings": "mlBAyBA,MAAMA,EAAWC,IACXC,EAAkBC,EAAwB,IAAI,EAEpDC,EAAU,SAAY,CACRC,GAAA,CACb,EAED,eAAeA,GAAc,CACrB,MAAAL,EAAS,UAAU,UACVM,GACjB,CAEA,SAASC,EAAYC,EAAkB,CACrC,OAAQA,EAAU,CAChB,IAAK,UACI,MAAA,eACT,IAAK,QACI,MAAA,cACT,QACS,MAAA,gBACX,CACF,CAEA,SAASF,GAAiB,CACpBJ,EAAgB,QACFA,EAAA,MAAM,UAAYA,EAAgB,MAAM,aAE5D,suBClDiBO,EAAA,EAAAC,EAAA,MAAAC,EAAA"}