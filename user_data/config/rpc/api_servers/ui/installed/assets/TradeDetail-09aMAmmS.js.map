{"version": 3, "file": "TradeDetail-09aMAmmS.js", "sources": ["../../src/components/ftbot/TradeDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"container text-start\">\n    <div class=\"row\">\n      <div class=\"col-lg-5\">\n        <h5 class=\"detail-header\">General</h5>\n        <ValuePair description=\"Trade Id\">{{ trade.trade_id }}</ValuePair>\n        <ValuePair description=\"Pair\">{{ trade.pair }}</ValuePair>\n\n        <ValuePair description=\"Open date\">{{ timestampms(trade.open_timestamp) }}</ValuePair>\n        <ValuePair v-if=\"trade.enter_tag\" description=\"Entry tag\">{{ trade.enter_tag }}</ValuePair>\n        <ValuePair description=\"Stake\"\n          >{{ formatPriceCurrency(trade.stake_amount, stakeCurrency) }}\n          {{ trade.leverage && trade.leverage !== 1 ? `(${trade.leverage}x)` : '' }}</ValuePair\n        >\n        <ValuePair description=\"Amount\">{{ trade.amount }}</ValuePair>\n        <ValuePair description=\"Open Rate\">{{ formatPrice(trade.open_rate) }}</ValuePair>\n        <ValuePair v-if=\"trade.is_open && trade.current_rate\" description=\"Current Rate\">{{\n          formatPrice(trade.current_rate)\n        }}</ValuePair>\n        <ValuePair v-if=\"!trade.is_open && trade.close_rate\" description=\"Close Rate\">{{\n          formatPrice(trade.close_rate)\n        }}</ValuePair>\n\n        <ValuePair v-if=\"trade.close_timestamp\" description=\"Close date\">{{\n          timestampms(trade.close_timestamp)\n        }}</ValuePair>\n        <ValuePair\n          v-if=\"trade.is_open && trade.realized_profit && !trade.total_profit_abs\"\n          description=\"Realized Profit\"\n        >\n          <trade-profit class=\"ms-2\" :trade=\"trade\" mode=\"realized\" />\n        </ValuePair>\n        <ValuePair v-if=\"trade.is_open && trade.total_profit_abs\" description=\"Total Profit\">\n          <trade-profit class=\"ms-2\" :trade=\"trade\" mode=\"total\" />\n        </ValuePair>\n        <ValuePair\n          v-if=\"trade.profit_ratio && trade.profit_abs\"\n          :description=\"`${trade.is_open ? 'Current Profit' : 'Close Profit'}`\"\n        >\n          <trade-profit class=\"ms-2\" :trade=\"trade\" />\n        </ValuePair>\n        <details>\n          <summary>Details</summary>\n          <ValuePair v-if=\"trade.min_rate\" description=\"Min Rate\">{{\n            formatPrice(trade.min_rate)\n          }}</ValuePair>\n          <ValuePair v-if=\"trade.max_rate\" description=\"Max Rate\">{{\n            formatPrice(trade.max_rate)\n          }}</ValuePair>\n          <ValuePair description=\"Open-Fees\">\n            {{ trade.fee_open_cost }} {{ trade.quote_currency }}\n            <span v-if=\"trade.quote_currency !== trade.fee_open_currency\">\n              (in {{ trade.fee_open_currency }})\n            </span>\n            ({{ formatPercent(trade.fee_open) }})\n          </ValuePair>\n          <ValuePair v-if=\"trade.fee_close_cost && trade.fee_close\" description=\"Fees close\">\n            {{ trade.fee_close_cost }} {{ trade.fee_close_currency }} ({{\n              formatPercent(trade.fee_close)\n            }})\n          </ValuePair>\n        </details>\n      </div>\n      <div class=\"mt-2 mt-lg-0 col-lg-7\">\n        <h5 class=\"detail-header\">Stoploss</h5>\n        <ValuePair description=\"Stoploss\">\n          {{ formatPercent(trade.stop_loss_pct / 100) }} |\n          {{ formatPrice(trade.stop_loss_abs) }}\n        </ValuePair>\n        <ValuePair\n          v-if=\"trade.initial_stop_loss_pct && trade.initial_stop_loss_abs\"\n          description=\"Initial Stoploss\"\n        >\n          {{ formatPercent(trade.initial_stop_loss_pct / 100) }} |\n          {{ formatPrice(trade.initial_stop_loss_abs) }}\n        </ValuePair>\n        <ValuePair\n          v-if=\"trade.is_open && trade.stoploss_current_dist_ratio && trade.stoploss_current_dist\"\n          description=\"Current stoploss dist\"\n        >\n          {{ formatPercent(trade.stoploss_current_dist_ratio) }} |\n          {{ formatPrice(trade.stoploss_current_dist) }}\n        </ValuePair>\n        <ValuePair v-if=\"trade.stoploss_last_update_timestamp\" description=\"Stoploss last updated\">\n          {{ timestampms(trade.stoploss_last_update_timestamp) }}\n        </ValuePair>\n        <div v-if=\"trade.trading_mode !== undefined && trade.trading_mode !== 'spot'\">\n          <h5 class=\"detail-header\">Futures/Margin</h5>\n          <ValuePair description=\"Direction\">\n            {{ trade.is_short ? 'short' : 'long' }} - {{ trade.leverage }}x\n          </ValuePair>\n          <ValuePair v-if=\"trade.funding_fees !== undefined\" description=\"Funding fees\">\n            {{ formatPrice(trade.funding_fees) }}\n          </ValuePair>\n          <ValuePair v-if=\"trade.interest_rate !== undefined\" description=\"Interest rate\">\n            {{ formatPrice(trade.interest_rate) }}\n          </ValuePair>\n          <ValuePair v-if=\"trade.liquidation_price !== undefined\" description=\"Liquidation Price\">\n            {{ formatPrice(trade.liquidation_price) }}\n          </ValuePair>\n        </div>\n        <details v-if=\"trade.orders\">\n          <summary>Orders {{ trade.orders.length > 1 ? `[${trade.orders.length}]` : '' }}</summary>\n          <div v-for=\"(order, key) in trade.orders\" :key=\"key\">\n            <span\n              :title=\"`${order.ft_order_side} ${order.order_type} order for ${formatPriceCurrency(\n                order.amount,\n                trade.base_currency ?? '',\n              )} at ${formatPriceCurrency(\n                order.safe_price,\n                trade.quote_currency ?? '',\n              )}, filled ${formatPrice(order.filled)}`\"\n            >\n              (#{{ key + 1 }})\n              <i-mdi-triangle\n                v-if=\"order.ft_order_side === 'buy'\"\n                class=\"me-1 color-up\"\n                style=\"font-size: 0.6rem\"\n              />\n              <i-mdi-triangle-down v-else class=\"me-1 color-down\" style=\"font-size: 0.6rem\" />\n              <DateTimeTZ\n                v-if=\"order.order_timestamp\"\n                :date=\"order.order_timestamp\"\n                show-timezone\n              />\n              <b class=\"ms-1\" :class=\"order.ft_order_side === 'buy' ? 'color-up' : 'color-down'\">{{\n                order.ft_order_side\n              }}</b>\n              for <b>{{ formatPrice(order.safe_price) }}</b> |\n              <span v-if=\"order.remaining && order.remaining !== 0\" title=\"remaining\"\n                >{{ formatPrice(order.remaining, 8) }} /\n              </span>\n              <span title=\"Filled\">{{ formatPrice(order.filled ?? 0, 8) }}</span>\n              <template v-if=\"order.ft_order_tag\"> | {{ order.ft_order_tag ?? '' }}</template>\n            </span>\n          </div>\n        </details>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent, formatPriceCurrency, formatPrice, timestampms } from '@/shared/formatters';\nimport { Trade } from '@/types';\n\nconst colorStore = useColorStore();\n\ndefineProps({\n  trade: { required: true, type: Object as () => Trade },\n  stakeCurrency: { required: true, type: String },\n});\n</script>\n<style scoped>\n.detail-header {\n  border-bottom: 1px solid;\n  padding-bottom: 5px;\n  width: 100%;\n  display: block;\n}\n.color-up {\n  color: v-bind('colorStore.colorUp');\n}\n\n.color-down {\n  color: v-bind('colorStore.colorDown');\n}\n</style>\n"], "names": ["colorStore", "useColorStore"], "mappings": "w0DAkJA,MAAMA,EAAaC"}