{"version": 3, "file": "DashboardView-CLnVpUSi.js", "sources": ["../../src/components/ftbot/BotComparisonList.vue", "../../src/views/DashboardView.vue"], "sourcesContent": ["<template>\n  <b-table\n    ref=\"tradesTable\"\n    small\n    hover\n    show-empty\n    primary-key=\"botId\"\n    :items=\"tableItems\"\n    :fields=\"tableFields\"\n  >\n    <template #cell(botName)=\"{ item, value }\">\n      <div class=\"d-flex flex-row\">\n        <b-form-checkbox\n          v-if=\"item.botId && botStore.botCount > 1\"\n          v-model=\"\n            botStore.botStores[(item as unknown as ComparisonTableItems).botId ?? ''].isSelected\n          \"\n          title=\"Show this bot in Dashboard\"\n          >{{ value }}</b-form-checkbox\n        >\n        <b-form-checkbox\n          v-if=\"!item.botId && botStore.botCount > 1\"\n          v-model=\"allToggled\"\n          title=\"Toggle all bots\"\n          >{{ value }}</b-form-checkbox\n        >\n        <span v-if=\"botStore.botCount <= 1\">{{ value }}</span>\n      </div>\n    </template>\n    <template #cell(profitOpen)=\"{ item }\">\n      <profit-pill\n        v-if=\"item.profitOpen && item.botId != 'Summary'\"\n        :profit-ratio=\"(item as unknown as ComparisonTableItems).profitOpenRatio\"\n        :profit-abs=\"(item as unknown as ComparisonTableItems).profitOpen\"\n        :profit-desc=\"`Total Profit (Open and realized) ${formatPercent(\n          (item as unknown as ComparisonTableItems).profitOpenRatio ?? 0.0,\n        )}`\"\n        :stake-currency=\"(item as unknown as ComparisonTableItems).stakeCurrency\"\n      />\n    </template>\n    <template #cell(profitClosed)=\"{ item }\">\n      <profit-pill\n        v-if=\"item.profitClosed && item.botId != 'Summary'\"\n        :profit-ratio=\"(item as unknown as ComparisonTableItems).profitClosedRatio\"\n        :profit-abs=\"(item as unknown as ComparisonTableItems).profitClosed\"\n        :stake-currency=\"(item as unknown as ComparisonTableItems).stakeCurrency\"\n      />\n    </template>\n\n    <template #cell(balance)=\"{ item }\">\n      <div v-if=\"item.balance\">\n        <span :title=\"(item as unknown as ComparisonTableItems).stakeCurrency\"\n          >{{\n            formatPrice(\n              (item as unknown as ComparisonTableItems).balance ?? 0,\n              (item as unknown as ComparisonTableItems).stakeCurrencyDecimals,\n            )\n          }}\n        </span>\n        <span class=\"text-small\">{{\n          ` ${item.stakeCurrency}${item.isDryRun ? ' (dry)' : ''}`\n        }}</span>\n      </div>\n    </template>\n    <template #cell(winVsLoss)=\"{ item }\">\n      <div v-if=\"item.losses !== undefined\">\n        <span class=\"text-profit\">{{ item.wins }}</span> /\n        <span class=\"text-loss\">{{ item.losses }}</span>\n      </div>\n    </template>\n  </b-table>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPrice, formatPercent } from '@/shared/formatters';\n\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { ProfitInterface, ComparisonTableItems } from '@/types';\nimport { TableField, TableItem } from 'bootstrap-vue-next';\n\nconst botStore = useBotStore();\n\nconst allToggled = computed<boolean>({\n  get: () => Object.values(botStore.botStores).every((i) => i.isSelected),\n  set: (val) => {\n    for (const botId in botStore.botStores) {\n      botStore.botStores[botId].isSelected = val;\n    }\n  },\n});\n\nconst tableFields: TableField[] = [\n  { key: 'botName', label: 'Bot' },\n  { key: 'trades', label: 'Trades' },\n  { key: 'profitOpen', label: 'Open Profit' },\n  { key: 'profitClosed', label: 'Closed Profit' },\n  { key: 'balance', label: 'Balance' },\n  { key: 'winVsLoss', label: 'W/L' },\n];\n\nconst tableItems = computed<TableItem[]>(() => {\n  const val: ComparisonTableItems[] = [];\n  const summary: ComparisonTableItems = {\n    botId: undefined,\n    botName: 'Summary',\n    profitClosed: 0,\n    profitClosedRatio: undefined,\n    profitOpen: 0,\n    profitOpenRatio: undefined,\n    stakeCurrency: 'USDT',\n    wins: 0,\n    losses: 0,\n  };\n\n  Object.entries(botStore.allProfit).forEach(([k, v]: [k: string, v: ProfitInterface]) => {\n    const allStakes = botStore.allOpenTrades[k].reduce((a, b) => a + b.stake_amount, 0);\n    const profitOpenRatio =\n      botStore.allOpenTrades[k].reduce(\n        (a, b) => a + (b.total_profit_ratio ?? b.profit_ratio) * b.stake_amount,\n        0,\n      ) / allStakes;\n    const profitOpen = botStore.allOpenTrades[k].reduce(\n      (a, b) => a + (b.total_profit_abs ?? b.profit_abs ?? 0),\n      0,\n    );\n\n    // TODO: handle one inactive bot ...\n    val.push({\n      botId: k,\n      botName: botStore.availableBots[k].botName || botStore.availableBots[k].botId,\n      trades: `${botStore.allOpenTradeCount[k]} / ${\n        botStore.allBotState[k]?.max_open_trades || 'N/A'\n      }`,\n      profitClosed: v.profit_closed_coin,\n      profitClosedRatio: v.profit_closed_ratio || 0,\n      stakeCurrency: botStore.allBotState[k]?.stake_currency || '',\n      profitOpenRatio,\n      profitOpen,\n      wins: v.winning_trades,\n      losses: v.losing_trades,\n      balance: botStore.allBalance[k]?.total_bot ?? botStore.allBalance[k]?.total,\n      stakeCurrencyDecimals: botStore.allBotState[k]?.stake_currency_decimals || 3,\n      isDryRun: botStore.allBotState[k]?.dry_run,\n    });\n    if (v.profit_closed_coin !== undefined) {\n      if (botStore.botStores[k].isSelected) {\n        // Summary should only include selected bots\n        summary.profitClosed += v.profit_closed_coin;\n        summary.profitOpen += profitOpen;\n        summary.wins += v.winning_trades;\n        summary.losses += v.losing_trades;\n        // summary.decimals = this.allBotState[k]?.stake_currency_decimals || summary.decimals;\n        // This will always take the last bot's stake currency\n        // And therefore may result in wrong values.\n        summary.stakeCurrency = botStore.allBotState[k]?.stake_currency || summary.stakeCurrency;\n      }\n    }\n  });\n  val.push(summary);\n  return val as unknown as TableItem[];\n});\n</script>\n\n<style scoped></style>\n", "<template>\n  <grid-layout\n    class=\"h-100 w-100\"\n    :row-height=\"50\"\n    :layout=\"gridLayoutData\"\n    :vertical-compact=\"false\"\n    :margin=\"[5, 5]\"\n    :responsive-layouts=\"responsiveGridLayouts\"\n    :is-resizable=\"!isLayoutLocked\"\n    :is-draggable=\"!isLayoutLocked\"\n    :responsive=\"true\"\n    :prevent-collision=\"true\"\n    :cols=\"{ lg: 12, md: 12, sm: 12, xs: 4, xxs: 2 }\"\n    :col-num=\"12\"\n    @layout-updated=\"layoutUpdatedEvent\"\n    @update:breakpoint=\"breakpointChanged\"\n  >\n    <template #default=\"{ gridItemProps }\">\n      <grid-item\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutDaily.i\"\n        :x=\"gridLayoutDaily.x\"\n        :y=\"gridLayoutDaily.y\"\n        :w=\"gridLayoutDaily.w\"\n        :h=\"gridLayoutDaily.h\"\n        :min-w=\"3\"\n        :min-h=\"4\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer :header=\"`Daily Profit ${botStore.botCount > 1 ? 'combined' : ''}`\">\n          <TimePeriodChart\n            v-if=\"botStore.allDailyStatsSelectedBots\"\n            :daily-stats=\"botStore.allDailyStatsSelectedBots\"\n            :show-title=\"false\"\n          />\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutBotComparison.i\"\n        :x=\"gridLayoutBotComparison.x\"\n        :y=\"gridLayoutBotComparison.y\"\n        :w=\"gridLayoutBotComparison.w\"\n        :h=\"gridLayoutBotComparison.h\"\n        :min-w=\"3\"\n        :min-h=\"4\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer header=\"Bot comparison\">\n          <bot-comparison-list />\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutAllOpenTrades.i\"\n        :x=\"gridLayoutAllOpenTrades.x\"\n        :y=\"gridLayoutAllOpenTrades.y\"\n        :w=\"gridLayoutAllOpenTrades.w\"\n        :h=\"gridLayoutAllOpenTrades.h\"\n        :min-w=\"3\"\n        :min-h=\"4\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer>\n          <template #header>\n            <div class=\"d-flex justify-content-center\">\n              Open Trades\n              <InfoBox\n                class=\"ms-2\"\n                hint=\"Open trades of all selected bots. Click on a trade to go to the trade page for that trade/bot.\"\n              />\n            </div>\n          </template>\n          <trade-list active-trades :trades=\"botStore.allOpenTradesSelectedBots\" multi-bot-view />\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutCumChart.i\"\n        :x=\"gridLayoutCumChart.x\"\n        :y=\"gridLayoutCumChart.y\"\n        :w=\"gridLayoutCumChart.w\"\n        :h=\"gridLayoutCumChart.h\"\n        :min-w=\"3\"\n        :min-h=\"4\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer header=\"Cumulative Profit\">\n          <CumProfitChart\n            :trades=\"botStore.allTradesSelectedBots\"\n            :open-trades=\"botStore.allOpenTradesSelectedBots\"\n            :show-title=\"false\"\n          />\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutAllClosedTrades.i\"\n        :x=\"gridLayoutAllClosedTrades.x\"\n        :y=\"gridLayoutAllClosedTrades.y\"\n        :w=\"gridLayoutAllClosedTrades.w\"\n        :h=\"gridLayoutAllClosedTrades.h\"\n        :min-w=\"3\"\n        :min-h=\"4\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer>\n          <template #header>\n            <div class=\"d-flex justify-content-center\">\n              Closed Trades\n              <InfoBox\n                class=\"ms-2\"\n                hint=\"Closed trades for all selected bots. Click on a trade to go to the trade page for that trade/bot.\"\n              />\n            </div>\n          </template>\n          <trade-list\n            :active-trades=\"false\"\n            show-filter\n            :trades=\"botStore.allClosedTradesSelectedBots\"\n            multi-bot-view\n          />\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutProfitDistribution.i\"\n        :x=\"gridLayoutProfitDistribution.x\"\n        :y=\"gridLayoutProfitDistribution.y\"\n        :w=\"gridLayoutProfitDistribution.w\"\n        :h=\"gridLayoutProfitDistribution.h\"\n        :min-w=\"3\"\n        :min-h=\"4\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer header=\"Profit Distribution\">\n          <ProfitDistributionChart :trades=\"botStore.allTradesSelectedBots\" :show-title=\"false\" />\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutTradesLogChart.i\"\n        :x=\"gridLayoutTradesLogChart.x\"\n        :y=\"gridLayoutTradesLogChart.y\"\n        :w=\"gridLayoutTradesLogChart.w\"\n        :h=\"gridLayoutTradesLogChart.h\"\n        :min-w=\"3\"\n        :min-h=\"4\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer header=\"Trades Log\">\n          <TradesLogChart :trades=\"botStore.allTradesSelectedBots\" :show-title=\"false\" />\n        </DraggableContainer>\n      </grid-item>\n    </template>\n  </grid-layout>\n</template>\n\n<script setup lang=\"ts\">\nimport { DashboardLayout, findGridLayout, useLayoutStore } from '@/stores/layout';\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { GridItemData } from '@/types';\n\nconst botStore = useBotStore();\n\nconst layoutStore = useLayoutStore();\nconst currentBreakpoint = ref('');\n\nconst breakpointChanged = (newBreakpoint: string) => {\n  // console.log('breakpoint:', newBreakpoint);\n  currentBreakpoint.value = newBreakpoint;\n};\nconst isResizableLayout = computed(() =>\n  ['', 'sm', 'md', 'lg', 'xl'].includes(currentBreakpoint.value),\n);\nconst isLayoutLocked = computed(() => {\n  return layoutStore.layoutLocked || !isResizableLayout.value;\n});\n\nconst gridLayoutData = computed((): GridItemData[] => {\n  if (isResizableLayout.value) {\n    return layoutStore.dashboardLayout;\n  }\n  return [...layoutStore.getDashboardLayoutSm];\n});\n\nconst layoutUpdatedEvent = (newLayout) => {\n  if (isResizableLayout.value) {\n    console.log('newlayout', newLayout);\n    console.log('saving dashboard');\n    layoutStore.dashboardLayout = newLayout;\n  }\n};\n\nconst gridLayoutDaily = computed((): GridItemData => {\n  return findGridLayout(gridLayoutData.value, DashboardLayout.dailyChart);\n});\n\nconst gridLayoutBotComparison = computed((): GridItemData => {\n  return findGridLayout(gridLayoutData.value, DashboardLayout.botComparison);\n});\n\nconst gridLayoutAllOpenTrades = computed((): GridItemData => {\n  return findGridLayout(gridLayoutData.value, DashboardLayout.allOpenTrades);\n});\nconst gridLayoutAllClosedTrades = computed((): GridItemData => {\n  return findGridLayout(gridLayoutData.value, DashboardLayout.allClosedTrades);\n});\n\nconst gridLayoutCumChart = computed((): GridItemData => {\n  return findGridLayout(gridLayoutData.value, DashboardLayout.cumChartChart);\n});\nconst gridLayoutProfitDistribution = computed((): GridItemData => {\n  return findGridLayout(gridLayoutData.value, DashboardLayout.profitDistributionChart);\n});\nconst gridLayoutTradesLogChart = computed((): GridItemData => {\n  return findGridLayout(gridLayoutData.value, DashboardLayout.tradesLogChart);\n});\n\nconst responsiveGridLayouts = computed(() => {\n  return {\n    sm: layoutStore.getDashboardLayoutSm,\n  };\n});\n\nonMounted(async () => {\n  botStore.allGetDaily({ timescale: 30 });\n  // botStore.activeBot.getTrades();\n  botStore.activeBot.getOpenTrades();\n  botStore.activeBot.getProfit();\n});\n</script>\n\n<style scoped></style>\n"], "names": ["botStore", "useBotStore", "allToggled", "computed", "i", "val", "botId", "tableFields", "tableItems", "summary", "k", "v", "allStakes", "a", "b", "profitOpenRatio", "profitOpen", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "layoutStore", "useLayoutStore", "currentBreakpoint", "ref", "breakpointChanged", "newBreakpoint", "isResizableLayout", "isLayoutLocked", "gridLayoutData", "layoutUpdatedEvent", "newLayout", "gridLayoutDaily", "findGridLayout", "DashboardLayout", "gridLayoutBotComparison", "gridLayoutAllOpenTrades", "gridLayoutAllClosedTrades", "gridLayoutCumChart", "gridLayoutProfitDistribution", "gridLayoutTradesLogChart", "responsiveGridLayouts", "onMounted"], "mappings": "iuBAgFA,MAAMA,EAAWC,IAEXC,EAAaC,EAAkB,CACnC,IAAK,IAAM,OAAO,OAAOH,EAAS,SAAS,EAAE,MAAOI,GAAMA,EAAE,UAAU,EACtE,IAAMC,GAAQ,CACD,UAAAC,KAASN,EAAS,UAClBA,EAAA,UAAUM,CAAK,EAAE,WAAaD,CAE3C,CAAA,CACD,EAEKE,EAA4B,CAChC,CAAE,IAAK,UAAW,MAAO,KAAM,EAC/B,CAAE,IAAK,SAAU,MAAO,QAAS,EACjC,CAAE,IAAK,aAAc,MAAO,aAAc,EAC1C,CAAE,IAAK,eAAgB,MAAO,eAAgB,EAC9C,CAAE,IAAK,UAAW,MAAO,SAAU,EACnC,CAAE,IAAK,YAAa,MAAO,KAAM,CAAA,EAG7BC,EAAaL,EAAsB,IAAM,CAC7C,MAAME,EAA8B,CAAA,EAC9BI,EAAgC,CACpC,MAAO,OACP,QAAS,UACT,aAAc,EACd,kBAAmB,OACnB,WAAY,EACZ,gBAAiB,OACjB,cAAe,OACf,KAAM,EACN,OAAQ,CAAA,EAGH,cAAA,QAAQT,EAAS,SAAS,EAAE,QAAQ,CAAC,CAACU,EAAGC,CAAC,IAAuC,mBACtF,MAAMC,EAAYZ,EAAS,cAAcU,CAAC,EAAE,OAAO,CAACG,EAAGC,IAAMD,EAAIC,EAAE,aAAc,CAAC,EAC5EC,EACJf,EAAS,cAAcU,CAAC,EAAE,OACxB,CAACG,EAAGC,IAAMD,GAAKC,EAAE,oBAAsBA,EAAE,cAAgBA,EAAE,aAC3D,CACE,EAAAF,EACAI,EAAahB,EAAS,cAAcU,CAAC,EAAE,OAC3C,CAACG,EAAGC,IAAMD,GAAKC,EAAE,kBAAoBA,EAAE,YAAc,GACrD,CAAA,EAIFT,EAAI,KAAK,CACP,MAAOK,EACP,QAASV,EAAS,cAAcU,CAAC,EAAE,SAAWV,EAAS,cAAcU,CAAC,EAAE,MACxE,OAAQ,GAAGV,EAAS,kBAAkBU,CAAC,CAAC,QACtCO,EAAAjB,EAAS,YAAYU,CAAC,IAAtB,YAAAO,EAAyB,kBAAmB,KAC9C,GACA,aAAcN,EAAE,mBAChB,kBAAmBA,EAAE,qBAAuB,EAC5C,gBAAeO,EAAAlB,EAAS,YAAYU,CAAC,IAAtB,YAAAQ,EAAyB,iBAAkB,GAC1D,gBAAAH,EACA,WAAAC,EACA,KAAML,EAAE,eACR,OAAQA,EAAE,cACV,UAASQ,EAAAnB,EAAS,WAAWU,CAAC,IAArB,YAAAS,EAAwB,cAAaC,EAAApB,EAAS,WAAWU,CAAC,IAArB,YAAAU,EAAwB,OACtE,wBAAuBC,EAAArB,EAAS,YAAYU,CAAC,IAAtB,YAAAW,EAAyB,0BAA2B,EAC3E,UAAUC,EAAAtB,EAAS,YAAYU,CAAC,IAAtB,YAAAY,EAAyB,OAAA,CACpC,EACGX,EAAE,qBAAuB,QACvBX,EAAS,UAAUU,CAAC,EAAE,aAExBD,EAAQ,cAAgBE,EAAE,mBAC1BF,EAAQ,YAAcO,EACtBP,EAAQ,MAAQE,EAAE,eAClBF,EAAQ,QAAUE,EAAE,cAIpBF,EAAQ,gBAAgBc,EAAAvB,EAAS,YAAYU,CAAC,IAAtB,YAAAa,EAAyB,iBAAkBd,EAAQ,cAE/E,CACD,EACDJ,EAAI,KAAKI,CAAO,EACTJ,CAAA,CACR,+yDCGD,MAAML,EAAWC,IAEXuB,EAAcC,KACdC,EAAoBC,GAAI,EAAE,EAE1BC,EAAqBC,GAA0B,CAEnDH,EAAkB,MAAQG,CAAA,EAEtBC,EAAoB3B,EAAS,IACjC,CAAC,GAAI,KAAM,KAAM,KAAM,IAAI,EAAE,SAASuB,EAAkB,KAAK,CAAA,EAEzDK,EAAiB5B,EAAS,IACvBqB,EAAY,cAAgB,CAACM,EAAkB,KACvD,EAEKE,EAAiB7B,EAAS,IAC1B2B,EAAkB,MACbN,EAAY,gBAEd,CAAC,GAAGA,EAAY,oBAAoB,CAC5C,EAEKS,EAAsBC,GAAc,CACpCJ,EAAkB,QACZ,QAAA,IAAI,YAAaI,CAAS,EAClC,QAAQ,IAAI,kBAAkB,EAC9BV,EAAY,gBAAkBU,EAChC,EAGIC,EAAkBhC,EAAS,IACxBiC,EAAeJ,EAAe,MAAOK,EAAgB,UAAU,CACvE,EAEKC,EAA0BnC,EAAS,IAChCiC,EAAeJ,EAAe,MAAOK,EAAgB,aAAa,CAC1E,EAEKE,EAA0BpC,EAAS,IAChCiC,EAAeJ,EAAe,MAAOK,EAAgB,aAAa,CAC1E,EACKG,EAA4BrC,EAAS,IAClCiC,EAAeJ,EAAe,MAAOK,EAAgB,eAAe,CAC5E,EAEKI,EAAqBtC,EAAS,IAC3BiC,EAAeJ,EAAe,MAAOK,EAAgB,aAAa,CAC1E,EACKK,EAA+BvC,EAAS,IACrCiC,EAAeJ,EAAe,MAAOK,EAAgB,uBAAuB,CACpF,EACKM,EAA2BxC,EAAS,IACjCiC,EAAeJ,EAAe,MAAOK,EAAgB,cAAc,CAC3E,EAEKO,EAAwBzC,EAAS,KAC9B,CACL,GAAIqB,EAAY,oBAAA,EAEnB,EAED,OAAAqB,GAAU,SAAY,CACpB7C,EAAS,YAAY,CAAE,UAAW,EAAI,CAAA,EAEtCA,EAAS,UAAU,gBACnBA,EAAS,UAAU,WAAU,CAC9B"}