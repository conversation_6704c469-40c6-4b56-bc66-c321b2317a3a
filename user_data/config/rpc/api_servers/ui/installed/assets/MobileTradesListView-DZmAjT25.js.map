{"version": 3, "file": "MobileTradesListView-DZmAjT25.js", "sources": ["../../src/components/ftbot/CustomTradeList.vue", "../../src/views/MobileTradesListView.vue"], "sourcesContent": ["<template>\n  <div class=\"h-100 overflow-auto p-1\">\n    <b-list-group id=\"tradeList\">\n      <b-list-group-item\n        v-for=\"trade in filteredTrades\"\n        :key=\"trade.trade_id\"\n        class=\"border border-secondary rounded my-05 px-1\"\n        @click=\"tradeClick(trade)\"\n      >\n        <CustomTradeListEntry :trade=\"trade\" :stake-currency-decimals=\"stakeCurrencyDecimals\" />\n      </b-list-group-item>\n    </b-list-group>\n\n    <span v-if=\"trades.length == 0\" class=\"mt-5\">{{ emptyText }}</span>\n\n    <div class=\"w-100 d-flex justify-content-between mt-1\">\n      <b-pagination\n        v-if=\"!activeTrades\"\n        v-model=\"currentPage\"\n        :total-rows=\"rows\"\n        :per-page=\"perPage\"\n        aria-controls=\"tradeList\"\n      ></b-pagination>\n      <b-form-input\n        v-if=\"showFilter\"\n        v-model=\"filterText\"\n        type=\"text\"\n        placeholder=\"Filter\"\n        size=\"sm\"\n        style=\"width: unset\"\n      />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { Trade } from '@/types';\n\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst props = defineProps({\n  trades: { required: true, type: Array as () => Trade[] },\n  title: { default: 'Trades', type: String },\n  stakeCurrency: { required: false, default: '', type: String },\n  activeTrades: { default: false, type: Boolean },\n  showFilter: { default: false, type: Boolean },\n  multiBotView: { default: false, type: Boolean },\n  emptyText: { default: 'No Trades to show.', type: String },\n  stakeCurrencyDecimals: { default: 3, type: Number },\n});\nconst botStore = useBotStore();\nconst currentPage = ref(1);\nconst filterText = ref('');\nconst perPage = props.activeTrades ? 200 : 25;\n\nconst rows = computed(() => props.trades.length);\n\nconst filteredTrades = computed(() => {\n  return props.trades.slice((currentPage.value - 1) * perPage, currentPage.value * perPage);\n});\n\nconst tradeClick = (trade) => {\n  botStore.activeBot.setDetailTrade(trade);\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.my-05 {\n  margin-top: 0.125rem;\n  margin-bottom: 0.125rem;\n}\n</style>\n", "<template>\n  <div>\n    <!-- <TradeList\n      class=\"open-trades\"\n      :trades=\"openTrades\"\n      title=\"Open trades\"\n      :active-trades=\"true\"\n      empty-text=\"Currently no open trades.\"\n    /> -->\n    <CustomTradeList\n      v-if=\"!history && !botStore.activeBot.detailTradeId\"\n      :trades=\"botStore.activeBot.openTrades\"\n      title=\"Open trades\"\n      :active-trades=\"true\"\n      :stake-currency-decimals=\"botStore.activeBot.stakeCurrencyDecimals\"\n      empty-text=\"No open Trades.\"\n    />\n    <CustomTradeList\n      v-if=\"history && !botStore.activeBot.detailTradeId\"\n      :trades=\"botStore.activeBot.closedTrades\"\n      title=\"Trade history\"\n      :stake-currency-decimals=\"botStore.activeBot.stakeCurrencyDecimals\"\n      empty-text=\"No closed trades so far.\"\n    />\n    <div\n      v-if=\"botStore.activeBot.detailTradeId && botStore.activeBot.tradeDetail\"\n      class=\"d-flex flex-column\"\n    >\n      <b-button\n        size=\"sm\"\n        class=\"align-self-start my-1 ms-1\"\n        @click=\"botStore.activeBot.setDetailTrade(null)\"\n        ><i-mdi-arrow-left /> Back</b-button\n      >\n      <TradeDetail\n        :trade=\"botStore.activeBot.tradeDetail\"\n        :stake-currency=\"botStore.activeBot.stakeCurrency\"\n      />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\ndefineProps({\n  history: { default: false, type: Boolean },\n});\nconst botStore = useBotStore();\n</script>\n\n<style scoped></style>\n"], "names": ["props", "__props", "botStore", "useBotStore", "currentPage", "ref", "filterText", "perPage", "rows", "computed", "filteredTrades", "tradeClick", "trade"], "mappings": "2tDAwCA,MAAMA,EAAQC,EAURC,EAAWC,IACXC,EAAcC,EAAI,CAAC,EACnBC,EAAaD,EAAI,EAAE,EACnBE,EAAUP,EAAM,aAAe,IAAM,GAErCQ,EAAOC,EAAS,IAAMT,EAAM,OAAO,MAAM,EAEzCU,EAAiBD,EAAS,IACvBT,EAAM,OAAO,OAAOI,EAAY,MAAQ,GAAKG,EAASH,EAAY,MAAQG,CAAO,CACzF,EAEKI,EAAcC,GAAU,CACnBV,EAAA,UAAU,eAAeU,CAAK,CAAA,0iCCdzC,MAAMV,EAAWC"}