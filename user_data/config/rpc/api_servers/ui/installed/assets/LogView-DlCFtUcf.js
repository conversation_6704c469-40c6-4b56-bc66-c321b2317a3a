import{o as n,c,a as t,g as w,u as L,r as b,q as C,F as k,K as B,e as p,x as r,L as y,h as V,b as s,w as m,i as N,v as R,_ as f}from"./index-B2p78N-x.js";const $={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},z=t("path",{fill:"currentColor",d:"M10 4h4v9l3.5-3.5l2.42 2.42L12 19.84l-7.92-7.92L6.5 9.5L10 13z"},null,-1),S=[z];function E(_,a){return n(),c("svg",$,[...S])}const T={name:"mdi-arrow-down-thick",render:E},F={class:"d-flex h-100 p-0 align-items-start"},I={class:"text-muted"},M={class:"text-{{ log[1] }}"},q={class:"d-flex flex-column gap-1 ms-1"},A=w({__name:"LogViewer",setup(_){const a=L(),e=b(null);C(async()=>{i()});async function i(){await a.activeBot.getLogs(),l()}function h(d){switch(d){case"WARNING":return"text-warning";case"ERROR":return"text-danger";default:return"text-secondary"}}function l(){e.value&&(e.value.scrollTop=e.value.scrollHeight)}return(d,O)=>{const g=N,u=R,v=T;return n(),c("div",F,[t("div",{ref_key:"scrollContainer",ref:e,class:"border p-1 text-start pb-5 w-100 h-100 overflow-auto"},[(n(!0),c(k,null,B(V(a).activeBot.lastLogs,(o,x)=>(n(),c("pre",{key:x,class:"m-0 overflow-visible",style:{"line-height":"unset"}},[t("span",I,[p(r(o[0])+" ",1),t("span",{class:y(h(o[3]))},r(o[3].padEnd(7," ")),3),p(" "+r(o[2])+" - ",1)]),t("span",M,r(o[4]),1)]))),128))],512),t("div",q,[s(u,{id:"refresh-logs",size:"sm",title:"Reload Logs",onClick:i},{default:m(()=>[s(g)]),_:1}),s(u,{size:"sm",title:"Scroll to bottom",onClick:l},{default:m(()=>[s(v)]),_:1})])])}}}),D=f(A,[["__scopeId","data-v-f7c6fcb3"]]),G={},H={class:"p-1 p-md-4 pe-md-2 h-100"};function K(_,a){const e=D;return n(),c("div",H,[s(e)])}const j=f(G,[["render",K]]);export{j as default};
//# sourceMappingURL=LogView-DlCFtUcf.js.map
