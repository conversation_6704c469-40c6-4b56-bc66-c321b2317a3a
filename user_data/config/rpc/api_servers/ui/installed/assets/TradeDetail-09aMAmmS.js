import{c as B,a as I,_ as z}from"./InfoBox.vue_vue_type_script_setup_true_lang-Dr_hBaFv.js";import{o as a,c as m,a as o,g as w,x as t,y as l,A as s,L as v,at as F,bk as L,b0 as D,b as f,w as n,e as i,h as d,t as k,B as b,m as u,n as h,F as S,K as R,p as T,d as M,_ as O}from"./index-B2p78N-x.js";const N={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},j=o("path",{fill:"currentColor",d:"M1 3h22L12 22"},null,-1),A=[j];function E(e,_){return a(),m("svg",N,[...A])}const G={name:"mdi-triangle-down",render:E},K={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},U=o("path",{fill:"currentColor",d:"M1 21h22L12 2"},null,-1),Z=[U];function H(e,_){return a(),m("svg",K,[...Z])}const J={name:"mdi-triangle",render:H},Q={class:"d-flex"},W=w({__name:"ValuePair",props:{description:{type:String,required:!0},help:{type:String,default:"",required:!1},classLabel:{type:String,default:"col-4 fw-bold mb-0"},classValue:{type:String,default:"col-8"}},setup(e){return(_,$)=>{const x=B;return a(),m("div",Q,[o("div",{class:v(["d-flex justify-content-between me-2",e.classLabel])},[o("label",null,t(e.description),1),e.help?(a(),l(x,{key:0,hint:e.help},null,8,["hint"])):s("",!0)],2),o("div",{class:v(e.classValue)},[F(_.$slots,"default")],2)])}}}),y=e=>(T("data-v-40a91bfb"),e=e(),M(),e),X={class:"container text-start"},Y={class:"row"},p={class:"col-lg-5"},ee=y(()=>o("h5",{class:"detail-header"},"General",-1)),te=y(()=>o("summary",null,"Details",-1)),ae={key:0},de={class:"mt-2 mt-lg-0 col-lg-7"},ie=y(()=>o("h5",{class:"detail-header"},"Stoploss",-1)),re={key:3},ne=y(()=>o("h5",{class:"detail-header"},"Futures/Margin",-1)),se={key:4},le=["title"],oe={key:3,title:"remaining"},ce={title:"Filled"},ue=w({__name:"TradeDetail",props:{trade:{required:!0,type:Object},stakeCurrency:{required:!0,type:String}},setup(e){L($=>({"1f823db6":d(_).colorUp,"47f906a8":d(_).colorDown}));const _=D();return($,x)=>{const r=W,g=I,P=J,q=G,V=z;return a(),m("div",X,[o("div",Y,[o("div",p,[ee,f(r,{description:"Trade Id"},{default:n(()=>[i(t(e.trade.trade_id),1)]),_:1}),f(r,{description:"Pair"},{default:n(()=>[i(t(e.trade.pair),1)]),_:1}),f(r,{description:"Open date"},{default:n(()=>[i(t(d(k)(e.trade.open_timestamp)),1)]),_:1}),e.trade.enter_tag?(a(),l(r,{key:0,description:"Entry tag"},{default:n(()=>[i(t(e.trade.enter_tag),1)]),_:1})):s("",!0),f(r,{description:"Stake"},{default:n(()=>[i(t(d(b)(e.trade.stake_amount,e.stakeCurrency))+" "+t(e.trade.leverage&&e.trade.leverage!==1?`(${e.trade.leverage}x)`:""),1)]),_:1}),f(r,{description:"Amount"},{default:n(()=>[i(t(e.trade.amount),1)]),_:1}),f(r,{description:"Open Rate"},{default:n(()=>[i(t(d(u)(e.trade.open_rate)),1)]),_:1}),e.trade.is_open&&e.trade.current_rate?(a(),l(r,{key:1,description:"Current Rate"},{default:n(()=>[i(t(d(u)(e.trade.current_rate)),1)]),_:1})):s("",!0),!e.trade.is_open&&e.trade.close_rate?(a(),l(r,{key:2,description:"Close Rate"},{default:n(()=>[i(t(d(u)(e.trade.close_rate)),1)]),_:1})):s("",!0),e.trade.close_timestamp?(a(),l(r,{key:3,description:"Close date"},{default:n(()=>[i(t(d(k)(e.trade.close_timestamp)),1)]),_:1})):s("",!0),e.trade.is_open&&e.trade.realized_profit&&!e.trade.total_profit_abs?(a(),l(r,{key:4,description:"Realized Profit"},{default:n(()=>[f(g,{class:"ms-2",trade:e.trade,mode:"realized"},null,8,["trade"])]),_:1})):s("",!0),e.trade.is_open&&e.trade.total_profit_abs?(a(),l(r,{key:5,description:"Total Profit"},{default:n(()=>[f(g,{class:"ms-2",trade:e.trade,mode:"total"},null,8,["trade"])]),_:1})):s("",!0),e.trade.profit_ratio&&e.trade.profit_abs?(a(),l(r,{key:6,description:`${e.trade.is_open?"Current Profit":"Close Profit"}`},{default:n(()=>[f(g,{class:"ms-2",trade:e.trade},null,8,["trade"])]),_:1},8,["description"])):s("",!0),o("details",null,[te,e.trade.min_rate?(a(),l(r,{key:0,description:"Min Rate"},{default:n(()=>[i(t(d(u)(e.trade.min_rate)),1)]),_:1})):s("",!0),e.trade.max_rate?(a(),l(r,{key:1,description:"Max Rate"},{default:n(()=>[i(t(d(u)(e.trade.max_rate)),1)]),_:1})):s("",!0),f(r,{description:"Open-Fees"},{default:n(()=>[i(t(e.trade.fee_open_cost)+" "+t(e.trade.quote_currency)+" ",1),e.trade.quote_currency!==e.trade.fee_open_currency?(a(),m("span",ae," (in "+t(e.trade.fee_open_currency)+") ",1)):s("",!0),i(" ("+t(d(h)(e.trade.fee_open))+") ",1)]),_:1}),e.trade.fee_close_cost&&e.trade.fee_close?(a(),l(r,{key:2,description:"Fees close"},{default:n(()=>[i(t(e.trade.fee_close_cost)+" "+t(e.trade.fee_close_currency)+" ("+t(d(h)(e.trade.fee_close))+") ",1)]),_:1})):s("",!0)])]),o("div",de,[ie,f(r,{description:"Stoploss"},{default:n(()=>[i(t(d(h)(e.trade.stop_loss_pct/100))+" | "+t(d(u)(e.trade.stop_loss_abs)),1)]),_:1}),e.trade.initial_stop_loss_pct&&e.trade.initial_stop_loss_abs?(a(),l(r,{key:0,description:"Initial Stoploss"},{default:n(()=>[i(t(d(h)(e.trade.initial_stop_loss_pct/100))+" | "+t(d(u)(e.trade.initial_stop_loss_abs)),1)]),_:1})):s("",!0),e.trade.is_open&&e.trade.stoploss_current_dist_ratio&&e.trade.stoploss_current_dist?(a(),l(r,{key:1,description:"Current stoploss dist"},{default:n(()=>[i(t(d(h)(e.trade.stoploss_current_dist_ratio))+" | "+t(d(u)(e.trade.stoploss_current_dist)),1)]),_:1})):s("",!0),e.trade.stoploss_last_update_timestamp?(a(),l(r,{key:2,description:"Stoploss last updated"},{default:n(()=>[i(t(d(k)(e.trade.stoploss_last_update_timestamp)),1)]),_:1})):s("",!0),e.trade.trading_mode!==void 0&&e.trade.trading_mode!=="spot"?(a(),m("div",re,[ne,f(r,{description:"Direction"},{default:n(()=>[i(t(e.trade.is_short?"short":"long")+" - "+t(e.trade.leverage)+"x ",1)]),_:1}),e.trade.funding_fees!==void 0?(a(),l(r,{key:0,description:"Funding fees"},{default:n(()=>[i(t(d(u)(e.trade.funding_fees)),1)]),_:1})):s("",!0),e.trade.interest_rate!==void 0?(a(),l(r,{key:1,description:"Interest rate"},{default:n(()=>[i(t(d(u)(e.trade.interest_rate)),1)]),_:1})):s("",!0),e.trade.liquidation_price!==void 0?(a(),l(r,{key:2,description:"Liquidation Price"},{default:n(()=>[i(t(d(u)(e.trade.liquidation_price)),1)]),_:1})):s("",!0)])):s("",!0),e.trade.orders?(a(),m("details",se,[o("summary",null,"Orders "+t(e.trade.orders.length>1?`[${e.trade.orders.length}]`:""),1),(a(!0),m(S,null,R(e.trade.orders,(c,C)=>(a(),m("div",{key:C},[o("span",{title:`${c.ft_order_side} ${c.order_type} order for ${d(b)(c.amount,e.trade.base_currency??"")} at ${d(b)(c.safe_price,e.trade.quote_currency??"")}, filled ${d(u)(c.filled)}`},[i(" (#"+t(C+1)+") ",1),c.ft_order_side==="buy"?(a(),l(P,{key:0,class:"me-1 color-up",style:{"font-size":"0.6rem"}})):(a(),l(q,{key:1,class:"me-1 color-down",style:{"font-size":"0.6rem"}})),c.order_timestamp?(a(),l(V,{key:2,date:c.order_timestamp,"show-timezone":""},null,8,["date"])):s("",!0),o("b",{class:v(["ms-1",c.ft_order_side==="buy"?"color-up":"color-down"])},t(c.ft_order_side),3),i(" for "),o("b",null,t(d(u)(c.safe_price)),1),i(" | "),c.remaining&&c.remaining!==0?(a(),m("span",oe,t(d(u)(c.remaining,8))+" / ",1)):s("",!0),o("span",ce,t(d(u)(c.filled??0,8)),1),c.ft_order_tag?(a(),m(S,{key:4},[i(" | "+t(c.ft_order_tag??""),1)],64)):s("",!0)],8,le)]))),128))])):s("",!0)])])])}}}),_e=O(ue,[["__scopeId","data-v-40a91bfb"]]);export{_e as _};
//# sourceMappingURL=TradeDetail-09aMAmmS.js.map
