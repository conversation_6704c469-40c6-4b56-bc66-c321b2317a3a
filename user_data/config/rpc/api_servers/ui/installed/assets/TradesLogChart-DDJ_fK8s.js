import{g as P,aN as v,k as g,o as x,c as Z,a as E,y as w,h as d,A as $,b as N,w as U,L as W,V as Y,Y as J,_ as k,b0 as H,r as K,b6 as Q,q as X,b7 as tt,M as et,m as V,aP as ot,t as at}from"./index-B2p78N-x.js";import{u as L,bf as O,be as S,ba as A,bb as I,bc as B,bd as D}from"./installCanvasRenderer-DFpQ5KDo.js";import{k as M,f as R,l as j,g as st,m as q,h as rt,j as nt}from"./install-DosLD5hS.js";function it(i,t){const n=Math.min(...i),p=(Math.max(...i)-n)*1.01/t,u=[...Array(t).keys()].map(r=>[Math.round((n+r*p)*1e3)/1e3,0]);for(let r=0;r<i.length;r++){const o=Math.min(Math.floor((i[r]-n)/p),t-1);isNaN(o)||u[o][1]++}return u}const lt={class:"d-flex flex-column h-100 position-relative"},ct={class:"flex-grow-1"},T="Trade count",mt=P({__name:"ProfitDistributionChart",props:{trades:{required:!0,type:Array},showTitle:{default:!0,type:Boolean}},setup(i){L([M,S,A,R,I,B,D]);const t=i,n=v(),y=[{text:"10",value:10},{text:"15",value:15},{text:"20",value:20},{text:"25",value:25},{text:"50",value:50}],p=g(()=>{const r=t.trades.map(o=>o.profit_ratio);return it(r,n.profitDistributionBins)}),u=g(()=>({title:{text:"Profit distribution",show:t.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",dataset:{source:p.value},tooltip:{trigger:"axis",axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},legend:{data:[T],right:"5%",selectedMode:!1},xAxis:{type:"category",name:"Profit %",nameLocation:"middle",nameGap:25},yAxis:[{type:"value",name:T,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:35,position:"left"}],series:[{type:"bar",name:T,animation:!0,encode:{x:"x0",y:"y0"}}]}));return(r,o)=>{const m=Y,b=J;return x(),Z("div",lt,[E("div",ct,[i.trades?(x(),w(d(O),{key:0,option:d(u),autoresize:"",theme:d(n).chartTheme},null,8,["option","theme"])):$("",!0)]),N(b,{class:W(["z-2",i.showTitle?"ms-5 ps-5":"position-absolute"]),label:"Bins",style:{width:"33%","min-width":"12rem"},"label-for":"input-bins","label-cols":"6","content-cols":"6",size:"sm"},{default:U(()=>[N(m,{id:"input-bins",modelValue:d(n).profitDistributionBins,"onUpdate:modelValue":o[0]||(o[0]=e=>d(n).profitDistributionBins=e),size:"sm",class:"mt-1",options:y},null,8,["modelValue"])]),_:1},8,["class"])])}}}),bt=k(mt,[["__scopeId","data-v-05bb1801"]]),C="Profit",pt=P({__name:"CumProfitChart",props:{trades:{required:!0,type:Array},openTrades:{required:!1,type:Array,default:()=>[]},showTitle:{default:!0,type:Boolean},profitColumn:{default:"profit_abs",type:String}},setup(i){L([M,j,S,A,R,st,I,B,D]);const t=i,n=v(),y=H(),p=K(),u=g(()=>t.openTrades.reduce((e,a)=>e+(a.total_profit_abs??a[t.profitColumn]??0),0)),r=g(()=>{const e=[],a={},l=t.trades.slice().sort((c,_)=>c.close_timestamp>_.close_timestamp?1:-1);let f=0;for(let c=0,_=l.length;c<_;c+=1){const s=l[c];s.close_timestamp&&s[t.profitColumn]&&(f+=s[t.profitColumn],a[s.close_timestamp]?(a[s.close_timestamp].profit+=s[t.profitColumn],a[s.close_timestamp][s.botId]?a[s.close_timestamp][s.botId]+=s[t.profitColumn]:a[s.close_timestamp][s.botId]=f):a[s.close_timestamp]={profit:f,[s.botId]:f},e.push({date:s.close_timestamp,profit:f,[s.botId]:f}))}const h=Object.entries(a).map(([c,_])=>({date:parseInt(c,10),profit:_.profit}));if(t.openTrades.length>0){let c=0,_=0;if(h.length>0){const z=h[h.length-1];c=z.profit??0,_=z.date??0}else _=t.openTrades[0].open_timestamp;const s=(c??0)+u.value;h.push({date:_,currentProfit:c});const F=Date.now()+24*60*60*1e3;h.push({date:F,currentProfit:s})}return h});function o(e=!1){const{colorProfit:a,colorLoss:l}=y;return{dataset:{dimensions:["date","profit","currentProfit"],source:r.value},series:[{type:"line",name:"currentProfit",animation:e,lineStyle:{color:u.value>0?a:l,type:"dotted"},itemStyle:{color:u.value>0?a:l},encode:{x:"date",y:"currentProfit"}},{type:"line",name:C,animation:e,step:"end",lineStyle:{color:n.chartTheme==="dark"?"#c2c2c2":"black"},itemStyle:{color:n.chartTheme==="dark"?"#c2c2c2":"black"},encode:{x:"date",y:"profit"}}]}}function m(e=!1){var l;const a=o(e);(l=p.value)==null||l.setOption(a,{replaceMerge:["series","dataset"]})}const b=Q(()=>t.trades,()=>{const e={title:{text:"Cumulative Profit",show:t.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",tooltip:{trigger:"axis",formatter:l=>{const f=l[0].data.profit,h=l[0].data.currentProfit,c=h?`Projected profit (incl. unrealized): ${V(h,3)}`:`Profit: ${V(f,3)}`;return`${ot(l[1].data.date)}<br />${l[1].marker}${c}`},axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},legend:{data:[C],right:"5%",selectedMode:!1},useUTC:!1,xAxis:{type:"time"},yAxis:[{type:"value",name:C,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:40}],grid:{bottom:80},dataZoom:[{type:"inside",start:0,end:100},{bottom:10,start:0,end:100,...q}]},a=o(!1);return e.series=a.series,e.dataset=a.dataset,e});return X(()=>{}),tt(()=>t.openTrades,()=>{m()},{throttle:60*1e3}),et(()=>n.chartTheme,()=>{b.trigger()}),(e,a)=>i.trades?(x(),w(d(O),{key:0,ref_key:"chart",ref:p,option:d(b),theme:d(n).chartTheme,autoresize:""},null,8,["option","theme"])):$("",!0)}}),yt=k(pt,[["__scopeId","data-v-4033702e"]]),G="Profit %",dt="#9be0a8",ut=P({__name:"TradesLogChart",props:{trades:{required:!0,type:Array},showTitle:{default:!0,type:Boolean}},setup(i){L([M,j,S,A,R,I,B,D,rt,nt]);const t=i,n=v(),y=H(),p=g(()=>{const r=[],o=t.trades.slice(0).sort((m,b)=>m.close_timestamp>b.close_timestamp?1:-1);for(let m=0,b=o.length;m<b;m+=1){const e=o[m],a=[m,(e.profit_ratio*100).toFixed(2),e.pair,e.botName,at(e.close_timestamp),e.is_short===void 0||!e.is_short?"Long":"Short"];r.push(a)}return r}),u=g(()=>{const r=p.value.length>0?(1-50/p.value.length)*100:100;return{title:{text:"Trades log",show:t.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",dataset:{dimensions:["date","profit"],source:p.value},tooltip:{trigger:"axis",formatter:o=>{const m=o[0].data[3]?` | ${o[0].data[3]}`:"";return`${o[0].data[2]} | ${o[0].data[5]} ${m}<br>${o[0].data[4]}<br>Profit ${o[0].data[1]} %`},axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},xAxis:{type:"value",show:!1},yAxis:[{type:"value",name:G,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:30}],grid:{bottom:80},dataZoom:[{type:"inside",start:r,end:100},{bottom:10,start:r,end:100,...q}],visualMap:[{show:!0,seriesIndex:0,pieces:[{max:0,color:y.colorLoss},{min:0,color:y.colorProfit}]}],series:[{type:"bar",name:G,barGap:"0%",barCategoryGap:"0%",animation:!1,label:{show:!0,position:"top",rotate:90,offset:[7.5,7.5],formatter:"{@[1]} %",color:n.chartTheme==="dark"?"#c2c2c2":"#3c3c3c"},encode:{x:0,y:1},itemStyle:{color:dt}}]}});return(r,o)=>i.trades.length>0?(x(),w(d(O),{key:0,option:d(u),autoresize:"",theme:d(n).chartTheme},null,8,["option","theme"])):$("",!0)}}),gt=k(ut,[["__scopeId","data-v-8041776f"]]);export{gt as _,yt as a,bt as b};
//# sourceMappingURL=TradesLogChart-DDJ_fK8s.js.map
