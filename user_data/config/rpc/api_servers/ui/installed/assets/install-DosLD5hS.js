import{aG as Q,e as A,aq as et,bg as ua,ah as tt,A as $t,N as Qa,bh as ti,bi as ei,aN as ri,h as F,aM as ai,bj as ii,x as B,M as ni,a7 as Ve,bk as ha,bl as te,aD as oi,bm as si,bn as ca,bo as k,r as rt,_ as R,G as U,c as ht,S as Pe,bp as Xe,j as li,l as ui,b as at,O as va,i as ct,d as ee,o as hi,aw as fa,K as Mt,s as Re,t as Kt,g as ut,bq as Ue,m as ci,p as pa,U as it,b1 as qt,b8 as Oe,X as _t,br as Ye,P as re,bs as vi,bt as $e,R as j,bu as Ee,bv as Ke,F as gt,Q as _e,bw as qe,C as Wt,bx as je,ai as pt,by as fi,bz as da,bA as ga,f as Je,bB as pi,V as di,af as Et,aO as kt,bC as Qe,bD as tr,bE as gi,bF as mi,bG as yi,bH as xi,bI as bi,ac as ae,T as Si,v as q,bJ as _i,ab as ma,ae as Mi,bK as Ai,aa as wi,bL as Ii,B as er,bM as rr,aX as Di,bN as Li,bO as ie,an as ke,bP as Ci,a4 as Tt,bQ as Ti,bR as ar,bS as ir,bT as Vi,bU as Pi,a0 as ya,bV as Me,bW as nr,bX as Ri,bY as Oi,bZ as Ei,b_ as ki,L as or,b$ as sr,al as ze,aF as zi,c0 as xa,q as P,w as X,c1 as lr,c2 as mt,E as Ni,c3 as Bi,c4 as ne,c5 as oe,c6 as Gi,c8 as se,c9 as Hi,u as At,b9 as Fi,ca as Zi,cb as Wi,cc as Xi,aV as ft,cd as Ui,ce as Yi,cf as ba,cg as $i,ch as Ki,Y as ur,aT as qi,aj as Vt,I as Xt,H as Sa,aW as Pt,ci as ji,z as _a,y as Ji,a9 as Qi,cj as tn,at as en,aU as hr,ck as rn,cl as an,cm as nn,b5 as on,cn as cr,b6 as sn,co as ln}from"./installCanvasRenderer-DFpQ5KDo.js";var un=function(){function i(e){this.coordSysDims=[],this.axisMap=et(),this.categoryAxisMap=et(),this.coordSysName=e}return i}();function hn(i){var e=i.get("coordinateSystem"),t=new un(e),r=cn[e];if(r)return r(i,t,t.axisMap,t.categoryAxisMap),t}var cn={cartesian2d:function(i,e,t,r){var a=i.getReferringComponents("xAxis",Q).models[0],n=i.getReferringComponents("yAxis",Q).models[0];e.coordSysDims=["x","y"],t.set("x",a),t.set("y",n),yt(a)&&(r.set("x",a),e.firstCategoryDimIndex=0),yt(n)&&(r.set("y",n),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=1))},singleAxis:function(i,e,t,r){var a=i.getReferringComponents("singleAxis",Q).models[0];e.coordSysDims=["single"],t.set("single",a),yt(a)&&(r.set("single",a),e.firstCategoryDimIndex=0)},polar:function(i,e,t,r){var a=i.getReferringComponents("polar",Q).models[0],n=a.findAxisModel("radiusAxis"),o=a.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],t.set("radius",n),t.set("angle",o),yt(n)&&(r.set("radius",n),e.firstCategoryDimIndex=0),yt(o)&&(r.set("angle",o),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=1))},geo:function(i,e,t,r){e.coordSysDims=["lng","lat"]},parallel:function(i,e,t,r){var a=i.ecModel,n=a.getComponent("parallel",i.get("parallelIndex")),o=e.coordSysDims=n.dimensions.slice();A(n.parallelAxisIndex,function(s,l){var u=a.getComponent("parallelAxis",s),h=o[l];t.set(h,u),yt(u)&&(r.set(h,u),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=l))})}};function yt(i){return i.get("type")==="category"}function vn(i,e){var t=i.get("coordinateSystem"),r=ii.get(t),a;return e&&e.coordSysDims&&(a=B(e.coordSysDims,function(n){var o={name:n},s=e.axisMap.get(n);if(s){var l=s.get("type");o.type=ni(l)}return o})),a||(a=r&&(r.getDimensionsInfo?r.getDimensionsInfo():r.dimensions.slice())||["x","y"]),a}function fn(i,e,t){var r,a;return t&&A(i,function(n,o){var s=n.coordDim,l=t.categoryAxisMap.get(s);l&&(r==null&&(r=o),n.ordinalMeta=l.getOrdinalMeta(),e&&(n.createInvertedIndices=!0)),n.otherDims.itemName!=null&&(a=!0)}),!a&&r!=null&&(i[r].otherDims.itemName=0),r}function Ne(i,e,t){t=t||{};var r=e.getSourceManager(),a,n=!1;a=r.getSource(),n=a.sourceFormat===ua;var o=hn(e),s=vn(e,o),l=t.useEncodeDefaulter,u=tt(l)?l:l?$t(Qa,s,e):null,h={coordDimensions:s,generateCoord:t.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!n},c=ti(a,h),f=fn(c.dimensions,t.createInvertedIndices,o),v=n?null:r.getSharedDataStore(c),p=ei(e,{schema:c,store:v}),d=new ri(c,e);d.setCalculationInfo(p);var g=f!=null&&pn(a)?function(y,m,x,b){return b===f?x:this.defaultDimValueGetter(y,m,x,b)}:null;return d.hasItemOption=!1,d.initData(n?a:v,null,g),d}function pn(i){if(i.sourceFormat===ua){var e=dn(i.data||[]);return!F(ai(e))}}function dn(i){for(var e=0;e<i.length&&i[e]==null;)e++;return i[e]}var gn=function(){function i(){}return i.prototype.getNeedCrossZero=function(){var e=this.option;return!e.scale},i.prototype.getCoordSysModel=function(){},i}(),Rt=Ve();function mn(i){return i.type==="category"?xn(i):Sn(i)}function yn(i,e){return i.type==="category"?bn(i,e):{ticks:B(i.scale.getTicks(),function(t){return t.value})}}function xn(i){var e=i.getLabelModel(),t=Ma(i,e);return!e.get("show")||i.scale.isBlank()?{labels:[],labelCategoryInterval:t.labelCategoryInterval}:t}function Ma(i,e){var t=Aa(i,"labels"),r=ha(e),a=wa(t,r);if(a)return a;var n,o;return tt(r)?n=La(i,r):(o=r==="auto"?_n(i):r,n=Da(i,o)),Ia(t,r,{labels:n,labelCategoryInterval:o})}function bn(i,e){var t=Aa(i,"ticks"),r=ha(e),a=wa(t,r);if(a)return a;var n,o;if((!e.get("show")||i.scale.isBlank())&&(n=[]),tt(r))n=La(i,r,!0);else if(r==="auto"){var s=Ma(i,i.getLabelModel());o=s.labelCategoryInterval,n=B(s.labels,function(l){return l.tickValue})}else o=r,n=Da(i,o,!0);return Ia(t,r,{ticks:n,tickCategoryInterval:o})}function Sn(i){var e=i.scale.getTicks(),t=te(i);return{labels:B(e,function(r,a){return{level:r.level,formattedLabel:t(r,a),rawLabel:i.scale.getLabel(r),tickValue:r.value}})}}function Aa(i,e){return Rt(i)[e]||(Rt(i)[e]=[])}function wa(i,e){for(var t=0;t<i.length;t++)if(i[t].key===e)return i[t].value}function Ia(i,e,t){return i.push({key:e,value:t}),t}function _n(i){var e=Rt(i).autoInterval;return e??(Rt(i).autoInterval=i.calculateCategoryInterval())}function Mn(i){var e=An(i),t=te(i),r=(e.axisRotate-e.labelRotate)/180*Math.PI,a=i.scale,n=a.getExtent(),o=a.count();if(n[1]-n[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=n[0],u=i.dataToCoord(l+1)-i.dataToCoord(l),h=Math.abs(u*Math.cos(r)),c=Math.abs(u*Math.sin(r)),f=0,v=0;l<=n[1];l+=s){var p=0,d=0,g=oi(t({value:l}),e.font,"center","top");p=g.width*1.3,d=g.height*1.3,f=Math.max(f,p,7),v=Math.max(v,d,7)}var y=f/h,m=v/c;isNaN(y)&&(y=1/0),isNaN(m)&&(m=1/0);var x=Math.max(0,Math.floor(Math.min(y,m))),b=Rt(i.model),_=i.getExtent(),S=b.lastAutoInterval,M=b.lastTickCount;return S!=null&&M!=null&&Math.abs(S-x)<=1&&Math.abs(M-o)<=1&&S>x&&b.axisExtent0===_[0]&&b.axisExtent1===_[1]?x=S:(b.lastTickCount=o,b.lastAutoInterval=x,b.axisExtent0=_[0],b.axisExtent1=_[1]),x}function An(i){var e=i.getLabelModel();return{axisRotate:i.getRotate?i.getRotate():i.isHorizontal&&!i.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function Da(i,e,t){var r=te(i),a=i.scale,n=a.getExtent(),o=i.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=n[0],h=a.count();u!==0&&l>1&&h/l>2&&(u=Math.round(Math.ceil(u/l)*l));var c=si(i),f=o.get("showMinLabel")||c,v=o.get("showMaxLabel")||c;f&&u!==n[0]&&d(n[0]);for(var p=u;p<=n[1];p+=l)d(p);v&&p-l!==n[1]&&d(n[1]);function d(g){var y={value:g};s.push(t?g:{formattedLabel:r(y),rawLabel:a.getLabel(y),tickValue:g})}return s}function La(i,e,t){var r=i.scale,a=te(i),n=[];return A(r.getTicks(),function(o){var s=r.getLabel(o),l=o.value;e(o.value,s)&&n.push(t?l:{formattedLabel:a(o),rawLabel:s,tickValue:l})}),n}var vr=[0,1],wn=function(){function i(e,t,r){this.onBand=!1,this.inverse=!1,this.dim=e,this.scale=t,this._extent=r||[0,0]}return i.prototype.contain=function(e){var t=this._extent,r=Math.min(t[0],t[1]),a=Math.max(t[0],t[1]);return e>=r&&e<=a},i.prototype.containData=function(e){return this.scale.contain(e)},i.prototype.getExtent=function(){return this._extent.slice()},i.prototype.getPixelPrecision=function(e){return ca(e||this.scale.getExtent(),this._extent)},i.prototype.setExtent=function(e,t){var r=this._extent;r[0]=e,r[1]=t},i.prototype.dataToCoord=function(e,t){var r=this._extent,a=this.scale;return e=a.normalize(e),this.onBand&&a.type==="ordinal"&&(r=r.slice(),fr(r,a.count())),k(e,vr,r,t)},i.prototype.coordToData=function(e,t){var r=this._extent,a=this.scale;this.onBand&&a.type==="ordinal"&&(r=r.slice(),fr(r,a.count()));var n=k(e,r,vr,t);return this.scale.scale(n)},i.prototype.pointToData=function(e,t){},i.prototype.getTicksCoords=function(e){e=e||{};var t=e.tickModel||this.getTickModel(),r=yn(this,t),a=r.ticks,n=B(a,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=t.get("alignWithLabel");return In(this,n,o,e.clamp),n},i.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var e=this.model.getModel("minorTick"),t=e.get("splitNumber");t>0&&t<100||(t=5);var r=this.scale.getMinorTicks(t),a=B(r,function(n){return B(n,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return a},i.prototype.getViewLabels=function(){return mn(this).labels},i.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},i.prototype.getTickModel=function(){return this.model.getModel("axisTick")},i.prototype.getBandWidth=function(){var e=this._extent,t=this.scale.getExtent(),r=t[1]-t[0]+(this.onBand?1:0);r===0&&(r=1);var a=Math.abs(e[1]-e[0]);return Math.abs(a)/r},i.prototype.calculateCategoryInterval=function(){return Mn(this)},i}();function fr(i,e){var t=i[1]-i[0],r=e,a=t/r/2;i[0]+=a,i[1]-=a}function In(i,e,t,r){var a=e.length;if(!i.onBand||t||!a)return;var n=i.getExtent(),o,s;if(a===1)e[0].coord=n[0],o=e[1]={coord:n[1]};else{var l=e[a-1].tickValue-e[0].tickValue,u=(e[a-1].coord-e[0].coord)/l;A(e,function(v){v.coord-=u/2});var h=i.scale.getExtent();s=1+h[1]-e[a-1].tickValue,o={coord:e[a-1].coord+u*s},e.push(o)}var c=n[0]>n[1];f(e[0].coord,n[0])&&(r?e[0].coord=n[0]:e.shift()),r&&f(n[0],e[0].coord)&&e.unshift({coord:n[0]}),f(n[1],o.coord)&&(r?o.coord=n[1]:e.pop()),r&&f(o.coord,n[1])&&e.push({coord:n[1]});function f(v,p){return v=rt(v),p=rt(p),c?v>p:v<p}}const Dn="path://M18.1 10.7V9.3c-.3-4.9-4.4-8.8-9.4-8.8-5 0-9.1 3.9-9.4 8.8v1.3c.3 4.9 4.4 8.8 9.4 8.8C13.7 19.5 17.8 15.6 18.1 10.7zM5.6 13.3V6.7H7v6.6H5.6zM10.4 13.3V6.7h1.4v6.6H10.4z",Os={show:!0,type:"slider",handleIcon:Dn,handleSize:"80%"};var Ln=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t){return Ne(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var r=new U,a=ht("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);r.add(a),a.setStyle(t.lineStyle);var n=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),s=n==="none"?"circle":n,l=t.itemHeight*.8,u=ht(s,(t.itemWidth-l)/2,(t.itemHeight-l)/2,l,l,t.itemStyle.fill);r.add(u),u.setStyle(t.itemStyle);var h=t.iconRotate==="inherit"?o:t.iconRotate||0;return u.rotation=h*Math.PI/180,u.setOrigin([t.itemWidth/2,t.itemHeight/2]),s.indexOf("empty")>-1&&(u.style.stroke=u.style.fill,u.style.fill="#fff",u.style.lineWidth=2),r},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(Pe);function Be(i,e){var t=i.mapDimensionsAll("defaultedLabel"),r=t.length;if(r===1){var a=Xe(i,e,t[0]);return a!=null?a+"":null}else if(r){for(var n=[],o=0;o<t.length;o++)n.push(Xe(i,e,t[o]));return n.join(" ")}}function Ca(i,e){var t=i.mapDimensionsAll("defaultedLabel");if(!F(e))return e+"";for(var r=[],a=0;a<t.length;a++){var n=i.getDimensionIndex(t[a]);n>=0&&r.push(e[n])}return r.join(" ")}var Ge=function(i){R(e,i);function e(t,r,a,n){var o=i.call(this)||this;return o.updateData(t,r,a,n),o}return e.prototype._createSymbol=function(t,r,a,n,o){this.removeAll();var s=ht(t,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:n[0]/2,scaleY:n[1]/2}),s.drift=Cn,this._symbolType=t,this.add(s)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){li(this.childAt(0))},e.prototype.downplay=function(){ui(this.childAt(0))},e.prototype.setZ=function(t,r){var a=this.childAt(0);a.zlevel=t,a.z=r},e.prototype.setDraggable=function(t,r){var a=this.childAt(0);a.draggable=t,a.cursor=!r&&t?"move":a.cursor},e.prototype.updateData=function(t,r,a,n){this.silent=!1;var o=t.getItemVisual(r,"symbol")||"circle",s=t.hostModel,l=e.getSymbolSize(t,r),u=o!==this._symbolType,h=n&&n.disableAnimation;if(u){var c=t.getItemVisual(r,"symbolKeepAspect");this._createSymbol(o,t,r,l,c)}else{var f=this.childAt(0);f.silent=!1;var v={scaleX:l[0]/2,scaleY:l[1]/2};h?f.attr(v):at(f,v,s,r),va(f)}if(this._updateCommon(t,r,l,a,n),u){var f=this.childAt(0);if(!h){var v={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:f.style.opacity}};f.scaleX=f.scaleY=0,f.style.opacity=0,ct(f,v,s,r)}}h&&this.childAt(0).stopAnimation("leave")},e.prototype._updateCommon=function(t,r,a,n,o){var s=this.childAt(0),l=t.hostModel,u,h,c,f,v,p,d,g,y;if(n&&(u=n.emphasisItemStyle,h=n.blurItemStyle,c=n.selectItemStyle,f=n.focus,v=n.blurScope,d=n.labelStatesModels,g=n.hoverScale,y=n.cursorStyle,p=n.emphasisDisabled),!n||t.hasItemOption){var m=n&&n.itemModel?n.itemModel:t.getItemModel(r),x=m.getModel("emphasis");u=x.getModel("itemStyle").getItemStyle(),c=m.getModel(["select","itemStyle"]).getItemStyle(),h=m.getModel(["blur","itemStyle"]).getItemStyle(),f=x.get("focus"),v=x.get("blurScope"),p=x.get("disabled"),d=ee(m),g=x.getShallow("scale"),y=m.getShallow("cursor")}var b=t.getItemVisual(r,"symbolRotate");s.attr("rotation",(b||0)*Math.PI/180||0);var _=hi(t.getItemVisual(r,"symbolOffset"),a);_&&(s.x=_[0],s.y=_[1]),y&&s.attr("cursor",y);var S=t.getItemVisual(r,"style"),M=S.fill;if(s instanceof fa){var D=s.style;s.useStyle(Mt({image:D.image,x:D.x,y:D.y,width:D.width,height:D.height},S))}else s.__isEmptyBrush?s.useStyle(Mt({},S)):s.useStyle(S),s.style.decal=null,s.setColor(M,o&&o.symbolInnerColor),s.style.strokeNoScale=!0;var L=t.getItemVisual(r,"liftZ"),T=this._z2;L!=null?T==null&&(this._z2=s.z2,s.z2+=L):T!=null&&(s.z2=T,this._z2=null);var w=o&&o.useNameLabel;Re(s,d,{labelFetcher:l,labelDataIndex:r,defaultText:I,inheritColor:M,defaultOpacity:S.opacity});function I(O){return w?t.getName(O):Be(t,O)}this._sizeX=a[0]/2,this._sizeY=a[1]/2;var C=s.ensureState("emphasis");C.style=u,s.ensureState("select").style=c,s.ensureState("blur").style=h;var E=g==null||g===!0?Math.max(1.1,3/this._sizeY):isFinite(g)&&g>0?+g:1;C.scaleX=this._sizeX*E,C.scaleY=this._sizeY*E,this.setSymbolScale(1),Kt(this,f,v,p)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,r,a){var n=this.childAt(0),o=ut(this).dataIndex,s=a&&a.animation;if(this.silent=n.silent=!0,a&&a.fadeLabel){var l=n.getTextContent();l&&Ue(l,{style:{opacity:0}},r,{dataIndex:o,removeOpt:s,cb:function(){n.removeTextContent()}})}else n.removeTextContent();Ue(n,{style:{opacity:0},scaleX:0,scaleY:0},r,{dataIndex:o,cb:t,removeOpt:s})},e.getSymbolSize=function(t,r){return ci(t.getItemVisual(r,"symbolSize"))},e}(U);function Cn(i,e){this.parent.drift(i,e)}function le(i,e,t,r){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(r.isIgnore&&r.isIgnore(t))&&!(r.clipShape&&!r.clipShape.contain(e[0],e[1]))&&i.getItemVisual(t,"symbol")!=="none"}function pr(i){return i!=null&&!it(i)&&(i={isIgnore:i}),i||{}}function dr(i){var e=i.hostModel,t=e.getModel("emphasis");return{emphasisItemStyle:t.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:t.get("focus"),blurScope:t.get("blurScope"),emphasisDisabled:t.get("disabled"),hoverScale:t.get("scale"),labelStatesModels:ee(e),cursorStyle:e.get("cursor")}}var Tn=function(){function i(e){this.group=new U,this._SymbolCtor=e||Ge}return i.prototype.updateData=function(e,t){this._progressiveEls=null,t=pr(t);var r=this.group,a=e.hostModel,n=this._data,o=this._SymbolCtor,s=t.disableAnimation,l=dr(e),u={disableAnimation:s},h=t.getSymbolPoint||function(c){return e.getItemLayout(c)};n||r.removeAll(),e.diff(n).add(function(c){var f=h(c);if(le(e,f,c,t)){var v=new o(e,c,l,u);v.setPosition(f),e.setItemGraphicEl(c,v),r.add(v)}}).update(function(c,f){var v=n.getItemGraphicEl(f),p=h(c);if(!le(e,p,c,t)){r.remove(v);return}var d=e.getItemVisual(c,"symbol")||"circle",g=v&&v.getSymbolType&&v.getSymbolType();if(!v||g&&g!==d)r.remove(v),v=new o(e,c,l,u),v.setPosition(p);else{v.updateData(e,c,l,u);var y={x:p[0],y:p[1]};s?v.attr(y):at(v,y,a)}r.add(v),e.setItemGraphicEl(c,v)}).remove(function(c){var f=n.getItemGraphicEl(c);f&&f.fadeOut(function(){r.remove(f)},a)}).execute(),this._getSymbolPoint=h,this._data=e},i.prototype.updateLayout=function(){var e=this,t=this._data;t&&t.eachItemGraphicEl(function(r,a){var n=e._getSymbolPoint(a);r.setPosition(n),r.markRedraw()})},i.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=dr(e),this._data=null,this.group.removeAll()},i.prototype.incrementalUpdate=function(e,t,r){this._progressiveEls=[],r=pr(r);function a(l){l.isGroup||(l.incremental=!0,l.ensureState("emphasis").hoverLayer=!0)}for(var n=e.start;n<e.end;n++){var o=t.getItemLayout(n);if(le(t,o,n,r)){var s=new this._SymbolCtor(t,n,this._seriesScope);s.traverse(a),s.setPosition(o),this.group.add(s),t.setItemGraphicEl(n,s),this._progressiveEls.push(s)}}},i.prototype.eachRendered=function(e){pa(this._progressiveEls||this.group,e)},i.prototype.remove=function(e){var t=this.group,r=this._data;r&&e?r.eachItemGraphicEl(function(a){a.fadeOut(function(){t.remove(a)},r.hostModel)}):t.removeAll()},i}();function Ta(i,e,t){var r=i.getBaseAxis(),a=i.getOtherAxis(r),n=Vn(a,t),o=r.dim,s=a.dim,l=e.mapDimension(s),u=e.mapDimension(o),h=s==="x"||s==="radius"?1:0,c=B(i.dimensions,function(p){return e.mapDimension(p)}),f=!1,v=e.getCalculationInfo("stackResultDimension");return qt(e,c[0])&&(f=!0,c[0]=v),qt(e,c[1])&&(f=!0,c[1]=v),{dataDimsForPoint:c,valueStart:n,valueAxisDim:s,baseAxisDim:o,stacked:!!f,valueDim:l,baseDim:u,baseDataOffset:h,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function Vn(i,e){var t=0,r=i.scale.getExtent();return e==="start"?t=r[0]:e==="end"?t=r[1]:Oe(e)&&!isNaN(e)?t=e:r[0]>0?t=r[0]:r[1]<0&&(t=r[1]),t}function Va(i,e,t,r){var a=NaN;i.stacked&&(a=t.get(t.getCalculationInfo("stackedOverDimension"),r)),isNaN(a)&&(a=i.valueStart);var n=i.baseDataOffset,o=[];return o[n]=t.get(i.baseDim,r),o[1-n]=a,e.dataToPoint(o)}function Pn(i,e){var t=[];return e.diff(i).add(function(r){t.push({cmd:"+",idx:r})}).update(function(r,a){t.push({cmd:"=",idx:a,idx1:r})}).remove(function(r){t.push({cmd:"-",idx:r})}).execute(),t}function Rn(i,e,t,r,a,n,o,s){for(var l=Pn(i,e),u=[],h=[],c=[],f=[],v=[],p=[],d=[],g=Ta(a,e,o),y=i.getLayout("points")||[],m=e.getLayout("points")||[],x=0;x<l.length;x++){var b=l[x],_=!0,S=void 0,M=void 0;switch(b.cmd){case"=":S=b.idx*2,M=b.idx1*2;var D=y[S],L=y[S+1],T=m[M],w=m[M+1];(isNaN(D)||isNaN(L))&&(D=T,L=w),u.push(D,L),h.push(T,w),c.push(t[S],t[S+1]),f.push(r[M],r[M+1]),d.push(e.getRawIndex(b.idx1));break;case"+":var I=b.idx,C=g.dataDimsForPoint,E=a.dataToPoint([e.get(C[0],I),e.get(C[1],I)]);M=I*2,u.push(E[0],E[1]),h.push(m[M],m[M+1]);var O=Va(g,a,e,I);c.push(O[0],O[1]),f.push(r[M],r[M+1]),d.push(e.getRawIndex(I));break;case"-":_=!1}_&&(v.push(b),p.push(p.length))}p.sort(function(Nt,H){return d[Nt]-d[H]});for(var N=u.length,Z=_t(N),V=_t(N),z=_t(N),Y=_t(N),W=[],x=0;x<p.length;x++){var wt=p[x],$=x*2,K=wt*2;Z[$]=u[K],Z[$+1]=u[K+1],V[$]=h[K],V[$+1]=h[K+1],z[$]=c[K],z[$+1]=c[K+1],Y[$]=f[K],Y[$+1]=f[K+1],W[x]=v[wt]}return{current:Z,next:V,stackedOnCurrent:z,stackedOnNext:Y,status:W}}var nt=Math.min,ot=Math.max;function dt(i,e){return isNaN(i)||isNaN(e)}function Ae(i,e,t,r,a,n,o,s,l){for(var u,h,c,f,v,p,d=t,g=0;g<r;g++){var y=e[d*2],m=e[d*2+1];if(d>=a||d<0)break;if(dt(y,m)){if(l){d+=n;continue}break}if(d===t)i[n>0?"moveTo":"lineTo"](y,m),c=y,f=m;else{var x=y-u,b=m-h;if(x*x+b*b<.5){d+=n;continue}if(o>0){for(var _=d+n,S=e[_*2],M=e[_*2+1];S===y&&M===m&&g<r;)g++,_+=n,d+=n,S=e[_*2],M=e[_*2+1],y=e[d*2],m=e[d*2+1],x=y-u,b=m-h;var D=g+1;if(l)for(;dt(S,M)&&D<r;)D++,_+=n,S=e[_*2],M=e[_*2+1];var L=.5,T=0,w=0,I=void 0,C=void 0;if(D>=r||dt(S,M))v=y,p=m;else{T=S-u,w=M-h;var E=y-u,O=S-y,N=m-h,Z=M-m,V=void 0,z=void 0;if(s==="x"){V=Math.abs(E),z=Math.abs(O);var Y=T>0?1:-1;v=y-Y*V*o,p=m,I=y+Y*z*o,C=m}else if(s==="y"){V=Math.abs(N),z=Math.abs(Z);var W=w>0?1:-1;v=y,p=m-W*V*o,I=y,C=m+W*z*o}else V=Math.sqrt(E*E+N*N),z=Math.sqrt(O*O+Z*Z),L=z/(z+V),v=y-T*o*(1-L),p=m-w*o*(1-L),I=y+T*o*L,C=m+w*o*L,I=nt(I,ot(S,y)),C=nt(C,ot(M,m)),I=ot(I,nt(S,y)),C=ot(C,nt(M,m)),T=I-y,w=C-m,v=y-T*V/z,p=m-w*V/z,v=nt(v,ot(u,y)),p=nt(p,ot(h,m)),v=ot(v,nt(u,y)),p=ot(p,nt(h,m)),T=y-v,w=m-p,I=y+T*z/V,C=m+w*z/V}i.bezierCurveTo(c,f,v,p,y,m),c=I,f=C}else i.lineTo(y,m)}u=y,h=m,d+=n}return g}var Pa=function(){function i(){this.smooth=0,this.smoothConstraint=!0}return i}(),On=function(i){R(e,i);function e(t){var r=i.call(this,t)||this;return r.type="ec-polyline",r}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Pa},e.prototype.buildPath=function(t,r){var a=r.points,n=0,o=a.length/2;if(r.connectNulls){for(;o>0&&dt(a[o*2-2],a[o*2-1]);o--);for(;n<o&&dt(a[n*2],a[n*2+1]);n++);}for(;n<o;)n+=Ae(t,a,n,o,o,1,r.smooth,r.smoothMonotone,r.connectNulls)+1},e.prototype.getPointOn=function(t,r){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var a=this.path,n=a.data,o=vi.CMD,s,l,u=r==="x",h=[],c=0;c<n.length;){var f=n[c++],v=void 0,p=void 0,d=void 0,g=void 0,y=void 0,m=void 0,x=void 0;switch(f){case o.M:s=n[c++],l=n[c++];break;case o.L:if(v=n[c++],p=n[c++],x=u?(t-s)/(v-s):(t-l)/(p-l),x<=1&&x>=0){var b=u?(p-l)*x+l:(v-s)*x+s;return u?[t,b]:[b,t]}s=v,l=p;break;case o.C:v=n[c++],p=n[c++],d=n[c++],g=n[c++],y=n[c++],m=n[c++];var _=u?Ye(s,v,d,y,t,h):Ye(l,p,g,m,t,h);if(_>0)for(var S=0;S<_;S++){var M=h[S];if(M<=1&&M>=0){var b=u?$e(l,p,g,m,M):$e(s,v,d,y,M);return u?[t,b]:[b,t]}}s=y,l=m;break}}},e}(re),En=function(i){R(e,i);function e(){return i!==null&&i.apply(this,arguments)||this}return e}(Pa),kn=function(i){R(e,i);function e(t){var r=i.call(this,t)||this;return r.type="ec-polygon",r}return e.prototype.getDefaultShape=function(){return new En},e.prototype.buildPath=function(t,r){var a=r.points,n=r.stackedOnPoints,o=0,s=a.length/2,l=r.smoothMonotone;if(r.connectNulls){for(;s>0&&dt(a[s*2-2],a[s*2-1]);s--);for(;o<s&&dt(a[o*2],a[o*2+1]);o++);}for(;o<s;){var u=Ae(t,a,o,s,s,1,r.smooth,l,r.connectNulls);Ae(t,n,o+u-1,u,s,-1,r.stackedOnSmooth,l,r.connectNulls),o+=u+1,t.closePath()}},e}(re);function Ra(i,e,t,r,a){var n=i.getArea(),o=n.x,s=n.y,l=n.width,u=n.height,h=t.get(["lineStyle","width"])||2;o-=h/2,s-=h/2,l+=h,u+=h,l=Math.ceil(l),o!==Math.floor(o)&&(o=Math.floor(o),l++);var c=new j({shape:{x:o,y:s,width:l,height:u}});if(e){var f=i.getBaseAxis(),v=f.isHorizontal(),p=f.inverse;v?(p&&(c.shape.x+=l),c.shape.width=0):(p||(c.shape.y+=u),c.shape.height=0);var d=tt(a)?function(g){a(g,c)}:null;ct(c,{shape:{width:l,height:u,x:o,y:s}},t,null,r,d)}return c}function Oa(i,e,t){var r=i.getArea(),a=rt(r.r0,1),n=rt(r.r,1),o=new Ee({shape:{cx:rt(i.cx,1),cy:rt(i.cy,1),r0:a,r:n,startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}});if(e){var s=i.getBaseAxis().dim==="angle";s?o.shape.endAngle=r.startAngle:o.shape.r=a,ct(o,{shape:{endAngle:r.endAngle,r:n}},t)}return o}function zn(i,e,t,r,a){if(i){if(i.type==="polar")return Oa(i,e,t);if(i.type==="cartesian2d")return Ra(i,e,t,r,a)}else return null;return null}function He(i,e){return i.type===e}function gr(i,e){if(i.length===e.length){for(var t=0;t<i.length;t++)if(i[t]!==e[t])return;return!0}}function mr(i){for(var e=1/0,t=1/0,r=-1/0,a=-1/0,n=0;n<i.length;){var o=i[n++],s=i[n++];isNaN(o)||(e=Math.min(o,e),r=Math.max(o,r)),isNaN(s)||(t=Math.min(s,t),a=Math.max(s,a))}return[[e,t],[r,a]]}function yr(i,e){var t=mr(i),r=t[0],a=t[1],n=mr(e),o=n[0],s=n[1];return Math.max(Math.abs(r[0]-o[0]),Math.abs(r[1]-o[1]),Math.abs(a[0]-s[0]),Math.abs(a[1]-s[1]))}function xr(i){return Oe(i)?i:i?.5:0}function Nn(i,e,t){if(!t.valueDim)return[];for(var r=e.count(),a=_t(r*2),n=0;n<r;n++){var o=Va(t,i,e,n);a[n*2]=o[0],a[n*2+1]=o[1]}return a}function st(i,e,t,r){var a=e.getBaseAxis(),n=a.dim==="x"||a.dim==="radius"?0:1,o=[],s=0,l=[],u=[],h=[],c=[];if(r){for(s=0;s<i.length;s+=2)!isNaN(i[s])&&!isNaN(i[s+1])&&c.push(i[s],i[s+1]);i=c}for(s=0;s<i.length-2;s+=2)switch(h[0]=i[s+2],h[1]=i[s+3],u[0]=i[s],u[1]=i[s+1],o.push(u[0],u[1]),t){case"end":l[n]=h[n],l[1-n]=u[1-n],o.push(l[0],l[1]);break;case"middle":var f=(u[n]+h[n])/2,v=[];l[n]=v[n]=f,l[1-n]=u[1-n],v[1-n]=h[1-n],o.push(l[0],l[1]),o.push(v[0],v[1]);break;default:l[n]=u[n],l[1-n]=h[1-n],o.push(l[0],l[1])}return o.push(i[s++],i[s++]),o}function Bn(i,e){var t=[],r=i.length,a,n;function o(h,c,f){var v=h.coord,p=(f-v)/(c.coord-v),d=pi(p,[h.color,c.color]);return{coord:f,color:d}}for(var s=0;s<r;s++){var l=i[s],u=l.coord;if(u<0)a=l;else if(u>e){n?t.push(o(n,l,e)):a&&t.push(o(a,l,0),o(a,l,e));break}else a&&(t.push(o(a,l,0)),a=null),t.push(l),n=l}return t}function Gn(i,e,t){var r=i.getVisual("visualMeta");if(!(!r||!r.length||!i.count())&&e.type==="cartesian2d"){for(var a,n,o=r.length-1;o>=0;o--){var s=i.getDimensionInfo(r[o].dimension);if(a=s&&s.coordDim,a==="x"||a==="y"){n=r[o];break}}if(n){var l=e.getAxis(a),u=B(n.stops,function(x){return{coord:l.toGlobalCoord(l.dataToCoord(x.value)),color:x.color}}),h=u.length,c=n.outerColors.slice();h&&u[0].coord>u[h-1].coord&&(u.reverse(),c.reverse());var f=Bn(u,a==="x"?t.getWidth():t.getHeight()),v=f.length;if(!v&&h)return u[0].coord<0?c[1]?c[1]:u[h-1].color:c[0]?c[0]:u[0].color;var p=10,d=f[0].coord-p,g=f[v-1].coord+p,y=g-d;if(y<.001)return"transparent";A(f,function(x){x.offset=(x.coord-d)/y}),f.push({offset:v?f[v-1].offset:.5,color:c[1]||"transparent"}),f.unshift({offset:v?f[0].offset:.5,color:c[0]||"transparent"});var m=new ga(0,0,0,0,f,!0);return m[a]=d,m[a+"2"]=g,m}}}function Hn(i,e,t){var r=i.get("showAllSymbol"),a=r==="auto";if(!(r&&!a)){var n=t.getAxesByScale("ordinal")[0];if(n&&!(a&&Fn(n,e))){var o=e.mapDimension(n.dim),s={};return A(n.getViewLabels(),function(l){var u=n.scale.getRawOrdinalNumber(l.tickValue);s[u]=1}),function(l){return!s.hasOwnProperty(e.get(o,l))}}}}function Fn(i,e){var t=i.getExtent(),r=Math.abs(t[1]-t[0])/i.scale.count();isNaN(r)&&(r=0);for(var a=e.count(),n=Math.max(1,Math.round(a/5)),o=0;o<a;o+=n)if(Ge.getSymbolSize(e,o)[i.isHorizontal()?1:0]*1.5>r)return!1;return!0}function Zn(i,e){return isNaN(i)||isNaN(e)}function Wn(i){for(var e=i.length/2;e>0&&Zn(i[e*2-2],i[e*2-1]);e--);return e-1}function br(i,e){return[i[e*2],i[e*2+1]]}function Xn(i,e,t){for(var r=i.length/2,a=t==="x"?0:1,n,o,s=0,l=-1,u=0;u<r;u++)if(o=i[u*2+a],!(isNaN(o)||isNaN(i[u*2+1-a]))){if(u===0){n=o;continue}if(n<=e&&o>=e||n>=e&&o<=e){l=u;break}s=u,n=o}return{range:[s,l],t:(e-n)/(o-n)}}function Ea(i){if(i.get(["endLabel","show"]))return!0;for(var e=0;e<Je.length;e++)if(i.get([Je[e],"endLabel","show"]))return!0;return!1}function ue(i,e,t,r){if(He(e,"cartesian2d")){var a=r.getModel("endLabel"),n=a.get("valueAnimation"),o=r.getData(),s={lastFrameIndex:0},l=Ea(r)?function(v,p){i._endLabelOnDuring(v,p,o,s,n,a,e)}:null,u=e.getBaseAxis().isHorizontal(),h=Ra(e,t,r,function(){var v=i._endLabel;v&&t&&s.originalX!=null&&v.attr({x:s.originalX,y:s.originalY})},l);if(!r.get("clip",!0)){var c=h.shape,f=Math.max(c.width,c.height);u?(c.y-=f,c.height+=f*2):(c.x-=f,c.width+=f*2)}return l&&l(1,h),h}else return Oa(e,t,r)}function Un(i,e){var t=e.getBaseAxis(),r=t.isHorizontal(),a=t.inverse,n=r?a?"right":"left":"center",o=r?"middle":a?"top":"bottom";return{normal:{align:i.get("align")||n,verticalAlign:i.get("verticalAlign")||o}}}var Yn=function(i){R(e,i);function e(){return i!==null&&i.apply(this,arguments)||this}return e.prototype.init=function(){var t=new U,r=new Tn;this.group.add(r.group),this._symbolDraw=r,this._lineGroup=t},e.prototype.render=function(t,r,a){var n=this,o=t.coordinateSystem,s=this.group,l=t.getData(),u=t.getModel("lineStyle"),h=t.getModel("areaStyle"),c=l.getLayout("points")||[],f=o.type==="polar",v=this._coordSys,p=this._symbolDraw,d=this._polyline,g=this._polygon,y=this._lineGroup,m=!r.ssr&&t.get("animation"),x=!h.isEmpty(),b=h.get("origin"),_=Ta(o,l,b),S=x&&Nn(o,l,_),M=t.get("showSymbol"),D=t.get("connectNulls"),L=M&&!f&&Hn(t,l,o),T=this._data;T&&T.eachItemGraphicEl(function(H,Ja){H.__temp&&(s.remove(H),T.setItemGraphicEl(Ja,null))}),M||p.remove(),s.add(y);var w=f?!1:t.get("step"),I;o&&o.getArea&&t.get("clip",!0)&&(I=o.getArea(),I.width!=null?(I.x-=.1,I.y-=.1,I.width+=.2,I.height+=.2):I.r0&&(I.r0-=.5,I.r+=.5)),this._clipShapeForSymbol=I;var C=Gn(l,o,a)||l.getVisual("style")[l.getVisual("drawType")];if(!(d&&v.type===o.type&&w===this._step))M&&p.updateData(l,{isIgnore:L,clipShape:I,disableAnimation:!0,getSymbolPoint:function(H){return[c[H*2],c[H*2+1]]}}),m&&this._initSymbolLabelAnimation(l,o,I),w&&(c=st(c,o,w,D),S&&(S=st(S,o,w,D))),d=this._newPolyline(c),x?g=this._newPolygon(c,S):g&&(y.remove(g),g=this._polygon=null),f||this._initOrUpdateEndLabel(t,o,Ke(C)),y.setClipPath(ue(this,o,!0,t));else{x&&!g?g=this._newPolygon(c,S):g&&!x&&(y.remove(g),g=this._polygon=null),f||this._initOrUpdateEndLabel(t,o,Ke(C));var E=y.getClipPath();if(E){var O=ue(this,o,!1,t);ct(E,{shape:O.shape},t)}else y.setClipPath(ue(this,o,!0,t));M&&p.updateData(l,{isIgnore:L,clipShape:I,disableAnimation:!0,getSymbolPoint:function(H){return[c[H*2],c[H*2+1]]}}),(!gr(this._stackedOnPoints,S)||!gr(this._points,c))&&(m?this._doUpdateAnimation(l,S,o,a,w,b,D):(w&&(c=st(c,o,w,D),S&&(S=st(S,o,w,D))),d.setShape({points:c}),g&&g.setShape({points:c,stackedOnPoints:S})))}var N=t.getModel("emphasis"),Z=N.get("focus"),V=N.get("blurScope"),z=N.get("disabled");if(d.useStyle(gt(u.getLineStyle(),{fill:"none",stroke:C,lineJoin:"bevel"})),_e(d,t,"lineStyle"),d.style.lineWidth>0&&t.get(["emphasis","lineStyle","width"])==="bolder"){var Y=d.getState("emphasis").style;Y.lineWidth=+d.style.lineWidth+1}ut(d).seriesIndex=t.seriesIndex,Kt(d,Z,V,z);var W=xr(t.get("smooth")),wt=t.get("smoothMonotone");if(d.setShape({smooth:W,smoothMonotone:wt,connectNulls:D}),g){var $=l.getCalculationInfo("stackedOnSeries"),K=0;g.useStyle(gt(h.getAreaStyle(),{fill:C,opacity:.7,lineJoin:"bevel",decal:l.getVisual("style").decal})),$&&(K=xr($.get("smooth"))),g.setShape({smooth:W,stackedOnSmooth:K,smoothMonotone:wt,connectNulls:D}),_e(g,t,"areaStyle"),ut(g).seriesIndex=t.seriesIndex,Kt(g,Z,V,z)}var Nt=function(H){n._changePolyState(H)};l.eachItemGraphicEl(function(H){H&&(H.onHoverStateChange=Nt)}),this._polyline.onHoverStateChange=Nt,this._data=l,this._coordSys=o,this._stackedOnPoints=S,this._points=c,this._step=w,this._valueOrigin=b,t.get("triggerLineEvent")&&(this.packEventData(t,d),g&&this.packEventData(t,g))},e.prototype.packEventData=function(t,r){ut(r).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,r,a,n){var o=t.getData(),s=qe(o,n);if(this._changePolyState("emphasis"),!(s instanceof Array)&&s!=null&&s>=0){var l=o.getLayout("points"),u=o.getItemGraphicEl(s);if(!u){var h=l[s*2],c=l[s*2+1];if(isNaN(h)||isNaN(c)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(h,c))return;var f=t.get("zlevel")||0,v=t.get("z")||0;u=new Ge(o,s),u.x=h,u.y=c,u.setZ(f,v);var p=u.getSymbolPath().getTextContent();p&&(p.zlevel=f,p.z=v,p.z2=this._polyline.z2+1),u.__temp=!0,o.setItemGraphicEl(s,u),u.stopSymbolAnimation(!0),this.group.add(u)}u.highlight()}else Wt.prototype.highlight.call(this,t,r,a,n)},e.prototype.downplay=function(t,r,a,n){var o=t.getData(),s=qe(o,n);if(this._changePolyState("normal"),s!=null&&s>=0){var l=o.getItemGraphicEl(s);l&&(l.__temp?(o.setItemGraphicEl(s,null),this.group.remove(l)):l.downplay())}else Wt.prototype.downplay.call(this,t,r,a,n)},e.prototype._changePolyState=function(t){var r=this._polygon;je(this._polyline,t),r&&je(r,t)},e.prototype._newPolyline=function(t){var r=this._polyline;return r&&this._lineGroup.remove(r),r=new On({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(r),this._polyline=r,r},e.prototype._newPolygon=function(t,r){var a=this._polygon;return a&&this._lineGroup.remove(a),a=new kn({shape:{points:t,stackedOnPoints:r},segmentIgnoreThreshold:2}),this._lineGroup.add(a),this._polygon=a,a},e.prototype._initSymbolLabelAnimation=function(t,r,a){var n,o,s=r.getBaseAxis(),l=s.inverse;r.type==="cartesian2d"?(n=s.isHorizontal(),o=!1):r.type==="polar"&&(n=s.dim==="angle",o=!0);var u=t.hostModel,h=u.get("animationDuration");tt(h)&&(h=h(null));var c=u.get("animationDelay")||0,f=tt(c)?c(null):c;t.eachItemGraphicEl(function(v,p){var d=v;if(d){var g=[v.x,v.y],y=void 0,m=void 0,x=void 0;if(a)if(o){var b=a,_=r.pointToCoord(g);n?(y=b.startAngle,m=b.endAngle,x=-_[1]/180*Math.PI):(y=b.r0,m=b.r,x=_[0])}else{var S=a;n?(y=S.x,m=S.x+S.width,x=v.x):(y=S.y+S.height,m=S.y,x=v.y)}var M=m===y?0:(x-y)/(m-y);l&&(M=1-M);var D=tt(c)?c(p):h*M+f,L=d.getSymbolPath(),T=L.getTextContent();d.attr({scaleX:0,scaleY:0}),d.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:D}),T&&T.animateFrom({style:{opacity:0}},{duration:300,delay:D}),L.disableLabelAnimation=!0}})},e.prototype._initOrUpdateEndLabel=function(t,r,a){var n=t.getModel("endLabel");if(Ea(t)){var o=t.getData(),s=this._polyline,l=o.getLayout("points");if(!l){s.removeTextContent(),this._endLabel=null;return}var u=this._endLabel;u||(u=this._endLabel=new pt({z2:200}),u.ignoreClip=!0,s.setTextContent(this._endLabel),s.disableLabelAnimation=!0);var h=Wn(l);h>=0&&(Re(s,ee(t,"endLabel"),{inheritColor:a,labelFetcher:t,labelDataIndex:h,defaultText:function(c,f,v){return v!=null?Ca(o,v):Be(o,c)},enableTextSetter:!0},Un(n,r)),s.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,r,a,n,o,s,l){var u=this._endLabel,h=this._polyline;if(u){t<1&&n.originalX==null&&(n.originalX=u.x,n.originalY=u.y);var c=a.getLayout("points"),f=a.hostModel,v=f.get("connectNulls"),p=s.get("precision"),d=s.get("distance")||0,g=l.getBaseAxis(),y=g.isHorizontal(),m=g.inverse,x=r.shape,b=m?y?x.x:x.y+x.height:y?x.x+x.width:x.y,_=(y?d:0)*(m?-1:1),S=(y?0:-d)*(m?-1:1),M=y?"x":"y",D=Xn(c,b,M),L=D.range,T=L[1]-L[0],w=void 0;if(T>=1){if(T>1&&!v){var I=br(c,L[0]);u.attr({x:I[0]+_,y:I[1]+S}),o&&(w=f.getRawValue(L[0]))}else{var I=h.getPointOn(b,M);I&&u.attr({x:I[0]+_,y:I[1]+S});var C=f.getRawValue(L[0]),E=f.getRawValue(L[1]);o&&(w=fi(a,p,C,E,D.t))}n.lastFrameIndex=L[0]}else{var O=t===1||n.lastFrameIndex>0?L[0]:0,I=br(c,O);o&&(w=f.getRawValue(O)),u.attr({x:I[0]+_,y:I[1]+S})}if(o){var N=da(u);typeof N.setLabelText=="function"&&N.setLabelText(w)}}},e.prototype._doUpdateAnimation=function(t,r,a,n,o,s,l){var u=this._polyline,h=this._polygon,c=t.hostModel,f=Rn(this._data,t,this._stackedOnPoints,r,this._coordSys,a,this._valueOrigin),v=f.current,p=f.stackedOnCurrent,d=f.next,g=f.stackedOnNext;if(o&&(v=st(f.current,a,o,l),p=st(f.stackedOnCurrent,a,o,l),d=st(f.next,a,o,l),g=st(f.stackedOnNext,a,o,l)),yr(v,d)>3e3||h&&yr(p,g)>3e3){u.stopAnimation(),u.setShape({points:d}),h&&(h.stopAnimation(),h.setShape({points:d,stackedOnPoints:g}));return}u.shape.__points=f.current,u.shape.points=v;var y={shape:{points:d}};f.current!==v&&(y.shape.__points=f.next),u.stopAnimation(),at(u,y,c),h&&(h.setShape({points:v,stackedOnPoints:p}),h.stopAnimation(),at(h,{shape:{stackedOnPoints:g}},c),u.shape.points!==h.shape.points&&(h.shape.points=u.shape.points));for(var m=[],x=f.status,b=0;b<x.length;b++){var _=x[b].cmd;if(_==="="){var S=t.getItemGraphicEl(x[b].idx1);S&&m.push({el:S,ptIdx:b})}}u.animators&&u.animators.length&&u.animators[0].during(function(){h&&h.dirtyShape();for(var M=u.shape.__points,D=0;D<m.length;D++){var L=m[D].el,T=m[D].ptIdx*2;L.x=M[T],L.y=M[T+1],L.markRedraw()}})},e.prototype.remove=function(t){var r=this.group,a=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),a&&a.eachItemGraphicEl(function(n,o){n.__temp&&(r.remove(n),a.setItemGraphicEl(o,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(Wt);function $n(i,e){return{seriesType:i,plan:di(),reset:function(t){var r=t.getData(),a=t.coordinateSystem,n=t.pipelineContext,o=e||n.large;if(a){var s=B(a.dimensions,function(v){return r.mapDimension(v)}).slice(0,2),l=s.length,u=r.getCalculationInfo("stackResultDimension");qt(r,s[0])&&(s[0]=u),qt(r,s[1])&&(s[1]=u);var h=r.getStore(),c=r.getDimensionIndex(s[0]),f=r.getDimensionIndex(s[1]);return l&&{progress:function(v,p){for(var d=v.end-v.start,g=o&&_t(d*l),y=[],m=[],x=v.start,b=0;x<v.end;x++){var _=void 0;if(l===1){var S=h.get(c,x);_=a.dataToPoint(S,null,m)}else y[0]=h.get(c,x),y[1]=h.get(f,x),_=a.dataToPoint(y,null,m);o?(g[b++]=_[0],g[b++]=_[1]):p.setItemLayout(x,_.slice())}o&&p.setLayout("points",g)}}}}}}var Kn={average:function(i){for(var e=0,t=0,r=0;r<i.length;r++)isNaN(i[r])||(e+=i[r],t++);return t===0?NaN:e/t},sum:function(i){for(var e=0,t=0;t<i.length;t++)e+=i[t]||0;return e},max:function(i){for(var e=-1/0,t=0;t<i.length;t++)i[t]>e&&(e=i[t]);return isFinite(e)?e:NaN},min:function(i){for(var e=1/0,t=0;t<i.length;t++)i[t]<e&&(e=i[t]);return isFinite(e)?e:NaN},minmax:function(i){for(var e=-1/0,t=-1/0,r=0;r<i.length;r++){var a=i[r],n=Math.abs(a);n>e&&(e=n,t=a)}return isFinite(t)?t:NaN},nearest:function(i){return i[0]}},qn=function(i){return Math.round(i.length/2)};function ka(i){return{seriesType:i,reset:function(e,t,r){var a=e.getData(),n=e.get("sampling"),o=e.coordinateSystem,s=a.count();if(s>10&&o.type==="cartesian2d"&&n){var l=o.getBaseAxis(),u=o.getOtherAxis(l),h=l.getExtent(),c=r.getDevicePixelRatio(),f=Math.abs(h[1]-h[0])*(c||1),v=Math.round(s/f);if(isFinite(v)&&v>1){n==="lttb"&&e.setData(a.lttbDownSample(a.mapDimension(u.dim),1/v));var p=void 0;Et(n)?p=Kn[n]:tt(n)&&(p=n),p&&e.setData(a.downSample(a.mapDimension(u.dim),1/v,p,qn))}}}}}function Es(i){i.registerChartView(Yn),i.registerSeriesModel(Ln),i.registerLayout($n("line",!0)),i.registerVisual({seriesType:"line",reset:function(e){var t=e.getData(),r=e.getModel("lineStyle").getLineStyle();r&&!r.stroke&&(r.stroke=t.getVisual("style").fill),t.setVisual("legendLineStyle",r)}}),i.registerProcessor(i.PRIORITY.PROCESSOR.STATISTIC,ka("line"))}var we=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){return Ne(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(t,r,a){var n=this.coordinateSystem;if(n&&n.clampData){var o=n.clampData(t),s=n.dataToPoint(o);if(a)A(n.getAxes(),function(f,v){if(f.type==="category"&&r!=null){var p=f.getTicksCoords(),d=f.getTickModel().get("alignWithLabel"),g=o[v],y=r[v]==="x1"||r[v]==="y1";if(y&&!d&&(g+=1),p.length<2)return;if(p.length===2){s[v]=f.toGlobalCoord(f.getExtent()[y?1:0]);return}for(var m=void 0,x=void 0,b=1,_=0;_<p.length;_++){var S=p[_].coord,M=_===p.length-1?p[_-1].tickValue+b:p[_].tickValue;if(M===g){x=S;break}else if(M<g)m=S;else if(m!=null&&M>g){x=(S+m)/2;break}_===1&&(b=M-p[0].tickValue)}x==null&&(m?m&&(x=p[p.length-1].coord):x=p[0].coord),s[v]=f.toGlobalCoord(x)}});else{var l=this.getData(),u=l.getLayout("offset"),h=l.getLayout("size"),c=n.getBaseAxis().isHorizontal()?0:1;s[c]+=u+h/2}return s}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},e}(Pe);Pe.registerClass(we);var jn=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(){return Ne(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return this.get("large")?this.get("progressive"):!1},e.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),r=this.get("largeThreshold");return r>t&&(t=r),t},e.prototype.brushSelector=function(t,r,a){return a.rect(r.getItemLayout(t))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=kt(we.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),e}(we),Jn=function(){function i(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return i}(),Sr=function(i){R(e,i);function e(t){var r=i.call(this,t)||this;return r.type="sausage",r}return e.prototype.getDefaultShape=function(){return new Jn},e.prototype.buildPath=function(t,r){var a=r.cx,n=r.cy,o=Math.max(r.r0||0,0),s=Math.max(r.r,0),l=(s-o)*.5,u=o+l,h=r.startAngle,c=r.endAngle,f=r.clockwise,v=Math.PI*2,p=f?c-h<v:h-c<v;p||(h=c-(f?v:-v));var d=Math.cos(h),g=Math.sin(h),y=Math.cos(c),m=Math.sin(c);p?(t.moveTo(d*o+a,g*o+n),t.arc(d*u+a,g*u+n,l,-Math.PI+h,h,!f)):t.moveTo(d*s+a,g*s+n),t.arc(a,n,s,h,c,!f),t.arc(y*u+a,m*u+n,l,c-Math.PI*2,c-Math.PI,!f),o!==0&&t.arc(a,n,o,c,h,f)},e}(re);function Qn(i,e){e=e||{};var t=e.isRoundCap;return function(r,a,n){var o=a.position;if(!o||o instanceof Array)return Qe(r,a,n);var s=i(o),l=a.distance!=null?a.distance:5,u=this.shape,h=u.cx,c=u.cy,f=u.r,v=u.r0,p=(f+v)/2,d=u.startAngle,g=u.endAngle,y=(d+g)/2,m=t?Math.abs(f-v)/2:0,x=Math.cos,b=Math.sin,_=h+f*x(d),S=c+f*b(d),M="left",D="top";switch(s){case"startArc":_=h+(v-l)*x(y),S=c+(v-l)*b(y),M="center",D="top";break;case"insideStartArc":_=h+(v+l)*x(y),S=c+(v+l)*b(y),M="center",D="bottom";break;case"startAngle":_=h+p*x(d)+Bt(d,l+m,!1),S=c+p*b(d)+Gt(d,l+m,!1),M="right",D="middle";break;case"insideStartAngle":_=h+p*x(d)+Bt(d,-l+m,!1),S=c+p*b(d)+Gt(d,-l+m,!1),M="left",D="middle";break;case"middle":_=h+p*x(y),S=c+p*b(y),M="center",D="middle";break;case"endArc":_=h+(f+l)*x(y),S=c+(f+l)*b(y),M="center",D="bottom";break;case"insideEndArc":_=h+(f-l)*x(y),S=c+(f-l)*b(y),M="center",D="top";break;case"endAngle":_=h+p*x(g)+Bt(g,l+m,!0),S=c+p*b(g)+Gt(g,l+m,!0),M="left",D="middle";break;case"insideEndAngle":_=h+p*x(g)+Bt(g,-l+m,!0),S=c+p*b(g)+Gt(g,-l+m,!0),M="right",D="middle";break;default:return Qe(r,a,n)}return r=r||{},r.x=_,r.y=S,r.align=M,r.verticalAlign=D,r}}function to(i,e,t,r){if(Oe(r)){i.setTextConfig({rotation:r});return}else if(F(e)){i.setTextConfig({rotation:0});return}var a=i.shape,n=a.clockwise?a.startAngle:a.endAngle,o=a.clockwise?a.endAngle:a.startAngle,s=(n+o)/2,l,u=t(e);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":l=s;break;case"startAngle":case"insideStartAngle":l=n;break;case"endAngle":case"insideEndAngle":l=o;break;default:i.setTextConfig({rotation:0});return}var h=Math.PI*1.5-l;u==="middle"&&h>Math.PI/2&&h<Math.PI*1.5&&(h-=Math.PI),i.setTextConfig({rotation:h})}function Bt(i,e,t){return e*Math.sin(i)*(t?-1:1)}function Gt(i,e,t){return e*Math.cos(i)*(t?1:-1)}var he=Math.max,ce=Math.min;function eo(i,e){var t=i.getArea&&i.getArea();if(He(i,"cartesian2d")){var r=i.getBaseAxis();if(r.type!=="category"||!r.onBand){var a=e.getLayout("bandWidth");r.isHorizontal()?(t.x-=a,t.width+=a*2):(t.y-=a,t.height+=a*2)}}return t}var ro=function(i){R(e,i);function e(){var t=i.call(this)||this;return t.type=e.type,t._isFirstFrame=!0,t}return e.prototype.render=function(t,r,a,n){this._model=t,this._removeOnRenderedListener(a),this._updateDrawMode(t);var o=t.get("coordinateSystem");(o==="cartesian2d"||o==="polar")&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,r,a):this._renderNormal(t,r,a,n))},e.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},e.prototype.incrementalRender=function(t,r){this._progressiveEls=[],this._incrementalRenderLarge(t,r)},e.prototype.eachRendered=function(t){pa(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t,r,a,n){var o=this.group,s=t.getData(),l=this._data,u=t.coordinateSystem,h=u.getBaseAxis(),c;u.type==="cartesian2d"?c=h.isHorizontal():u.type==="polar"&&(c=h.dim==="angle");var f=t.isAnimationEnabled()?t:null,v=ao(t,u);v&&this._enableRealtimeSort(v,s,a);var p=t.get("clip",!0)||v,d=eo(u,s);o.removeClipPath();var g=t.get("roundCap",!0),y=t.get("showBackground",!0),m=t.getModel("backgroundStyle"),x=m.get("borderRadius")||0,b=[],_=this._backgroundEls,S=n&&n.isInitSort,M=n&&n.type==="changeAxisOrder";function D(w){var I=Ht[u.type](s,w),C=ho(u,c,I);return C.useStyle(m.getItemStyle()),u.type==="cartesian2d"?C.setShape("r",x):C.setShape("cornerRadius",x),b[w]=C,C}s.diff(l).add(function(w){var I=s.getItemModel(w),C=Ht[u.type](s,w,I);if(y&&D(w),!(!s.hasValue(w)||!Ir[u.type](C))){var E=!1;p&&(E=_r[u.type](d,C));var O=Mr[u.type](t,s,w,C,c,f,h.model,!1,g);v&&(O.forceLabelAnimation=!0),Dr(O,s,w,I,C,t,c,u.type==="polar"),S?O.attr({shape:C}):v?Ar(v,f,O,C,w,c,!1,!1):ct(O,{shape:C},t,w),s.setItemGraphicEl(w,O),o.add(O),O.ignore=E}}).update(function(w,I){var C=s.getItemModel(w),E=Ht[u.type](s,w,C);if(y){var O=void 0;_.length===0?O=D(I):(O=_[I],O.useStyle(m.getItemStyle()),u.type==="cartesian2d"?O.setShape("r",x):O.setShape("cornerRadius",x),b[w]=O);var N=Ht[u.type](s,w),Z=Na(c,N,u);at(O,{shape:Z},f,w)}var V=l.getItemGraphicEl(I);if(!s.hasValue(w)||!Ir[u.type](E)){o.remove(V);return}var z=!1;if(p&&(z=_r[u.type](d,E),z&&o.remove(V)),V?va(V):V=Mr[u.type](t,s,w,E,c,f,h.model,!!V,g),v&&(V.forceLabelAnimation=!0),M){var Y=V.getTextContent();if(Y){var W=da(Y);W.prevValue!=null&&(W.prevValue=W.value)}}else Dr(V,s,w,C,E,t,c,u.type==="polar");S?V.attr({shape:E}):v?Ar(v,f,V,E,w,c,!0,M):at(V,{shape:E},t,w,null),s.setItemGraphicEl(w,V),V.ignore=z,o.add(V)}).remove(function(w){var I=l.getItemGraphicEl(w);I&&tr(I,t,w)}).execute();var L=this._backgroundGroup||(this._backgroundGroup=new U);L.removeAll();for(var T=0;T<b.length;++T)L.add(b[T]);o.add(L),this._backgroundEls=b,this._data=s},e.prototype._renderLarge=function(t,r,a){this._clear(),Cr(t,this.group),this._updateLargeClip(t)},e.prototype._incrementalRenderLarge=function(t,r){this._removeBackground(),Cr(r,this.group,this._progressiveEls,!0)},e.prototype._updateLargeClip=function(t){var r=t.get("clip",!0)&&zn(t.coordinateSystem,!1,t),a=this.group;r?a.setClipPath(r):a.removeClipPath()},e.prototype._enableRealtimeSort=function(t,r,a){var n=this;if(r.count()){var o=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(r,t,a),this._isFirstFrame=!1;else{var s=function(l){var u=r.getItemGraphicEl(l),h=u&&u.shape;return h&&Math.abs(o.isHorizontal()?h.height:h.width)||0};this._onRendered=function(){n._updateSortWithinSameData(r,s,o,a)},a.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(t,r,a){var n=[];return t.each(t.mapDimension(r.dim),function(o,s){var l=a(s);l=l??NaN,n.push({dataIndex:s,mappedValue:l,ordinalNumber:o})}),n.sort(function(o,s){return s.mappedValue-o.mappedValue}),{ordinalNumbers:B(n,function(o){return o.ordinalNumber})}},e.prototype._isOrderChangedWithinSameData=function(t,r,a){for(var n=a.scale,o=t.mapDimension(a.dim),s=Number.MAX_VALUE,l=0,u=n.getOrdinalMeta().categories.length;l<u;++l){var h=t.rawIndexOf(o,n.getRawOrdinalNumber(l)),c=h<0?Number.MIN_VALUE:r(t.indexOfRawIndex(h));if(c>s)return!0;s=c}return!1},e.prototype._isOrderDifferentInView=function(t,r){for(var a=r.scale,n=a.getExtent(),o=Math.max(0,n[0]),s=Math.min(n[1],a.getOrdinalMeta().categories.length-1);o<=s;++o)if(t.ordinalNumbers[o]!==a.getRawOrdinalNumber(o))return!0},e.prototype._updateSortWithinSameData=function(t,r,a,n){if(this._isOrderChangedWithinSameData(t,r,a)){var o=this._dataSort(t,a,r);this._isOrderDifferentInView(o,a)&&(this._removeOnRenderedListener(n),n.dispatchAction({type:"changeAxisOrder",componentType:a.dim+"Axis",axisId:a.index,sortInfo:o}))}},e.prototype._dispatchInitSort=function(t,r,a){var n=r.baseAxis,o=this._dataSort(t,n,function(s){return t.get(t.mapDimension(r.otherAxis.dim),s)});a.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",isInitSort:!0,axisId:n.index,sortInfo:o})},e.prototype.remove=function(t,r){this._clear(this._model),this._removeOnRenderedListener(r)},e.prototype.dispose=function(t,r){this._removeOnRenderedListener(r)},e.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(t){var r=this.group,a=this._data;t&&t.isAnimationEnabled()&&a&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],a.eachItemGraphicEl(function(n){tr(n,t,ut(n).dataIndex)})):r.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(Wt),_r={cartesian2d:function(i,e){var t=e.width<0?-1:1,r=e.height<0?-1:1;t<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height);var a=i.x+i.width,n=i.y+i.height,o=he(e.x,i.x),s=ce(e.x+e.width,a),l=he(e.y,i.y),u=ce(e.y+e.height,n),h=s<o,c=u<l;return e.x=h&&o>a?s:o,e.y=c&&l>n?u:l,e.width=h?0:s-o,e.height=c?0:u-l,t<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height),h||c},polar:function(i,e){var t=e.r0<=e.r?1:-1;if(t<0){var r=e.r;e.r=e.r0,e.r0=r}var a=ce(e.r,i.r),n=he(e.r0,i.r0);e.r=a,e.r0=n;var o=a-n<0;if(t<0){var r=e.r;e.r=e.r0,e.r0=r}return o}},Mr={cartesian2d:function(i,e,t,r,a,n,o,s,l){var u=new j({shape:Mt({},r),z2:1});if(u.__dataIndex=t,u.name="item",n){var h=u.shape,c=a?"height":"width";h[c]=0}return u},polar:function(i,e,t,r,a,n,o,s,l){var u=!a&&l?Sr:Ee,h=new u({shape:r,z2:1});h.name="item";var c=za(a);if(h.calculateTextPosition=Qn(c,{isRoundCap:u===Sr}),n){var f=h.shape,v=a?"r":"endAngle",p={};f[v]=a?r.r0:r.startAngle,p[v]=r[v],(s?at:ct)(h,{shape:p},n)}return h}};function ao(i,e){var t=i.get("realtimeSort",!0),r=e.getBaseAxis();if(t&&r.type==="category"&&e.type==="cartesian2d")return{baseAxis:r,otherAxis:e.getOtherAxis(r)}}function Ar(i,e,t,r,a,n,o,s){var l,u;n?(u={x:r.x,width:r.width},l={y:r.y,height:r.height}):(u={y:r.y,height:r.height},l={x:r.x,width:r.width}),s||(o?at:ct)(t,{shape:l},e,a,null);var h=e?i.baseAxis.model:null;(o?at:ct)(t,{shape:u},h,a)}function wr(i,e){for(var t=0;t<e.length;t++)if(!isFinite(i[e[t]]))return!0;return!1}var io=["x","y","width","height"],no=["cx","cy","r","startAngle","endAngle"],Ir={cartesian2d:function(i){return!wr(i,io)},polar:function(i){return!wr(i,no)}},Ht={cartesian2d:function(i,e,t){var r=i.getItemLayout(e),a=t?so(t,r):0,n=r.width>0?1:-1,o=r.height>0?1:-1;return{x:r.x+n*a/2,y:r.y+o*a/2,width:r.width-n*a,height:r.height-o*a}},polar:function(i,e,t){var r=i.getItemLayout(e);return{cx:r.cx,cy:r.cy,r0:r.r0,r:r.r,startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}}};function oo(i){return i.startAngle!=null&&i.endAngle!=null&&i.startAngle===i.endAngle}function za(i){return function(e){var t=e?"Arc":"Angle";return function(r){switch(r){case"start":case"insideStart":case"end":case"insideEnd":return r+t;default:return r}}}(i)}function Dr(i,e,t,r,a,n,o,s){var l=e.getItemVisual(t,"style");if(s){if(!n.get("roundCap")){var h=i.shape,c=gi(r.getModel("itemStyle"),h,!0);Mt(h,c),i.setShape(h)}}else{var u=r.get(["itemStyle","borderRadius"])||0;i.setShape("r",u)}i.useStyle(l);var f=r.getShallow("cursor");f&&i.attr("cursor",f);var v=s?o?a.r>=a.r0?"endArc":"startArc":a.endAngle>=a.startAngle?"endAngle":"startAngle":o?a.height>=0?"bottom":"top":a.width>=0?"right":"left",p=ee(r);Re(i,p,{labelFetcher:n,labelDataIndex:t,defaultText:Be(n.getData(),t),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:v});var d=i.getTextContent();if(s&&d){var g=r.get(["label","position"]);i.textConfig.inside=g==="middle"?!0:null,to(i,g==="outside"?v:g,za(o),r.get(["label","rotate"]))}mi(d,p,n.getRawValue(t),function(m){return Ca(e,m)});var y=r.getModel(["emphasis"]);Kt(i,y.get("focus"),y.get("blurScope"),y.get("disabled")),_e(i,r),oo(a)&&(i.style.fill="none",i.style.stroke="none",A(i.states,function(m){m.style&&(m.style.fill=m.style.stroke="none")}))}function so(i,e){var t=i.get(["itemStyle","borderColor"]);if(!t||t==="none")return 0;var r=i.get(["itemStyle","borderWidth"])||0,a=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),n=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(r,a,n)}var lo=function(){function i(){}return i}(),Lr=function(i){R(e,i);function e(t){var r=i.call(this,t)||this;return r.type="largeBar",r}return e.prototype.getDefaultShape=function(){return new lo},e.prototype.buildPath=function(t,r){for(var a=r.points,n=this.baseDimIdx,o=1-this.baseDimIdx,s=[],l=[],u=this.barWidth,h=0;h<a.length;h+=3)l[n]=u,l[o]=a[h+2],s[n]=a[h+n],s[o]=a[h+o],t.rect(s[0],s[1],l[0],l[1])},e}(re);function Cr(i,e,t,r){var a=i.getData(),n=a.getLayout("valueAxisHorizontal")?1:0,o=a.getLayout("largeDataIndices"),s=a.getLayout("size"),l=i.getModel("backgroundStyle"),u=a.getLayout("largeBackgroundPoints");if(u){var h=new Lr({shape:{points:u},incremental:!!r,silent:!0,z2:0});h.baseDimIdx=n,h.largeDataIndices=o,h.barWidth=s,h.useStyle(l.getItemStyle()),e.add(h),t&&t.push(h)}var c=new Lr({shape:{points:a.getLayout("largePoints")},incremental:!!r,ignoreCoarsePointer:!0,z2:1});c.baseDimIdx=n,c.largeDataIndices=o,c.barWidth=s,e.add(c),c.useStyle(a.getVisual("style")),ut(c).seriesIndex=i.seriesIndex,i.get("silent")||(c.on("mousedown",Tr),c.on("mousemove",Tr)),t&&t.push(c)}var Tr=yi(function(i){var e=this,t=uo(e,i.offsetX,i.offsetY);ut(e).dataIndex=t>=0?t:null},30,!1);function uo(i,e,t){for(var r=i.baseDimIdx,a=1-r,n=i.shape.points,o=i.largeDataIndices,s=[],l=[],u=i.barWidth,h=0,c=n.length/3;h<c;h++){var f=h*3;if(l[r]=u,l[a]=n[f+2],s[r]=n[f+r],s[a]=n[f+a],l[a]<0&&(s[a]+=l[a],l[a]=-l[a]),e>=s[0]&&e<=s[0]+l[0]&&t>=s[1]&&t<=s[1]+l[1])return o[h]}return-1}function Na(i,e,t){if(He(t,"cartesian2d")){var r=e,a=t.getArea();return{x:i?r.x:a.x,y:i?a.y:r.y,width:i?r.width:a.width,height:i?a.height:r.height}}else{var a=t.getArea(),n=e;return{cx:a.cx,cy:a.cy,r0:i?a.r0:n.r0,r:i?a.r:n.r,startAngle:i?n.startAngle:0,endAngle:i?n.endAngle:Math.PI*2}}}function ho(i,e,t){var r=i.type==="polar"?Ee:j;return new r({shape:Na(e,t,i),silent:!0,z2:0})}function ks(i){i.registerChartView(ro),i.registerSeriesModel(jn),i.registerLayout(i.PRIORITY.VISUAL.LAYOUT,$t(bi,"bar")),i.registerLayout(i.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,xi("bar")),i.registerProcessor(i.PRIORITY.PROCESSOR.STATISTIC,ka("bar")),i.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(e,t){var r=e.componentType||"series";t.eachComponent({mainType:r,query:e},function(a){e.sortInfo&&a.axis.setCategorySortInfo(e.sortInfo)})})}var co=function(i){R(e,i);function e(){return i!==null&&i.apply(this,arguments)||this}return e.type="grid",e.dependencies=["xAxis","yAxis"],e.layoutMode="box",e.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},e}(ae),Ie=function(i){R(e,i);function e(){return i!==null&&i.apply(this,arguments)||this}return e.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Q).models[0]},e.type="cartesian2dAxis",e}(ae);Si(Ie,gn);var Ba={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},vo=q({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Ba),Fe=q({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Ba),fo=q({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Fe),po=gt({logBase:10},Fe);const go={category:vo,value:Fe,time:fo,log:po};var mo={value:1,category:1,time:1,log:1};function Vr(i,e,t,r){A(mo,function(a,n){var o=q(q({},go[n],!0),r,!0),s=function(l){R(u,l);function u(){var h=l!==null&&l.apply(this,arguments)||this;return h.type=e+"Axis."+n,h}return u.prototype.mergeDefaultAndTheme=function(h,c){var f=_i(this),v=f?ma(h):{},p=c.getTheme();q(h,p.get(n+"Axis")),q(h,this.getDefaultOption()),h.type=Pr(h),f&&Mi(h,v,f)},u.prototype.optionUpdated=function(){var h=this.option;h.type==="category"&&(this.__ordinalMeta=Ai.createByAxisModel(this))},u.prototype.getCategories=function(h){var c=this.option;if(c.type==="category")return h?c.data:this.__ordinalMeta.categories},u.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},u.type=e+"Axis."+n,u.defaultOption=o,u}(t);i.registerComponentModel(s)}),i.registerSubTypeDefaulter(e+"Axis",Pr)}function Pr(i){return i.type||(i.data?"category":"value")}var yo=function(){function i(e){this.type="cartesian",this._dimList=[],this._axes={},this.name=e||""}return i.prototype.getAxis=function(e){return this._axes[e]},i.prototype.getAxes=function(){return B(this._dimList,function(e){return this._axes[e]},this)},i.prototype.getAxesByScale=function(e){return e=e.toLowerCase(),wi(this.getAxes(),function(t){return t.scale.type===e})},i.prototype.addAxis=function(e){var t=e.dim;this._axes[t]=e,this._dimList.push(t)},i}(),De=["x","y"];function Rr(i){return i.type==="interval"||i.type==="time"}var xo=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type="cartesian2d",t.dimensions=De,t}return e.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,r=this.getAxis("y").scale;if(!(!Rr(t)||!Rr(r))){var a=t.getExtent(),n=r.getExtent(),o=this.dataToPoint([a[0],n[0]]),s=this.dataToPoint([a[1],n[1]]),l=a[1]-a[0],u=n[1]-n[0];if(!(!l||!u)){var h=(s[0]-o[0])/l,c=(s[1]-o[1])/u,f=o[0]-a[0]*h,v=o[1]-n[0]*c,p=this._transform=[h,0,0,c,f,v];this._invTransform=Ii([],p)}}},e.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},e.prototype.containPoint=function(t){var r=this.getAxis("x"),a=this.getAxis("y");return r.contain(r.toLocalCoord(t[0]))&&a.contain(a.toLocalCoord(t[1]))},e.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},e.prototype.containZone=function(t,r){var a=this.dataToPoint(t),n=this.dataToPoint(r),o=this.getArea(),s=new er(a[0],a[1],n[0]-a[0],n[1]-a[1]);return o.intersect(s)},e.prototype.dataToPoint=function(t,r,a){a=a||[];var n=t[0],o=t[1];if(this._transform&&n!=null&&isFinite(n)&&o!=null&&isFinite(o))return rr(a,t,this._transform);var s=this.getAxis("x"),l=this.getAxis("y");return a[0]=s.toGlobalCoord(s.dataToCoord(n,r)),a[1]=l.toGlobalCoord(l.dataToCoord(o,r)),a},e.prototype.clampData=function(t,r){var a=this.getAxis("x").scale,n=this.getAxis("y").scale,o=a.getExtent(),s=n.getExtent(),l=a.parse(t[0]),u=n.parse(t[1]);return r=r||[],r[0]=Math.min(Math.max(Math.min(o[0],o[1]),l),Math.max(o[0],o[1])),r[1]=Math.min(Math.max(Math.min(s[0],s[1]),u),Math.max(s[0],s[1])),r},e.prototype.pointToData=function(t,r){var a=[];if(this._invTransform)return rr(a,t,this._invTransform);var n=this.getAxis("x"),o=this.getAxis("y");return a[0]=n.coordToData(n.toLocalCoord(t[0]),r),a[1]=o.coordToData(o.toLocalCoord(t[1]),r),a},e.prototype.getOtherAxis=function(t){return this.getAxis(t.dim==="x"?"y":"x")},e.prototype.getArea=function(t){t=t||0;var r=this.getAxis("x").getGlobalExtent(),a=this.getAxis("y").getGlobalExtent(),n=Math.min(r[0],r[1])-t,o=Math.min(a[0],a[1])-t,s=Math.max(r[0],r[1])-n+t,l=Math.max(a[0],a[1])-o+t;return new er(n,o,s,l)},e}(yo),bo=function(i){R(e,i);function e(t,r,a,n,o){var s=i.call(this,t,r,a)||this;return s.index=0,s.type=n||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.getGlobalExtent=function(t){var r=this.getExtent();return r[0]=this.toGlobalCoord(r[0]),r[1]=this.toGlobalCoord(r[1]),t&&r[0]>r[1]&&r.reverse(),r},e.prototype.pointToData=function(t,r){return this.coordToData(this.toLocalCoord(t[this.dim==="x"?0:1]),r)},e.prototype.setCategorySortInfo=function(t){if(this.type!=="category")return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},e}(wn),ve=Math.log;function So(i,e,t){var r=Di.prototype,a=r.getTicks.call(t),n=r.getTicks.call(t,!0),o=a.length-1,s=r.getInterval.call(t),l=Li(i,e),u=l.extent,h=l.fixMin,c=l.fixMax;if(i.type==="log"){var f=ve(i.base);u=[ve(u[0])/f,ve(u[1])/f]}i.setExtent(u[0],u[1]),i.calcNiceExtent({splitNumber:o,fixMin:h,fixMax:c});var v=r.getExtent.call(i);h&&(u[0]=v[0]),c&&(u[1]=v[1]);var p=r.getInterval.call(i),d=u[0],g=u[1];if(h&&c)p=(g-d)/o;else if(h)for(g=u[0]+p*o;g<u[1]&&isFinite(g)&&isFinite(u[1]);)p=ie(p),g=u[0]+p*o;else if(c)for(d=u[1]-p*o;d>u[0]&&isFinite(d)&&isFinite(u[0]);)p=ie(p),d=u[1]-p*o;else{var y=i.getTicks().length-1;y>o&&(p=ie(p));var m=p*o;g=Math.ceil(u[1]/p)*p,d=rt(g-m),d<0&&u[0]>=0?(d=0,g=rt(m)):g>0&&u[1]<=0&&(g=0,d=-rt(m))}var x=(a[0].value-n[0].value)/s,b=(a[o].value-n[o].value)/s;r.setExtent.call(i,d+p*x,g+p*b),r.setInterval.call(i,p),(x||b)&&r.setNiceExtent.call(i,d+p,g-p)}var _o=function(){function i(e,t,r){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=De,this._initCartesian(e,t,r),this.model=e}return i.prototype.getRect=function(){return this._rect},i.prototype.update=function(e,t){var r=this._axesMap;this._updateScale(e,this.model);function a(o){var s,l=ya(o),u=l.length;if(u){for(var h=[],c=u-1;c>=0;c--){var f=+l[c],v=o[f],p=v.model,d=v.scale;Me(d)&&p.get("alignTicks")&&p.get("interval")==null?h.push(v):(nr(d,p),Me(d)&&(s=v))}h.length&&(s||(s=h.pop(),nr(s.scale,s.model)),A(h,function(g){So(g.scale,g.model,s.scale)}))}}a(r.x),a(r.y);var n={};A(r.x,function(o){Or(r,"y",o,n)}),A(r.y,function(o){Or(r,"x",o,n)}),this.resize(this.model,t)},i.prototype.resize=function(e,t,r){var a=e.getBoxLayoutParams(),n=!r&&e.get("containLabel"),o=ke(a,{width:t.getWidth(),height:t.getHeight()});this._rect=o;var s=this._axesList;l(),n&&(A(s,function(u){if(!u.model.get(["axisLabel","inside"])){var h=Ci(u);if(h){var c=u.isHorizontal()?"height":"width",f=u.model.get(["axisLabel","margin"]);o[c]-=h[c]+f,u.position==="top"?o.y+=h.height+f:u.position==="left"&&(o.x+=h.width+f)}}}),l()),A(this._coordsList,function(u){u.calcAffineTransform()});function l(){A(s,function(u){var h=u.isHorizontal(),c=h?[0,o.width]:[0,o.height],f=u.inverse?1:0;u.setExtent(c[f],c[1-f]),Mo(u,h?o.x:o.y)})}},i.prototype.getAxis=function(e,t){var r=this._axesMap[e];if(r!=null)return r[t||0]},i.prototype.getAxes=function(){return this._axesList.slice()},i.prototype.getCartesian=function(e,t){if(e!=null&&t!=null){var r="x"+e+"y"+t;return this._coordsMap[r]}it(e)&&(t=e.yAxisIndex,e=e.xAxisIndex);for(var a=0,n=this._coordsList;a<n.length;a++)if(n[a].getAxis("x").index===e||n[a].getAxis("y").index===t)return n[a]},i.prototype.getCartesians=function(){return this._coordsList.slice()},i.prototype.convertToPixel=function(e,t,r){var a=this._findConvertTarget(t);return a.cartesian?a.cartesian.dataToPoint(r):a.axis?a.axis.toGlobalCoord(a.axis.dataToCoord(r)):null},i.prototype.convertFromPixel=function(e,t,r){var a=this._findConvertTarget(t);return a.cartesian?a.cartesian.pointToData(r):a.axis?a.axis.coordToData(a.axis.toLocalCoord(r)):null},i.prototype._findConvertTarget=function(e){var t=e.seriesModel,r=e.xAxisModel||t&&t.getReferringComponents("xAxis",Q).models[0],a=e.yAxisModel||t&&t.getReferringComponents("yAxis",Q).models[0],n=e.gridModel,o=this._coordsList,s,l;if(t)s=t.coordinateSystem,Tt(o,s)<0&&(s=null);else if(r&&a)s=this.getCartesian(r.componentIndex,a.componentIndex);else if(r)l=this.getAxis("x",r.componentIndex);else if(a)l=this.getAxis("y",a.componentIndex);else if(n){var u=n.coordinateSystem;u===this&&(s=this._coordsList[0])}return{cartesian:s,axis:l}},i.prototype.containPoint=function(e){var t=this._coordsList[0];if(t)return t.containPoint(e)},i.prototype._initCartesian=function(e,t,r){var a=this,n=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},l={x:0,y:0};if(t.eachComponent("xAxis",u("x"),this),t.eachComponent("yAxis",u("y"),this),!l.x||!l.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,A(s.x,function(h,c){A(s.y,function(f,v){var p="x"+c+"y"+v,d=new xo(p);d.master=a,d.model=e,a._coordsMap[p]=d,a._coordsList.push(d),d.addAxis(h),d.addAxis(f)})});function u(h){return function(c,f){if(fe(c,e)){var v=c.get("position");h==="x"?v!=="top"&&v!=="bottom"&&(v=o.bottom?"top":"bottom"):v!=="left"&&v!=="right"&&(v=o.left?"right":"left"),o[v]=!0;var p=new bo(h,Ti(c),[0,0],c.get("type"),v),d=p.type==="category";p.onBand=d&&c.get("boundaryGap"),p.inverse=c.get("inverse"),c.axis=p,p.model=c,p.grid=n,p.index=f,n._axesList.push(p),s[h][f]=p,l[h]++}}}},i.prototype._updateScale=function(e,t){A(this._axesList,function(a){if(a.scale.setExtent(1/0,-1/0),a.type==="category"){var n=a.model.get("categorySortInfo");a.scale.setSortInfo(n)}}),e.eachSeries(function(a){if(ar(a)){var n=ir(a),o=n.xAxisModel,s=n.yAxisModel;if(!fe(o,t)||!fe(s,t))return;var l=this.getCartesian(o.componentIndex,s.componentIndex),u=a.getData(),h=l.getAxis("x"),c=l.getAxis("y");r(u,h),r(u,c)}},this);function r(a,n){A(Vi(a,n.dim),function(o){n.scale.unionExtentFromData(a,o)})}},i.prototype.getTooltipAxes=function(e){var t=[],r=[];return A(this.getCartesians(),function(a){var n=e!=null&&e!=="auto"?a.getAxis(e):a.getBaseAxis(),o=a.getOtherAxis(n);Tt(t,n)<0&&t.push(n),Tt(r,o)<0&&r.push(o)}),{baseAxes:t,otherAxes:r}},i.create=function(e,t){var r=[];return e.eachComponent("grid",function(a,n){var o=new i(a,e,t);o.name="grid_"+n,o.resize(a,t,!0),a.coordinateSystem=o,r.push(o)}),e.eachSeries(function(a){if(ar(a)){var n=ir(a),o=n.xAxisModel,s=n.yAxisModel,l=o.getCoordSysModel(),u=l.coordinateSystem;a.coordinateSystem=u.getCartesian(o.componentIndex,s.componentIndex)}}),r},i.dimensions=De,i}();function fe(i,e){return i.getCoordSysModel()===e}function Or(i,e,t,r){t.getAxesOnZeroOf=function(){return n?[n]:[]};var a=i[e],n,o=t.model,s=o.get(["axisLine","onZero"]),l=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(l!=null)Er(a[l])&&(n=a[l]);else for(var u in a)if(a.hasOwnProperty(u)&&Er(a[u])&&!r[h(a[u])]){n=a[u];break}n&&(r[h(n)]=!0);function h(c){return c.dim+"_"+c.index}}function Er(i){return i&&i.type!=="category"&&i.type!=="time"&&Pi(i)}function Mo(i,e){var t=i.getExtent(),r=t[0]+t[1];i.toGlobalCoord=i.dim==="x"?function(a){return a+e}:function(a){return r-a+e},i.toLocalCoord=i.dim==="x"?function(a){return a-e}:function(a){return r-a+e}}var Le=Ve();function Ao(i,e,t,r){var a=t.axis;if(!a.scale.isBlank()){var n=t.getModel("splitArea"),o=n.getModel("areaStyle"),s=o.get("color"),l=r.coordinateSystem.getRect(),u=a.getTicksCoords({tickModel:n,clamp:!0});if(u.length){var h=s.length,c=Le(i).splitAreaColors,f=et(),v=0;if(c)for(var p=0;p<u.length;p++){var d=c.get(u[p].tickValue);if(d!=null){v=(d+(h-1)*p)%h;break}}var g=a.toGlobalCoord(u[0].coord),y=o.getAreaStyle();s=F(s)?s:[s];for(var p=1;p<u.length;p++){var m=a.toGlobalCoord(u[p].coord),x=void 0,b=void 0,_=void 0,S=void 0;a.isHorizontal()?(x=g,b=l.y,_=m-x,S=l.height,g=x+_):(x=l.x,b=g,_=l.width,S=m-b,g=b+S);var M=u[p-1].tickValue;M!=null&&f.set(M,v),e.add(new j({anid:M!=null?"area_"+M:null,shape:{x,y:b,width:_,height:S},style:gt({fill:s[v]},y),autoBatch:!0,silent:!0})),v=(v+1)%h}Le(i).splitAreaColors=f}}}function wo(i){Le(i).splitAreaColors=null}var Io=["axisLine","axisTickLabel","axisName"],Do=["splitArea","splitLine","minorSplitLine"],Ga=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="CartesianAxisPointer",t}return e.prototype.render=function(t,r,a,n){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new U,this.group.add(this._axisGroup),!!t.get("show")){var s=t.getCoordSysModel(),l=Ri(s,t),u=new Oi(t,Mt({handleAutoShown:function(c){for(var f=s.coordinateSystem.getCartesians(),v=0;v<f.length;v++)if(Me(f[v].getOtherAxis(t.axis).scale))return!0;return!1}},l));A(Io,u.add,u),this._axisGroup.add(u.getGroup()),A(Do,function(c){t.get([c,"show"])&&Lo[c](this,this._axisGroup,t,s)},this);var h=n&&n.type==="changeAxisOrder"&&n.isInitSort;h||Ei(o,this._axisGroup,t),i.prototype.render.call(this,t,r,a,n)}},e.prototype.remove=function(){wo(this)},e.type="cartesianAxis",e}(ki),Lo={splitLine:function(i,e,t,r){var a=t.axis;if(!a.scale.isBlank()){var n=t.getModel("splitLine"),o=n.getModel("lineStyle"),s=o.get("color");s=F(s)?s:[s];for(var l=r.coordinateSystem.getRect(),u=a.isHorizontal(),h=0,c=a.getTicksCoords({tickModel:n}),f=[],v=[],p=o.getLineStyle(),d=0;d<c.length;d++){var g=a.toGlobalCoord(c[d].coord);u?(f[0]=g,f[1]=l.y,v[0]=g,v[1]=l.y+l.height):(f[0]=l.x,f[1]=g,v[0]=l.x+l.width,v[1]=g);var y=h++%s.length,m=c[d].tickValue,x=new or({anid:m!=null?"line_"+c[d].tickValue:null,autoBatch:!0,shape:{x1:f[0],y1:f[1],x2:v[0],y2:v[1]},style:gt({stroke:s[y]},p),silent:!0});sr(x.shape,p.lineWidth),e.add(x)}}},minorSplitLine:function(i,e,t,r){var a=t.axis,n=t.getModel("minorSplitLine"),o=n.getModel("lineStyle"),s=r.coordinateSystem.getRect(),l=a.isHorizontal(),u=a.getMinorTicksCoords();if(u.length)for(var h=[],c=[],f=o.getLineStyle(),v=0;v<u.length;v++)for(var p=0;p<u[v].length;p++){var d=a.toGlobalCoord(u[v][p].coord);l?(h[0]=d,h[1]=s.y,c[0]=d,c[1]=s.y+s.height):(h[0]=s.x,h[1]=d,c[0]=s.x+s.width,c[1]=d);var g=new or({anid:"minor_line_"+u[v][p].tickValue,autoBatch:!0,shape:{x1:h[0],y1:h[1],x2:c[0],y2:c[1]},style:f,silent:!0});sr(g.shape,f.lineWidth),e.add(g)}},splitArea:function(i,e,t,r){Ao(i,e,t,r)}},Ha=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t}return e.type="xAxis",e}(Ga),Co=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=Ha.type,t}return e.type="yAxis",e}(Ga),To=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type="grid",t}return e.prototype.render=function(t,r){this.group.removeAll(),t.get("show")&&this.group.add(new j({shape:t.coordinateSystem.getRect(),style:gt({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},e.type="grid",e}(ze),kr={offset:0};function Vo(i){i.registerComponentView(To),i.registerComponentModel(co),i.registerCoordinateSystem("cartesian2d",_o),Vr(i,"x",Ie,kr),Vr(i,"y",Ie,kr),i.registerComponentView(Ha),i.registerComponentView(Co),i.registerPreprocessor(function(e){e.xAxis&&e.yAxis&&!e.grid&&(e.grid={})})}var zr="\0_ec_interaction_mutex";function zs(i,e,t){var r=Ze(i);r[e]=t}function Ns(i,e,t){var r=Ze(i),a=r[e];a===t&&(r[e]=null)}function Nr(i,e){return!!Ze(i)[e]}function Ze(i){return i[zr]||(i[zr]={})}zi({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},xa);var Po=function(i){R(e,i);function e(t){var r=i.call(this)||this;r._zr=t;var a=P(r._mousedownHandler,r),n=P(r._mousemoveHandler,r),o=P(r._mouseupHandler,r),s=P(r._mousewheelHandler,r),l=P(r._pinchHandler,r);return r.enable=function(u,h){this.disable(),this._opt=gt(X(h)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),u==null&&(u=!0),(u===!0||u==="move"||u==="pan")&&(t.on("mousedown",a),t.on("mousemove",n),t.on("mouseup",o)),(u===!0||u==="scale"||u==="zoom")&&(t.on("mousewheel",s),t.on("pinch",l))},r.disable=function(){t.off("mousedown",a),t.off("mousemove",n),t.off("mouseup",o),t.off("mousewheel",s),t.off("pinch",l)},r}return e.prototype.isDragging=function(){return this._dragging},e.prototype.isPinching=function(){return this._pinching},e.prototype.setPointerChecker=function(t){this.pointerChecker=t},e.prototype.dispose=function(){this.disable()},e.prototype._mousedownHandler=function(t){if(!lr(t)){for(var r=t.target;r;){if(r.draggable)return;r=r.__hostTarget||r.parent}var a=t.offsetX,n=t.offsetY;this.pointerChecker&&this.pointerChecker(t,a,n)&&(this._x=a,this._y=n,this._dragging=!0)}},e.prototype._mousemoveHandler=function(t){if(!(!this._dragging||!Ut("moveOnMouseMove",t,this._opt)||t.gestureEvent==="pinch"||Nr(this._zr,"globalPan"))){var r=t.offsetX,a=t.offsetY,n=this._x,o=this._y,s=r-n,l=a-o;this._x=r,this._y=a,this._opt.preventDefaultMouseMove&&mt(t.event),Fa(this,"pan","moveOnMouseMove",t,{dx:s,dy:l,oldX:n,oldY:o,newX:r,newY:a,isAvailableBehavior:null})}},e.prototype._mouseupHandler=function(t){lr(t)||(this._dragging=!1)},e.prototype._mousewheelHandler=function(t){var r=Ut("zoomOnMouseWheel",t,this._opt),a=Ut("moveOnMouseWheel",t,this._opt),n=t.wheelDelta,o=Math.abs(n),s=t.offsetX,l=t.offsetY;if(!(n===0||!r&&!a)){if(r){var u=o>3?1.4:o>1?1.2:1.1,h=n>0?u:1/u;pe(this,"zoom","zoomOnMouseWheel",t,{scale:h,originX:s,originY:l,isAvailableBehavior:null})}if(a){var c=Math.abs(n),f=(n>0?1:-1)*(c>3?.4:c>1?.15:.05);pe(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:f,originX:s,originY:l,isAvailableBehavior:null})}}},e.prototype._pinchHandler=function(t){if(!Nr(this._zr,"globalPan")){var r=t.pinchScale>1?1.1:1/1.1;pe(this,"zoom",null,t,{scale:r,originX:t.pinchX,originY:t.pinchY,isAvailableBehavior:null})}},e}(Ni);function pe(i,e,t,r,a){i.pointerChecker&&i.pointerChecker(r,a.originX,a.originY)&&(mt(r.event),Fa(i,e,t,r,a))}function Fa(i,e,t,r,a){a.isAvailableBehavior=P(Ut,null,t,r),i.trigger(e,a)}function Ut(i,e,t){var r=t[i];return!i||r&&(!Et(r)||e.event[r+"Key"])}var Ot=A,Ro=it,jt=-1,G=function(){function i(e){var t=e.mappingMethod,r=e.type,a=this.option=X(e);this.type=r,this.mappingMethod=t,this._normalizeData=ko[t];var n=i.visualHandlers[r];this.applyVisual=n.applyVisual,this.getColorMapper=n.getColorMapper,this._normalizedToVisual=n._normalizedToVisual[t],t==="piecewise"?(de(a),Oo(a)):t==="category"?a.categories?Eo(a):de(a,!0):(Bi(t!=="linear"||a.dataExtent),de(a))}return i.prototype.mapValueToVisual=function(e){var t=this._normalizeData(e);return this._normalizedToVisual(t,e)},i.prototype.getNormalizer=function(){return P(this._normalizeData,this)},i.listVisualTypes=function(){return ya(i.visualHandlers)},i.isValidType=function(e){return i.visualHandlers.hasOwnProperty(e)},i.eachVisual=function(e,t,r){it(e)?A(e,t,r):t.call(r,e)},i.mapVisual=function(e,t,r){var a,n=F(e)?[]:it(e)?{}:(a=!0,null);return i.eachVisual(e,function(o,s){var l=t.call(r,o,s);a?n=l:n[s]=l}),n},i.retrieveVisuals=function(e){var t={},r;return e&&Ot(i.visualHandlers,function(a,n){e.hasOwnProperty(n)&&(t[n]=e[n],r=!0)}),r?t:null},i.prepareVisualTypes=function(e){if(F(e))e=e.slice();else if(Ro(e)){var t=[];Ot(e,function(r,a){t.push(a)}),e=t}else return[];return e.sort(function(r,a){return a==="color"&&r!=="color"&&r.indexOf("color")===0?1:-1}),e},i.dependsOn=function(e,t){return t==="color"?!!(e&&e.indexOf(t)===0):e===t},i.findPieceIndex=function(e,t,r){for(var a,n=1/0,o=0,s=t.length;o<s;o++){var l=t[o].value;if(l!=null){if(l===e||Et(l)&&l===e+"")return o;r&&f(l,o)}}for(var o=0,s=t.length;o<s;o++){var u=t[o],h=u.interval,c=u.close;if(h){if(h[0]===-1/0){if(Zt(c[1],e,h[1]))return o}else if(h[1]===1/0){if(Zt(c[0],h[0],e))return o}else if(Zt(c[0],h[0],e)&&Zt(c[1],e,h[1]))return o;r&&f(h[0],o),r&&f(h[1],o)}}if(r)return e===1/0?t.length-1:e===-1/0?0:a;function f(v,p){var d=Math.abs(v-e);d<n&&(n=d,a=p)}},i.visualHandlers={color:{applyVisual:It("color"),getColorMapper:function(){var e=this.option;return P(e.mappingMethod==="category"?function(t,r){return!r&&(t=this._normalizeData(t)),Ct.call(this,t)}:function(t,r,a){var n=!!a;return!r&&(t=this._normalizeData(t)),a=ne(t,e.parsedVisual,a),n?a:oe(a,"rgba")},this)},_normalizedToVisual:{linear:function(e){return oe(ne(e,this.option.parsedVisual),"rgba")},category:Ct,piecewise:function(e,t){var r=Te.call(this,t);return r==null&&(r=oe(ne(e,this.option.parsedVisual),"rgba")),r},fixed:vt}},colorHue:Ft(function(e,t){return se(e,t)}),colorSaturation:Ft(function(e,t){return se(e,null,t)}),colorLightness:Ft(function(e,t){return se(e,null,null,t)}),colorAlpha:Ft(function(e,t){return Hi(e,t)}),decal:{applyVisual:It("decal"),_normalizedToVisual:{linear:null,category:Ct,piecewise:null,fixed:null}},opacity:{applyVisual:It("opacity"),_normalizedToVisual:Ce([0,1])},liftZ:{applyVisual:It("liftZ"),_normalizedToVisual:{linear:vt,category:vt,piecewise:vt,fixed:vt}},symbol:{applyVisual:function(e,t,r){var a=this.mapValueToVisual(e);r("symbol",a)},_normalizedToVisual:{linear:Br,category:Ct,piecewise:function(e,t){var r=Te.call(this,t);return r==null&&(r=Br.call(this,e)),r},fixed:vt}},symbolSize:{applyVisual:It("symbolSize"),_normalizedToVisual:Ce([0,1])}},i}();function Oo(i){var e=i.pieceList;i.hasSpecialVisual=!1,A(e,function(t,r){t.originIndex=r,t.visual!=null&&(i.hasSpecialVisual=!0)})}function Eo(i){var e=i.categories,t=i.categoryMap={},r=i.visual;if(Ot(e,function(o,s){t[o]=s}),!F(r)){var a=[];it(r)?Ot(r,function(o,s){var l=t[s];a[l??jt]=o}):a[jt]=r,r=Za(i,a)}for(var n=e.length-1;n>=0;n--)r[n]==null&&(delete t[e[n]],e.pop())}function de(i,e){var t=i.visual,r=[];it(t)?Ot(t,function(n){r.push(n)}):t!=null&&r.push(t);var a={color:1,symbol:1};!e&&r.length===1&&!a.hasOwnProperty(i.type)&&(r[1]=r[0]),Za(i,r)}function Ft(i){return{applyVisual:function(e,t,r){var a=this.mapValueToVisual(e);r("color",i(t("color"),a))},_normalizedToVisual:Ce([0,1])}}function Br(i){var e=this.option.visual;return e[Math.round(k(i,[0,1],[0,e.length-1],!0))]||{}}function It(i){return function(e,t,r){r(i,this.mapValueToVisual(e))}}function Ct(i){var e=this.option.visual;return e[this.option.loop&&i!==jt?i%e.length:i]}function vt(){return this.option.visual[0]}function Ce(i){return{linear:function(e){return k(e,i,this.option.visual,!0)},category:Ct,piecewise:function(e,t){var r=Te.call(this,t);return r==null&&(r=k(e,i,this.option.visual,!0)),r},fixed:vt}}function Te(i){var e=this.option,t=e.pieceList;if(e.hasSpecialVisual){var r=G.findPieceIndex(i,t),a=t[r];if(a&&a.visual)return a.visual[this.type]}}function Za(i,e){return i.visual=e,i.type==="color"&&(i.parsedVisual=B(e,function(t){var r=Gi(t);return r||[0,0,0,1]})),e}var ko={linear:function(i){return k(i,this.option.dataExtent,[0,1],!0)},piecewise:function(i){var e=this.option.pieceList,t=G.findPieceIndex(i,e,!0);if(t!=null)return k(t,[0,e.length-1],[0,1],!0)},category:function(i){var e=this.option.categories?this.option.categoryMap[i]:i;return e??jt},fixed:xa};function Zt(i,e,t){return i?e<=t:e<t}function zt(i,e,t,r,a,n){i=i||0;var o=t[1]-t[0];if(a!=null&&(a=xt(a,[0,o])),n!=null&&(n=Math.max(n,a??0)),r==="all"){var s=Math.abs(e[1]-e[0]);s=xt(s,[0,o]),a=n=xt(s,[a,n]),r=0}e[0]=xt(e[0],t),e[1]=xt(e[1],t);var l=ge(e,r);e[r]+=i;var u=a||0,h=t.slice();l.sign<0?h[0]+=u:h[1]-=u,e[r]=xt(e[r],h);var c;return c=ge(e,r),a!=null&&(c.sign!==l.sign||c.span<a)&&(e[1-r]=e[r]+l.sign*a),c=ge(e,r),n!=null&&c.span>n&&(e[1-r]=e[r]+c.sign*n),e}function ge(i,e){var t=i[e]-i[1-e];return{span:Math.abs(t),sign:t>0?-1:t<0?1:e?-1:1}}function xt(i,e){return Math.min(e[1]!=null?e[1]:1/0,Math.max(e[0]!=null?e[0]:-1/0,i))}function Bs(i){At(Vo),At(Fi)}var Gr=["x","y","radius","angle","single"],zo=["cartesian2d","polar","singleAxis"];function No(i){var e=i.get("coordinateSystem");return Tt(zo,e)>=0}function lt(i){return i+"Axis"}function Bo(i,e){var t=et(),r=[],a=et();i.eachComponent({mainType:"dataZoom",query:e},function(h){a.get(h.uid)||s(h)});var n;do n=!1,i.eachComponent("dataZoom",o);while(n);function o(h){!a.get(h.uid)&&l(h)&&(s(h),n=!0)}function s(h){a.set(h.uid,!0),r.push(h),u(h)}function l(h){var c=!1;return h.eachTargetAxis(function(f,v){var p=t.get(f);p&&p[v]&&(c=!0)}),c}function u(h){h.eachTargetAxis(function(c,f){(t.get(c)||t.set(c,[]))[f]=!0})}return r}function Wa(i){var e=i.ecModel,t={infoList:[],infoMap:et()};return i.eachTargetAxis(function(r,a){var n=e.getComponent(lt(r),a);if(n){var o=n.getCoordSysModel();if(o){var s=o.uid,l=t.infoMap.get(s);l||(l={model:o,axisModels:[]},t.infoList.push(l),t.infoMap.set(s,l)),l.axisModels.push(n)}}}),t}var me=function(){function i(){this.indexList=[],this.indexMap=[]}return i.prototype.add=function(e){this.indexMap[e]||(this.indexList.push(e),this.indexMap[e]=!0)},i}(),Jt=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t._autoThrottle=!0,t._noTarget=!0,t._rangePropMode=["percent","percent"],t}return e.prototype.init=function(t,r,a){var n=Hr(t);this.settledOption=n,this.mergeDefaultAndTheme(t,a),this._doInit(n)},e.prototype.mergeOption=function(t){var r=Hr(t);q(this.option,t,!0),q(this.settledOption,r,!0),this._doInit(r)},e.prototype._doInit=function(t){var r=this.option;this._setDefaultThrottle(t),this._updateRangeUse(t);var a=this.settledOption;A([["start","startValue"],["end","endValue"]],function(n,o){this._rangePropMode[o]==="value"&&(r[n[0]]=a[n[0]]=null)},this),this._resetTarget()},e.prototype._resetTarget=function(){var t=this.get("orient",!0),r=this._targetAxisInfoMap=et(),a=this._fillSpecifiedTargetAxis(r);a?this._orient=t||this._makeAutoOrientByTargetAxis():(this._orient=t||"horizontal",this._fillAutoTargetAxisByOrient(r,this._orient)),this._noTarget=!0,r.each(function(n){n.indexList.length&&(this._noTarget=!1)},this)},e.prototype._fillSpecifiedTargetAxis=function(t){var r=!1;return A(Gr,function(a){var n=this.getReferringComponents(lt(a),Zi);if(n.specified){r=!0;var o=new me;A(n.models,function(s){o.add(s.componentIndex)}),t.set(a,o)}},this),r},e.prototype._fillAutoTargetAxisByOrient=function(t,r){var a=this.ecModel,n=!0;if(n){var o=r==="vertical"?"y":"x",s=a.findComponents({mainType:o+"Axis"});l(s,o)}if(n){var s=a.findComponents({mainType:"singleAxis",filter:function(h){return h.get("orient",!0)===r}});l(s,"single")}function l(u,h){var c=u[0];if(c){var f=new me;if(f.add(c.componentIndex),t.set(h,f),n=!1,h==="x"||h==="y"){var v=c.getReferringComponents("grid",Q).models[0];v&&A(u,function(p){c.componentIndex!==p.componentIndex&&v===p.getReferringComponents("grid",Q).models[0]&&f.add(p.componentIndex)})}}}n&&A(Gr,function(u){if(n){var h=a.findComponents({mainType:lt(u),filter:function(f){return f.get("type",!0)==="category"}});if(h[0]){var c=new me;c.add(h[0].componentIndex),t.set(u,c),n=!1}}},this)},e.prototype._makeAutoOrientByTargetAxis=function(){var t;return this.eachTargetAxis(function(r){!t&&(t=r)},this),t==="y"?"vertical":"horizontal"},e.prototype._setDefaultThrottle=function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var r=this.ecModel.option;this.option.throttle=r.animation&&r.animationDurationUpdate>0?100:20}},e.prototype._updateRangeUse=function(t){var r=this._rangePropMode,a=this.get("rangeMode");A([["start","startValue"],["end","endValue"]],function(n,o){var s=t[n[0]]!=null,l=t[n[1]]!=null;s&&!l?r[o]="percent":!s&&l?r[o]="value":a?r[o]=a[o]:s&&(r[o]="percent")})},e.prototype.noTarget=function(){return this._noTarget},e.prototype.getFirstTargetAxisModel=function(){var t;return this.eachTargetAxis(function(r,a){t==null&&(t=this.ecModel.getComponent(lt(r),a))},this),t},e.prototype.eachTargetAxis=function(t,r){this._targetAxisInfoMap.each(function(a,n){A(a.indexList,function(o){t.call(r,n,o)})})},e.prototype.getAxisProxy=function(t,r){var a=this.getAxisModel(t,r);if(a)return a.__dzAxisProxy},e.prototype.getAxisModel=function(t,r){var a=this._targetAxisInfoMap.get(t);if(a&&a.indexMap[r])return this.ecModel.getComponent(lt(t),r)},e.prototype.setRawRange=function(t){var r=this.option,a=this.settledOption;A([["start","startValue"],["end","endValue"]],function(n){(t[n[0]]!=null||t[n[1]]!=null)&&(r[n[0]]=a[n[0]]=t[n[0]],r[n[1]]=a[n[1]]=t[n[1]])},this),this._updateRangeUse(t)},e.prototype.setCalculatedRange=function(t){var r=this.option;A(["start","startValue","end","endValue"],function(a){r[a]=t[a]})},e.prototype.getPercentRange=function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},e.prototype.getValueRange=function(t,r){if(t==null&&r==null){var a=this.findRepresentativeAxisProxy();if(a)return a.getDataValueWindow()}else return this.getAxisProxy(t,r).getDataValueWindow()},e.prototype.findRepresentativeAxisProxy=function(t){if(t)return t.__dzAxisProxy;for(var r,a=this._targetAxisInfoMap.keys(),n=0;n<a.length;n++)for(var o=a[n],s=this._targetAxisInfoMap.get(o),l=0;l<s.indexList.length;l++){var u=this.getAxisProxy(o,s.indexList[l]);if(u.hostedBy(this))return u;r||(r=u)}return r},e.prototype.getRangePropMode=function(){return this._rangePropMode.slice()},e.prototype.getOrient=function(){return this._orient},e.type="dataZoom",e.dependencies=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","series","toolbox"],e.defaultOption={z:4,filterMode:"filter",start:0,end:100},e}(ae);function Hr(i){var e={};return A(["start","end","startValue","endValue","throttle"],function(t){i.hasOwnProperty(t)&&(e[t]=i[t])}),e}var Xa=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,a,n){this.dataZoomModel=t,this.ecModel=r,this.api=a},e.type="dataZoom",e}(ze),St=A,Fr=ft,Go=function(){function i(e,t,r,a){this._dimName=e,this._axisIndex=t,this.ecModel=a,this._dataZoomModel=r}return i.prototype.hostedBy=function(e){return this._dataZoomModel===e},i.prototype.getDataValueWindow=function(){return this._valueWindow.slice()},i.prototype.getDataPercentWindow=function(){return this._percentWindow.slice()},i.prototype.getTargetSeriesModels=function(){var e=[];return this.ecModel.eachSeries(function(t){if(No(t)){var r=lt(this._dimName),a=t.getReferringComponents(r,Q).models[0];a&&this._axisIndex===a.componentIndex&&e.push(t)}},this),e},i.prototype.getAxisModel=function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},i.prototype.getMinMaxSpan=function(){return X(this._minMaxSpan)},i.prototype.calculateDataWindow=function(e){var t=this._dataExtent,r=this.getAxisModel(),a=r.axis.scale,n=this._dataZoomModel.getRangePropMode(),o=[0,100],s=[],l=[],u;St(["start","end"],function(f,v){var p=e[f],d=e[f+"Value"];n[v]==="percent"?(p==null&&(p=o[v]),d=a.parse(k(p,o,t))):(u=!0,d=d==null?t[v]:a.parse(d),p=k(d,t,o)),l[v]=d==null||isNaN(d)?t[v]:d,s[v]=p==null||isNaN(p)?o[v]:p}),Fr(l),Fr(s);var h=this._minMaxSpan;u?c(l,s,t,o,!1):c(s,l,o,t,!0);function c(f,v,p,d,g){var y=g?"Span":"ValueSpan";zt(0,f,p,"all",h["min"+y],h["max"+y]);for(var m=0;m<2;m++)v[m]=k(f[m],p,d,!0),g&&(v[m]=a.parse(v[m]))}return{valueWindow:l,percentWindow:s}},i.prototype.reset=function(e){if(e===this._dataZoomModel){var t=this.getTargetSeriesModels();this._dataExtent=Ho(this,this._dimName,t),this._updateMinMaxSpan();var r=this.calculateDataWindow(e.settledOption);this._valueWindow=r.valueWindow,this._percentWindow=r.percentWindow,this._setAxisModel()}},i.prototype.filterData=function(e,t){if(e!==this._dataZoomModel)return;var r=this._dimName,a=this.getTargetSeriesModels(),n=e.get("filterMode"),o=this._valueWindow;if(n==="none")return;St(a,function(l){var u=l.getData(),h=u.mapDimensionsAll(r);if(h.length){if(n==="weakFilter"){var c=u.getStore(),f=B(h,function(v){return u.getDimensionIndex(v)},u);u.filterSelf(function(v){for(var p,d,g,y=0;y<h.length;y++){var m=c.get(f[y],v),x=!isNaN(m),b=m<o[0],_=m>o[1];if(x&&!b&&!_)return!0;x&&(g=!0),b&&(p=!0),_&&(d=!0)}return g&&p&&d})}else St(h,function(v){if(n==="empty")l.setData(u=u.map(v,function(d){return s(d)?d:NaN}));else{var p={};p[v]=o,u.selectRange(p)}});St(h,function(v){u.setApproximateExtent(o,v)})}});function s(l){return l>=o[0]&&l<=o[1]}},i.prototype._updateMinMaxSpan=function(){var e=this._minMaxSpan={},t=this._dataZoomModel,r=this._dataExtent;St(["min","max"],function(a){var n=t.get(a+"Span"),o=t.get(a+"ValueSpan");o!=null&&(o=this.getAxisModel().axis.scale.parse(o)),o!=null?n=k(r[0]+o,r,[0,100],!0):n!=null&&(o=k(n,[0,100],r,!0)-r[0]),e[a+"Span"]=n,e[a+"ValueSpan"]=o},this)},i.prototype._setAxisModel=function(){var e=this.getAxisModel(),t=this._percentWindow,r=this._valueWindow;if(t){var a=ca(r,[0,500]);a=Math.min(a,20);var n=e.axis.scale.rawExtentInfo;t[0]!==0&&n.setDeterminedMinMax("min",+r[0].toFixed(a)),t[1]!==100&&n.setDeterminedMinMax("max",+r[1].toFixed(a)),n.freeze()}},i}();function Ho(i,e,t){var r=[1/0,-1/0];St(t,function(o){Wi(r,o.getData(),e)});var a=i.getAxisModel(),n=Xi(a.axis.scale,a,r).calculate();return[n.min,n.max]}var Fo={getTargetSeries:function(i){function e(a){i.eachComponent("dataZoom",function(n){n.eachTargetAxis(function(o,s){var l=i.getComponent(lt(o),s);a(o,s,l,n)})})}e(function(a,n,o,s){o.__dzAxisProxy=null});var t=[];e(function(a,n,o,s){o.__dzAxisProxy||(o.__dzAxisProxy=new Go(a,n,s,i),t.push(o.__dzAxisProxy))});var r=et();return A(t,function(a){A(a.getTargetSeriesModels(),function(n){r.set(n.uid,n)})}),r},overallReset:function(i,e){i.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(r,a){t.getAxisProxy(r,a).reset(t)}),t.eachTargetAxis(function(r,a){t.getAxisProxy(r,a).filterData(t,e)})}),i.eachComponent("dataZoom",function(t){var r=t.findRepresentativeAxisProxy();if(r){var a=r.getDataPercentWindow(),n=r.getDataValueWindow();t.setCalculatedRange({start:a[0],end:a[1],startValue:n[0],endValue:n[1]})}})}};function Zo(i){i.registerAction("dataZoom",function(e,t){var r=Bo(t,e);A(r,function(a){a.setRawRange({start:e.start,end:e.end,startValue:e.startValue,endValue:e.endValue})})})}var Zr=!1;function Ua(i){Zr||(Zr=!0,i.registerProcessor(i.PRIORITY.PROCESSOR.FILTER,Fo),Zo(i),i.registerSubTypeDefaulter("dataZoom",function(){return"slider"}))}var Wr=A;function Xr(i){if(i){for(var e in i)if(i.hasOwnProperty(e))return!0}}function Ur(i,e,t){var r={};return Wr(e,function(n){var o=r[n]=a();Wr(i[n],function(s,l){if(G.isValidType(l)){var u={type:l,visual:s};t&&t(u,n),o[l]=new G(u),l==="opacity"&&(u=X(u),u.type="colorAlpha",o.__hidden.__alphaForOpacity=new G(u))}})}),r;function a(){var n=function(){};n.prototype.__hidden=n.prototype;var o=new n;return o}}function Wo(i,e,t){var r;A(t,function(a){e.hasOwnProperty(a)&&Xr(e[a])&&(r=!0)}),r&&A(t,function(a){e.hasOwnProperty(a)&&Xr(e[a])?i[a]=X(e[a]):delete i[a]})}function Xo(i,e,t,r){var a={};return A(i,function(n){var o=G.prepareVisualTypes(e[n]);a[n]=o}),{progress:function(o,s){var l;r!=null&&(l=s.getDimensionIndex(r));function u(_){return Ui(s,c,_)}function h(_,S){Yi(s,c,_,S)}for(var c,f=s.getStore();(c=o.next())!=null;){var v=s.getRawDataItem(c);if(!(v&&v.visualMap===!1))for(var p=r!=null?f.get(l,c):c,d=t(p),g=e[d],y=a[d],m=0,x=y.length;m<x;m++){var b=y[m];g[b]&&g[b].applyVisual(p,u,h)}}}}}var Uo=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.inside",e.defaultOption=kt(Jt.defaultOption,{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),e}(Jt),We=Ve();function Yo(i,e,t){We(i).coordSysRecordMap.each(function(r){var a=r.dataZoomInfoMap.get(e.uid);a&&(a.getRange=t)})}function $o(i,e){for(var t=We(i).coordSysRecordMap,r=t.keys(),a=0;a<r.length;a++){var n=r[a],o=t.get(n),s=o.dataZoomInfoMap;if(s){var l=e.uid,u=s.get(l);u&&(s.removeKey(l),s.keys().length||Ya(t,o))}}}function Ya(i,e){if(e){i.removeKey(e.model.uid);var t=e.controller;t&&t.dispose()}}function Ko(i,e){var t={model:e,containsPoint:$t(jo,e),dispatchAction:$t(qo,i),dataZoomInfoMap:null,controller:null},r=t.controller=new Po(i.getZr());return A(["pan","zoom","scrollMove"],function(a){r.on(a,function(n){var o=[];t.dataZoomInfoMap.each(function(s){if(n.isAvailableBehavior(s.model.option)){var l=(s.getRange||{})[a],u=l&&l(s.dzReferCoordSysInfo,t.model.mainType,t.controller,n);!s.model.get("disabled",!0)&&u&&o.push({dataZoomId:s.model.id,start:u[0],end:u[1]})}}),o.length&&t.dispatchAction(o)})}),t}function qo(i,e){i.isDisposed()||i.dispatchAction({type:"dataZoom",animation:{easing:"cubicOut",duration:100},batch:e})}function jo(i,e,t,r){return i.coordinateSystem.containPoint([t,r])}function Jo(i){var e,t="type_",r={type_true:2,type_move:1,type_false:0,type_undefined:-1},a=!0;return i.each(function(n){var o=n.model,s=o.get("disabled",!0)?!1:o.get("zoomLock",!0)?"move":!0;r[t+s]>r[t+e]&&(e=s),a=a&&o.get("preventDefaultMouseMove",!0)}),{controlType:e,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!a}}}function Qo(i){i.registerProcessor(i.PRIORITY.PROCESSOR.FILTER,function(e,t){var r=We(t),a=r.coordSysRecordMap||(r.coordSysRecordMap=et());a.each(function(n){n.dataZoomInfoMap=null}),e.eachComponent({mainType:"dataZoom",subType:"inside"},function(n){var o=Wa(n);A(o.infoList,function(s){var l=s.model.uid,u=a.get(l)||a.set(l,Ko(t,s.model)),h=u.dataZoomInfoMap||(u.dataZoomInfoMap=et());h.set(n.uid,{dzReferCoordSysInfo:s,model:n,getRange:null})})}),a.each(function(n){var o=n.controller,s,l=n.dataZoomInfoMap;if(l){var u=l.keys()[0];u!=null&&(s=l.get(u))}if(!s){Ya(a,n);return}var h=Jo(l);o.enable(h.controlType,h.opt),o.setPointerChecker(n.containsPoint),ba(n,"dispatchAction",s.model.get("throttle",!0),"fixRate")})})}var ts=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type="dataZoom.inside",t}return e.prototype.render=function(t,r,a){if(i.prototype.render.apply(this,arguments),t.noTarget()){this._clear();return}this.range=t.getPercentRange(),Yo(a,t,{pan:P(ye.pan,this),zoom:P(ye.zoom,this),scrollMove:P(ye.scrollMove,this)})},e.prototype.dispose=function(){this._clear(),i.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){$o(this.api,this.dataZoomModel),this.range=null},e.type="dataZoom.inside",e}(Xa),ye={zoom:function(i,e,t,r){var a=this.range,n=a.slice(),o=i.axisModels[0];if(o){var s=xe[e](null,[r.originX,r.originY],o,t,i),l=(s.signal>0?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(n[1]-n[0])+n[0],u=Math.max(1/r.scale,0);n[0]=(n[0]-l)*u+l,n[1]=(n[1]-l)*u+l;var h=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();if(zt(0,n,[0,100],0,h.minSpan,h.maxSpan),this.range=n,a[0]!==n[0]||a[1]!==n[1])return n}},pan:Yr(function(i,e,t,r,a,n){var o=xe[r]([n.oldX,n.oldY],[n.newX,n.newY],e,a,t);return o.signal*(i[1]-i[0])*o.pixel/o.pixelLength}),scrollMove:Yr(function(i,e,t,r,a,n){var o=xe[r]([0,0],[n.scrollDelta,n.scrollDelta],e,a,t);return o.signal*(i[1]-i[0])*n.scrollDelta})};function Yr(i){return function(e,t,r,a){var n=this.range,o=n.slice(),s=e.axisModels[0];if(s){var l=i(o,s,e,t,r,a);if(zt(l,o,[0,100],"all"),this.range=o,n[0]!==o[0]||n[1]!==o[1])return o}}}var xe={grid:function(i,e,t,r,a){var n=t.axis,o={},s=a.model.coordinateSystem.getRect();return i=i||[0,0],n.dim==="x"?(o.pixel=e[0]-i[0],o.pixelLength=s.width,o.pixelStart=s.x,o.signal=n.inverse?1:-1):(o.pixel=e[1]-i[1],o.pixelLength=s.height,o.pixelStart=s.y,o.signal=n.inverse?-1:1),o},polar:function(i,e,t,r,a){var n=t.axis,o={},s=a.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return i=i?s.pointToCoord(i):[0,0],e=s.pointToCoord(e),t.mainType==="radiusAxis"?(o.pixel=e[0]-i[0],o.pixelLength=l[1]-l[0],o.pixelStart=l[0],o.signal=n.inverse?1:-1):(o.pixel=e[1]-i[1],o.pixelLength=u[1]-u[0],o.pixelStart=u[0],o.signal=n.inverse?-1:1),o},singleAxis:function(i,e,t,r,a){var n=t.axis,o=a.model.coordinateSystem.getRect(),s={};return i=i||[0,0],n.orient==="horizontal"?(s.pixel=e[0]-i[0],s.pixelLength=o.width,s.pixelStart=o.x,s.signal=n.inverse?1:-1):(s.pixel=e[1]-i[1],s.pixelLength=o.height,s.pixelStart=o.y,s.signal=n.inverse?-1:1),s}};function es(i){Ua(i),i.registerComponentModel(Uo),i.registerComponentView(ts),Qo(i)}var rs=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.slider",e.layoutMode="box",e.defaultOption=kt(Jt.defaultOption,{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,borderColor:"#d2dbee",borderRadius:3,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#d2dbee",width:.5},areaStyle:{color:"#d2dbee",opacity:.2}},selectedDataBackground:{lineStyle:{color:"#8fb0f7",width:.5},areaStyle:{color:"#8fb0f7",opacity:.2}},fillerColor:"rgba(135,175,274,0.2)",handleIcon:"path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",handleSize:"100%",handleStyle:{color:"#fff",borderColor:"#ACB8D1"},moveHandleSize:7,moveHandleIcon:"path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z",moveHandleStyle:{color:"#D2DBEE",opacity:.7},showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#6E7079"},brushSelect:!0,brushStyle:{color:"rgba(135,175,274,0.15)"},emphasis:{handleStyle:{borderColor:"#8FB0F7"},moveHandleStyle:{color:"#8FB0F7"}}}),e}(Jt),Dt=j,$r=7,as=1,be=30,is=7,Lt="horizontal",Kr="vertical",ns=5,os=["line","bar","candlestick","scatter"],ss={easing:"cubicOut",duration:100,delay:0},ls=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t._displayables={},t}return e.prototype.init=function(t,r){this.api=r,this._onBrush=P(this._onBrush,this),this._onBrushEnd=P(this._onBrushEnd,this)},e.prototype.render=function(t,r,a,n){if(i.prototype.render.apply(this,arguments),ba(this,"_dispatchZoomAction",t.get("throttle"),"fixRate"),this._orient=t.getOrient(),t.get("show")===!1){this.group.removeAll();return}if(t.noTarget()){this._clear(),this.group.removeAll();return}(!n||n.type!=="dataZoom"||n.from!==this.uid)&&this._buildView(),this._updateView()},e.prototype.dispose=function(){this._clear(),i.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){$i(this,"_dispatchZoomAction");var t=this.api.getZr();t.off("mousemove",this._onBrush),t.off("mouseup",this._onBrushEnd)},e.prototype._buildView=function(){var t=this.group;t.removeAll(),this._brushing=!1,this._displayables.brushRect=null,this._resetLocation(),this._resetInterval();var r=this._displayables.sliderGroup=new U;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(r),this._positionGroup()},e.prototype._resetLocation=function(){var t=this.dataZoomModel,r=this.api,a=t.get("brushSelect"),n=a?is:0,o=this._findCoordRect(),s={width:r.getWidth(),height:r.getHeight()},l=this._orient===Lt?{right:s.width-o.x-o.width,top:s.height-be-$r-n,width:o.width,height:be}:{right:$r,top:o.y,width:be,height:o.height},u=ma(t.option);A(["right","top","width","height"],function(c){u[c]==="ph"&&(u[c]=l[c])});var h=ke(u,s);this._location={x:h.x,y:h.y},this._size=[h.width,h.height],this._orient===Kr&&this._size.reverse()},e.prototype._positionGroup=function(){var t=this.group,r=this._location,a=this._orient,n=this.dataZoomModel.getFirstTargetAxisModel(),o=n&&n.get("inverse"),s=this._displayables.sliderGroup,l=(this._dataShadowInfo||{}).otherAxisInverse;s.attr(a===Lt&&!o?{scaleY:l?1:-1,scaleX:1}:a===Lt&&o?{scaleY:l?1:-1,scaleX:-1}:a===Kr&&!o?{scaleY:l?-1:1,scaleX:1,rotation:Math.PI/2}:{scaleY:l?-1:1,scaleX:-1,rotation:Math.PI/2});var u=t.getBoundingRect([s]);t.x=r.x-u.x,t.y=r.y-u.y,t.markRedraw()},e.prototype._getViewExtent=function(){return[0,this._size[0]]},e.prototype._renderBackground=function(){var t=this.dataZoomModel,r=this._size,a=this._displayables.sliderGroup,n=t.get("brushSelect");a.add(new Dt({silent:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:t.get("backgroundColor")},z2:-40}));var o=new Dt({shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:"transparent"},z2:0,onclick:P(this._onClickPanel,this)}),s=this.api.getZr();n?(o.on("mousedown",this._onBrushStart,this),o.cursor="crosshair",s.on("mousemove",this._onBrush),s.on("mouseup",this._onBrushEnd)):(s.off("mousemove",this._onBrush),s.off("mouseup",this._onBrushEnd)),a.add(o)},e.prototype._renderDataShadow=function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(this._displayables.dataShadowSegs=[],!t)return;var r=this._size,a=this._shadowSize||[],n=t.series,o=n.getRawData(),s=n.getShadowDim&&n.getShadowDim(),l=s&&o.getDimensionInfo(s)?n.getShadowDim():t.otherDim;if(l==null)return;var u=this._shadowPolygonPts,h=this._shadowPolylinePts;if(o!==this._shadowData||l!==this._shadowDim||r[0]!==a[0]||r[1]!==a[1]){var c=o.getDataExtent(l),f=(c[1]-c[0])*.3;c=[c[0]-f,c[1]+f];var v=[0,r[1]],p=[0,r[0]],d=[[r[0],0],[0,0]],g=[],y=p[1]/(o.count()-1),m=0,x=Math.round(o.count()/r[0]),b;o.each([l],function(L,T){if(x>0&&T%x){m+=y;return}var w=L==null||isNaN(L)||L==="",I=w?0:k(L,c,v,!0);w&&!b&&T?(d.push([d[d.length-1][0],0]),g.push([g[g.length-1][0],0])):!w&&b&&(d.push([m,0]),g.push([m,0])),d.push([m,I]),g.push([m,I]),m+=y,b=w}),u=this._shadowPolygonPts=d,h=this._shadowPolylinePts=g}this._shadowData=o,this._shadowDim=l,this._shadowSize=[r[0],r[1]];var _=this.dataZoomModel;function S(L){var T=_.getModel(L?"selectedDataBackground":"dataBackground"),w=new U,I=new _a({shape:{points:u},segmentIgnoreThreshold:1,style:T.getModel("areaStyle").getAreaStyle(),silent:!0,z2:-20}),C=new Ji({shape:{points:h},segmentIgnoreThreshold:1,style:T.getModel("lineStyle").getLineStyle(),silent:!0,z2:-19});return w.add(I),w.add(C),w}for(var M=0;M<3;M++){var D=S(M===1);this._displayables.sliderGroup.add(D),this._displayables.dataShadowSegs.push(D)}},e.prototype._prepareDataShadowInfo=function(){var t=this.dataZoomModel,r=t.get("showDataShadow");if(r!==!1){var a,n=this.ecModel;return t.eachTargetAxis(function(o,s){var l=t.getAxisProxy(o,s).getTargetSeriesModels();A(l,function(u){if(!a&&!(r!==!0&&Tt(os,u.get("type"))<0)){var h=n.getComponent(lt(o),s).axis,c=us(o),f,v=u.coordinateSystem;c!=null&&v.getOtherAxis&&(f=v.getOtherAxis(h).inverse),c=u.getData().mapDimension(c),a={thisAxis:h,series:u,thisDim:o,otherDim:c,otherAxisInverse:f}}},this)},this),a}},e.prototype._renderHandle=function(){var t=this.group,r=this._displayables,a=r.handles=[null,null],n=r.handleLabels=[null,null],o=this._displayables.sliderGroup,s=this._size,l=this.dataZoomModel,u=this.api,h=l.get("borderRadius")||0,c=l.get("brushSelect"),f=r.filler=new Dt({silent:c,style:{fill:l.get("fillerColor")},textConfig:{position:"inside"}});o.add(f),o.add(new Dt({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:s[0],height:s[1],r:h},style:{stroke:l.get("dataBackgroundColor")||l.get("borderColor"),lineWidth:as,fill:"rgba(0,0,0,0)"}})),A([0,1],function(x){var b=l.get("handleIcon");!Ki[b]&&b.indexOf("path://")<0&&b.indexOf("image://")<0&&(b="path://"+b);var _=ht(b,-1,0,2,2,null,!0);_.attr({cursor:qr(this._orient),draggable:!0,drift:P(this._onDragMove,this,x),ondragend:P(this._onDragEnd,this),onmouseover:P(this._showDataInfo,this,!0),onmouseout:P(this._showDataInfo,this,!1),z2:5});var S=_.getBoundingRect(),M=l.get("handleSize");this._handleHeight=ur(M,this._size[1]),this._handleWidth=S.width/S.height*this._handleHeight,_.setStyle(l.getModel("handleStyle").getItemStyle()),_.style.strokeNoScale=!0,_.rectHover=!0,_.ensureState("emphasis").style=l.getModel(["emphasis","handleStyle"]).getItemStyle(),qi(_);var D=l.get("handleColor");D!=null&&(_.style.fill=D),o.add(a[x]=_);var L=l.getModel("textStyle");t.add(n[x]=new pt({silent:!0,invisible:!0,style:Vt(L,{x:0,y:0,text:"",verticalAlign:"middle",align:"center",fill:L.getTextColor(),font:L.getFont()}),z2:10}))},this);var v=f;if(c){var p=ur(l.get("moveHandleSize"),s[1]),d=r.moveHandle=new j({style:l.getModel("moveHandleStyle").getItemStyle(),silent:!0,shape:{r:[0,0,2,2],y:s[1]-.5,height:p}}),g=p*.8,y=r.moveHandleIcon=ht(l.get("moveHandleIcon"),-g/2,-g/2,g,g,"#fff",!0);y.silent=!0,y.y=s[1]+p/2-.5,d.ensureState("emphasis").style=l.getModel(["emphasis","moveHandleStyle"]).getItemStyle();var m=Math.min(s[1]/2,Math.max(p,10));v=r.moveZone=new j({invisible:!0,shape:{y:s[1]-m,height:p+m}}),v.on("mouseover",function(){u.enterEmphasis(d)}).on("mouseout",function(){u.leaveEmphasis(d)}),o.add(d),o.add(y),o.add(v)}v.attr({draggable:!0,cursor:qr(this._orient),drift:P(this._onDragMove,this,"all"),ondragstart:P(this._showDataInfo,this,!0),ondragend:P(this._onDragEnd,this),onmouseover:P(this._showDataInfo,this,!0),onmouseout:P(this._showDataInfo,this,!1)})},e.prototype._resetInterval=function(){var t=this._range=this.dataZoomModel.getPercentRange(),r=this._getViewExtent();this._handleEnds=[k(t[0],[0,100],r,!0),k(t[1],[0,100],r,!0)]},e.prototype._updateInterval=function(t,r){var a=this.dataZoomModel,n=this._handleEnds,o=this._getViewExtent(),s=a.findRepresentativeAxisProxy().getMinMaxSpan(),l=[0,100];zt(r,n,o,a.get("zoomLock")?"all":t,s.minSpan!=null?k(s.minSpan,l,o,!0):null,s.maxSpan!=null?k(s.maxSpan,l,o,!0):null);var u=this._range,h=this._range=ft([k(n[0],o,l,!0),k(n[1],o,l,!0)]);return!u||u[0]!==h[0]||u[1]!==h[1]},e.prototype._updateView=function(t){var r=this._displayables,a=this._handleEnds,n=ft(a.slice()),o=this._size;A([0,1],function(v){var p=r.handles[v],d=this._handleHeight;p.attr({scaleX:d/2,scaleY:d/2,x:a[v]+(v?-1:1),y:o[1]/2-d/2})},this),r.filler.setShape({x:n[0],y:0,width:n[1]-n[0],height:o[1]});var s={x:n[0],width:n[1]-n[0]};r.moveHandle&&(r.moveHandle.setShape(s),r.moveZone.setShape(s),r.moveZone.getBoundingRect(),r.moveHandleIcon&&r.moveHandleIcon.attr("x",s.x+s.width/2));for(var l=r.dataShadowSegs,u=[0,n[0],n[1],o[0]],h=0;h<l.length;h++){var c=l[h],f=c.getClipPath();f||(f=new j,c.setClipPath(f)),f.setShape({x:u[h],y:0,width:u[h+1]-u[h],height:o[1]})}this._updateDataInfo(t)},e.prototype._updateDataInfo=function(t){var r=this.dataZoomModel,a=this._displayables,n=a.handleLabels,o=this._orient,s=["",""];if(r.get("showDetail")){var l=r.findRepresentativeAxisProxy();if(l){var u=l.getAxisModel().axis,h=this._range,c=t?l.calculateDataWindow({start:h[0],end:h[1]}).valueWindow:l.getDataValueWindow();s=[this._formatLabel(c[0],u),this._formatLabel(c[1],u)]}}var f=ft(this._handleEnds.slice());v.call(this,0),v.call(this,1);function v(p){var d=Xt(a.handles[p].parent,this.group),g=Sa(p===0?"right":"left",d),y=this._handleWidth/2+ns,m=Pt([f[p]+(p===0?-y:y),this._size[1]/2],d);n[p].setStyle({x:m[0],y:m[1],verticalAlign:o===Lt?"middle":g,align:o===Lt?g:"center",text:s[p]})}},e.prototype._formatLabel=function(t,r){var a=this.dataZoomModel,n=a.get("labelFormatter"),o=a.get("labelPrecision");(o==null||o==="auto")&&(o=r.getPixelPrecision());var s=t==null||isNaN(t)?"":r.type==="category"||r.type==="time"?r.scale.getLabel({value:Math.round(t)}):t.toFixed(Math.min(o,20));return tt(n)?n(t,s):Et(n)?n.replace("{value}",s):s},e.prototype._showDataInfo=function(t){t=this._dragging||t;var r=this._displayables,a=r.handleLabels;a[0].attr("invisible",!t),a[1].attr("invisible",!t),r.moveHandle&&this.api[t?"enterEmphasis":"leaveEmphasis"](r.moveHandle,1)},e.prototype._onDragMove=function(t,r,a,n){this._dragging=!0,mt(n.event);var o=this._displayables.sliderGroup.getLocalTransform(),s=Pt([r,a],o,!0),l=this._updateInterval(t,s[0]),u=this.dataZoomModel.get("realtime");this._updateView(!u),l&&u&&this._dispatchZoomAction(!0)},e.prototype._onDragEnd=function(){this._dragging=!1,this._showDataInfo(!1);var t=this.dataZoomModel.get("realtime");!t&&this._dispatchZoomAction(!1)},e.prototype._onClickPanel=function(t){var r=this._size,a=this._displayables.sliderGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(a[0]<0||a[0]>r[0]||a[1]<0||a[1]>r[1])){var n=this._handleEnds,o=(n[0]+n[1])/2,s=this._updateInterval("all",a[0]-o);this._updateView(),s&&this._dispatchZoomAction(!1)}},e.prototype._onBrushStart=function(t){var r=t.offsetX,a=t.offsetY;this._brushStart=new ji(r,a),this._brushing=!0,this._brushStartTime=+new Date},e.prototype._onBrushEnd=function(t){if(this._brushing){var r=this._displayables.brushRect;if(this._brushing=!1,!!r){r.attr("ignore",!0);var a=r.shape,n=+new Date;if(!(n-this._brushStartTime<200&&Math.abs(a.width)<5)){var o=this._getViewExtent(),s=[0,100];this._range=ft([k(a.x,o,s,!0),k(a.x+a.width,o,s,!0)]),this._handleEnds=[a.x,a.x+a.width],this._updateView(),this._dispatchZoomAction(!1)}}}},e.prototype._onBrush=function(t){this._brushing&&(mt(t.event),this._updateBrushRect(t.offsetX,t.offsetY))},e.prototype._updateBrushRect=function(t,r){var a=this._displayables,n=this.dataZoomModel,o=a.brushRect;o||(o=a.brushRect=new Dt({silent:!0,style:n.getModel("brushStyle").getItemStyle()}),a.sliderGroup.add(o)),o.attr("ignore",!1);var s=this._brushStart,l=this._displayables.sliderGroup,u=l.transformCoordToLocal(t,r),h=l.transformCoordToLocal(s.x,s.y),c=this._size;u[0]=Math.max(Math.min(c[0],u[0]),0),o.setShape({x:h[0],y:0,width:u[0]-h[0],height:c[1]})},e.prototype._dispatchZoomAction=function(t){var r=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,animation:t?ss:null,start:r[0],end:r[1]})},e.prototype._findCoordRect=function(){var t,r=Wa(this.dataZoomModel).infoList;if(!t&&r.length){var a=r[0].model.coordinateSystem;t=a.getRect&&a.getRect()}if(!t){var n=this.api.getWidth(),o=this.api.getHeight();t={x:n*.2,y:o*.2,width:n*.6,height:o*.6}}return t},e.type="dataZoom.slider",e}(Xa);function us(i){var e={x:"y",y:"x",radius:"angle",angle:"radius"};return e[i]}function qr(i){return i==="vertical"?"ns-resize":"ew-resize"}function hs(i){i.registerComponentModel(rs),i.registerComponentView(ls),Ua(i)}function Gs(i){At(es),At(hs)}var $a={get:function(i,e,t){var r=X((cs[i]||{})[e]);return t&&F(r)?r[r.length-1]:r}},cs={color:{active:["#006edd","#e0ffff"],inactive:["rgba(0,0,0,0)"]},colorHue:{active:[0,360],inactive:[0,0]},colorSaturation:{active:[.3,1],inactive:[0,0]},colorLightness:{active:[.9,.5],inactive:[0,0]},colorAlpha:{active:[.3,1],inactive:[0,0]},opacity:{active:[.3,1],inactive:[0,0]},symbol:{active:["circle","roundRect","diamond"],inactive:["none"]},symbolSize:{active:[10,50],inactive:[0,0]}},jr=G.mapVisual,vs=G.eachVisual,fs=F,Jr=A,ps=ft,ds=k,Qt=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t.stateList=["inRange","outOfRange"],t.replacableOptionKeys=["inRange","outOfRange","target","controller","color"],t.layoutMode={type:"box",ignoreSize:!0},t.dataBound=[-1/0,1/0],t.targetVisuals={},t.controllerVisuals={},t}return e.prototype.init=function(t,r,a){this.mergeDefaultAndTheme(t,a)},e.prototype.optionUpdated=function(t,r){var a=this.option;!r&&Wo(a,t,this.replacableOptionKeys),this.textStyleModel=this.getModel("textStyle"),this.resetItemSize(),this.completeVisualOption()},e.prototype.resetVisual=function(t){var r=this.stateList;t=P(t,this),this.controllerVisuals=Ur(this.option.controller,r,t),this.targetVisuals=Ur(this.option.target,r,t)},e.prototype.getItemSymbol=function(){return null},e.prototype.getTargetSeriesIndices=function(){var t=this.option.seriesIndex,r=[];return t==null||t==="all"?this.ecModel.eachSeries(function(a,n){r.push(n)}):r=Qi(t),r},e.prototype.eachTargetSeries=function(t,r){A(this.getTargetSeriesIndices(),function(a){var n=this.ecModel.getSeriesByIndex(a);n&&t.call(r,n)},this)},e.prototype.isTargetSeries=function(t){var r=!1;return this.eachTargetSeries(function(a){a===t&&(r=!0)}),r},e.prototype.formatValueText=function(t,r,a){var n=this.option,o=n.precision,s=this.dataBound,l=n.formatter,u;a=a||["<",">"],F(t)&&(t=t.slice(),u=!0);var h=r?t:u?[c(t[0]),c(t[1])]:c(t);if(Et(l))return l.replace("{value}",u?h[0]:h).replace("{value2}",u?h[1]:h);if(tt(l))return u?l(t[0],t[1]):l(t);if(u)return t[0]===s[0]?a[0]+" "+h[1]:t[1]===s[1]?a[1]+" "+h[0]:h[0]+" - "+h[1];return h;function c(f){return f===s[0]?"min":f===s[1]?"max":(+f).toFixed(Math.min(o,20))}},e.prototype.resetExtent=function(){var t=this.option,r=ps([t.min,t.max]);this._dataExtent=r},e.prototype.getDataDimensionIndex=function(t){var r=this.option.dimension;if(r!=null)return t.getDimensionIndex(r);for(var a=t.dimensions,n=a.length-1;n>=0;n--){var o=a[n],s=t.getDimensionInfo(o);if(!s.isCalculationCoord)return s.storeDimIndex}},e.prototype.getExtent=function(){return this._dataExtent.slice()},e.prototype.completeVisualOption=function(){var t=this.ecModel,r=this.option,a={inRange:r.inRange,outOfRange:r.outOfRange},n=r.target||(r.target={}),o=r.controller||(r.controller={});q(n,a),q(o,a);var s=this.isCategory();l.call(this,n),l.call(this,o),u.call(this,n,"inRange","outOfRange"),h.call(this,o);function l(c){fs(r.color)&&!c.inRange&&(c.inRange={color:r.color.slice().reverse()}),c.inRange=c.inRange||{color:t.get("gradientColor")}}function u(c,f,v){var p=c[f],d=c[v];p&&!d&&(d=c[v]={},Jr(p,function(g,y){if(G.isValidType(y)){var m=$a.get(y,"inactive",s);m!=null&&(d[y]=m,y==="color"&&!d.hasOwnProperty("opacity")&&!d.hasOwnProperty("colorAlpha")&&(d.opacity=[0,0]))}}))}function h(c){var f=(c.inRange||{}).symbol||(c.outOfRange||{}).symbol,v=(c.inRange||{}).symbolSize||(c.outOfRange||{}).symbolSize,p=this.get("inactiveColor"),d=this.getItemSymbol(),g=d||"roundRect";Jr(this.stateList,function(y){var m=this.itemSize,x=c[y];x||(x=c[y]={color:s?p:[p]}),x.symbol==null&&(x.symbol=f&&X(f)||(s?g:[g])),x.symbolSize==null&&(x.symbolSize=v&&X(v)||(s?m[0]:[m[0],m[0]])),x.symbol=jr(x.symbol,function(S){return S==="none"?g:S});var b=x.symbolSize;if(b!=null){var _=-1/0;vs(b,function(S){S>_&&(_=S)}),x.symbolSize=jr(b,function(S){return ds(S,[0,_],[0,m[0]],!0)})}},this)}},e.prototype.resetItemSize=function(){this.itemSize=[parseFloat(this.get("itemWidth")),parseFloat(this.get("itemHeight"))]},e.prototype.isCategory=function(){return!!this.option.categories},e.prototype.setSelected=function(t){},e.prototype.getSelected=function(){return null},e.prototype.getValueState=function(t){return null},e.prototype.getVisualMeta=function(t){return null},e.type="visualMap",e.dependencies=["series"],e.defaultOption={show:!0,z:4,seriesIndex:"all",min:0,max:200,left:0,right:null,top:null,bottom:0,itemWidth:null,itemHeight:null,inverse:!1,orient:"vertical",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",contentColor:"#5793f3",inactiveColor:"#aaa",borderWidth:0,padding:5,textGap:10,precision:0,textStyle:{color:"#333"}},e}(ae),Qr=[20,140],gs=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(t,r){i.prototype.optionUpdated.apply(this,arguments),this.resetExtent(),this.resetVisual(function(a){a.mappingMethod="linear",a.dataExtent=this.getExtent()}),this._resetRange()},e.prototype.resetItemSize=function(){i.prototype.resetItemSize.apply(this,arguments);var t=this.itemSize;(t[0]==null||isNaN(t[0]))&&(t[0]=Qr[0]),(t[1]==null||isNaN(t[1]))&&(t[1]=Qr[1])},e.prototype._resetRange=function(){var t=this.getExtent(),r=this.option.range;!r||r.auto?(t.auto=1,this.option.range=t):F(r)&&(r[0]>r[1]&&r.reverse(),r[0]=Math.max(r[0],t[0]),r[1]=Math.min(r[1],t[1]))},e.prototype.completeVisualOption=function(){i.prototype.completeVisualOption.apply(this,arguments),A(this.stateList,function(t){var r=this.option.controller[t].symbolSize;r&&r[0]!==r[1]&&(r[0]=r[1]/3)},this)},e.prototype.setSelected=function(t){this.option.range=t.slice(),this._resetRange()},e.prototype.getSelected=function(){var t=this.getExtent(),r=ft((this.get("range")||[]).slice());return r[0]>t[1]&&(r[0]=t[1]),r[1]>t[1]&&(r[1]=t[1]),r[0]<t[0]&&(r[0]=t[0]),r[1]<t[0]&&(r[1]=t[0]),r},e.prototype.getValueState=function(t){var r=this.option.range,a=this.getExtent();return(r[0]<=a[0]||r[0]<=t)&&(r[1]>=a[1]||t<=r[1])?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[];return this.eachTargetSeries(function(a){var n=[],o=a.getData();o.each(this.getDataDimensionIndex(o),function(s,l){t[0]<=s&&s<=t[1]&&n.push(l)},this),r.push({seriesId:a.id,dataIndex:n})},this),r},e.prototype.getVisualMeta=function(t){var r=ta(this,"outOfRange",this.getExtent()),a=ta(this,"inRange",this.option.range.slice()),n=[];function o(v,p){n.push({value:v,color:t(v,p)})}for(var s=0,l=0,u=a.length,h=r.length;l<h&&(!a.length||r[l]<=a[0]);l++)r[l]<a[s]&&o(r[l],"outOfRange");for(var c=1;s<u;s++,c=0)c&&n.length&&o(a[s],"outOfRange"),o(a[s],"inRange");for(var c=1;l<h;l++)(!a.length||a[a.length-1]<r[l])&&(c&&(n.length&&o(n[n.length-1].value,"outOfRange"),c=0),o(r[l],"outOfRange"));var f=n.length;return{stops:n,outerColors:[f?n[0].color:"transparent",f?n[f-1].color:"transparent"]}},e.type="visualMap.continuous",e.defaultOption=kt(Qt.defaultOption,{align:"auto",calculable:!1,hoverLink:!0,realtime:!0,handleIcon:"path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z",handleSize:"120%",handleStyle:{borderColor:"#fff",borderWidth:1},indicatorIcon:"circle",indicatorSize:"50%",indicatorStyle:{borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}}),e}(Qt);function ta(i,e,t){if(t[0]===t[1])return t.slice();for(var r=200,a=(t[1]-t[0])/r,n=t[0],o=[],s=0;s<=r&&n<t[1];s++)o.push(n),n+=a;return o.push(t[1]),o}var Ka=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t.autoPositionValues={left:1,right:1,top:1,bottom:1},t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r},e.prototype.render=function(t,r,a,n){if(this.visualMapModel=t,t.get("show")===!1){this.group.removeAll();return}this.doRender(t,r,a,n)},e.prototype.renderBackground=function(t){var r=this.visualMapModel,a=tn(r.get("padding")||0),n=t.getBoundingRect();t.add(new j({z2:-1,silent:!0,shape:{x:n.x-a[3],y:n.y-a[0],width:n.width+a[3]+a[1],height:n.height+a[0]+a[2]},style:{fill:r.get("backgroundColor"),stroke:r.get("borderColor"),lineWidth:r.get("borderWidth")}}))},e.prototype.getControllerVisual=function(t,r,a){a=a||{};var n=a.forceState,o=this.visualMapModel,s={};if(r==="color"){var l=o.get("contentColor");s.color=l}function u(v){return s[v]}function h(v,p){s[v]=p}var c=o.controllerVisuals[n||o.getValueState(t)],f=G.prepareVisualTypes(c);return A(f,function(v){var p=c[v];a.convertOpacityToAlpha&&v==="opacity"&&(v="colorAlpha",p=c.__alphaForOpacity),G.dependsOn(v,r)&&p&&p.applyVisual(t,u,h)}),s[r]},e.prototype.positionGroup=function(t){var r=this.visualMapModel,a=this.api;en(t,r.getBoxLayoutParams(),{width:a.getWidth(),height:a.getHeight()})},e.prototype.doRender=function(t,r,a,n){},e.type="visualMap",e}(ze),ea=[["left","right","width"],["top","bottom","height"]];function qa(i,e,t){var r=i.option,a=r.align;if(a!=null&&a!=="auto")return a;for(var n={width:e.getWidth(),height:e.getHeight()},o=r.orient==="horizontal"?1:0,s=ea[o],l=[0,null,10],u={},h=0;h<3;h++)u[ea[1-o][h]]=l[h],u[s[h]]=h===2?t[0]:r[s[h]];var c=[["x","width",3],["y","height",0]][o],f=ke(u,n,r.padding);return s[(f.margin[c[2]]||0)+f[c[0]]+f[c[1]]*.5<n[c[1]]*.5?0:1]}function Yt(i,e){return A(i||[],function(t){t.dataIndex!=null&&(t.dataIndexInside=t.dataIndex,t.dataIndex=null),t.highlightKey="visualMap"+(e?e.componentIndex:"")}),i}var J=k,ms=A,ra=Math.min,Se=Math.max,ys=12,xs=6,bs=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t._shapes={},t._dataInterval=[],t._handleEnds=[],t._hoverLinkDataIndices=[],t}return e.prototype.init=function(t,r){i.prototype.init.call(this,t,r),this._hoverLinkFromSeriesMouseOver=P(this._hoverLinkFromSeriesMouseOver,this),this._hideIndicator=P(this._hideIndicator,this)},e.prototype.doRender=function(t,r,a,n){(!n||n.type!=="selectDataRange"||n.from!==this.uid)&&this._buildView()},e.prototype._buildView=function(){this.group.removeAll();var t=this.visualMapModel,r=this.group;this._orient=t.get("orient"),this._useHandle=t.get("calculable"),this._resetInterval(),this._renderBar(r);var a=t.get("text");this._renderEndsText(r,a,0),this._renderEndsText(r,a,1),this._updateView(!0),this.renderBackground(r),this._updateView(),this._enableHoverLinkToSeries(),this._enableHoverLinkFromSeries(),this.positionGroup(r)},e.prototype._renderEndsText=function(t,r,a){if(r){var n=r[1-a];n=n!=null?n+"":"";var o=this.visualMapModel,s=o.get("textGap"),l=o.itemSize,u=this._shapes.mainGroup,h=this._applyTransform([l[0]/2,a===0?-s:l[1]+s],u),c=this._applyTransform(a===0?"bottom":"top",u),f=this._orient,v=this.visualMapModel.textStyleModel;this.group.add(new pt({style:Vt(v,{x:h[0],y:h[1],verticalAlign:f==="horizontal"?"middle":c,align:f==="horizontal"?c:"center",text:n})}))}},e.prototype._renderBar=function(t){var r=this.visualMapModel,a=this._shapes,n=r.itemSize,o=this._orient,s=this._useHandle,l=qa(r,this.api,n),u=a.mainGroup=this._createBarGroup(l),h=new U;u.add(h),h.add(a.outOfRange=aa()),h.add(a.inRange=aa(null,s?na(this._orient):null,P(this._dragHandle,this,"all",!1),P(this._dragHandle,this,"all",!0))),h.setClipPath(new j({shape:{x:0,y:0,width:n[0],height:n[1],r:3}}));var c=r.textStyleModel.getTextRect("国"),f=Se(c.width,c.height);s&&(a.handleThumbs=[],a.handleLabels=[],a.handleLabelPoints=[],this._createHandle(r,u,0,n,f,o),this._createHandle(r,u,1,n,f,o)),this._createIndicator(r,u,n,f,o),t.add(u)},e.prototype._createHandle=function(t,r,a,n,o,s){var l=P(this._dragHandle,this,a,!1),u=P(this._dragHandle,this,a,!0),h=hr(t.get("handleSize"),n[0]),c=ht(t.get("handleIcon"),-h/2,-h/2,h,h,null,!0),f=na(this._orient);c.attr({cursor:f,draggable:!0,drift:l,ondragend:u,onmousemove:function(y){mt(y.event)}}),c.x=n[0]/2,c.useStyle(t.getModel("handleStyle").getItemStyle()),c.setStyle({strokeNoScale:!0,strokeFirst:!0}),c.style.lineWidth*=2,c.ensureState("emphasis").style=t.getModel(["emphasis","handleStyle"]).getItemStyle(),rn(c,!0),r.add(c);var v=this.visualMapModel.textStyleModel,p=new pt({cursor:f,draggable:!0,drift:l,onmousemove:function(y){mt(y.event)},ondragend:u,style:Vt(v,{x:0,y:0,text:""})});p.ensureState("blur").style={opacity:.1},p.stateTransition={duration:200},this.group.add(p);var d=[h,0],g=this._shapes;g.handleThumbs[a]=c,g.handleLabelPoints[a]=d,g.handleLabels[a]=p},e.prototype._createIndicator=function(t,r,a,n,o){var s=hr(t.get("indicatorSize"),a[0]),l=ht(t.get("indicatorIcon"),-s/2,-s/2,s,s,null,!0);l.attr({cursor:"move",invisible:!0,silent:!0,x:a[0]/2});var u=t.getModel("indicatorStyle").getItemStyle();if(l instanceof fa){var h=l.style;l.useStyle(Mt({image:h.image,x:h.x,y:h.y,width:h.width,height:h.height},u))}else l.useStyle(u);r.add(l);var c=this.visualMapModel.textStyleModel,f=new pt({silent:!0,invisible:!0,style:Vt(c,{x:0,y:0,text:""})});this.group.add(f);var v=[(o==="horizontal"?n/2:xs)+a[0]/2,0],p=this._shapes;p.indicator=l,p.indicatorLabel=f,p.indicatorLabelPoint=v,this._firstShowIndicator=!0},e.prototype._dragHandle=function(t,r,a,n){if(this._useHandle){if(this._dragging=!r,!r){var o=this._applyTransform([a,n],this._shapes.mainGroup,!0);this._updateInterval(t,o[1]),this._hideIndicator(),this._updateView()}r===!this.visualMapModel.get("realtime")&&this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:this._dataInterval.slice()}),r?!this._hovering&&this._clearHoverLinkToSeries():ia(this.visualMapModel)&&this._doHoverLinkToSeries(this._handleEnds[t],!1)}},e.prototype._resetInterval=function(){var t=this.visualMapModel,r=this._dataInterval=t.getSelected(),a=t.getExtent(),n=[0,t.itemSize[1]];this._handleEnds=[J(r[0],a,n,!0),J(r[1],a,n,!0)]},e.prototype._updateInterval=function(t,r){r=r||0;var a=this.visualMapModel,n=this._handleEnds,o=[0,a.itemSize[1]];zt(r,n,o,t,0);var s=a.getExtent();this._dataInterval=[J(n[0],o,s,!0),J(n[1],o,s,!0)]},e.prototype._updateView=function(t){var r=this.visualMapModel,a=r.getExtent(),n=this._shapes,o=[0,r.itemSize[1]],s=t?o:this._handleEnds,l=this._createBarVisual(this._dataInterval,a,s,"inRange"),u=this._createBarVisual(a,a,o,"outOfRange");n.inRange.setStyle({fill:l.barColor}).setShape("points",l.barPoints),n.outOfRange.setStyle({fill:u.barColor}).setShape("points",u.barPoints),this._updateHandle(s,l)},e.prototype._createBarVisual=function(t,r,a,n){var o={forceState:n,convertOpacityToAlpha:!0},s=this._makeColorGradient(t,o),l=[this.getControllerVisual(t[0],"symbolSize",o),this.getControllerVisual(t[1],"symbolSize",o)],u=this._createBarPoints(a,l);return{barColor:new ga(0,0,0,1,s),barPoints:u,handlesColor:[s[0].color,s[s.length-1].color]}},e.prototype._makeColorGradient=function(t,r){var a=100,n=[],o=(t[1]-t[0])/a;n.push({color:this.getControllerVisual(t[0],"color",r),offset:0});for(var s=1;s<a;s++){var l=t[0]+o*s;if(l>t[1])break;n.push({color:this.getControllerVisual(l,"color",r),offset:s/a})}return n.push({color:this.getControllerVisual(t[1],"color",r),offset:1}),n},e.prototype._createBarPoints=function(t,r){var a=this.visualMapModel.itemSize;return[[a[0]-r[0],t[0]],[a[0],t[0]],[a[0],t[1]],[a[0]-r[1],t[1]]]},e.prototype._createBarGroup=function(t){var r=this._orient,a=this.visualMapModel.get("inverse");return new U(r==="horizontal"&&!a?{scaleX:t==="bottom"?1:-1,rotation:Math.PI/2}:r==="horizontal"&&a?{scaleX:t==="bottom"?-1:1,rotation:-Math.PI/2}:r==="vertical"&&!a?{scaleX:t==="left"?1:-1,scaleY:-1}:{scaleX:t==="left"?1:-1})},e.prototype._updateHandle=function(t,r){if(this._useHandle){var a=this._shapes,n=this.visualMapModel,o=a.handleThumbs,s=a.handleLabels,l=n.itemSize,u=n.getExtent();ms([0,1],function(h){var c=o[h];c.setStyle("fill",r.handlesColor[h]),c.y=t[h];var f=J(t[h],[0,l[1]],u,!0),v=this.getControllerVisual(f,"symbolSize");c.scaleX=c.scaleY=v/l[0],c.x=l[0]-v/2;var p=Pt(a.handleLabelPoints[h],Xt(c,this.group));s[h].setStyle({x:p[0],y:p[1],text:n.formatValueText(this._dataInterval[h]),verticalAlign:"middle",align:this._orient==="vertical"?this._applyTransform("left",a.mainGroup):"center"})},this)}},e.prototype._showIndicator=function(t,r,a,n){var o=this.visualMapModel,s=o.getExtent(),l=o.itemSize,u=[0,l[1]],h=this._shapes,c=h.indicator;if(c){c.attr("invisible",!1);var f={convertOpacityToAlpha:!0},v=this.getControllerVisual(t,"color",f),p=this.getControllerVisual(t,"symbolSize"),d=J(t,s,u,!0),g=l[0]-p/2,y={x:c.x,y:c.y};c.y=d,c.x=g;var m=Pt(h.indicatorLabelPoint,Xt(c,this.group)),x=h.indicatorLabel;x.attr("invisible",!1);var b=this._applyTransform("left",h.mainGroup),_=this._orient,S=_==="horizontal";x.setStyle({text:(a||"")+o.formatValueText(r),verticalAlign:S?b:"middle",align:S?"center":b});var M={x:g,y:d,style:{fill:v}},D={style:{x:m[0],y:m[1]}};if(o.ecModel.isAnimationEnabled()&&!this._firstShowIndicator){var L={duration:100,easing:"cubicInOut",additive:!0};c.x=y.x,c.y=y.y,c.animateTo(M,L),x.animateTo(D,L)}else c.attr(M),x.attr(D);this._firstShowIndicator=!1;var T=this._shapes.handleLabels;if(T)for(var w=0;w<T.length;w++)this.api.enterBlur(T[w])}},e.prototype._enableHoverLinkToSeries=function(){var t=this;this._shapes.mainGroup.on("mousemove",function(r){if(t._hovering=!0,!t._dragging){var a=t.visualMapModel.itemSize,n=t._applyTransform([r.offsetX,r.offsetY],t._shapes.mainGroup,!0,!0);n[1]=ra(Se(0,n[1]),a[1]),t._doHoverLinkToSeries(n[1],0<=n[0]&&n[0]<=a[0])}}).on("mouseout",function(){t._hovering=!1,!t._dragging&&t._clearHoverLinkToSeries()})},e.prototype._enableHoverLinkFromSeries=function(){var t=this.api.getZr();this.visualMapModel.option.hoverLink?(t.on("mouseover",this._hoverLinkFromSeriesMouseOver,this),t.on("mouseout",this._hideIndicator,this)):this._clearHoverLinkFromSeries()},e.prototype._doHoverLinkToSeries=function(t,r){var a=this.visualMapModel,n=a.itemSize;if(a.option.hoverLink){var o=[0,n[1]],s=a.getExtent();t=ra(Se(o[0],t),o[1]);var l=Ss(a,s,o),u=[t-l,t+l],h=J(t,o,s,!0),c=[J(u[0],o,s,!0),J(u[1],o,s,!0)];u[0]<o[0]&&(c[0]=-1/0),u[1]>o[1]&&(c[1]=1/0),r&&(c[0]===-1/0?this._showIndicator(h,c[1],"< ",l):c[1]===1/0?this._showIndicator(h,c[0],"> ",l):this._showIndicator(h,h,"≈ ",l));var f=this._hoverLinkDataIndices,v=[];(r||ia(a))&&(v=this._hoverLinkDataIndices=a.findTargetDataIndices(c));var p=an(f,v);this._dispatchHighDown("downplay",Yt(p[0],a)),this._dispatchHighDown("highlight",Yt(p[1],a))}},e.prototype._hoverLinkFromSeriesMouseOver=function(t){var r;if(nn(t.target,function(l){var u=ut(l);if(u.dataIndex!=null)return r=u,!0},!0),!!r){var a=this.ecModel.getSeriesByIndex(r.seriesIndex),n=this.visualMapModel;if(n.isTargetSeries(a)){var o=a.getData(r.dataType),s=o.getStore().get(n.getDataDimensionIndex(o),r.dataIndex);isNaN(s)||this._showIndicator(s,s)}}},e.prototype._hideIndicator=function(){var t=this._shapes;t.indicator&&t.indicator.attr("invisible",!0),t.indicatorLabel&&t.indicatorLabel.attr("invisible",!0);var r=this._shapes.handleLabels;if(r)for(var a=0;a<r.length;a++)this.api.leaveBlur(r[a])},e.prototype._clearHoverLinkToSeries=function(){this._hideIndicator();var t=this._hoverLinkDataIndices;this._dispatchHighDown("downplay",Yt(t,this.visualMapModel)),t.length=0},e.prototype._clearHoverLinkFromSeries=function(){this._hideIndicator();var t=this.api.getZr();t.off("mouseover",this._hoverLinkFromSeriesMouseOver),t.off("mouseout",this._hideIndicator)},e.prototype._applyTransform=function(t,r,a,n){var o=Xt(r,n?null:this.group);return F(t)?Pt(t,o,a):Sa(t,o,a)},e.prototype._dispatchHighDown=function(t,r){r&&r.length&&this.api.dispatchAction({type:t,batch:r})},e.prototype.dispose=function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()},e.type="visualMap.continuous",e}(Ka);function aa(i,e,t,r){return new _a({shape:{points:i},draggable:!!t,cursor:e,drift:t,onmousemove:function(a){mt(a.event)},ondragend:r})}function Ss(i,e,t){var r=ys/2,a=i.get("hoverLinkDataSize");return a&&(r=J(a,e,t,!0)/2),r}function ia(i){var e=i.get("hoverLinkOnHandle");return!!(e??i.get("realtime"))}function na(i){return i==="vertical"?"ns-resize":"ew-resize"}var _s={type:"selectDataRange",event:"dataRangeSelected",update:"update"},Ms=function(i,e){e.eachComponent({mainType:"visualMap",query:i},function(t){t.setSelected(i.selected)})},As=[{createOnAllSeries:!0,reset:function(i,e){var t=[];return e.eachComponent("visualMap",function(r){var a=i.pipelineContext;!r.isTargetSeries(i)||a&&a.large||t.push(Xo(r.stateList,r.targetVisuals,P(r.getValueState,r),r.getDataDimensionIndex(i.getData())))}),t}},{createOnAllSeries:!0,reset:function(i,e){var t=i.getData(),r=[];e.eachComponent("visualMap",function(a){if(a.isTargetSeries(i)){var n=a.getVisualMeta(P(ws,null,i,a))||{stops:[],outerColors:[]},o=a.getDataDimensionIndex(t);o>=0&&(n.dimension=o,r.push(n))}}),i.getData().setVisual("visualMeta",r)}}];function ws(i,e,t,r){for(var a=e.targetVisuals[r],n=G.prepareVisualTypes(a),o={color:on(i.getData(),"color")},s=0,l=n.length;s<l;s++){var u=n[s],h=a[u==="opacity"?"__alphaForOpacity":u];h&&h.applyVisual(t,c,f)}return o.color;function c(v){return o[v]}function f(v,p){o[v]=p}}var oa=A;function Is(i){var e=i&&i.visualMap;F(e)||(e=e?[e]:[]),oa(e,function(t){if(t){bt(t,"splitList")&&!bt(t,"pieces")&&(t.pieces=t.splitList,delete t.splitList);var r=t.pieces;r&&F(r)&&oa(r,function(a){it(a)&&(bt(a,"start")&&!bt(a,"min")&&(a.min=a.start),bt(a,"end")&&!bt(a,"max")&&(a.max=a.end))})}})}function bt(i,e){return i&&i.hasOwnProperty&&i.hasOwnProperty(e)}var sa=!1;function ja(i){sa||(sa=!0,i.registerSubTypeDefaulter("visualMap",function(e){return!e.categories&&(!(e.pieces?e.pieces.length>0:e.splitNumber>0)||e.calculable)?"continuous":"piecewise"}),i.registerAction(_s,Ms),A(As,function(e){i.registerVisual(i.PRIORITY.VISUAL.COMPONENT,e)}),i.registerPreprocessor(Is))}function Ds(i){i.registerComponentModel(gs),i.registerComponentView(bs),ja(i)}var Ls=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t._pieceList=[],t}return e.prototype.optionUpdated=function(t,r){i.prototype.optionUpdated.apply(this,arguments),this.resetExtent();var a=this._mode=this._determineMode();this._pieceList=[],Cs[this._mode].call(this,this._pieceList),this._resetSelected(t,r);var n=this.option.categories;this.resetVisual(function(o,s){a==="categories"?(o.mappingMethod="category",o.categories=X(n)):(o.dataExtent=this.getExtent(),o.mappingMethod="piecewise",o.pieceList=B(this._pieceList,function(l){return l=X(l),s!=="inRange"&&(l.visual=null),l}))})},e.prototype.completeVisualOption=function(){var t=this.option,r={},a=G.listVisualTypes(),n=this.isCategory();A(t.pieces,function(s){A(a,function(l){s.hasOwnProperty(l)&&(r[l]=1)})}),A(r,function(s,l){var u=!1;A(this.stateList,function(h){u=u||o(t,h,l)||o(t.target,h,l)},this),!u&&A(this.stateList,function(h){(t[h]||(t[h]={}))[l]=$a.get(l,h==="inRange"?"active":"inactive",n)})},this);function o(s,l,u){return s&&s[l]&&s[l].hasOwnProperty(u)}i.prototype.completeVisualOption.apply(this,arguments)},e.prototype._resetSelected=function(t,r){var a=this.option,n=this._pieceList,o=(r?a:t).selected||{};if(a.selected=o,A(n,function(l,u){var h=this.getSelectedMapKey(l);o.hasOwnProperty(h)||(o[h]=!0)},this),a.selectedMode==="single"){var s=!1;A(n,function(l,u){var h=this.getSelectedMapKey(l);o[h]&&(s?o[h]=!1:s=!0)},this)}},e.prototype.getItemSymbol=function(){return this.get("itemSymbol")},e.prototype.getSelectedMapKey=function(t){return this._mode==="categories"?t.value+"":t.index+""},e.prototype.getPieceList=function(){return this._pieceList},e.prototype._determineMode=function(){var t=this.option;return t.pieces&&t.pieces.length>0?"pieces":this.option.categories?"categories":"splitNumber"},e.prototype.setSelected=function(t){this.option.selected=X(t)},e.prototype.getValueState=function(t){var r=G.findPieceIndex(t,this._pieceList);return r!=null&&this.option.selected[this.getSelectedMapKey(this._pieceList[r])]?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[],a=this._pieceList;return this.eachTargetSeries(function(n){var o=[],s=n.getData();s.each(this.getDataDimensionIndex(s),function(l,u){var h=G.findPieceIndex(l,a);h===t&&o.push(u)},this),r.push({seriesId:n.id,dataIndex:o})},this),r},e.prototype.getRepresentValue=function(t){var r;if(this.isCategory())r=t.value;else if(t.value!=null)r=t.value;else{var a=t.interval||[];r=a[0]===-1/0&&a[1]===1/0?0:(a[0]+a[1])/2}return r},e.prototype.getVisualMeta=function(t){if(this.isCategory())return;var r=[],a=["",""],n=this;function o(h,c){var f=n.getRepresentValue({interval:h});c||(c=n.getValueState(f));var v=t(f,c);h[0]===-1/0?a[0]=v:h[1]===1/0?a[1]=v:r.push({value:h[0],color:v},{value:h[1],color:v})}var s=this._pieceList.slice();if(!s.length)s.push({interval:[-1/0,1/0]});else{var l=s[0].interval[0];l!==-1/0&&s.unshift({interval:[-1/0,l]}),l=s[s.length-1].interval[1],l!==1/0&&s.push({interval:[l,1/0]})}var u=-1/0;return A(s,function(h){var c=h.interval;c&&(c[0]>u&&o([u,c[0]],"outOfRange"),o(c.slice()),u=c[1])},this),{stops:r,outerColors:a}},e.type="visualMap.piecewise",e.defaultOption=kt(Qt.defaultOption,{selected:null,minOpen:!1,maxOpen:!1,align:"auto",itemWidth:20,itemHeight:14,itemSymbol:"roundRect",pieces:null,categories:null,splitNumber:5,selectedMode:"multiple",itemGap:10,hoverLink:!0}),e}(Qt),Cs={splitNumber:function(i){var e=this.option,t=Math.min(e.precision,20),r=this.getExtent(),a=e.splitNumber;a=Math.max(parseInt(a,10),1),e.splitNumber=a;for(var n=(r[1]-r[0])/a;+n.toFixed(t)!==n&&t<5;)t++;e.precision=t,n=+n.toFixed(t),e.minOpen&&i.push({interval:[-1/0,r[0]],close:[0,0]});for(var o=0,s=r[0];o<a;s+=n,o++){var l=o===a-1?r[1]:s+n;i.push({interval:[s,l],close:[1,1]})}e.maxOpen&&i.push({interval:[r[1],1/0],close:[0,0]}),cr(i),A(i,function(u,h){u.index=h,u.text=this.formatValueText(u.interval)},this)},categories:function(i){var e=this.option;A(e.categories,function(t){i.push({text:this.formatValueText(t,!0),value:t})},this),la(e,i)},pieces:function(i){var e=this.option;A(e.pieces,function(t,r){it(t)||(t={value:t});var a={text:"",index:r};if(t.label!=null&&(a.text=t.label),t.hasOwnProperty("value")){var n=a.value=t.value;a.interval=[n,n],a.close=[1,1]}else{for(var o=a.interval=[],s=a.close=[0,0],l=[1,0,1],u=[-1/0,1/0],h=[],c=0;c<2;c++){for(var f=[["gte","gt","min"],["lte","lt","max"]][c],v=0;v<3&&o[c]==null;v++)o[c]=t[f[v]],s[c]=l[v],h[c]=v===2;o[c]==null&&(o[c]=u[c])}h[0]&&o[1]===1/0&&(s[0]=0),h[1]&&o[0]===-1/0&&(s[1]=0),o[0]===o[1]&&s[0]&&s[1]&&(a.value=o[0])}a.visual=G.retrieveVisuals(t),i.push(a)},this),la(e,i),cr(i),A(i,function(t){var r=t.close,a=[["<","≤"][r[1]],[">","≥"][r[0]]];t.text=t.text||this.formatValueText(t.value!=null?t.value:t.interval,!1,a)},this)}};function la(i,e){var t=i.inverse;(i.orient==="vertical"?!t:t)&&e.reverse()}var Ts=function(i){R(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.doRender=function(){var t=this.group;t.removeAll();var r=this.visualMapModel,a=r.get("textGap"),n=r.textStyleModel,o=n.getFont(),s=n.getTextColor(),l=this._getItemAlign(),u=r.itemSize,h=this._getViewData(),c=h.endsText,f=sn(r.get("showLabel",!0),!c);c&&this._renderEndsText(t,c[0],u,f,l),A(h.viewPieceList,function(v){var p=v.piece,d=new U;d.onclick=P(this._onItemClick,this,p),this._enableHoverLink(d,v.indexInModelPieceList);var g=r.getRepresentValue(p);if(this._createItemSymbol(d,g,[0,0,u[0],u[1]]),f){var y=this.visualMapModel.getValueState(g);d.add(new pt({style:{x:l==="right"?-a:u[0]+a,y:u[1]/2,text:p.text,verticalAlign:"middle",align:l,font:o,fill:s,opacity:y==="outOfRange"?.5:1}}))}t.add(d)},this),c&&this._renderEndsText(t,c[1],u,f,l),ln(r.get("orient"),t,r.get("itemGap")),this.renderBackground(t),this.positionGroup(t)},e.prototype._enableHoverLink=function(t,r){var a=this;t.on("mouseover",function(){return n("highlight")}).on("mouseout",function(){return n("downplay")});var n=function(o){var s=a.visualMapModel;s.option.hoverLink&&a.api.dispatchAction({type:o,batch:Yt(s.findTargetDataIndices(r),s)})}},e.prototype._getItemAlign=function(){var t=this.visualMapModel,r=t.option;if(r.orient==="vertical")return qa(t,this.api,t.itemSize);var a=r.align;return(!a||a==="auto")&&(a="left"),a},e.prototype._renderEndsText=function(t,r,a,n,o){if(r){var s=new U,l=this.visualMapModel.textStyleModel;s.add(new pt({style:Vt(l,{x:n?o==="right"?a[0]:0:a[0]/2,y:a[1]/2,verticalAlign:"middle",align:n?o:"center",text:r})})),t.add(s)}},e.prototype._getViewData=function(){var t=this.visualMapModel,r=B(t.getPieceList(),function(s,l){return{piece:s,indexInModelPieceList:l}}),a=t.get("text"),n=t.get("orient"),o=t.get("inverse");return(n==="horizontal"?o:!o)?r.reverse():a&&(a=a.slice().reverse()),{viewPieceList:r,endsText:a}},e.prototype._createItemSymbol=function(t,r,a){t.add(ht(this.getControllerVisual(r,"symbol"),a[0],a[1],a[2],a[3],this.getControllerVisual(r,"color")))},e.prototype._onItemClick=function(t){var r=this.visualMapModel,a=r.option,n=a.selectedMode;if(n){var o=X(a.selected),s=r.getSelectedMapKey(t);n==="single"||n===!0?(o[s]=!0,A(o,function(l,u){o[u]=u===s})):o[s]=!o[s],this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:o})}},e.type="visualMap.piecewise",e}(Ka);function Vs(i){i.registerComponentModel(Ls),i.registerComponentView(Ts),ja(i)}function Hs(i){At(Ds),At(Vs)}export{wn as A,Jt as D,Tn as S,zn as a,Xa as b,Ne as c,Ua as d,He as e,Gs as f,Bs as g,Hs as h,Vo as i,Vs as j,ks as k,Es as l,Os as m,$n as p,Ns as r,zt as s,zs as t};
//# sourceMappingURL=install-DosLD5hS.js.map
