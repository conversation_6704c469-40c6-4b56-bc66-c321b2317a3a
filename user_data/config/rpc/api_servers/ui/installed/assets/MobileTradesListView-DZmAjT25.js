import{_ as S}from"./TradeDetail-09aMAmmS.js";import{o as s,c as l,a as n,g as h,x as v,b as c,_ as b,u as C,r as B,k as g,w as k,F as z,K as F,h as t,y as _,A as i,z as w,N as I,P as q,bc as j,Z as E,e as P,v as A}from"./index-B2p78N-x.js";import{a as M,_ as O}from"./InfoBox.vue_vue_type_script_setup_true_lang-Dr_hBaFv.js";const U={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Z=n("path",{fill:"currentColor",d:"M20 11v2H8l5.5 5.5l-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5L8 11z"},null,-1),H=[Z];function J(a,e){return s(),l("svg",U,[...H])}const K={name:"mdi-arrow-left",render:J},R={class:"d-flex"},G={class:"px-1 d-flex flex-row flex-fill text-start justify-content-between align-items-center"},Q={class:"me-1 fw-bold"},W={class:"text-secondary"},X=h({__name:"CustomTradeListEntry",props:{trade:{type:Object,required:!0},stakeCurrencyDecimals:{type:Number,required:!0},showDetails:{type:Boolean,default:!1}},setup(a){return(e,p)=>{const o=O;return s(),l("div",R,[n("div",G,[n("span",null,[n("span",Q,v(a.trade.pair),1),n("small",W,"(#"+v(a.trade.trade_id)+")",1)]),n("small",null,[c(o,{date:a.trade.open_timestamp,"date-only":!0},null,8,["date"])])]),c(M,{class:"col-5",trade:a.trade},null,8,["trade"])])}}}),Y=b(X,[["__scopeId","data-v-3ae36702"]]),ee={class:"h-100 overflow-auto p-1"},te={key:0,class:"mt-5"},ae={class:"w-100 d-flex justify-content-between mt-1"},se=h({__name:"CustomTradeList",props:{trades:{required:!0,type:Array},title:{default:"Trades",type:String},stakeCurrency:{required:!1,default:"",type:String},activeTrades:{default:!1,type:Boolean},showFilter:{default:!1,type:Boolean},multiBotView:{default:!1,type:Boolean},emptyText:{default:"No Trades to show.",type:String},stakeCurrencyDecimals:{default:3,type:Number}},setup(a){const e=a,p=C(),o=B(1),d=B(""),u=e.activeTrades?200:25,y=g(()=>e.trades.length),f=g(()=>e.trades.slice((o.value-1)*u,o.value*u)),x=T=>{p.activeBot.setDetailTrade(T)};return(T,m)=>{const $=Y,D=I,V=q,L=j,N=E;return s(),l("div",ee,[c(V,{id:"tradeList"},{default:k(()=>[(s(!0),l(z,null,F(t(f),r=>(s(),_(D,{key:r.trade_id,class:"border border-secondary rounded my-05 px-1",onClick:ne=>x(r)},{default:k(()=>[c($,{trade:r,"stake-currency-decimals":a.stakeCurrencyDecimals},null,8,["trade","stake-currency-decimals"])]),_:2},1032,["onClick"]))),128))]),_:1}),a.trades.length==0?(s(),l("span",te,v(a.emptyText),1)):i("",!0),n("div",ae,[a.activeTrades?i("",!0):(s(),_(L,{key:0,modelValue:t(o),"onUpdate:modelValue":m[0]||(m[0]=r=>w(o)?o.value=r:null),"total-rows":t(y),"per-page":t(u),"aria-controls":"tradeList"},null,8,["modelValue","total-rows","per-page"])),a.showFilter?(s(),_(N,{key:1,modelValue:t(d),"onUpdate:modelValue":m[1]||(m[1]=r=>w(d)?d.value=r:null),type:"text",placeholder:"Filter",size:"sm",style:{width:"unset"}},null,8,["modelValue"])):i("",!0)])])}}}),oe=b(se,[["__scopeId","data-v-b42a4b4f"]]),re={key:2,class:"d-flex flex-column"},ie=h({__name:"MobileTradesListView",props:{history:{default:!1,type:Boolean}},setup(a){const e=C();return(p,o)=>{const d=oe,u=K,y=A,f=S;return s(),l("div",null,[!a.history&&!t(e).activeBot.detailTradeId?(s(),_(d,{key:0,trades:t(e).activeBot.openTrades,title:"Open trades","active-trades":!0,"stake-currency-decimals":t(e).activeBot.stakeCurrencyDecimals,"empty-text":"No open Trades."},null,8,["trades","stake-currency-decimals"])):i("",!0),a.history&&!t(e).activeBot.detailTradeId?(s(),_(d,{key:1,trades:t(e).activeBot.closedTrades,title:"Trade history","stake-currency-decimals":t(e).activeBot.stakeCurrencyDecimals,"empty-text":"No closed trades so far."},null,8,["trades","stake-currency-decimals"])):i("",!0),t(e).activeBot.detailTradeId&&t(e).activeBot.tradeDetail?(s(),l("div",re,[c(y,{size:"sm",class:"align-self-start my-1 ms-1",onClick:o[0]||(o[0]=x=>t(e).activeBot.setDetailTrade(null))},{default:k(()=>[c(u),P(" Back")]),_:1}),c(f,{trade:t(e).activeBot.tradeDetail,"stake-currency":t(e).activeBot.stakeCurrency},null,8,["trade","stake-currency"])])):i("",!0)])}}});export{ie as default};
//# sourceMappingURL=MobileTradesListView-DZmAjT25.js.map
