{"version": 3, "file": "LoginView-BGHIqNmr.js", "sources": ["../../src/views/LoginView.vue"], "sourcesContent": ["<template>\n  <div class=\"container\">\n    <b-card header=\"Freqtrade bot Login\">\n      <BotLogin ref=\"loginForm\" />\n    </b-card>\n  </div>\n</template>\n\n<script setup lang=\"ts\"></script>\n\n<style scoped>\n.container {\n  max-width: 520px;\n}\n</style>\n"], "names": ["_openBlock", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_b_card", "_withCtx"], "mappings": "yJAIaA,EAAA,EAAAC,EAFK,MAAsBC,EAAA,CAFxCC,EAAAC,EAGkC,CAAA,OAAA,qBAAA,EAAA,SAAAC,EAAA,IAAA,iCAHlC"}