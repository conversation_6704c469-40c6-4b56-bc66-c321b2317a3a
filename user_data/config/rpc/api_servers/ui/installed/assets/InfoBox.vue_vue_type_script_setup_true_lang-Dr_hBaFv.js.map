{"version": 3, "file": "InfoBox.vue_vue_type_script_setup_true_lang-Dr_hBaFv.js", "sources": ["../../src/components/general/DateTimeTZ.vue", "../../src/components/general/ProfitSymbol.vue", "../../src/components/general/ProfitPill.vue", "../../src/components/ftbot/TradeProfit.vue"], "sourcesContent": ["<template>\n  <span :title=\"timezoneTooltip\">{{ formattedDate }}</span>\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  timestampmsOrNa,\n  timestampmsWithTimezone,\n  timestampToDateString,\n} from '@/shared/formatters';\n\nconst props = defineProps({\n  date: { required: true, type: Number },\n  showTimezone: { required: false, type: Boolean, default: false },\n  dateOnly: { required: false, type: Boolean, default: false },\n});\nconst formattedDate = computed((): string => {\n  if (props.dateOnly) {\n    return timestampToDateString(props.date);\n  }\n  if (props.showTimezone) {\n    return timestampmsWithTimezone(props.date);\n  }\n  return timestampmsOrNa(props.date);\n});\n\nconst timezoneTooltip = computed((): string => {\n  const time1 = timestampmsWithTimezone(props.date);\n  const timeUTC = timestampmsWithTimezone(props.date, 'UTC');\n  if (time1 === timeUTC) {\n    return timeUTC;\n  }\n\n  return `${time1}\\n${timeUTC}`;\n});\n</script>\n\n<style scoped></style>\n", "<template>\n  <div class=\"d-inline-block\">\n    <div :class=\"isProfitable ? 'triangle-up' : 'triangle-down'\"></div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nconst props = defineProps({\n  profit: { type: Number, required: true },\n});\nconst isProfitable = computed(() => {\n  return props.profit > 0;\n});\n</script>\n\n<style scoped lang=\"scss\">\n.triangle-up {\n  width: 0;\n  height: 0;\n  border-style: solid;\n  border-width: 0 0.45rem 0.7rem 0.45rem;\n  border-color: transparent transparent $color-profit transparent;\n}\n.triangle-down {\n  width: 0;\n  height: 0;\n  border-style: solid;\n  border-width: 0.7rem 0.45rem 0 0.45rem;\n  border-color: $color-loss transparent transparent transparent;\n}\n</style>\n", "<template>\n  <div\n    class=\"d-flex justify-content-between align-items-center profit-pill ps-2 pe-1\"\n    :class=\"isProfitable ? 'profit-pill-profit' : ''\"\n    :title=\"profitDesc\"\n  >\n    <profit-symbol :profit=\"(profitRatio || profitAbs) ?? 0\" />\n\n    <div class=\"d-flex justify-content-center align-items-center flex-grow-1\">\n      {{ profitRatio !== undefined ? formatPercent(profitRatio, 2) : '' }}\n      <span\n        v-if=\"profitString\"\n        class=\"ms-1\"\n        :class=\"profitRatio ? 'small' : ''\"\n        :title=\"stakeCurrency\"\n        >{{ profitString }}</span\n      >\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent, formatPrice, formatPriceCurrency } from '@/shared/formatters';\n\nconst props = defineProps({\n  profitRatio: { required: false, default: undefined, type: Number },\n  profitAbs: { required: false, default: undefined, type: Number },\n  stakeCurrency: { required: true, type: String },\n  profitDesc: { required: false, default: '', type: String },\n});\nconst isProfitable = computed(() => {\n  return (\n    (props.profitRatio !== undefined && props.profitRatio > 0) ||\n    (props.profitRatio === undefined && props.profitAbs !== undefined && props.profitAbs > 0)\n  );\n});\n\nconst profitString = computed((): string => {\n  if (props.profitRatio !== undefined && props.profitAbs !== undefined) {\n    return `(${formatPrice(props.profitAbs, 3)})`;\n  } else if (props.profitAbs !== undefined) {\n    if (props.stakeCurrency !== undefined) {\n      return `${formatPriceCurrency(props.profitAbs, props.stakeCurrency, 3)}`;\n    } else {\n      return `${formatPrice(props.profitAbs, 3)}`;\n    }\n  }\n  return '';\n});\n</script>\n\n<style scoped lang=\"scss\">\n.profit-pill {\n  border: 2px solid $color-loss;\n  border-radius: 6px;\n}\n.profit-pill-profit {\n  border: 2px solid $color-profit;\n}\n</style>\n", "<template>\n  <ProfitPill\n    :profit-ratio=\"profitRatio\"\n    :profit-abs=\"profitAbs\"\n    :profit-desc=\"profitDesc\"\n    :stake-currency=\"trade.quote_currency || 'USDT'\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent, timestampms } from '@/shared/formatters';\nimport { Trade } from '@/types';\n\ntype modes = 'default' | 'total' | 'realized';\n\nconst props = defineProps({\n  trade: { required: true, type: Object as () => Trade },\n  mode: {\n    required: false,\n    default: 'default',\n    type: String as PropType<modes>,\n  },\n});\n\nconst modeDescs: { [key in modes]: string } = {\n  default: 'Current profit',\n  total: 'Total profit',\n  realized: 'Realized profit',\n};\n\nconst profitRatio = computed<number | undefined>(() => {\n  switch (props.mode) {\n    case 'default':\n      return props.trade.profit_ratio;\n    case 'total':\n      return props.trade.total_profit_ratio;\n    case 'realized':\n      return props.trade.realized_profit_ratio;\n    default:\n      return undefined;\n  }\n});\nconst profitAbs = computed<number | undefined>(() => {\n  switch (props.mode) {\n    case 'default':\n      return props.trade.profit_abs;\n    case 'total':\n      return props.trade.total_profit_abs;\n    case 'realized':\n      return props.trade.realized_profit;\n    default:\n      return undefined;\n  }\n});\nconst profitDesc = computed((): string => {\n  let profit = `${modeDescs[props.mode]}: ${\n    profitRatio.value ? formatPercent(profitRatio.value) : ''\n  } (${profitAbs.value})`;\n  profit += `\\nOpen since: ${timestampms(props.trade.open_timestamp)}`;\n  return profit;\n});\n</script>\n\n<style scoped></style>\n"], "names": ["props", "__props", "formattedDate", "computed", "timestampToDateString", "timestampmsWithTimezone", "timestampmsOrNa", "timezoneTooltip", "time1", "timeUTC", "isProfitable", "profitString", "formatPrice", "formatPriceCurrency", "modeDescs", "profitRatio", "profitAbs", "profitDesc", "profit", "formatPercent", "timestampms"], "mappings": "6WAWA,MAAMA,EAAQC,EAKRC,EAAgBC,EAAS,IACzBH,EAAM,SACDI,EAAsBJ,EAAM,IAAI,EAErCA,EAAM,aACDK,EAAwBL,EAAM,IAAI,EAEpCM,EAAgBN,EAAM,IAAI,CAClC,EAEKO,EAAkBJ,EAAS,IAAc,CACvC,MAAAK,EAAQH,EAAwBL,EAAM,IAAI,EAC1CS,EAAUJ,EAAwBL,EAAM,KAAM,KAAK,EACzD,OAAIQ,IAAUC,EACLA,EAGF,GAAGD,CAAK;AAAA,EAAKC,CAAO,EAAA,CAC5B,mKC3BD,MAAMT,EAAQC,EAGRS,EAAeP,EAAS,IACrBH,EAAM,OAAS,CACvB,udCYD,MAAMA,EAAQC,EAMRS,EAAeP,EAAS,IAEzBH,EAAM,cAAgB,QAAaA,EAAM,YAAc,GACvDA,EAAM,cAAgB,QAAaA,EAAM,YAAc,QAAaA,EAAM,UAAY,CAE1F,EAEKW,EAAeR,EAAS,IACxBH,EAAM,cAAgB,QAAaA,EAAM,YAAc,OAClD,IAAIY,EAAYZ,EAAM,UAAW,CAAC,CAAC,IACjCA,EAAM,YAAc,OACzBA,EAAM,gBAAkB,OACnB,GAAGa,EAAoBb,EAAM,UAAWA,EAAM,cAAe,CAAC,CAAC,GAE/D,GAAGY,EAAYZ,EAAM,UAAW,CAAC,CAAC,GAGtC,EACR,kmBCjCD,MAAMA,EAAQC,EASRa,EAAwC,CAC5C,QAAS,iBACT,MAAO,eACP,SAAU,iBAAA,EAGNC,EAAcZ,EAA6B,IAAM,CACrD,OAAQH,EAAM,KAAM,CAClB,IAAK,UACH,OAAOA,EAAM,MAAM,aACrB,IAAK,QACH,OAAOA,EAAM,MAAM,mBACrB,IAAK,WACH,OAAOA,EAAM,MAAM,sBACrB,QACS,MACX,CAAA,CACD,EACKgB,EAAYb,EAA6B,IAAM,CACnD,OAAQH,EAAM,KAAM,CAClB,IAAK,UACH,OAAOA,EAAM,MAAM,WACrB,IAAK,QACH,OAAOA,EAAM,MAAM,iBACrB,IAAK,WACH,OAAOA,EAAM,MAAM,gBACrB,QACS,MACX,CAAA,CACD,EACKiB,EAAad,EAAS,IAAc,CACxC,IAAIe,EAAS,GAAGJ,EAAUd,EAAM,IAAI,CAAC,KACnCe,EAAY,MAAQI,EAAcJ,EAAY,KAAK,EAAI,EACzD,KAAKC,EAAU,KAAK,IACV,OAAAE,GAAA;AAAA,cAAiBE,EAAYpB,EAAM,MAAM,cAAc,CAAC,GAC3DkB,CAAA,CACR"}