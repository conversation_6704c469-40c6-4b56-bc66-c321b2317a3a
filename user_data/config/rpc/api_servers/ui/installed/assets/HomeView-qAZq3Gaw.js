import{_ as n,o as c,c as l,a as e,b as d,p as _,d as r,e as s,f as i}from"./index-B2p78N-x.js";const h={},t=o=>(_("data-v-7e82f96a"),o=o(),r(),o),u={class:"home"},p={class:"d-flex justify-content-center"},f=t(()=>e("hr",null,null,-1)),m=t(()=>e("div",{title:"Freqtrade logo",class:"logo-svg my-5 mx-auto"},null,-1)),v=t(()=>e("div",null,[e("h1",null,"Welcome to the Freqtrade UI")],-1)),w=t(()=>e("div",null,"This page allows you to control your trading bot.",-1)),y=t(()=>e("br",null,null,-1)),x=t(()=>e("p",null,[s(" If you need any help, please refer to the "),e("a",{href:"https://www.freqtrade.io/en/latest/"},"Freqtrade Documentation"),s(". ")],-1)),I=t(()=>e("p",null,[s("Have fun - "),e("i",null,"wishes you the Freqtrade team")],-1));function g(o,q){const a=i;return c(),l("div",u,[e("div",p,[d(a)]),f,m,v,w,y,x,I])}const F=n(h,[["render",g],["__scopeId","data-v-7e82f96a"]]);export{F as default};
//# sourceMappingURL=HomeView-qAZq3Gaw.js.map
