{"version": 3, "file": "BacktestingView-Cr3-RO0Y.js", "sources": ["../../src/components/ftbot/TradeListNav.vue", "../../src/components/ftbot/BacktestResultChart.vue", "../../src/shared/objectToTableItems.ts", "../../src/shared/backtestMetrics.ts", "../../src/components/ftbot/BacktestResultComparison.vue", "../../src/components/ftbot/BacktestResultPeriodBreakdown.vue", "../../src/components/ftbot/BacktestResultTablePer.vue", "../../src/components/ftbot/BacktestResultAnalysis.vue", "../../src/components/ftbot/FreqaiModelSelect.vue", "../../src/stores/btStore.ts", "../../src/components/ftbot/BacktestRun.vue", "../../src/components/ftbot/BacktestHistoryLoad.vue", "../../src/components/ftbot/BacktestResultSelect.vue", "../../src/views/BacktestingView.vue"], "sourcesContent": ["<template>\n  <div>\n    <b-list-group>\n      <b-list-group-item\n        button\n        class=\"d-flex flex-wrap justify-content-center align-items-center\"\n        :title=\"'Trade Navigation'\"\n        @click=\"sortNewestFirst = !sortNewestFirst\"\n        >Trade Navigation {{ sortNewestFirst ? '&#8595;' : '&#8593;' }}\n      </b-list-group-item>\n      <b-list-group-item\n        v-for=\"(trade, i) in sortedTrades\"\n        :key=\"trade.open_timestamp\"\n        button\n        class=\"d-flex flex-column py-1 pe-1 align-items-stretch\"\n        :title=\"`${trade.pair}`\"\n        :active=\"trade.open_timestamp === selectedTrade.open_timestamp\"\n        @click=\"onTradeSelect(trade)\"\n      >\n        <div class=\"d-flex\">\n          <div class=\"d-flex flex-column\">\n            <div>\n              <span v-if=\"botStore.activeBot.botState.trading_mode !== 'spot'\">{{\n                trade.is_short ? 'S-' : 'L-'\n              }}</span>\n              <DateTimeTZ :date=\"trade.open_timestamp\" />\n            </div>\n            <TradeProfit :trade=\"trade\" class=\"my-1\" />\n            <ProfitPill\n              v-if=\"backtestMode\"\n              :profit-ratio=\"trade.profit_ratio\"\n              :stake-currency=\"botStore.activeBot.stakeCurrency\"\n            />\n          </div>\n          <b-button\n            size=\"sm\"\n            class=\"ms-auto mt-auto\"\n            variant=\"outline-secondary\"\n            @click=\"ordersVisible[i] = !ordersVisible[i]\"\n            ><i-mdi-chevron-right v-if=\"!ordersVisible[i]\" width=\"24\" height=\"24\" />\n            <i-mdi-chevron-down v-if=\"ordersVisible[i]\" width=\"24\" height=\"24\" />\n          </b-button>\n        </div>\n        <b-collapse v-model=\"ordersVisible[i]\">\n          <ul class=\"px-3 m-0\">\n            <li\n              v-for=\"order in trade.orders?.filter((o) => o.order_filled_timestamp !== null)\"\n              :key=\"order.order_timestamp\"\n            >\n              {{ order.ft_order_side }} {{ order.amount }} at {{ order.safe_price }}\n            </li>\n          </ul>\n        </b-collapse>\n      </b-list-group-item>\n      <b-list-group-item v-if=\"trades.length === 0\">No trades to show...</b-list-group-item>\n    </b-list-group>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { Trade } from '@/types';\n\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst props = defineProps({\n  trades: { required: true, type: Array as () => Trade[] },\n  backtestMode: { required: false, default: false, type: Boolean },\n});\nconst emit = defineEmits(['trade-select']);\n\nconst botStore = useBotStore();\nconst selectedTrade = ref({} as Trade);\nconst sortNewestFirst = ref(true);\n\nconst onTradeSelect = (trade: Trade) => {\n  selectedTrade.value = trade;\n  emit('trade-select', trade);\n};\n\nconst sortedTrades = computed(() => {\n  return props.trades\n    .slice()\n    .sort((a, b) =>\n      sortNewestFirst.value\n        ? b.open_timestamp - a.open_timestamp\n        : a.open_timestamp - b.open_timestamp,\n    );\n});\n\nconst ordersVisible = ref(sortedTrades.value.map(() => false));\n\nwatch(\n  () => botStore.activeBot.selectedPair,\n  () => {\n    ordersVisible.value = sortedTrades.value.map(() => false);\n  },\n);\n</script>\n\n<style scoped>\n.list-group {\n  text-align: left;\n}\n</style>\n", "<template>\n  <div>\n    <div class=\"d-flex flex-row mb-1 align-items-center\">\n      <div class=\"me-2\">\n        <b-button\n          aria-label=\"Close\"\n          title=\"Pair Navigation\"\n          variant=\"outline-secondary\"\n          size=\"sm\"\n          @click=\"isBarVisible.left = !isBarVisible.left\"\n        >\n          <i-mdi-chevron-right v-if=\"!isBarVisible.left\" width=\"24\" height=\"24\" />\n          <i-mdi-chevron-left v-if=\"isBarVisible.left\" width=\"24\" height=\"24\" />\n        </b-button>\n      </div>\n      <span class=\"flex-fill\">\n        Graph will always show the latest values for the selected strategy. <br />\n        Timerange: {{ timerange }} - {{ strategy }}\n      </span>\n      <div class=\"col-md-1 text-end\">\n        <b-button\n          aria-label=\"Close\"\n          variant=\"outline-secondary\"\n          title=\"Trade Navigation\"\n          size=\"sm\"\n          @click=\"isBarVisible.right = !isBarVisible.right\"\n        >\n          <i-mdi-chevron-right v-if=\"isBarVisible.right\" width=\"24\" height=\"24\" />\n          <i-mdi-chevron-left v-if=\"!isBarVisible.right\" width=\"24\" height=\"24\" />\n        </b-button>\n      </div>\n    </div>\n    <div class=\"text-center d-flex flex-row h-100 align-items-stretch\">\n      <Transition name=\"fadeleft\">\n        <PairSummary\n          v-if=\"isBarVisible.left\"\n          class=\"col-md-2 overflow-y-auto overflow-x-hidden\"\n          style=\"max-height: calc(100vh - 200px)\"\n          :pairlist=\"pairlist\"\n          :trades=\"trades\"\n          sort-method=\"profit\"\n          :backtest-mode=\"true\"\n        />\n      </Transition>\n      <CandleChartContainer\n        :available-pairs=\"pairlist\"\n        :historic-view=\"!!true\"\n        :timeframe=\"timeframe\"\n        :timerange=\"timerange\"\n        :strategy=\"strategy\"\n        :trades=\"trades\"\n        class=\"flex-shrink-1 candle-chart-container w-100 px-0 h-100 align-self-stretch\"\n        :slider-position=\"sliderPosition\"\n        :freqai-model=\"freqaiModel\"\n      >\n      </CandleChartContainer>\n      <Transition name=\"fade\">\n        <TradeListNav\n          v-if=\"isBarVisible.right\"\n          class=\"overflow-y-auto col-md-2 overflow-x-visible\"\n          style=\"max-height: calc(100vh - 200px)\"\n          :trades=\"trades.filter((t) => t.pair === botStore.activeBot.selectedPair)\"\n          @trade-select=\"navigateChartToTrade\"\n        />\n      </Transition>\n    </div>\n    <b-card header=\"Single trades\" class=\"row mt-2 w-100\">\n      <TradeList class=\"row trade-history mt-2 w-100\" :trades=\"trades\" :show-filter=\"true\" />\n    </b-card>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nimport { ChartSliderPosition, Trade } from '@/types';\n\ndefineProps({\n  timeframe: { required: true, type: String },\n  strategy: { required: true, type: String },\n  freqaiModel: { required: false, default: undefined, type: String },\n  timerange: { required: true, type: String },\n  pairlist: { required: true, type: Array as () => string[] },\n  trades: { required: true, type: Array as () => Trade[] },\n});\nconst botStore = useBotStore();\nconst isBarVisible = ref({ right: true, left: true });\nconst sliderPosition = ref<ChartSliderPosition>();\n\nconst navigateChartToTrade = (trade: Trade) => {\n  sliderPosition.value = {\n    startValue: trade.open_timestamp,\n    endValue: trade.close_timestamp,\n  };\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.candle-chart-container {\n  // TODO: Rough estimate - still to fix correctly\n  // Applies to all \"calc\" usages in this file.\n  height: calc(100vh - 250px) !important;\n}\n\n.fade-enter-active,\n.fade-leave-active {\n  transition: all 0.2s;\n}\n\n.fade-enter-from,\n.fade-leave-to {\n  opacity: 0;\n  transform: translateX(30px);\n}\n.fadeleft-enter-active,\n.fadeleft-leave-active {\n  transition: all 0.2s;\n}\n\n.fadeleft-enter-from,\n.fadeleft-leave-to {\n  opacity: 0;\n  transform: translateX(-30px);\n}\n</style>\n", "interface childObjects {\n  [key: string]: string | boolean | number | undefined;\n}\ninterface MutatingObject {\n  [key: string]: childObjects[];\n}\n\n/**\n *\n * @param originalobj Object in the form {Name, [{metric: value}]]}\n * @param valueKey Key to use for result\n * @returns Object in the form [{valueKey: metric, Name: value}]\n */\nexport function formatObjectForTable(originalobj: MutatingObject, valueKey: string) {\n  const result = Object.entries(originalobj).reduce((acc: childObjects[], [key, value]) => {\n    value.forEach((item) => {\n      const [metric, val] = Object.entries(item)[0];\n      const existingItem = acc.find((i) => i[valueKey] === metric);\n      if (existingItem) {\n        existingItem[key] = val;\n      } else {\n        acc.push({\n          [valueKey]: metric,\n          [key]: val,\n        });\n      }\n    });\n    return acc;\n  }, []);\n  return result;\n}\n", "import { StrategyBacktestResult, Trade } from '@/types';\nimport {\n  formatPercent,\n  formatPrice,\n  humanizeDurationFromSeconds,\n  isNotUndefined,\n  timestampms,\n} from './formatters';\n\nfunction getSortedTrades(trades: Trade[]): Trade[] {\n  const sortedTrades = trades.slice().sort((a, b) => a.profit_ratio - b.profit_ratio);\n  return sortedTrades;\n}\n\nfunction getBestPair(trades: Trade[]) {\n  if (trades.length === 0) {\n    return 'N/A';\n  }\n  const value = trades[trades.length - 1];\n  return `${value.pair} ${formatPercent(value.profit_ratio, 2)}`;\n}\n\nfunction getWorstPair(trades: Trade[]) {\n  if (trades.length === 0) {\n    return 'N/A';\n  }\n  const value = trades[0];\n  return `${value.pair} ${formatPercent(value.profit_ratio, 2)}`;\n}\n\nfunction useFormatPriceStake(stake_currency_decimals: number, stake_currency: string) {\n  const formatPriceStake = (price) => {\n    return `${formatPrice(price, stake_currency_decimals)} ${stake_currency}`;\n  };\n  return formatPriceStake;\n}\n\nexport function generateBacktestMetricRows(result: StrategyBacktestResult) {\n  const sortedTrades = getSortedTrades(result.trades);\n  const bestPair = getBestPair(sortedTrades);\n  const worstPair = getWorstPair(sortedTrades);\n  const pairSummary = result.results_per_pair[result.results_per_pair.length - 1];\n\n  const formatPriceStake = useFormatPriceStake(\n    result.stake_currency_decimals,\n    result.stake_currency,\n  );\n\n  // Transpose Result into readable format\n  const shortMetrics =\n    result.trade_count_short && result.trade_count_short > 0\n      ? [\n          { '___ ': '___' },\n          {\n            'Long / Short': `${result.trade_count_long} / ${result.trade_count_short}`,\n          },\n          {\n            'Total profit Long': `${formatPercent(\n              result.profit_total_long || 0,\n            )} | ${formatPriceStake(result.profit_total_long_abs)}`,\n          },\n          {\n            'Total profit Short': `${formatPercent(\n              result.profit_total_short || 0,\n            )} | ${formatPriceStake(result.profit_total_short_abs)}`,\n          },\n        ]\n      : [];\n\n  const tmp = [\n    {\n      'Total Profit': `${formatPercent(result.profit_total)} | ${formatPriceStake(\n        result.profit_total_abs,\n      )}`,\n    },\n    {\n      CAGR: `${result.cagr ? formatPercent(result.cagr) : 'N/A'}`,\n    },\n    {\n      Sortino: `${result.sortino ? result.sortino.toFixed(2) : 'N/A'}`,\n    },\n    {\n      Sharpe: `${result.sharpe ? result.sharpe.toFixed(2) : 'N/A'}`,\n    },\n    {\n      Calmar: `${result.calmar ? result.calmar.toFixed(2) : 'N/A'}`,\n    },\n    {\n      [`Expectancy ${result.expectancy_ratio ? '(ratio)' : ''}`]: `${\n        result.expectancy\n          ? result.expectancy_ratio\n            ? result.expectancy.toFixed(2) + ' (' + result.expectancy_ratio.toFixed(2) + ')'\n            : result.expectancy.toFixed(2)\n          : 'N/A'\n      }`,\n    },\n    {\n      'Profit factor': `${result.profit_factor ? formatPrice(result.profit_factor, 3) : 'N/A'}`,\n    },\n    {\n      'Total trades / Daily Avg Trades': `${result.total_trades} / ${result.trades_per_day}`,\n    },\n    // { 'First trade': result.backtest_fi },\n    // { 'First trade Pair': result.backtest_best_day },\n    {\n      'Best day': `${formatPercent(result.backtest_best_day, 2)} | ${formatPriceStake(\n        result.backtest_best_day_abs,\n      )}`,\n    },\n    {\n      'Worst day': `${formatPercent(result.backtest_worst_day, 2)} | ${formatPriceStake(\n        result.backtest_worst_day_abs,\n      )}`,\n    },\n\n    {\n      'Win/Draw/Loss': `${pairSummary.wins} / ${pairSummary.draws} / ${pairSummary.losses} ${\n        isNotUndefined(pairSummary.winrate)\n          ? '(WR: ' +\n            formatPercent(\n              result.results_per_pair[result.results_per_pair.length - 1].winrate ?? 0,\n              2,\n            ) +\n            ')'\n          : ''\n      }`,\n    },\n    {\n      'Days win/draw/loss': `${result.winning_days} / ${result.draw_days} / ${result.losing_days}`,\n    },\n    {\n      'Avg. Duration winners': humanizeDurationFromSeconds(result.winner_holding_avg_s),\n    },\n    {\n      'Avg. Duration Losers': humanizeDurationFromSeconds(result.loser_holding_avg_s),\n    },\n    {\n      'Max Consecutive Wins / Loss':\n        result.max_consecutive_wins === undefined\n          ? 'N/A'\n          : `${result.max_consecutive_wins} / ${result.max_consecutive_losses}`,\n    },\n    { 'Rejected entry signals': result.rejected_signals },\n    {\n      'Entry/Exit timeouts': `${result.timedout_entry_orders} / ${result.timedout_exit_orders}`,\n    },\n    {\n      'Canceled Trade Entries': result.canceled_trade_entries ?? 'N/A',\n    },\n    {\n      'Canceled Entry Orders': result.canceled_entry_orders ?? 'N/A',\n    },\n    {\n      'Replaced Entry Orders': result.replaced_entry_orders ?? 'N/A',\n    },\n\n    ...shortMetrics,\n\n    { ___: '___' },\n    { 'Min balance': formatPriceStake(result.csum_min) },\n    { 'Max balance': formatPriceStake(result.csum_max) },\n    { 'Market change': formatPercent(result.market_change) },\n    { '___  ': '___' },\n    {\n      'Max Drawdown (Account)': formatPercent(result.max_drawdown_account),\n    },\n    {\n      'Max Drawdown ABS': formatPriceStake(result.max_drawdown_abs),\n    },\n    {\n      'Drawdown high | low': `${formatPriceStake(result.max_drawdown_high)} | ${formatPriceStake(\n        result.max_drawdown_low,\n      )}`,\n    },\n    { 'Drawdown start': timestampms(result.drawdown_start_ts) },\n    { 'Drawdown end': timestampms(result.drawdown_end_ts) },\n    { '___   ': '___' },\n\n    {\n      'Best Pair': `${result.best_pair.key} ${formatPercent(result.best_pair.profit_total)}`,\n    },\n    {\n      'Worst Pair': `${result.worst_pair.key} ${formatPercent(result.worst_pair.profit_total)}`,\n    },\n    { 'Best single Trade': bestPair },\n    { 'Worst single Trade': worstPair },\n  ];\n  return tmp;\n}\n\nexport function generateBacktestSettingRows(result: StrategyBacktestResult) {\n  const formatPriceStake = useFormatPriceStake(\n    result.stake_currency_decimals,\n    result.stake_currency,\n  );\n\n  return [\n    { 'Backtesting from': timestampms(result.backtest_start_ts) },\n    { 'Backtesting to': timestampms(result.backtest_end_ts) },\n    {\n      'BT execution time': humanizeDurationFromSeconds(\n        result.backtest_run_end_ts - result.backtest_run_start_ts,\n      ),\n    },\n    { 'Max open trades': result.max_open_trades },\n    { Timeframe: result.timeframe },\n    { 'Timeframe Detail': result.timeframe_detail || 'N/A' },\n    { Timerange: result.timerange },\n    { Stoploss: formatPercent(result.stoploss, 2) },\n    { 'Trailing Stoploss': result.trailing_stop },\n    {\n      'Trail only when offset is reached': result.trailing_only_offset_is_reached,\n    },\n    { 'Trailing Stop positive': result.trailing_stop_positive },\n    {\n      'Trailing stop positive offset': result.trailing_stop_positive_offset,\n    },\n    { 'Custom Stoploss': result.use_custom_stoploss },\n    { ROI: JSON.stringify(result.minimal_roi) },\n    {\n      'Use Exit Signal':\n        result.use_exit_signal !== undefined ? result.use_exit_signal : result.use_sell_signal,\n    },\n    {\n      'Exit profit only':\n        result.exit_profit_only !== undefined ? result.exit_profit_only : result.sell_profit_only,\n    },\n    {\n      'Exit profit offset':\n        result.exit_profit_offset !== undefined\n          ? result.exit_profit_offset\n          : result.sell_profit_offset,\n    },\n    { 'Enable protections': result.enable_protections },\n    {\n      'Starting balance': formatPriceStake(result.starting_balance),\n    },\n    {\n      'Final balance': formatPriceStake(result.final_balance),\n    },\n    {\n      'Avg. stake amount': formatPriceStake(result.avg_stake_amount),\n    },\n    {\n      'Total trade volume': formatPriceStake(result.total_volume),\n    },\n  ];\n}\n", "<template>\n  <div class=\"px-0 mw-100\">\n    <div class=\"d-flex justify-content-center\">\n      <h3>Backtest-result comparison</h3>\n    </div>\n\n    <!-- <div class=\"d-flex\">\n      <div v-for=\"[key, result] in Object.entries(backtestResults)\" :key=\"key\" class=\"border m-1\">\n        <BacktestResultSelectEntry :backtest-result=\"result\" />\n      </div>\n    </div> -->\n    <div class=\"d-flex flex-column text-start ms-0 me-2 gap-2\">\n      <div class=\"d-flex flex-column flex-xl-row\">\n        <div class=\"px-0 px-xl-0 pt-2 pt-xl-0 ps-xl-1 flex-fill\">\n          <b-table bordered :items=\"backtestResultStats\" :fields=\"backtestResultFields\">\n            <template\n              v-for=\"[key, result] in Object.entries(backtestResults)\"\n              #[`head(${key})`]\n              :key=\"key\"\n            >\n              <BacktestResultSelectEntry :backtest-result=\"result\" />\n            </template>\n          </b-table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { BacktestResultInMemory } from '@/types';\nimport { formatObjectForTable } from '@/shared/objectToTableItems';\n\nimport { generateBacktestMetricRows } from '@/shared/backtestMetrics';\nimport { TableField } from 'bootstrap-vue-next';\n\nconst props = defineProps({\n  backtestResults: { required: true, type: Object as () => Record<string, BacktestResultInMemory> },\n});\n\nconst backtestResultStats = computed(() => {\n  const values = {};\n  Object.entries(props.backtestResults).forEach(([key, result]) => {\n    const tmp = generateBacktestMetricRows(result.strategy);\n    values[key] = tmp;\n  });\n  console.log(values);\n  // return '';\n  return formatObjectForTable(values, 'metric');\n});\n\nconst backtestResultFields = computed<TableField[]>(() => {\n  const res = [{ key: 'metric', label: 'Metric' }];\n  Object.entries(props.backtestResults).forEach(([key, value]) => {\n    res.push({ key, label: value.metadata.strategyName });\n  });\n  return res;\n});\n</script>\n\n<style lang=\"scss\" scoped></style>\n", "<script setup lang=\"ts\">\nimport { PeriodicBreakdown } from '@/types';\nimport { TableField, TableItem } from 'bootstrap-vue-next';\n\ndefineProps({\n  periodicBreakdown: {\n    type: Object as () => PeriodicBreakdown,\n    required: true,\n  },\n});\nconst periodicBreakdownSelections = [\n  { value: 'day', text: 'Days' },\n  { value: 'week', text: 'Weeks' },\n  { value: 'month', text: 'Months' },\n];\n\nconst periodicBreakdownPeriod = ref<string>('month');\n\nconst periodicBreakdownFields = computed<TableField[]>(() => {\n  return [\n    { key: 'date', label: 'Date' },\n    { key: 'wins', label: 'Wins' },\n    { key: 'draws', label: 'Draws' },\n    { key: 'loses', label: 'Losses' },\n  ];\n});\n</script>\n\n<template>\n  <b-form-radio-group\n    id=\"order-direction\"\n    v-model=\"periodicBreakdownPeriod\"\n    :options=\"periodicBreakdownSelections\"\n    name=\"radios-btn-default\"\n    size=\"sm\"\n    buttons\n    style=\"min-width: 10em\"\n    button-variant=\"outline-primary\"\n  ></b-form-radio-group>\n  <b-table\n    small\n    hover\n    stacked=\"sm\"\n    :items=\"periodicBreakdown[periodicBreakdownPeriod] as unknown as TableItem[]\"\n    :fields=\"periodicBreakdownFields\"\n  >\n  </b-table>\n</template>\n", "<script setup lang=\"ts\">\nimport { formatPercent, formatPrice } from '@/shared/formatters';\nimport type { ExitReasonResults, PairResult } from '@/types';\nimport { TableItem } from 'bootstrap-vue-next';\n\nconst props = defineProps({\n  title: { type: String, required: true },\n  results: { type: Array as PropType<(PairResult | ExitReasonResults)[]>, required: true },\n  stakeCurrency: { type: String, required: true },\n  stakeCurrencyDecimals: { type: Number, required: true },\n  keyHeader: { type: String, default: '' },\n  keyHeaders: { type: Array as PropType<string[]>, default: () => [] },\n});\n\nconst tableItems = computed(() => props.results as unknown as TableItem[]);\n\nconst perTagReason = computed(() => {\n  // TODO: should be TableField - but it's not working correctly\n  const firstFields: any[] = [];\n  if (props.keyHeaders.length > 0) {\n    // Keys could be an array\n    for (let i = 0; i < props.keyHeaders.length; i += 1) {\n      firstFields.push({\n        key: `key`,\n        label: props.keyHeaders[i],\n        formatter: (value, _, item) =>\n          Array.isArray(value) ? value[i] : value || item['exit_reason'] || 'OTHER',\n      });\n    }\n  } else {\n    firstFields.push({\n      key: 'key',\n      label: props.keyHeader,\n      formatter: (value, _, item) => (value || item['exit_reason'] || 'OTHER') as string,\n    });\n  }\n\n  return [\n    ...firstFields,\n    { key: 'trades', label: 'Trades' },\n    {\n      key: 'profit_mean',\n      label: 'Avg Profit %',\n      formatter: (value: number) => formatPercent(value, 2),\n    },\n    {\n      key: 'profit_total_abs',\n      label: `Tot Profit ${props.stakeCurrency}`,\n\n      formatter: (value) => formatPrice(value as number, props.stakeCurrencyDecimals),\n    },\n    {\n      key: 'profit_total',\n      label: 'Tot Profit %',\n      formatter: (value) => formatPercent(value as number, 2),\n    },\n    { key: 'wins', label: 'Wins' },\n    { key: 'draws', label: 'Draws' },\n    { key: 'losses', label: 'Losses' },\n  ];\n});\n</script>\n<template>\n  <b-card :header=\"title\">\n    <b-table small hover stacked=\"sm\" :items=\"tableItems\" :fields=\"perTagReason\"> </b-table>\n  </b-card>\n</template>\n", "<template>\n  <div class=\"px-0 mw-100\">\n    <div class=\"d-flex justify-content-center\">\n      <h3>Backtest-result for {{ backtestResult.strategy_name }}</h3>\n    </div>\n\n    <div class=\"d-flex flex-column text-start ms-0 me-2 gap-2\">\n      <div class=\"d-flex flex-column flex-xl-row\">\n        <div class=\"px-0 px-xl-0 pe-xl-1 flex-fill\">\n          <b-card header=\"Strategy settings\">\n            <b-table\n              small\n              borderless\n              :items=\"backtestResultSettings\"\n              :fields=\"backtestsettingFields\"\n            >\n            </b-table>\n          </b-card>\n        </div>\n        <div class=\"px-0 px-xl-0 pt-2 pt-xl-0 ps-xl-1 flex-fill\">\n          <b-card header=\"Metrics\">\n            <b-table small borderless :items=\"backtestResultStats\" :fields=\"backtestResultFields\">\n            </b-table>\n          </b-card>\n        </div>\n      </div>\n      <BacktestResultTablePer\n        title=\"Results per Enter tag\"\n        :results=\"backtestResult.results_per_enter_tag\"\n        :stake-currency=\"backtestResult.stake_currency\"\n        key-header=\"Enter Tag\"\n        :stake-currency-decimals=\"backtestResult.stake_currency_decimals\"\n      />\n\n      <BacktestResultTablePer\n        title=\"Results per Exit reason\"\n        :results=\"backtestResult.exit_reason_summary ?? []\"\n        :stake-currency=\"backtestResult.stake_currency\"\n        key-header=\"Exit Reason\"\n        :stake-currency-decimals=\"backtestResult.stake_currency_decimals\"\n      />\n\n      <BacktestResultTablePer\n        v-if=\"backtestResult.mix_tag_stats\"\n        title=\"Results Mixed Tag\"\n        :results=\"backtestResult.mix_tag_stats ?? []\"\n        :stake-currency=\"backtestResult.stake_currency\"\n        :key-headers=\"['Enter Tag', 'Exit Tag']\"\n        :stake-currency-decimals=\"backtestResult.stake_currency_decimals\"\n      />\n\n      <BacktestResultTablePer\n        title=\"Results per pair\"\n        :results=\"backtestResult.results_per_pair\"\n        :stake-currency=\"backtestResult.stake_currency\"\n        key-header=\"Pair\"\n        :stake-currency-decimals=\"backtestResult.stake_currency_decimals\"\n      />\n      <b-card v-if=\"backtestResult.periodic_breakdown\" header=\"Periodic breakdown\">\n        <BacktestResultPeriodBreakdown :periodic-breakdown=\"backtestResult.periodic_breakdown\">\n        </BacktestResultPeriodBreakdown>\n      </b-card>\n\n      <b-card header=\"Single trades\">\n        <TradeList\n          :trades=\"backtestResult.trades\"\n          :show-filter=\"true\"\n          :stake-currency=\"backtestResult.stake_currency\"\n        />\n      </b-card>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { StrategyBacktestResult } from '@/types';\nimport { formatObjectForTable } from '@/shared/objectToTableItems';\n\nimport { generateBacktestMetricRows, generateBacktestSettingRows } from '@/shared/backtestMetrics';\nimport { TableField } from 'bootstrap-vue-next';\n\nconst props = defineProps({\n  backtestResult: { required: true, type: Object as () => StrategyBacktestResult },\n});\n\nconst backtestResultStats = computed(() => {\n  const tmp = generateBacktestMetricRows(props.backtestResult);\n  return formatObjectForTable({ value: tmp }, 'metric');\n});\n\nconst backtestResultSettings = computed(() => {\n  // Transpose Result into readable format\n  const tmp = generateBacktestSettingRows(props.backtestResult);\n\n  return formatObjectForTable({ value: tmp }, 'setting');\n});\nconst backtestResultFields: TableField[] = [\n  { key: 'metric', label: 'Metric' },\n  { key: 'value', label: 'Value' },\n];\n\nconst backtestsettingFields: TableField[] = [\n  { key: 'setting', label: 'Setting' },\n  { key: 'value', label: 'Value' },\n];\n</script>\n\n<style lang=\"scss\" scoped></style>\n", "<template>\n  <div>\n    <div class=\"w-100 d-flex\">\n      <b-form-select\n        id=\"freqaiModel-select\"\n        v-model=\"locFreqaiModel\"\n        :options=\"botStore.activeBot.freqaiModelList\"\n      >\n      </b-form-select>\n      <div class=\"ms-2\">\n        <b-button @click=\"botStore.activeBot.getFreqAIModelList\">\n          <i-mdi-refresh />\n        </b-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst props = defineProps({\n  modelValue: { type: String, required: true },\n});\nconst emit = defineEmits(['update:modelValue']);\nconst botStore = useBotStore();\n\nconst locFreqaiModel = computed({\n  get() {\n    return props.modelValue;\n  },\n  set(freqaiModel: string) {\n    emit('update:modelValue', freqaiModel);\n  },\n});\n\nonMounted(() => {\n  if (botStore.activeBot.freqaiModelList.length === 0) {\n    botStore.activeBot.getFreqAIModelList();\n  }\n});\n</script>\n", "import { defineStore } from 'pinia';\n\nexport const useBtStore = defineStore('btStore', {\n  state: () => {\n    return {\n      strategy: '',\n      selectedTimeframe: '',\n      selectedDetailTimeframe: '',\n      timerange: '',\n      maxOpenTrades: '',\n      stakeAmount: '',\n      startingCapital: '',\n      allowCache: true,\n      enableProtections: false,\n      stakeAmountUnlimited: false,\n      freqAI: {\n        enabled: false,\n        model: '',\n        identifier: '',\n      },\n    };\n  },\n  getters: {\n    canRunBacktest: (state) => state.strategy !== '',\n  },\n  actions: {},\n});\n", "<template>\n  <div class=\"mb-2\">\n    <span>Strategy</span>\n    <StrategySelect v-model=\"btStore.strategy\"></StrategySelect>\n  </div>\n  <b-card :disabled=\"botStore.activeBot.backtestRunning\">\n    <!-- Backtesting parameters -->\n    <b-form-group\n      label-cols-lg=\"2\"\n      label=\"Backtest params\"\n      label-size=\"sm\"\n      label-class=\"fw-bold pt-0\"\n      class=\"mb-0\"\n    >\n      <b-form-group\n        label-cols-sm=\"5\"\n        label=\"Timeframe:\"\n        label-align-sm=\"right\"\n        label-for=\"timeframe-select\"\n      >\n        <TimeframeSelect id=\"timeframe-select\" v-model=\"btStore.selectedTimeframe\" />\n      </b-form-group>\n      <b-form-group\n        label-cols-sm=\"5\"\n        label=\"Detail Timeframe:\"\n        label-align-sm=\"right\"\n        label-for=\"timeframe-detail-select\"\n        title=\"Detail timeframe, to simulate intra-candle results. Not setting this will not use this functionality.\"\n      >\n        <TimeframeSelect\n          id=\"timeframe-detail-select\"\n          v-model=\"btStore.selectedDetailTimeframe\"\n          :below-timeframe=\"btStore.selectedTimeframe\"\n        />\n      </b-form-group>\n\n      <b-form-group\n        label-cols-sm=\"5\"\n        label=\"Max open trades:\"\n        label-align-sm=\"right\"\n        label-for=\"max-open-trades\"\n      >\n        <b-form-input\n          id=\"max-open-trades\"\n          v-model=\"btStore.maxOpenTrades\"\n          placeholder=\"Use strategy default\"\n          type=\"number\"\n        ></b-form-input>\n      </b-form-group>\n      <b-form-group\n        label-cols-sm=\"5\"\n        label=\"Starting capital:\"\n        label-align-sm=\"right\"\n        label-for=\"starting-capital\"\n      >\n        <b-form-input\n          id=\"starting-capital\"\n          v-model=\"btStore.startingCapital\"\n          placeholder=\"Use config default\"\n          type=\"number\"\n          step=\"0.001\"\n        ></b-form-input>\n      </b-form-group>\n      <b-form-group\n        label-cols-sm=\"5\"\n        label=\"Stake amount:\"\n        label-align-sm=\"right\"\n        label-for=\"stake-amount\"\n      >\n        <div class=\"d-flex align-items-center\">\n          <div style=\"flex-basis: 100%\" class=\"d-flex\">\n            <b-form-checkbox id=\"stake-amount-bool\" v-model=\"btStore.stakeAmountUnlimited\"\n              >Unlimited stake</b-form-checkbox\n            >\n          </div>\n          <b-form-input\n            id=\"stake-amount\"\n            v-model=\"btStore.stakeAmount\"\n            type=\"number\"\n            placeholder=\"Use strategy default\"\n            step=\"0.01\"\n            style=\"flex-basis: 100%\"\n            :disabled=\"btStore.stakeAmountUnlimited\"\n          ></b-form-input>\n        </div>\n      </b-form-group>\n\n      <b-form-group\n        label-cols-sm=\"5\"\n        label=\"Enable Protections:\"\n        label-align-sm=\"right\"\n        label-for=\"enable-protections\"\n        class=\"align-items-center\"\n      >\n        <b-form-checkbox\n          id=\"enable-protections\"\n          v-model=\"btStore.enableProtections\"\n        ></b-form-checkbox>\n      </b-form-group>\n      <b-form-group\n        v-if=\"botStore.activeBot.botApiVersion >= 2.22\"\n        label-cols-sm=\"5\"\n        label=\"Cache Backtest results:\"\n        label-align-sm=\"right\"\n        label-for=\"enable-cache\"\n        class=\"align-items-center\"\n      >\n        <b-form-checkbox id=\"enable-cache\" v-model=\"btStore.allowCache\"></b-form-checkbox>\n      </b-form-group>\n      <template v-if=\"botStore.activeBot.botApiVersion >= 2.22\">\n        <b-form-group\n          label-cols-sm=\"5\"\n          label=\"Enable FreqAI:\"\n          label-align-sm=\"right\"\n          label-for=\"enable-freqai\"\n          class=\"align-items-center\"\n        >\n          <template #label>\n            <div class=\"d-flex justify-content-center\">\n              <span class=\"me-2\">Enable FreqAI:</span>\n              <InfoBox\n                hint=\"Assumes freqAI configuration is setup in the configuration, and the strategy is a freqAI strategy. Will fail if that's not the case.\"\n              />\n            </div>\n          </template>\n          <b-form-checkbox id=\"enable-freqai\" v-model=\"btStore.freqAI.enabled\"></b-form-checkbox>\n        </b-form-group>\n        <b-form-group\n          v-if=\"btStore.freqAI.enabled\"\n          label-cols-sm=\"5\"\n          label=\"FreqAI identifier:\"\n          label-align-sm=\"right\"\n          label-for=\"freqai-identifier\"\n        >\n          <b-form-input\n            id=\"freqai-identifier\"\n            v-model=\"btStore.freqAI.identifier\"\n            placeholder=\"Use config default\"\n          ></b-form-input>\n        </b-form-group>\n        <b-form-group\n          v-if=\"btStore.freqAI.enabled\"\n          label-cols-sm=\"5\"\n          label=\"FreqAI Model\"\n          label-align-sm=\"right\"\n          label-for=\"freqai-model\"\n        >\n          <FreqaiModelSelect id=\"freqai-model\" v-model=\"btStore.freqAI.model\"></FreqaiModelSelect>\n        </b-form-group>\n      </template>\n\n      <!-- <b-form-group label-cols-sm=\"5\" label=\"Fee:\" label-align-sm=\"right\" label-for=\"fee\">\n              <b-form-input\n                id=\"fee\"\n                type=\"number\"\n                placeholder=\"Use exchange default\"\n                step=\"0.01\"\n              ></b-form-input>\n            </b-form-group> -->\n      <hr />\n      <TimeRangeSelect v-model=\"btStore.timerange\" class=\"mt-2\"></TimeRangeSelect>\n    </b-form-group>\n  </b-card>\n\n  <h3 class=\"mt-3\">Backtesting summary</h3>\n  <div class=\"d-flex flex-wrap flex-md-nowrap justify-content-between justify-content-md-center\">\n    <b-button\n      id=\"start-backtest\"\n      variant=\"primary\"\n      :disabled=\"\n        !btStore.canRunBacktest ||\n        botStore.activeBot.backtestRunning ||\n        !botStore.activeBot.canRunBacktest\n      \"\n      class=\"mx-1\"\n      @click=\"clickBacktest\"\n    >\n      Start backtest\n    </b-button>\n    <b-button\n      variant=\"secondary\"\n      :disabled=\"botStore.activeBot.backtestRunning || !botStore.activeBot.canRunBacktest\"\n      class=\"mx-1\"\n      @click=\"botStore.activeBot.pollBacktest\"\n    >\n      Load backtest result\n    </b-button>\n    <b-button\n      variant=\"secondary\"\n      class=\"mx-1\"\n      :disabled=\"!botStore.activeBot.backtestRunning\"\n      @click=\"botStore.activeBot.stopBacktest\"\n      >Stop Backtest</b-button\n    >\n    <b-button\n      variant=\"secondary\"\n      class=\"mx-1\"\n      :disabled=\"botStore.activeBot.backtestRunning || !botStore.activeBot.canRunBacktest\"\n      @click=\"botStore.activeBot.removeBacktest\"\n      >Reset Backtest</b-button\n    >\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { BacktestPayload } from '@/types';\n\nimport { useBtStore } from '@/stores/btStore';\nconst botStore = useBotStore();\nconst btStore = useBtStore();\n\nfunction clickBacktest() {\n  const btPayload: BacktestPayload = {\n    strategy: btStore.strategy,\n    timerange: btStore.timerange,\n    enable_protections: btStore.enableProtections,\n  };\n  const openTradesInt = parseInt(btStore.maxOpenTrades, 10);\n  if (openTradesInt) {\n    btPayload.max_open_trades = openTradesInt;\n  }\n  if (btStore.stakeAmountUnlimited) {\n    btPayload.stake_amount = 'unlimited';\n  } else {\n    const stakeAmountLoc = Number(btStore.stakeAmount);\n    if (stakeAmountLoc) {\n      btPayload.stake_amount = stakeAmountLoc.toString();\n    }\n  }\n\n  const startingCapitalLoc = Number(btStore.startingCapital);\n  if (startingCapitalLoc) {\n    btPayload.dry_run_wallet = startingCapitalLoc;\n  }\n\n  if (btStore.selectedTimeframe) {\n    btPayload.timeframe = btStore.selectedTimeframe;\n  }\n  if (btStore.selectedDetailTimeframe) {\n    btPayload.timeframe_detail = btStore.selectedDetailTimeframe;\n  }\n  if (!btStore.allowCache) {\n    btPayload.backtest_cache = 'none';\n  }\n  if (btStore.freqAI.enabled) {\n    btPayload.freqaimodel = btStore.freqAI.model;\n    if (btStore.freqAI.identifier !== '') {\n      btPayload.freqai = { identifier: btStore.freqAI.identifier };\n    }\n  }\n\n  botStore.activeBot.startBacktest(btPayload);\n}\n</script>\n\n<style scoped></style>\n", "<template>\n  <div>\n    <button\n      class=\"btn btn-secondary float-end\"\n      title=\"Refresh\"\n      aria-label=\"Refresh\"\n      @click=\"botStore.activeBot.getBacktestHistory\"\n    >\n      <i-mdi-refresh />\n    </button>\n    <p>\n      Load Historic results from disk. You can click on multiple results to load all of them into\n      freqUI.\n    </p>\n    <div class=\"d-flex align-items-center\">\n      <b-form-group\n        v-if=\"botStore.activeBot.backtestHistoryList.length > 0\"\n        class=\"my-2\"\n        label-for=\"trade-filter\"\n      >\n        <b-form-input\n          id=\"trade-filter\"\n          v-model=\"filterText\"\n          type=\"text\"\n          placeholder=\"Filter Strategies\"\n          tilte=\"Filter Strategies\"\n        />\n      </b-form-group>\n    </div>\n    <BTableSimple\n      v-if=\"botStore.activeBot.backtestHistoryList.length > 0\"\n      responsive\n      small\n      class=\"rounded-1 table-rounded-corners\"\n    >\n      <BThead>\n        <BTr>\n          <BTh>Strategy</BTh>\n          <BTh>Details</BTh>\n          <BTh>Backtest Time</BTh>\n          <BTh>Filename</BTh>\n          <BTh>Actions</BTh>\n        </BTr>\n      </BThead>\n      <BTbody>\n        <BTr\n          v-for=\"res in botStore.activeBot.backtestHistoryList.filter(\n            (r) =>\n              r.filename.toLowerCase().includes(filterTextDebounced.toLowerCase()) ||\n              r.strategy.toLowerCase().includes(filterTextDebounced.toLowerCase()),\n          )\"\n          :key=\"res.filename + res.strategy\"\n          role=\"button\"\n          @click=\"botStore.activeBot.getBacktestHistoryResult(res)\"\n        >\n          <BTd>{{ res.strategy }}</BTd>\n          <BTd>\n            <strong>{{ res.timeframe }}</strong>\n            <span v-if=\"res.backtest_start_ts && res.backtest_end_ts\" class=\"ms-1\">\n              {{ timestampToTimeRangeString(res.backtest_start_ts * 1000) }}-{{\n                timestampToTimeRangeString(res.backtest_end_ts * 1000)\n              }}</span\n            >\n          </BTd>\n          <BTd\n            ><small>{{ timestampms(res.backtest_start_time * 1000) }}</small></BTd\n          >\n          <BTd>\n            <small>{{ res.filename }}</small>\n          </BTd>\n          <BTd>\n            <div class=\"d-flex align-items-center\">\n              <InfoBox\n                v-if=\"botStore.activeBot.botApiVersion >= 2.32\"\n                :class=\"res.notes ? 'opacity-100' : 'opacity-0'\"\n                :hint=\"res.notes ?? ''\"\n              ></InfoBox>\n              <b-button\n                v-if=\"botStore.activeBot.botApiVersion >= 2.31\"\n                class=\"ms-1\"\n                size=\"sm\"\n                title=\"Load this Result.\"\n                :disabled=\"res.run_id in botStore.activeBot.backtestHistory\"\n                @click.stop=\"botStore.activeBot.getBacktestHistoryResult(res)\"\n              >\n                <i-mdi-arrow-right />\n              </b-button>\n              <b-button\n                v-if=\"botStore.activeBot.botApiVersion >= 2.31\"\n                class=\"ms-1\"\n                size=\"sm\"\n                title=\"Delete this Result.\"\n                :disabled=\"res.run_id in botStore.activeBot.backtestHistory\"\n                @click.stop=\"deleteBacktestResult(res)\"\n              >\n                <i-mdi-delete />\n              </b-button>\n            </div>\n          </BTd>\n        </BTr>\n      </BTbody>\n    </BTableSimple>\n  </div>\n  <MessageBox ref=\"msgBox\" />\n</template>\n\n<script setup lang=\"ts\">\nimport MessageBox, { MsgBoxObject } from '@/components/general/MessageBox.vue';\nimport { timestampms, timestampToTimeRangeString } from '@/shared/formatters';\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { BacktestHistoryEntry } from '@/types';\nimport InfoBox from '../general/InfoBox.vue';\n\nconst botStore = useBotStore();\nconst msgBox = ref<typeof MessageBox>();\nconst filterText = ref('');\nconst filterTextDebounced = refDebounced(filterText, 350, { maxWait: 1000 });\n\nonMounted(() => {\n  botStore.activeBot.getBacktestHistory();\n});\n\nfunction deleteBacktestResult(result: BacktestHistoryEntry) {\n  const msg: MsgBoxObject = {\n    title: 'Delete result',\n    message: `Delete result ${result.filename} from disk?`,\n    accept: () => {\n      botStore.activeBot.deleteBacktestHistoryResult(result);\n    },\n  };\n  msgBox.value?.show(msg);\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.table-rounded-corners {\n  box-shadow: 0 0 0 1px var(--bs-border-color);\n  overflow: hidden;\n  vertical-align: middle;\n}\n</style>\n", "<template>\n  <div class=\"container d-flex flex-column align-items-stretch\">\n    <h3>Available results:</h3>\n    <b-list-group class=\"ms-2\">\n      <b-list-group-item\n        v-for=\"[key, result] in Object.entries(backtestHistory)\"\n        :key=\"key\"\n        button\n        :active=\"key === selectedBacktestResultKey\"\n        class=\"d-flex justify-content-between align-items-center py-1 pe-1\"\n        @click=\"setBacktestResult(key)\"\n      >\n        <template v-if=\"!result.metadata.editing\">\n          <BacktestResultSelectEntry :backtest-result=\"result\" />\n          <div class=\"d-flex\">\n            <b-button\n              v-if=\"canUseModify\"\n              class=\"flex-nowrap\"\n              size=\"sm\"\n              title=\"Modify result notes.\"\n              @click.stop=\"result.metadata.editing = !result.metadata.editing\"\n            >\n              <i-mdi-pencil />\n            </b-button>\n            <b-button\n              size=\"sm\"\n              class=\"flex-nowrap\"\n              title=\"Delete this Result from UI.\"\n              @click.stop=\"emit('removeResult', key)\"\n            >\n              <i-mdi-delete />\n            </b-button>\n          </div>\n        </template>\n        <template v-if=\"result.metadata.editing\">\n          <b-form-textarea v-model=\"result.metadata.notes\" placeholder=\"notes\" size=\"sm\">\n          </b-form-textarea>\n\n          <b-button size=\"sm\" title=\"Confirm\" @click.stop=\"confirmInput(key, result)\">\n            <i-mdi-check />\n          </b-button>\n        </template>\n      </b-list-group-item>\n    </b-list-group>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { BacktestResultInMemory, BacktestResultUpdate } from '@/types';\n\ndefineProps({\n  backtestHistory: {\n    required: true,\n    type: Object as () => Record<string, BacktestResultInMemory>,\n  },\n  selectedBacktestResultKey: { required: false, default: '', type: String },\n  canUseModify: { required: false, default: false, type: Boolean },\n});\nconst emit = defineEmits<{\n  selectionChange: [value: string];\n  removeResult: [value: string];\n  updateResult: [value: BacktestResultUpdate];\n}>();\n\nconst setBacktestResult = (key: string) => {\n  emit('selectionChange', key);\n};\n\nfunction confirmInput(run_id: string, result: BacktestResultInMemory) {\n  result.metadata.editing = !result.metadata.editing;\n  if (result.metadata.filename) {\n    emit('updateResult', {\n      run_id: run_id,\n      notes: result.metadata.notes ?? '',\n      filename: result.metadata.filename,\n      strategy: result.metadata.strategyName,\n    });\n  }\n}\n</script>\n\n<style scoped></style>\n", "<template>\n  <div class=\"d-flex flex-column pt-1 me-1\" style=\"height: calc(100vh - 60px)\">\n    <div>\n      <div class=\"d-flex flex-row\">\n        <h2 class=\"ms-5\">Backtesting</h2>\n        <p v-if=\"!botStore.activeBot.canRunBacktest\">\n          Bot must be in webserver mode to enable Backtesting.\n        </p>\n        <div class=\"w-100\">\n          <div\n            class=\"mx-md-5 d-flex flex-wrap justify-content-md-center justify-content-between mb-4 gap-2\"\n          >\n            <b-form-radio\n              v-if=\"botStore.activeBot.botApiVersion >= 2.15\"\n              v-model=\"btFormMode\"\n              name=\"bt-form-radios\"\n              button\n              class=\"mx-1 flex-samesize-items\"\n              value=\"historicResults\"\n              :disabled=\"!botStore.activeBot.canRunBacktest\"\n              ><i-mdi-cloud-download class=\"me-2\" />Load Results</b-form-radio\n            >\n            <b-form-radio\n              v-model=\"btFormMode\"\n              name=\"bt-form-radios\"\n              button\n              class=\"mx-1 flex-samesize-items\"\n              value=\"run\"\n              :disabled=\"!botStore.activeBot.canRunBacktest\"\n              ><i-mdi-run-fast class=\"me-2\" />Run backtest</b-form-radio\n            >\n            <b-form-radio\n              id=\"bt-analyze-btn\"\n              v-model=\"btFormMode\"\n              name=\"bt-form-radios\"\n              button\n              class=\"mx-1 flex-samesize-items\"\n              value=\"results\"\n              :disabled=\"!hasBacktestResult\"\n              ><i-mdi-table-eye class=\"me-2\" />Analyze result</b-form-radio\n            >\n            <b-form-radio\n              v-if=\"hasMultiBacktestResult\"\n              v-model=\"btFormMode\"\n              name=\"bt-form-radios\"\n              button\n              class=\"mx-1 flex-samesize-items\"\n              value=\"compare-results\"\n              :disabled=\"!hasMultiBacktestResult\"\n              ><i-mdi-compare-horizontal class=\"me-2\" />Compare results</b-form-radio\n            >\n            <b-form-radio\n              v-model=\"btFormMode\"\n              name=\"bt-form-radios\"\n              button\n              class=\"mx-1 flex-samesize-items\"\n              value=\"visualize-summary\"\n              :disabled=\"!hasBacktestResult\"\n              ><i-mdi-chart-bell-curve-cumulative class=\"me-2\" />Visualize summary</b-form-radio\n            >\n            <b-form-radio\n              v-model=\"btFormMode\"\n              name=\"bt-form-radios\"\n              button\n              class=\"mx-1 flex-samesize-items\"\n              value=\"visualize\"\n              :disabled=\"!hasBacktestResult\"\n              ><i-mdi-chart-timeline-variant-shimmer class=\"me-2\" />Visualize result</b-form-radio\n            >\n          </div>\n          <small v-show=\"botStore.activeBot.backtestRunning\" class=\"text-end bt-running-label\"\n            >Backtest running: {{ botStore.activeBot.backtestStep }}\n            {{ formatPercent(botStore.activeBot.backtestProgress, 2) }}</small\n          >\n        </div>\n      </div>\n    </div>\n    <div class=\"d-flex flex-md-row\">\n      <!-- Left bar -->\n      <div\n        v-if=\"btFormMode !== 'visualize'\"\n        :class=\"`${showLeftBar ? 'col-md-3' : ''}`\"\n        class=\"sticky-top sticky-offset me-3 d-flex flex-column absolute\"\n        style=\"max-height: calc(100vh - 60px)\"\n      >\n        <b-button\n          class=\"align-self-start\"\n          aria-label=\"Close\"\n          size=\"sm\"\n          variant=\"outline-secondary\"\n          @click=\"showLeftBar = !showLeftBar\"\n        >\n          <i-mdi-chevron-right v-if=\"!showLeftBar\" width=\"24\" height=\"24\" />\n          <i-mdi-chevron-left v-if=\"showLeftBar\" width=\"24\" height=\"24\" />\n        </b-button>\n        <transition name=\"fade\">\n          <BacktestResultSelect\n            v-if=\"showLeftBar\"\n            :backtest-history=\"botStore.activeBot.backtestHistory\"\n            :selected-backtest-result-key=\"botStore.activeBot.selectedBacktestResultKey\"\n            :can-use-modify=\"botStore.activeBot.botApiVersion >= 2.32\"\n            @selection-change=\"botStore.activeBot.setBacktestResultKey\"\n            @remove-result=\"botStore.activeBot.removeBacktestResultFromMemory\"\n            @update-result=\"botStore.activeBot.saveBacktestResultMetadata\"\n          />\n        </transition>\n      </div>\n      <!-- End Left bar -->\n      <div class=\"d-flex flex-column flex-fill mw-100\">\n        <div class=\"d-md-flex\">\n          <div\n            v-if=\"btFormMode === 'historicResults'\"\n            class=\"flex-fill d-flex flex-column bt-config\"\n          >\n            <BacktestHistoryLoad />\n          </div>\n          <div v-if=\"btFormMode === 'run'\" class=\"flex-fill d-flex flex-column bt-config\">\n            <BacktestRun />\n          </div>\n          <BacktestResultAnalysis\n            v-if=\"hasBacktestResult && btFormMode === 'results'\"\n            :backtest-result=\"botStore.activeBot.selectedBacktestResult\"\n            class=\"flex-fill\"\n          />\n\n          <BacktestResultComparison\n            v-if=\"hasBacktestResult && btFormMode === 'compare-results'\"\n            :backtest-results=\"botStore.activeBot.backtestHistory\"\n            class=\"flex-fill\"\n          />\n\n          <BacktestGraphs\n            v-if=\"hasBacktestResult && btFormMode === 'visualize-summary'\"\n            :trades=\"botStore.activeBot.selectedBacktestResult.trades\"\n            class=\"flex-fill\"\n          />\n        </div>\n\n        <div v-if=\"hasBacktestResult && btFormMode === 'visualize'\" class=\"text-center w-100 mt-2\">\n          <BacktestResultChart\n            :timeframe=\"timeframe\"\n            :strategy=\"btStore.strategy\"\n            :timerange=\"btStore.timerange\"\n            :pairlist=\"botStore.activeBot.selectedBacktestResult.pairlist\"\n            :trades=\"botStore.activeBot.selectedBacktestResult.trades\"\n            :freqai-model=\"btStore.freqAI.enabled ? btStore.freqAI.model : undefined\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent } from '@/shared/formatters';\nimport { useBtStore } from '@/stores/btStore';\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nenum BtRunModes {\n  run = 'run',\n  results = 'results',\n  visualize = 'visualize',\n  visualizesummary = 'visualize-summary',\n  compareresults = 'compare-results',\n  historicresults = 'historicResults',\n}\n\nconst botStore = useBotStore();\nconst btStore = useBtStore();\n\nconst hasBacktestResult = computed(() =>\n  botStore.activeBot.backtestHistory\n    ? Object.keys(botStore.activeBot.backtestHistory).length !== 0\n    : false,\n);\nconst hasMultiBacktestResult = computed(() =>\n  botStore.activeBot.backtestHistory\n    ? Object.keys(botStore.activeBot.backtestHistory).length > 1\n    : false,\n);\n\nconst timeframe = computed((): string => {\n  try {\n    return botStore.activeBot.selectedBacktestResult.timeframe;\n  } catch (err) {\n    return '';\n  }\n});\n\nconst showLeftBar = ref(false);\n\nconst btFormMode = ref<BtRunModes>(BtRunModes.run);\nconst pollInterval = ref<number | null>(null);\n\nconst selectBacktestResult = () => {\n  // Set parameters for this result\n  btStore.strategy = botStore.activeBot.selectedBacktestResult.strategy_name;\n  botStore.activeBot.getStrategy(btStore.strategy);\n  btStore.selectedTimeframe = botStore.activeBot.selectedBacktestResult.timeframe;\n  btStore.selectedDetailTimeframe =\n    botStore.activeBot.selectedBacktestResult.timeframe_detail || '';\n  // TODO: maybe this should not use timerange, but the actual backtest start/end results instead?\n  btStore.timerange = botStore.activeBot.selectedBacktestResult.timerange;\n};\n\nwatch(\n  () => botStore.activeBot.selectedBacktestResultKey,\n  () => {\n    selectBacktestResult();\n  },\n);\n\nonMounted(() => botStore.activeBot.getState());\nwatch(\n  () => botStore.activeBot.backtestRunning,\n  () => {\n    if (botStore.activeBot.backtestRunning === true) {\n      pollInterval.value = window.setInterval(botStore.activeBot.pollBacktest, 1000);\n    } else if (pollInterval.value) {\n      clearInterval(pollInterval.value);\n      pollInterval.value = null;\n    }\n  },\n);\n</script>\n\n<style lang=\"scss\" scoped>\n.bt-running-label {\n  position: absolute;\n  right: 2em;\n  margin-top: 1em;\n}\n\n.sticky-offset {\n  top: 2em;\n}\n.flex-samesize-items {\n  flex: 1 1 0;\n  @media md {\n    flex: unset;\n  }\n}\n\n.fade-enter-active,\n.fade-leave-active {\n  transition: all 0.2s;\n}\n\n.fade-enter,\n.fade-leave-to {\n  opacity: 0;\n}\n\n.bt-config {\n  @media (min-width: 992px) {\n    margin-left: auto;\n    margin-right: auto;\n    max-width: 75vw;\n  }\n}\n</style>\n"], "names": ["props", "__props", "emit", "__emit", "botStore", "useBotStore", "selected<PERSON><PERSON>", "ref", "sortNewestFirst", "onTradeSelect", "trade", "sortedTrades", "computed", "a", "ordersVisible", "watch", "isBarVisible", "sliderPosition", "navigateChartToTrade", "formatObjectForTable", "<PERSON><PERSON><PERSON>", "valueKey", "acc", "key", "value", "item", "metric", "val", "existingItem", "i", "getSortedTrades", "trades", "b", "getBestPair", "formatPercent", "getWorstPair", "useFormatPriceStake", "stake_currency_decimals", "stake_currency", "price", "formatPrice", "generateBacktestMetricRows", "result", "bestPair", "worstPair", "pair<PERSON><PERSON><PERSON><PERSON>", "formatPriceStake", "shortMetrics", "isNotUndefined", "humanizeDurationFromSeconds", "timestampms", "generateBacktestSettingRows", "backtestResultStats", "values", "tmp", "backtest<PERSON><PERSON><PERSON>Fields", "res", "periodicBreakdownSelections", "periodicBreakdownPeriod", "periodicBreakdownFields", "tableItems", "perTagReason", "firstFields", "_", "backtestResultSettings", "backtestsettingFields", "locFreqaiModel", "freqaiModel", "onMounted", "useBtStore", "defineStore", "state", "btStore", "clickBacktest", "btPayload", "openTradesInt", "stakeAmountLoc", "startingCapitalLoc", "msgBox", "filterText", "filterTextDebounced", "refDebounced", "deleteBacktestResult", "msg", "_a", "setBacktestResult", "confirmInput", "run_id", "hasBacktestResult", "hasMultiBacktestResult", "timeframe", "showLeftBar", "btFormMode", "pollInterval", "selectBacktestResult"], "mappings": "0sDAgEA,MAAMA,EAAQC,EAIRC,EAAOC,EAEPC,EAAWC,IACXC,EAAgBC,EAAI,CAAA,CAAW,EAC/BC,EAAkBD,EAAI,EAAI,EAE1BE,EAAiBC,GAAiB,CACtCJ,EAAc,MAAQI,EACtBR,EAAK,eAAgBQ,CAAK,CAAA,EAGtBC,EAAeC,EAAS,IACrBZ,EAAM,OACV,MAAA,EACA,KAAK,CAACa,EAAG,IACRL,EAAgB,MACZ,EAAE,eAAiBK,EAAE,eACrBA,EAAE,eAAiB,EAAE,cAAA,CAE9B,EAEKC,EAAgBP,EAAII,EAAa,MAAM,IAAI,IAAM,EAAK,CAAC,EAE7D,OAAAI,EACE,IAAMX,EAAS,UAAU,aACzB,IAAM,CACJU,EAAc,MAAQH,EAAa,MAAM,IAAI,IAAM,EAAK,CAC1D,CAAA,6+ECVF,MAAMP,EAAWC,IACXW,EAAeT,EAAI,CAAE,MAAO,GAAM,KAAM,GAAM,EAC9CU,EAAiBV,IAEjBW,EAAwBR,GAAiB,CAC7CO,EAAe,MAAQ,CACrB,WAAYP,EAAM,eAClB,SAAUA,EAAM,eAAA,CAClB,mrGChFc,SAAAS,GAAqBC,EAA6BC,EAAkB,CAgB3E,OAfQ,OAAO,QAAQD,CAAW,EAAE,OAAO,CAACE,EAAqB,CAACC,EAAKC,CAAK,KAC3EA,EAAA,QAASC,GAAS,CAChB,KAAA,CAACC,EAAQC,CAAG,EAAI,OAAO,QAAQF,CAAI,EAAE,CAAC,EACtCG,EAAeN,EAAI,KAAMO,GAAMA,EAAER,CAAQ,IAAMK,CAAM,EACvDE,EACFA,EAAaL,CAAG,EAAII,EAEpBL,EAAI,KAAK,CACP,CAACD,CAAQ,EAAGK,EACZ,CAACH,CAAG,EAAGI,CAAA,CACR,CACH,CACD,EACML,GACN,CAAE,CAAA,CAEP,CCrBA,SAASQ,GAAgBC,EAA0B,CAE1C,OADcA,EAAO,MAAA,EAAQ,KAAK,CAAClB,EAAGmB,IAAMnB,EAAE,aAAemB,EAAE,YAAY,CAEpF,CAEA,SAASC,GAAYF,EAAiB,CAChC,GAAAA,EAAO,SAAW,EACb,MAAA,MAET,MAAMP,EAAQO,EAAOA,EAAO,OAAS,CAAC,EAC/B,MAAA,GAAGP,EAAM,IAAI,IAAIU,EAAcV,EAAM,aAAc,CAAC,CAAC,EAC9D,CAEA,SAASW,GAAaJ,EAAiB,CACjC,GAAAA,EAAO,SAAW,EACb,MAAA,MAEH,MAAAP,EAAQO,EAAO,CAAC,EACf,MAAA,GAAGP,EAAM,IAAI,IAAIU,EAAcV,EAAM,aAAc,CAAC,CAAC,EAC9D,CAEA,SAASY,GAAoBC,EAAiCC,EAAwB,CAI7E,OAHmBC,GACjB,GAAGC,GAAYD,EAAOF,CAAuB,CAAC,IAAIC,CAAc,EAG3E,CAEO,SAASG,GAA2BC,EAAgC,CACnE,MAAA/B,EAAemB,GAAgBY,EAAO,MAAM,EAC5CC,EAAWV,GAAYtB,CAAY,EACnCiC,EAAYT,GAAaxB,CAAY,EACrCkC,EAAcH,EAAO,iBAAiBA,EAAO,iBAAiB,OAAS,CAAC,EAExEI,EAAmBV,GACvBM,EAAO,wBACPA,EAAO,cAAA,EAIHK,EACJL,EAAO,mBAAqBA,EAAO,kBAAoB,EACnD,CACE,CAAE,OAAQ,KAAM,EAChB,CACE,eAAgB,GAAGA,EAAO,gBAAgB,MAAMA,EAAO,iBAAiB,EAC1E,EACA,CACE,oBAAqB,GAAGR,EACtBQ,EAAO,mBAAqB,CAC7B,CAAA,MAAMI,EAAiBJ,EAAO,qBAAqB,CAAC,EACvD,EACA,CACE,qBAAsB,GAAGR,EACvBQ,EAAO,oBAAsB,CAC9B,CAAA,MAAMI,EAAiBJ,EAAO,sBAAsB,CAAC,EACxD,GAEF,GAwHC,MAtHK,CACV,CACE,eAAgB,GAAGR,EAAcQ,EAAO,YAAY,CAAC,MAAMI,EACzDJ,EAAO,gBAAA,CACR,EACH,EACA,CACE,KAAM,GAAGA,EAAO,KAAOR,EAAcQ,EAAO,IAAI,EAAI,KAAK,EAC3D,EACA,CACE,QAAS,GAAGA,EAAO,QAAUA,EAAO,QAAQ,QAAQ,CAAC,EAAI,KAAK,EAChE,EACA,CACE,OAAQ,GAAGA,EAAO,OAASA,EAAO,OAAO,QAAQ,CAAC,EAAI,KAAK,EAC7D,EACA,CACE,OAAQ,GAAGA,EAAO,OAASA,EAAO,OAAO,QAAQ,CAAC,EAAI,KAAK,EAC7D,EACA,CACE,CAAC,cAAcA,EAAO,iBAAmB,UAAY,EAAE,EAAE,EAAG,GAC1DA,EAAO,WACHA,EAAO,iBACLA,EAAO,WAAW,QAAQ,CAAC,EAAI,KAAOA,EAAO,iBAAiB,QAAQ,CAAC,EAAI,IAC3EA,EAAO,WAAW,QAAQ,CAAC,EAC7B,KACN,EACF,EACA,CACE,gBAAiB,GAAGA,EAAO,cAAgBF,GAAYE,EAAO,cAAe,CAAC,EAAI,KAAK,EACzF,EACA,CACE,kCAAmC,GAAGA,EAAO,YAAY,MAAMA,EAAO,cAAc,EACtF,EAGA,CACE,WAAY,GAAGR,EAAcQ,EAAO,kBAAmB,CAAC,CAAC,MAAMI,EAC7DJ,EAAO,qBAAA,CACR,EACH,EACA,CACE,YAAa,GAAGR,EAAcQ,EAAO,mBAAoB,CAAC,CAAC,MAAMI,EAC/DJ,EAAO,sBAAA,CACR,EACH,EAEA,CACE,gBAAiB,GAAGG,EAAY,IAAI,MAAMA,EAAY,KAAK,MAAMA,EAAY,MAAM,IACjFG,GAAeH,EAAY,OAAO,EAC9B,QACAX,EACEQ,EAAO,iBAAiBA,EAAO,iBAAiB,OAAS,CAAC,EAAE,SAAW,EACvE,CAAA,EAEF,IACA,EACN,EACF,EACA,CACE,qBAAsB,GAAGA,EAAO,YAAY,MAAMA,EAAO,SAAS,MAAMA,EAAO,WAAW,EAC5F,EACA,CACE,wBAAyBO,GAA4BP,EAAO,oBAAoB,CAClF,EACA,CACE,uBAAwBO,GAA4BP,EAAO,mBAAmB,CAChF,EACA,CACE,8BACEA,EAAO,uBAAyB,OAC5B,MACA,GAAGA,EAAO,oBAAoB,MAAMA,EAAO,sBAAsB,EACzE,EACA,CAAE,yBAA0BA,EAAO,gBAAiB,EACpD,CACE,sBAAuB,GAAGA,EAAO,qBAAqB,MAAMA,EAAO,oBAAoB,EACzF,EACA,CACE,yBAA0BA,EAAO,wBAA0B,KAC7D,EACA,CACE,wBAAyBA,EAAO,uBAAyB,KAC3D,EACA,CACE,wBAAyBA,EAAO,uBAAyB,KAC3D,EAEA,GAAGK,EAEH,CAAE,IAAK,KAAM,EACb,CAAE,cAAeD,EAAiBJ,EAAO,QAAQ,CAAE,EACnD,CAAE,cAAeI,EAAiBJ,EAAO,QAAQ,CAAE,EACnD,CAAE,gBAAiBR,EAAcQ,EAAO,aAAa,CAAE,EACvD,CAAE,QAAS,KAAM,EACjB,CACE,yBAA0BR,EAAcQ,EAAO,oBAAoB,CACrE,EACA,CACE,mBAAoBI,EAAiBJ,EAAO,gBAAgB,CAC9D,EACA,CACE,sBAAuB,GAAGI,EAAiBJ,EAAO,iBAAiB,CAAC,MAAMI,EACxEJ,EAAO,gBAAA,CACR,EACH,EACA,CAAE,iBAAkBQ,EAAYR,EAAO,iBAAiB,CAAE,EAC1D,CAAE,eAAgBQ,EAAYR,EAAO,eAAe,CAAE,EACtD,CAAE,SAAU,KAAM,EAElB,CACE,YAAa,GAAGA,EAAO,UAAU,GAAG,IAAIR,EAAcQ,EAAO,UAAU,YAAY,CAAC,EACtF,EACA,CACE,aAAc,GAAGA,EAAO,WAAW,GAAG,IAAIR,EAAcQ,EAAO,WAAW,YAAY,CAAC,EACzF,EACA,CAAE,oBAAqBC,CAAS,EAChC,CAAE,qBAAsBC,CAAU,CAAA,CAGtC,CAEO,SAASO,GAA4BT,EAAgC,CAC1E,MAAMI,EAAmBV,GACvBM,EAAO,wBACPA,EAAO,cAAA,EAGF,MAAA,CACL,CAAE,mBAAoBQ,EAAYR,EAAO,iBAAiB,CAAE,EAC5D,CAAE,iBAAkBQ,EAAYR,EAAO,eAAe,CAAE,EACxD,CACE,oBAAqBO,GACnBP,EAAO,oBAAsBA,EAAO,qBACtC,CACF,EACA,CAAE,kBAAmBA,EAAO,eAAgB,EAC5C,CAAE,UAAWA,EAAO,SAAU,EAC9B,CAAE,mBAAoBA,EAAO,kBAAoB,KAAM,EACvD,CAAE,UAAWA,EAAO,SAAU,EAC9B,CAAE,SAAUR,EAAcQ,EAAO,SAAU,CAAC,CAAE,EAC9C,CAAE,oBAAqBA,EAAO,aAAc,EAC5C,CACE,oCAAqCA,EAAO,+BAC9C,EACA,CAAE,yBAA0BA,EAAO,sBAAuB,EAC1D,CACE,gCAAiCA,EAAO,6BAC1C,EACA,CAAE,kBAAmBA,EAAO,mBAAoB,EAChD,CAAE,IAAK,KAAK,UAAUA,EAAO,WAAW,CAAE,EAC1C,CACE,kBACEA,EAAO,kBAAoB,OAAYA,EAAO,gBAAkBA,EAAO,eAC3E,EACA,CACE,mBACEA,EAAO,mBAAqB,OAAYA,EAAO,iBAAmBA,EAAO,gBAC7E,EACA,CACE,qBACEA,EAAO,qBAAuB,OAC1BA,EAAO,mBACPA,EAAO,kBACf,EACA,CAAE,qBAAsBA,EAAO,kBAAmB,EAClD,CACE,mBAAoBI,EAAiBJ,EAAO,gBAAgB,CAC9D,EACA,CACE,gBAAiBI,EAAiBJ,EAAO,aAAa,CACxD,EACA,CACE,oBAAqBI,EAAiBJ,EAAO,gBAAgB,CAC/D,EACA,CACE,qBAAsBI,EAAiBJ,EAAO,YAAY,CAC5D,CAAA,CAEJ,sYCnNA,MAAM1C,EAAQC,EAIRmD,EAAsBxC,EAAS,IAAM,CACzC,MAAMyC,EAAS,CAAA,EACR,cAAA,QAAQrD,EAAM,eAAe,EAAE,QAAQ,CAAC,CAACuB,EAAKmB,CAAM,IAAM,CACzD,MAAAY,EAAMb,GAA2BC,EAAO,QAAQ,EACtDW,EAAO9B,CAAG,EAAI+B,CAAA,CACf,EACD,QAAQ,IAAID,CAAM,EAEXlC,GAAqBkC,EAAQ,QAAQ,CAAA,CAC7C,EAEKE,EAAuB3C,EAAuB,IAAM,CACxD,MAAM4C,EAAM,CAAC,CAAE,IAAK,SAAU,MAAO,SAAU,EACxC,cAAA,QAAQxD,EAAM,eAAe,EAAE,QAAQ,CAAC,CAACuB,EAAKC,CAAK,IAAM,CAC9DgC,EAAI,KAAK,CAAE,IAAAjC,EAAK,MAAOC,EAAM,SAAS,aAAc,CAAA,CACrD,EACMgC,CAAA,CACR,uaC/CD,MAAMC,EAA8B,CAClC,CAAE,MAAO,MAAO,KAAM,MAAO,EAC7B,CAAE,MAAO,OAAQ,KAAM,OAAQ,EAC/B,CAAE,MAAO,QAAS,KAAM,QAAS,CAAA,EAG7BC,EAA0BnD,EAAY,OAAO,EAE7CoD,EAA0B/C,EAAuB,IAC9C,CACL,CAAE,IAAK,OAAQ,MAAO,MAAO,EAC7B,CAAE,IAAK,OAAQ,MAAO,MAAO,EAC7B,CAAE,IAAK,QAAS,MAAO,OAAQ,EAC/B,CAAE,IAAK,QAAS,MAAO,QAAS,CAAA,CAEnC,srBCpBD,MAAMZ,EAAQC,EASR2D,EAAahD,EAAS,IAAMZ,EAAM,OAAiC,EAEnE6D,EAAejD,EAAS,IAAM,CAElC,MAAMkD,EAAqB,CAAA,EACvB,GAAA9D,EAAM,WAAW,OAAS,EAE5B,QAAS6B,EAAI,EAAGA,EAAI7B,EAAM,WAAW,OAAQ6B,GAAK,EAChDiC,EAAY,KAAK,CACf,IAAK,MACL,MAAO9D,EAAM,WAAW6B,CAAC,EACzB,UAAW,CAACL,EAAOuC,EAAGtC,IACpB,MAAM,QAAQD,CAAK,EAAIA,EAAMK,CAAC,EAAIL,GAASC,EAAK,aAAkB,OAAA,CACrE,OAGHqC,EAAY,KAAK,CACf,IAAK,MACL,MAAO9D,EAAM,UACb,UAAW,CAACwB,EAAOuC,EAAGtC,IAAUD,GAASC,EAAK,aAAkB,OAAA,CACjE,EAGI,MAAA,CACL,GAAGqC,EACH,CAAE,IAAK,SAAU,MAAO,QAAS,EACjC,CACE,IAAK,cACL,MAAO,eACP,UAAYtC,GAAkBU,EAAcV,EAAO,CAAC,CACtD,EACA,CACE,IAAK,mBACL,MAAO,cAAcxB,EAAM,aAAa,GAExC,UAAYwB,GAAUgB,GAAYhB,EAAiBxB,EAAM,qBAAqB,CAChF,EACA,CACE,IAAK,eACL,MAAO,eACP,UAAYwB,GAAUU,EAAcV,EAAiB,CAAC,CACxD,EACA,CAAE,IAAK,OAAQ,MAAO,MAAO,EAC7B,CAAE,IAAK,QAAS,MAAO,OAAQ,EAC/B,CAAE,IAAK,SAAU,MAAO,QAAS,CAAA,CACnC,CACD,8iBCqBD,MAAMxB,EAAQC,EAIRmD,EAAsBxC,EAAS,IAAM,CACnC,MAAA0C,EAAMb,GAA2BzC,EAAM,cAAc,EAC3D,OAAOmB,GAAqB,CAAE,MAAOmC,GAAO,QAAQ,CAAA,CACrD,EAEKU,EAAyBpD,EAAS,IAAM,CAEtC,MAAA0C,EAAMH,GAA4BnD,EAAM,cAAc,EAE5D,OAAOmB,GAAqB,CAAE,MAAOmC,GAAO,SAAS,CAAA,CACtD,EACKC,EAAqC,CACzC,CAAE,IAAK,SAAU,MAAO,QAAS,EACjC,CAAE,IAAK,QAAS,MAAO,OAAQ,CAAA,EAG3BU,EAAsC,CAC1C,CAAE,IAAK,UAAW,MAAO,SAAU,EACnC,CAAE,IAAK,QAAS,MAAO,OAAQ,CAAA,4qEClFjC,MAAMjE,EAAQC,EAGRC,EAAOC,EACPC,EAAWC,IAEX6D,EAAiBtD,EAAS,CAC9B,KAAM,CACJ,OAAOZ,EAAM,UACf,EACA,IAAImE,EAAqB,CACvBjE,EAAK,oBAAqBiE,CAAW,CACvC,CAAA,CACD,EAED,OAAAC,GAAU,IAAM,CACVhE,EAAS,UAAU,gBAAgB,SAAW,GAChDA,EAAS,UAAU,oBACrB,CACD,oWCtCYiE,GAAaC,GAAY,UAAW,CAC/C,MAAO,KACE,CACL,SAAU,GACV,kBAAmB,GACnB,wBAAyB,GACzB,UAAW,GACX,cAAe,GACf,YAAa,GACb,gBAAiB,GACjB,WAAY,GACZ,kBAAmB,GACnB,qBAAsB,GACtB,OAAQ,CACN,QAAS,GACT,MAAO,GACP,WAAY,EACd,CAAA,GAGJ,QAAS,CACP,eAAiBC,GAAUA,EAAM,WAAa,EAChD,EACA,QAAS,CAAC,CACZ,CAAC,obCuLD,MAAMnE,EAAWC,IACXmE,EAAUH,KAEhB,SAASI,GAAgB,CACvB,MAAMC,EAA6B,CACjC,SAAUF,EAAQ,SAClB,UAAWA,EAAQ,UACnB,mBAAoBA,EAAQ,iBAAA,EAExBG,EAAgB,SAASH,EAAQ,cAAe,EAAE,EAIxD,GAHIG,IACFD,EAAU,gBAAkBC,GAE1BH,EAAQ,qBACVE,EAAU,aAAe,gBACpB,CACC,MAAAE,EAAiB,OAAOJ,EAAQ,WAAW,EAC7CI,IACQF,EAAA,aAAeE,EAAe,WAE5C,CAEM,MAAAC,EAAqB,OAAOL,EAAQ,eAAe,EACrDK,IACFH,EAAU,eAAiBG,GAGzBL,EAAQ,oBACVE,EAAU,UAAYF,EAAQ,mBAE5BA,EAAQ,0BACVE,EAAU,iBAAmBF,EAAQ,yBAElCA,EAAQ,aACXE,EAAU,eAAiB,QAEzBF,EAAQ,OAAO,UACPE,EAAA,YAAcF,EAAQ,OAAO,MACnCA,EAAQ,OAAO,aAAe,KAChCE,EAAU,OAAS,CAAE,WAAYF,EAAQ,OAAO,cAI3CpE,EAAA,UAAU,cAAcsE,CAAS,CAC5C,qwLC5IA,MAAMtE,EAAWC,IACXyE,EAASvE,IACTwE,EAAaxE,EAAI,EAAE,EACnByE,EAAsBC,GAAaF,EAAY,IAAK,CAAE,QAAS,IAAM,EAE3EX,GAAU,IAAM,CACdhE,EAAS,UAAU,oBAAmB,CACvC,EAED,SAAS8E,EAAqBxC,EAA8B,OAC1D,MAAMyC,EAAoB,CACxB,MAAO,gBACP,QAAS,iBAAiBzC,EAAO,QAAQ,cACzC,OAAQ,IAAM,CACHtC,EAAA,UAAU,4BAA4BsC,CAAM,CACvD,CAAA,GAEK0C,EAAAN,EAAA,QAAA,MAAAM,EAAO,KAAKD,EACrB,mkGCzEA,MAAMjF,EAAOC,EAMPkF,EAAqB9D,GAAgB,CACzCrB,EAAK,kBAAmBqB,CAAG,CAAA,EAGpB,SAAA+D,EAAaC,EAAgB7C,EAAgC,CACpEA,EAAO,SAAS,QAAU,CAACA,EAAO,SAAS,QACvCA,EAAO,SAAS,UAClBxC,EAAK,eAAgB,CACnB,OAAAqF,EACA,MAAO7C,EAAO,SAAS,OAAS,GAChC,SAAUA,EAAO,SAAS,SAC1B,SAAUA,EAAO,SAAS,YAAA,CAC3B,CAEL,q9HCyFA,MAAMtC,EAAWC,IACXmE,EAAUH,KAEVmB,EAAoB5E,EAAS,IACjCR,EAAS,UAAU,gBACf,OAAO,KAAKA,EAAS,UAAU,eAAe,EAAE,SAAW,EAC3D,EAAA,EAEAqF,EAAyB7E,EAAS,IACtCR,EAAS,UAAU,gBACf,OAAO,KAAKA,EAAS,UAAU,eAAe,EAAE,OAAS,EACzD,EAAA,EAGAsF,EAAY9E,EAAS,IAAc,CACnC,GAAA,CACK,OAAAR,EAAS,UAAU,uBAAuB,eACrC,CACL,MAAA,EACT,CAAA,CACD,EAEKuF,EAAcpF,EAAI,EAAK,EAEvBqF,EAAarF,EAAgB,OAC7BsF,EAAetF,EAAmB,IAAI,EAEtCuF,EAAuB,IAAM,CAEzBtB,EAAA,SAAWpE,EAAS,UAAU,uBAAuB,cACpDA,EAAA,UAAU,YAAYoE,EAAQ,QAAQ,EACvCA,EAAA,kBAAoBpE,EAAS,UAAU,uBAAuB,UACtEoE,EAAQ,wBACNpE,EAAS,UAAU,uBAAuB,kBAAoB,GAExDoE,EAAA,UAAYpE,EAAS,UAAU,uBAAuB,SAAA,EAGhE,OAAAW,EACE,IAAMX,EAAS,UAAU,0BACzB,IAAM,CACiB0F,GACvB,CAAA,EAGF1B,GAAU,IAAMhE,EAAS,UAAU,SAAU,CAAA,EAC7CW,EACE,IAAMX,EAAS,UAAU,gBACzB,IAAM,CACAA,EAAS,UAAU,kBAAoB,GACzCyF,EAAa,MAAQ,OAAO,YAAYzF,EAAS,UAAU,aAAc,GAAI,EACpEyF,EAAa,QACtB,cAAcA,EAAa,KAAK,EAChCA,EAAa,MAAQ,KAEzB,CAAA"}