{"version": 3, "file": "TradeList-BP6RUI29.js", "sources": ["../../src/components/ftbot/ForceEntryForm.vue", "../../src/components/ftbot/ForceExitForm.vue", "../../src/components/ftbot/TradeActionsPopover.vue", "../../src/components/ftbot/TradeList.vue"], "sourcesContent": ["<template>\n  <b-modal\n    id=\"forceentry-modal\"\n    ref=\"modal\"\n    v-model=\"model\"\n    :title=\"positionIncrease ? `Increasing position for ${pair}` : 'Force entering a trade'\"\n    @show=\"resetForm\"\n    @hidden=\"resetForm\"\n    @ok=\"handleEntry\"\n  >\n    <form ref=\"form\" @submit.stop.prevent=\"handleSubmit\">\n      <b-form-group\n        v-if=\"botStore.activeBot.botApiVersion >= 2.13 && botStore.activeBot.shortAllowed\"\n        label=\"Order direction (Long or Short)\"\n        label-for=\"order-direction\"\n        invalid-feedback=\"Order direction must be set\"\n        :state=\"orderSide !== undefined\"\n      >\n        <b-form-radio-group\n          id=\"order-direction\"\n          v-model=\"orderSide\"\n          :options=\"orderSideOptions\"\n          name=\"radios-btn-default\"\n          size=\"sm\"\n          buttons\n          style=\"min-width: 10em\"\n          button-variant=\"outline-primary\"\n        ></b-form-radio-group>\n      </b-form-group>\n      <b-form-group\n        label=\"Pair\"\n        label-for=\"pair-input\"\n        invalid-feedback=\"Pair is required\"\n        :state=\"selectedPair !== undefined\"\n      >\n        <b-form-input\n          id=\"pair-input\"\n          v-model=\"selectedPair\"\n          required\n          :disabled=\"positionIncrease\"\n          @keydown.enter=\"handleEntry\"\n          @focus=\"inputSelect\"\n        ></b-form-input>\n      </b-form-group>\n      <b-form-group\n        label=\"*Price [optional]\"\n        label-for=\"price-input\"\n        invalid-feedback=\"Price must be empty or a positive number\"\n        :state=\"!price || price > 0\"\n      >\n        <b-form-input\n          id=\"price-input\"\n          v-model=\"price\"\n          type=\"number\"\n          step=\"0.00000001\"\n          @keydown.enter=\"handleEntry\"\n        ></b-form-input>\n      </b-form-group>\n      <b-form-group\n        :label=\"`*Stake-amount in ${botStore.activeBot.stakeCurrency} [optional]`\"\n        label-for=\"stake-input\"\n        invalid-feedback=\"Stake-amount must be empty or a positive number\"\n        :state=\"!stakeAmount || stakeAmount > 0\"\n      >\n        <b-form-input\n          id=\"stake-input\"\n          v-model=\"stakeAmount\"\n          type=\"number\"\n          step=\"0.000001\"\n          @keydown.enter=\"handleEntry\"\n        ></b-form-input>\n      </b-form-group>\n      <b-form-group\n        v-if=\"botStore.activeBot.botApiVersion > 2.16 && botStore.activeBot.shortAllowed\"\n        :label=\"`*Leverage to apply [optional]`\"\n        label-for=\"leverage-input\"\n        invalid-feedback=\"Leverage must be empty or a positive number\"\n        :state=\"!leverage || leverage > 0\"\n      >\n        <b-form-input\n          id=\"leverage-input\"\n          v-model=\"leverage\"\n          type=\"number\"\n          step=\"0.01\"\n          @keydown.enter=\"handleEntry\"\n        ></b-form-input>\n      </b-form-group>\n      <b-form-group\n        label=\"OrderType\"\n        label-for=\"ordertype-input\"\n        invalid-feedback=\"OrderType\"\n        :state=\"true\"\n      >\n        <b-form-radio-group\n          id=\"ordertype-input\"\n          v-model=\"ordertype\"\n          :options=\"orderTypeOptions\"\n          name=\"radios-btn-orderType\"\n          buttons\n          button-variant=\"outline-primary\"\n          style=\"min-width: 10em\"\n          size=\"sm\"\n        ></b-form-radio-group>\n      </b-form-group>\n      <b-form-group\n        v-if=\"botStore.activeBot.botApiVersion > 1.16\"\n        label=\"*Custom entry tag Optional]\"\n        label-for=\"enterTag-input\"\n      >\n        <b-form-input\n          id=\"enterTag-input\"\n          v-model=\"enterTag\"\n          type=\"text\"\n          name=\"radios-btn-orderType\"\n        ></b-form-input>\n      </b-form-group>\n    </form>\n  </b-modal>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { ForceEnterPayload, OrderSides } from '@/types';\n\nconst props = defineProps({\n  modelValue: { required: true, default: false, type: Boolean },\n  pair: { type: String, default: '' },\n  positionIncrease: { type: Boolean, default: false },\n});\nconst emit = defineEmits(['update:modelValue']);\nconst botStore = useBotStore();\n\nconst form = ref<HTMLFormElement>();\nconst selectedPair = ref('');\nconst price = ref<number | undefined>(undefined);\nconst stakeAmount = ref<number | undefined>(undefined);\nconst leverage = ref<number | undefined>(undefined);\n\nconst ordertype = ref('');\nconst orderSide = ref<OrderSides>(OrderSides.long);\nconst enterTag = ref('force_entry');\n\nconst orderTypeOptions = [\n  { value: 'market', text: 'Market' },\n  { value: 'limit', text: 'Limit' },\n];\nconst orderSideOptions = [\n  { value: 'long', text: 'Long' },\n  { value: 'short', text: 'Short' },\n];\n\nconst model = computed({\n  get() {\n    return props.modelValue;\n  },\n  set(value: boolean) {\n    emit('update:modelValue', value);\n  },\n});\n\nconst checkFormValidity = () => {\n  const valid = form.value?.checkValidity();\n\n  return valid;\n};\n\nconst handleSubmit = async () => {\n  // Exit when the form isn't valid\n  if (!checkFormValidity()) {\n    return;\n  }\n\n  // call forceentry\n  const payload: ForceEnterPayload = { pair: selectedPair.value };\n  if (price.value) {\n    payload.price = Number(price.value);\n  }\n  if (ordertype.value) {\n    payload.ordertype = ordertype.value;\n  }\n  if (stakeAmount.value) {\n    payload.stakeamount = stakeAmount.value;\n  }\n  if (botStore.activeBot.botApiVersion >= 2.13 && botStore.activeBot.shortAllowed) {\n    payload.side = orderSide.value;\n  }\n  if (botStore.activeBot.botApiVersion >= 2.16 && enterTag.value) {\n    payload.entry_tag = enterTag.value;\n  }\n\n  if (leverage.value) {\n    payload.leverage = leverage.value;\n  }\n  botStore.activeBot.forceentry(payload);\n  await nextTick();\n  emit('update:modelValue', false);\n};\nconst resetForm = () => {\n  console.log('resetForm');\n  selectedPair.value = props.pair;\n  price.value = undefined;\n  stakeAmount.value = undefined;\n  ordertype.value =\n    botStore.activeBot.botState?.order_types?.forcebuy ||\n    botStore.activeBot.botState?.order_types?.force_entry ||\n    botStore.activeBot.botState?.order_types?.buy ||\n    botStore.activeBot.botState?.order_types?.entry ||\n    'limit';\n};\n\nconst handleEntry = () => {\n  // Trigger submit handler\n  handleSubmit();\n};\nconst inputSelect = (bvModalEvt) => {\n  bvModalEvt.srcElement?.select();\n};\n</script>\n", "<template>\n  <div>\n    <b-modal\n      id=\"forceexit-modal\"\n      v-model=\"model\"\n      title=\"Force exiting a trade\"\n      @show=\"resetForm\"\n      @hidden=\"resetForm\"\n      @ok=\"handleEntry\"\n    >\n      <form ref=\"form\" @submit.stop.prevent=\"handleSubmit\">\n        <p>\n          <span>Exiting Trade #{{ trade.trade_id }} {{ trade.pair }}.</span>\n          <br />\n          <span>Currently owning {{ trade.amount }} {{ trade.base_currency }}</span>\n        </p>\n        <b-form-group\n          :label=\"`*Amount in ${trade.base_currency} [optional]`\"\n          label-for=\"stake-input\"\n          invalid-feedback=\"Amount must be empty or a positive number\"\n          :state=\"amount !== undefined && amount > 0\"\n        >\n          <b-form-input\n            id=\"stake-input\"\n            v-model=\"amount\"\n            type=\"number\"\n            step=\"0.000001\"\n          ></b-form-input>\n          <b-form-input\n            id=\"stake-input\"\n            v-model=\"amount\"\n            type=\"range\"\n            step=\"0.000001\"\n            min=\"0\"\n            :max=\"trade.amount\"\n          ></b-form-input>\n        </b-form-group>\n\n        <b-form-group\n          label=\"*OrderType\"\n          label-for=\"ordertype-input\"\n          invalid-feedback=\"OrderType\"\n          :state=\"ordertype !== undefined\"\n        >\n          <b-form-select\n            v-model=\"ordertype\"\n            class=\"ms-2\"\n            :options=\"['market', 'limit']\"\n            style=\"min-width: 7em\"\n            size=\"sm\"\n          >\n          </b-form-select>\n        </b-form-group>\n      </form>\n    </b-modal>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { ForceSellPayload, Trade } from '@/types';\n\nconst props = defineProps({\n  trade: {\n    type: Object as () => Trade,\n    required: true,\n  },\n  modelValue: { required: true, default: false, type: Boolean },\n});\nconst emit = defineEmits(['update:modelValue']);\nconst botStore = useBotStore();\n\nconst form = ref<HTMLFormElement>();\nconst amount = ref<number | undefined>(undefined);\nconst ordertype = ref('limit');\n\nconst checkFormValidity = () => {\n  const valid = form.value?.checkValidity();\n\n  return valid;\n};\n\nconst model = computed({\n  get() {\n    return props.modelValue;\n  },\n  set(value: boolean) {\n    emit('update:modelValue', value);\n  },\n});\n\nfunction handleSubmit() {\n  // Exit when the form isn't valid\n  if (!checkFormValidity()) {\n    return;\n  }\n  // call forceentry\n  const payload: ForceSellPayload = { tradeid: String(props.trade.trade_id) };\n\n  if (ordertype.value) {\n    payload.ordertype = ordertype.value;\n  }\n  if (amount.value) {\n    payload.amount = amount.value;\n  }\n  botStore.activeBot.forceexit(payload);\n  model.value = false;\n}\n\nfunction resetForm() {\n  amount.value = props.trade.amount;\n  ordertype.value =\n    botStore.activeBot.botState?.order_types?.force_exit ||\n    botStore.activeBot.botState?.order_types?.exit ||\n    'limit';\n}\n\nfunction handleEntry() {\n  // Trigger submit handler\n  handleSubmit();\n}\n</script>\n", "<script setup lang=\"ts\">\nimport { Trade } from '@/types';\n\ndefineProps({\n  trade: { type: Object as () => Trade, required: true },\n  id: { type: Number, required: true },\n  botApiVersion: { type: Number, required: true },\n  enableForceEntry: { type: Boolean, default: false },\n});\nconst emit = defineEmits([\n  'forceExit',\n  'forceExitPartial',\n  'cancelOpenOrder',\n  'reloadTrade',\n  'deleteTrade',\n  'forceEntry',\n]);\nconst popoverOpen = ref(false);\n\nfunction forceExitHandler(item: Trade, ordertype: string | undefined = undefined) {\n  popoverOpen.value = false;\n  emit('forceExit', item, ordertype);\n}\nfunction forceExitPartialHandler(item: Trade) {\n  popoverOpen.value = false;\n  emit('forceExitPartial', item);\n}\nfunction cancelOpenOrderHandler(item: Trade) {\n  popoverOpen.value = false;\n  emit('cancelOpenOrder', item);\n}\nfunction handleReloadTrade(item: Trade) {\n  popoverOpen.value = false;\n  emit('reloadTrade', item);\n}\nfunction handleDeleteTrade(item: Trade) {\n  popoverOpen.value = false;\n  emit('deleteTrade', item);\n}\nfunction handleForceEntry(item: Trade) {\n  popoverOpen.value = false;\n  emit('forceEntry', item);\n}\n</script>\n\n<template>\n  <div>\n    <b-button\n      :id=\"`btn-actions-${id}`\"\n      class=\"btn-xs\"\n      size=\"sm\"\n      title=\"Actions\"\n      @click=\"popoverOpen = !popoverOpen\"\n    >\n      <i-mdi-gesture-tap />\n    </b-button>\n    <BPopover\n      teleport-to=\"body\"\n      :target=\"`btn-actions-${id}`\"\n      :title=\"`Actions for ${trade.pair}`\"\n      triggers=\"manual\"\n      :show=\"popoverOpen\"\n      placement=\"left\"\n    >\n      <trade-actions\n        :trade=\"trade\"\n        :bot-api-version=\"botApiVersion\"\n        :enable-force-entry=\"enableForceEntry\"\n        @force-exit=\"forceExitHandler\"\n        @force-exit-partial=\"forceExitPartialHandler\"\n        @delete-trade=\"handleDeleteTrade(trade)\"\n        @cancel-open-order=\"cancelOpenOrderHandler\"\n        @reload-trade=\"handleReloadTrade\"\n        @force-entry=\"handleForceEntry\"\n      />\n      <b-button class=\"mt-1 w-100 text-start\" size=\"sm\" @click=\"popoverOpen = false\">\n        <i-mdi-cancel class=\"me-1\" />Close Actions menu\n      </b-button>\n    </BPopover>\n  </div>\n</template>\n\n<style scoped></style>\n", "<template>\n  <div class=\"h-100 overflow-auto w-100\">\n    <b-table\n      ref=\"tradesTable\"\n      small\n      hover\n      stacked=\"md\"\n      :items=\"\n        trades.filter(\n          (t) =>\n            t.pair.toLowerCase().includes(filterText.toLowerCase()) ||\n            t.exit_reason?.toLowerCase().includes(filterText.toLowerCase()) ||\n            t.enter_tag?.toLowerCase().includes(filterText.toLowerCase()),\n        ) as unknown as TableItem[]\n      \"\n      :fields=\"tableFields\"\n      show-empty\n      :empty-text=\"emptyText\"\n      :per-page=\"perPage\"\n      :current-page=\"currentPage\"\n      primary-key=\"botTradeId\"\n      selectable\n      :select-head=\"false\"\n      select-mode=\"single\"\n      @row-contextmenu=\"handleContextMenuEvent\"\n      @row-clicked=\"onRowClicked\"\n      @row-selected=\"onRowSelected\"\n    >\n      <template #cell(actions)=\"{ index, item }\">\n        <TradeActionsPopover\n          :id=\"index\"\n          :enable-force-entry=\"botStore.activeBot.botState.force_entry_enable\"\n          :trade=\"item as unknown as Trade\"\n          :bot-api-version=\"botStore.activeBot.botApiVersion\"\n          @delete-trade=\"removeTradeHandler(item as unknown as Trade)\"\n          @force-exit=\"forceExitHandler\"\n          @force-exit-partial=\"forceExitPartialHandler\"\n          @cancel-open-order=\"cancelOpenOrderHandler\"\n          @reload-trade=\"reloadTradeHandler\"\n          @force-entry=\"handleForceEntry\"\n        />\n      </template>\n      <template #cell(pair)=\"row\">\n        <span>\n          {{ `${row.item.pair}${row.item.open_order_id || row.item.has_open_orders ? '*' : ''}` }}\n        </span>\n      </template>\n      <template #cell(trade_id)=\"row\">\n        {{ row.item.trade_id }}\n        {{\n          botStore.activeBot.botApiVersion > 2.0 && row.item.trading_mode !== 'spot'\n            ? '| ' + (row.item.is_short ? 'Short' : 'Long')\n            : ''\n        }}\n      </template>\n      <template #cell(stake_amount)=\"row\">\n        {{ formatPriceWithDecimals(row.item.stake_amount) }}\n        {{ row.item.trading_mode !== 'spot' ? `(${row.item.leverage}x)` : '' }}\n      </template>\n      <template #cell(profit)=\"row\">\n        <trade-profit :trade=\"row.item as unknown as Trade\" />\n      </template>\n      <template #cell(open_timestamp)=\"row\">\n        <DateTimeTZ :date=\"(row.item as unknown as Trade).open_timestamp\" />\n      </template>\n      <template #cell(close_timestamp)=\"row\">\n        <DateTimeTZ :date=\"(row.item as unknown as Trade).close_timestamp ?? 0\" />\n      </template>\n    </b-table>\n    <div class=\"w-100 d-flex justify-content-between\">\n      <b-pagination\n        v-if=\"!activeTrades\"\n        v-model=\"currentPage\"\n        :total-rows=\"rows\"\n        :per-page=\"perPage\"\n        aria-controls=\"my-table\"\n      ></b-pagination>\n      <b-form-group v-if=\"showFilter\" label-for=\"trade-filter\">\n        <b-form-input id=\"trade-filter\" v-model=\"filterText\" type=\"text\" placeholder=\"Filter\" />\n      </b-form-group>\n    </div>\n    <force-exit-form v-if=\"activeTrades\" v-model=\"forceExitVisible\" :trade=\"feTrade\" />\n    <ForceEntryForm\n      v-model=\"increasePosition.visible\"\n      :pair=\"increasePosition.trade?.pair\"\n      position-increase\n    />\n\n    <b-modal v-model=\"removeTradeVisible\" title=\"Exit trade\" @ok=\"forceExitExecuter\">\n      {{ confirmExitText }}\n    </b-modal>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent, formatPrice } from '@/shared/formatters';\nimport { MultiDeletePayload, MultiForcesellPayload, Trade } from '@/types';\n\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { useRouter } from 'vue-router';\nimport { TableField, TableItem } from 'bootstrap-vue-next';\n\nenum ModalReasons {\n  removeTrade,\n  forceExit,\n  forceExitPartial,\n  cancelOpenOrder,\n}\n\nconst props = defineProps({\n  trades: { required: true, type: Array as () => Array<Trade> },\n  title: { default: 'Trades', type: String },\n  stakeCurrency: { required: false, default: '', type: String },\n  activeTrades: { default: false, type: Boolean },\n  showFilter: { default: false, type: Boolean },\n  multiBotView: { default: false, type: Boolean },\n  emptyText: { default: 'No Trades to show.', type: String },\n});\nconst botStore = useBotStore();\nconst router = useRouter();\nconst settingsStore = useSettingsStore();\nconst currentPage = ref(1);\nconst selectedItemIndex = ref();\nconst filterText = ref('');\nconst feTrade = ref<Trade>({} as Trade);\nconst perPage = props.activeTrades ? 200 : 15;\nconst tradesTable = ref<HTMLFormElement>();\nconst forceExitVisible = ref(false);\nconst removeTradeVisible = ref(false);\nconst confirmExitText = ref('');\nconst confirmExitValue = ref<ModalReasons | null>(null);\n\nconst increasePosition = ref({ visible: false, trade: {} as Trade });\nconst openFields: TableField[] = [{ key: 'actions' }];\nconst closedFields: TableField[] = [\n  { key: 'close_timestamp', label: 'Close date' },\n  { key: 'exit_reason', label: 'Close Reason' },\n];\nfunction formatPriceWithDecimals(price) {\n  return formatPrice(price, botStore.activeBot.stakeCurrencyDecimals);\n}\nconst rows = computed(() => {\n  return props.trades.length;\n});\n\n// This using \"TableField[]\" below causes\n// Error: Debug Failure. No error for last overload signature\nconst tableFields = ref<any[]>([]);\n\nonMounted(() => {\n  tableFields.value = [\n    { key: 'trade_id', label: 'ID' },\n    { key: 'pair', label: 'Pair' },\n    { key: 'amount', label: 'Amount' },\n    {\n      key: 'stake_amount',\n      label: 'Stake amount',\n    },\n    {\n      key: 'open_rate',\n      label: 'Open rate',\n      formatter: (value: unknown) => formatPrice(value as number),\n    },\n    {\n      key: props.activeTrades ? 'current_rate' : 'close_rate',\n      label: props.activeTrades ? 'Current rate' : 'Close rate',\n      formatter: (value: unknown) => formatPrice(value as number),\n    },\n    {\n      key: 'profit',\n      label: props.activeTrades ? 'Current profit %' : 'Profit %',\n      formatter: (value: unknown, key?: string, item?: unknown) => {\n        if (!item) {\n          return '';\n        }\n        const typedItem = item as Trade;\n        const percent = formatPercent(typedItem.profit_ratio, 2);\n        return `${percent} ${`(${formatPriceWithDecimals(typedItem.profit_abs)})`}`;\n      },\n    },\n    { key: 'open_timestamp', label: 'Open date' },\n    ...(props.activeTrades ? openFields : closedFields),\n  ];\n  if (props.multiBotView) {\n    tableFields.value.unshift({ key: 'botName', label: 'Bot' });\n  }\n});\n\nconst feOrderType = ref<string | undefined>(undefined);\nfunction forceExitHandler(item: Trade, ordertype: string | undefined = undefined) {\n  feTrade.value = item;\n  confirmExitValue.value = ModalReasons.forceExit;\n  confirmExitText.value = `Really exit trade ${item.trade_id} (Pair ${item.pair}) using ${ordertype} Order?`;\n  feOrderType.value = ordertype;\n  if (settingsStore.confirmDialog === true) {\n    removeTradeVisible.value = true;\n  } else {\n    forceExitExecuter();\n  }\n}\n\nfunction forceExitExecuter() {\n  if (confirmExitValue.value === ModalReasons.removeTrade) {\n    const payload: MultiDeletePayload = {\n      tradeid: String(feTrade.value.trade_id),\n      botId: feTrade.value.botId,\n    };\n    botStore.deleteTradeMulti(payload).catch((error) => console.log(error.response));\n  }\n  if (confirmExitValue.value === ModalReasons.forceExit) {\n    const payload: MultiForcesellPayload = {\n      tradeid: String(feTrade.value.trade_id),\n      botId: feTrade.value.botId,\n    };\n    if (feOrderType.value) {\n      payload.ordertype = feOrderType.value;\n    }\n    botStore\n      .forceSellMulti(payload)\n      .then((xxx) => console.log(xxx))\n      .catch((error) => console.log(error.response));\n  }\n  if (confirmExitValue.value === ModalReasons.cancelOpenOrder) {\n    const payload: MultiDeletePayload = {\n      tradeid: String(feTrade.value.trade_id),\n      botId: feTrade.value.botId,\n    };\n    botStore.cancelOpenOrderMulti(payload);\n  }\n\n  feOrderType.value = undefined;\n  removeTradeVisible.value = false;\n}\n\nfunction removeTradeHandler(item: Trade) {\n  confirmExitText.value = `Really delete trade ${item.trade_id} (Pair ${item.pair})?`;\n  confirmExitValue.value = ModalReasons.removeTrade;\n  feTrade.value = item;\n  removeTradeVisible.value = true;\n}\n\nfunction forceExitPartialHandler(item: Trade) {\n  feTrade.value = item;\n  forceExitVisible.value = true;\n}\n\nfunction cancelOpenOrderHandler(item: Trade) {\n  confirmExitText.value = `Cancel open order for trade ${item.trade_id} (Pair ${item.pair})?`;\n  feTrade.value = item;\n  confirmExitValue.value = ModalReasons.cancelOpenOrder;\n  removeTradeVisible.value = true;\n}\n\nfunction reloadTradeHandler(item: Trade) {\n  botStore.reloadTradeMulti({ tradeid: String(item.trade_id), botId: item.botId });\n}\n\nfunction handleForceEntry(item: Trade) {\n  increasePosition.value.trade = item;\n  increasePosition.value.visible = true;\n}\n\nfunction handleContextMenuEvent(item, index, event) {\n  // stop browser context menu from appearing\n  if (!props.activeTrades) {\n    return;\n  }\n  event.preventDefault();\n  // log the selected item to the console\n  console.log(item);\n}\n\nconst onRowClicked = (item) => {\n  if (props.multiBotView && botStore.selectedBot !== item.botId) {\n    // Multibotview - on click switch to the bot trade view\n    botStore.selectBot(item.botId);\n  }\n  if (item && item.trade_id !== botStore.activeBot.detailTradeId) {\n    botStore.activeBot.setDetailTrade(item);\n    if (props.multiBotView) {\n      router.push({ name: 'Freqtrade Trading' });\n    }\n  } else {\n    botStore.activeBot.setDetailTrade(null);\n  }\n};\n\nconst onRowSelected = () => {\n  if (botStore.activeBot.detailTradeId) {\n    const itemIndex = props.trades.findIndex(\n      (v) => v.trade_id === botStore.activeBot.detailTradeId,\n    );\n    if (itemIndex >= 0) {\n      tradesTable.value?.selectRow(itemIndex);\n    } else {\n      console.log(`Unsetting item for tradeid ${selectedItemIndex.value}`);\n      selectedItemIndex.value = undefined;\n    }\n  }\n};\n\nwatch(\n  () => botStore.activeBot.detailTradeId,\n  (val) => {\n    const index = props.trades.findIndex((v) => v.trade_id === val);\n    // Unselect when another tradeTable is selected!\n    if (index < 0) {\n      tradesTable.value?.clearSelected();\n    }\n  },\n);\n</script>\n\n<style lang=\"scss\" scoped>\n.card-body {\n  padding: 0 0.2em;\n}\n.table-sm {\n  font-size: $fontsize-small;\n}\n.btn-xs {\n  padding: 0.1rem 0.25rem;\n  font-size: 0.75rem;\n}\n</style>\n"], "names": ["props", "__props", "emit", "__emit", "botStore", "useBotStore", "form", "ref", "<PERSON><PERSON><PERSON>", "price", "stakeAmount", "leverage", "ordertype", "orderSide", "OrderSides", "enterTag", "orderTypeOptions", "orderSideOptions", "model", "computed", "value", "checkFormValidity", "_a", "handleSubmit", "payload", "nextTick", "resetForm", "_b", "_d", "_c", "_f", "_e", "_h", "_g", "handleEntry", "inputSelect", "bvModalEvt", "amount", "popoverOpen", "forceExitHandler", "item", "forceExitPartialHandler", "cancelOpenOrderHandler", "handleReloadTrade", "handleDeleteTrade", "handleForceEntry", "router", "useRouter", "settingsStore", "useSettingsStore", "currentPage", "selectedItemIndex", "filterText", "feTrade", "perPage", "tradesTable", "forceExitVisible", "removeTradeVisible", "confirmExitText", "confirmExitValue", "increasePosition", "openFields", "closedFields", "formatPriceWithDecimals", "formatPrice", "rows", "tableFields", "onMounted", "key", "typedItem", "formatPercent", "feOrderType", "forceExitExecuter", "error", "xxx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reloadTradeHandler", "handleContextMenuEvent", "index", "event", "onRowClicked", "onRowSelected", "itemIndex", "v", "watch", "val"], "mappings": "6mBA4HA,MAAMA,EAAQC,EAKRC,EAAOC,EACPC,EAAWC,IAEXC,EAAOC,IACPC,EAAeD,EAAI,EAAE,EACrBE,EAAQF,EAAwB,MAAS,EACzCG,EAAcH,EAAwB,MAAS,EAC/CI,EAAWJ,EAAwB,MAAS,EAE5CK,EAAYL,EAAI,EAAE,EAClBM,EAAYN,EAAgBO,GAAW,IAAI,EAC3CC,EAAWR,EAAI,aAAa,EAE5BS,EAAmB,CACvB,CAAE,MAAO,SAAU,KAAM,QAAS,EAClC,CAAE,MAAO,QAAS,KAAM,OAAQ,CAAA,EAE5BC,EAAmB,CACvB,CAAE,MAAO,OAAQ,KAAM,MAAO,EAC9B,CAAE,MAAO,QAAS,KAAM,OAAQ,CAAA,EAG5BC,EAAQC,EAAS,CACrB,KAAM,CACJ,OAAOnB,EAAM,UACf,EACA,IAAIoB,EAAgB,CAClBlB,EAAK,oBAAqBkB,CAAK,CACjC,CAAA,CACD,EAEKC,EAAoB,IAAM,OAGvB,OAFOC,EAAAhB,EAAK,QAAL,YAAAgB,EAAY,eAEnB,EAGHC,EAAe,SAAY,CAE3B,GAAA,CAACF,IACH,OAIF,MAAMG,EAA6B,CAAE,KAAMhB,EAAa,KAAM,EAC1DC,EAAM,QACAe,EAAA,MAAQ,OAAOf,EAAM,KAAK,GAEhCG,EAAU,QACZY,EAAQ,UAAYZ,EAAU,OAE5BF,EAAY,QACdc,EAAQ,YAAcd,EAAY,OAEhCN,EAAS,UAAU,eAAiB,MAAQA,EAAS,UAAU,eACjEoB,EAAQ,KAAOX,EAAU,OAEvBT,EAAS,UAAU,eAAiB,MAAQW,EAAS,QACvDS,EAAQ,UAAYT,EAAS,OAG3BJ,EAAS,QACXa,EAAQ,SAAWb,EAAS,OAErBP,EAAA,UAAU,WAAWoB,CAAO,EACrC,MAAMC,GAAS,EACfvB,EAAK,oBAAqB,EAAK,CAAA,EAE3BwB,EAAY,IAAM,qBACtB,QAAQ,IAAI,WAAW,EACvBlB,EAAa,MAAQR,EAAM,KAC3BS,EAAM,MAAQ,OACdC,EAAY,MAAQ,OACVE,EAAA,QACRe,GAAAL,EAAAlB,EAAS,UAAU,WAAnB,YAAAkB,EAA6B,cAA7B,YAAAK,EAA0C,aAC1CC,GAAAC,EAAAzB,EAAS,UAAU,WAAnB,YAAAyB,EAA6B,cAA7B,YAAAD,EAA0C,gBAC1CE,GAAAC,EAAA3B,EAAS,UAAU,WAAnB,YAAA2B,EAA6B,cAA7B,YAAAD,EAA0C,QAC1CE,GAAAC,EAAA7B,EAAS,UAAU,WAAnB,YAAA6B,EAA6B,cAA7B,YAAAD,EAA0C,QAC1C,OAAA,EAGEE,EAAc,IAAM,CAEXX,GAAA,EAETY,EAAeC,GAAe,QAClCd,EAAAc,EAAW,aAAX,MAAAd,EAAuB,QAAO,uwGCzJhC,MAAMtB,EAAQC,EAORC,EAAOC,EACPC,EAAWC,IAEXC,EAAOC,IACP8B,EAAS9B,EAAwB,MAAS,EAC1CK,EAAYL,EAAI,OAAO,EAEvBc,EAAoB,IAAM,OAGvB,OAFOC,EAAAhB,EAAK,QAAL,YAAAgB,EAAY,eAEnB,EAGHJ,EAAQC,EAAS,CACrB,KAAM,CACJ,OAAOnB,EAAM,UACf,EACA,IAAIoB,EAAgB,CAClBlB,EAAK,oBAAqBkB,CAAK,CACjC,CAAA,CACD,EAED,SAASG,GAAe,CAElB,GAAA,CAACF,IACH,OAGF,MAAMG,EAA4B,CAAE,QAAS,OAAOxB,EAAM,MAAM,QAAQ,GAEpEY,EAAU,QACZY,EAAQ,UAAYZ,EAAU,OAE5ByB,EAAO,QACTb,EAAQ,OAASa,EAAO,OAEjBjC,EAAA,UAAU,UAAUoB,CAAO,EACpCN,EAAM,MAAQ,EAChB,CAEA,SAASQ,GAAY,aACZW,EAAA,MAAQrC,EAAM,MAAM,OACjBY,EAAA,QACRe,GAAAL,EAAAlB,EAAS,UAAU,WAAnB,YAAAkB,EAA6B,cAA7B,YAAAK,EAA0C,eAC1CC,GAAAC,EAAAzB,EAAS,UAAU,WAAnB,YAAAyB,EAA6B,cAA7B,YAAAD,EAA0C,OAC1C,OACJ,CAEA,SAASM,GAAc,CAERX,GACf,44LC/GA,MAAMrB,EAAOC,EAQPmC,EAAc/B,EAAI,EAAK,EAEpB,SAAAgC,EAAiBC,EAAa5B,EAAgC,OAAW,CAChF0B,EAAY,MAAQ,GACfpC,EAAA,YAAasC,EAAM5B,CAAS,CACnC,CACA,SAAS6B,EAAwBD,EAAa,CAC5CF,EAAY,MAAQ,GACpBpC,EAAK,mBAAoBsC,CAAI,CAC/B,CACA,SAASE,EAAuBF,EAAa,CAC3CF,EAAY,MAAQ,GACpBpC,EAAK,kBAAmBsC,CAAI,CAC9B,CACA,SAASG,EAAkBH,EAAa,CACtCF,EAAY,MAAQ,GACpBpC,EAAK,cAAesC,CAAI,CAC1B,CACA,SAASI,EAAkBJ,EAAa,CACtCF,EAAY,MAAQ,GACpBpC,EAAK,cAAesC,CAAI,CAC1B,CACA,SAASK,EAAiBL,EAAa,CACrCF,EAAY,MAAQ,GACpBpC,EAAK,aAAcsC,CAAI,CACzB,6uCCmEA,MAAMxC,EAAQC,EASRG,EAAWC,IACXyC,EAASC,KACTC,EAAgBC,KAChBC,EAAc3C,EAAI,CAAC,EACnB4C,EAAoB5C,IACpB6C,EAAa7C,EAAI,EAAE,EACnB8C,EAAU9C,EAAW,CAAA,CAAW,EAChC+C,EAAUtD,EAAM,aAAe,IAAM,GACrCuD,EAAchD,IACdiD,EAAmBjD,EAAI,EAAK,EAC5BkD,EAAqBlD,EAAI,EAAK,EAC9BmD,EAAkBnD,EAAI,EAAE,EACxBoD,EAAmBpD,EAAyB,IAAI,EAEhDqD,EAAmBrD,EAAI,CAAE,QAAS,GAAO,MAAO,GAAa,EAC7DsD,EAA2B,CAAC,CAAE,IAAK,SAAW,CAAA,EAC9CC,EAA6B,CACjC,CAAE,IAAK,kBAAmB,MAAO,YAAa,EAC9C,CAAE,IAAK,cAAe,MAAO,cAAe,CAAA,EAE9C,SAASC,EAAwBtD,EAAO,CACtC,OAAOuD,EAAYvD,EAAOL,EAAS,UAAU,qBAAqB,CACpE,CACM,MAAA6D,EAAO9C,EAAS,IACbnB,EAAM,OAAO,MACrB,EAIKkE,EAAc3D,EAAW,CAAA,CAAE,EAEjC4D,GAAU,IAAM,CACdD,EAAY,MAAQ,CAClB,CAAE,IAAK,WAAY,MAAO,IAAK,EAC/B,CAAE,IAAK,OAAQ,MAAO,MAAO,EAC7B,CAAE,IAAK,SAAU,MAAO,QAAS,EACjC,CACE,IAAK,eACL,MAAO,cACT,EACA,CACE,IAAK,YACL,MAAO,YACP,UAAY9C,GAAmB4C,EAAY5C,CAAe,CAC5D,EACA,CACE,IAAKpB,EAAM,aAAe,eAAiB,aAC3C,MAAOA,EAAM,aAAe,eAAiB,aAC7C,UAAYoB,GAAmB4C,EAAY5C,CAAe,CAC5D,EACA,CACE,IAAK,SACL,MAAOpB,EAAM,aAAe,mBAAqB,WACjD,UAAW,CAACoB,EAAgBgD,EAAc5B,IAAmB,CAC3D,GAAI,CAACA,EACI,MAAA,GAET,MAAM6B,EAAY7B,EAEX,MAAA,GADS8B,GAAcD,EAAU,aAAc,CAAC,CACtC,IAAI,IAAIN,EAAwBM,EAAU,UAAU,CAAC,GAAG,EAC3E,CACF,EACA,CAAE,IAAK,iBAAkB,MAAO,WAAY,EAC5C,GAAIrE,EAAM,aAAe6D,EAAaC,CAAA,EAEpC9D,EAAM,cACRkE,EAAY,MAAM,QAAQ,CAAE,IAAK,UAAW,MAAO,MAAO,CAC5D,CACD,EAEK,MAAAK,EAAchE,EAAwB,MAAS,EAC5C,SAAAgC,EAAiBC,EAAa5B,EAAgC,OAAW,CAChFyC,EAAQ,MAAQb,EAChBmB,EAAiB,MAAQ,EACTD,EAAA,MAAQ,qBAAqBlB,EAAK,QAAQ,UAAUA,EAAK,IAAI,WAAW5B,CAAS,UACjG2D,EAAY,MAAQ3D,EAChBoC,EAAc,gBAAkB,GAClCS,EAAmB,MAAQ,GAETe,GAEtB,CAEA,SAASA,GAAoB,CACvB,GAAAb,EAAiB,QAAU,EAA0B,CACvD,MAAMnC,EAA8B,CAClC,QAAS,OAAO6B,EAAQ,MAAM,QAAQ,EACtC,MAAOA,EAAQ,MAAM,KAAA,EAEdjD,EAAA,iBAAiBoB,CAAO,EAAE,MAAOiD,GAAU,QAAQ,IAAIA,EAAM,QAAQ,CAAC,CACjF,CACI,GAAAd,EAAiB,QAAU,EAAwB,CACrD,MAAMnC,EAAiC,CACrC,QAAS,OAAO6B,EAAQ,MAAM,QAAQ,EACtC,MAAOA,EAAQ,MAAM,KAAA,EAEnBkB,EAAY,QACd/C,EAAQ,UAAY+C,EAAY,OAElCnE,EACG,eAAeoB,CAAO,EACtB,KAAMkD,GAAQ,QAAQ,IAAIA,CAAG,CAAC,EAC9B,MAAOD,GAAU,QAAQ,IAAIA,EAAM,QAAQ,CAAC,CACjD,CACI,GAAAd,EAAiB,QAAU,EAA8B,CAC3D,MAAMnC,EAA8B,CAClC,QAAS,OAAO6B,EAAQ,MAAM,QAAQ,EACtC,MAAOA,EAAQ,MAAM,KAAA,EAEvBjD,EAAS,qBAAqBoB,CAAO,CACvC,CAEA+C,EAAY,MAAQ,OACpBd,EAAmB,MAAQ,EAC7B,CAEA,SAASkB,EAAmBnC,EAAa,CACvCkB,EAAgB,MAAQ,uBAAuBlB,EAAK,QAAQ,UAAUA,EAAK,IAAI,KAC/EmB,EAAiB,MAAQ,EACzBN,EAAQ,MAAQb,EAChBiB,EAAmB,MAAQ,EAC7B,CAEA,SAAShB,EAAwBD,EAAa,CAC5Ca,EAAQ,MAAQb,EAChBgB,EAAiB,MAAQ,EAC3B,CAEA,SAASd,EAAuBF,EAAa,CAC3CkB,EAAgB,MAAQ,+BAA+BlB,EAAK,QAAQ,UAAUA,EAAK,IAAI,KACvFa,EAAQ,MAAQb,EAChBmB,EAAiB,MAAQ,EACzBF,EAAmB,MAAQ,EAC7B,CAEA,SAASmB,EAAmBpC,EAAa,CAC9BpC,EAAA,iBAAiB,CAAE,QAAS,OAAOoC,EAAK,QAAQ,EAAG,MAAOA,EAAK,KAAO,CAAA,CACjF,CAEA,SAASK,EAAiBL,EAAa,CACrCoB,EAAiB,MAAM,MAAQpB,EAC/BoB,EAAiB,MAAM,QAAU,EACnC,CAES,SAAAiB,GAAuBrC,EAAMsC,EAAOC,EAAO,CAE7C/E,EAAM,eAGX+E,EAAM,eAAe,EAErB,QAAQ,IAAIvC,CAAI,EAClB,CAEM,MAAAwC,GAAgBxC,GAAS,CACzBxC,EAAM,cAAgBI,EAAS,cAAgBoC,EAAK,OAE7CpC,EAAA,UAAUoC,EAAK,KAAK,EAE3BA,GAAQA,EAAK,WAAapC,EAAS,UAAU,eACtCA,EAAA,UAAU,eAAeoC,CAAI,EAClCxC,EAAM,cACR8C,EAAO,KAAK,CAAE,KAAM,mBAAqB,CAAA,GAGlC1C,EAAA,UAAU,eAAe,IAAI,CACxC,EAGI6E,GAAgB,IAAM,OACtB,GAAA7E,EAAS,UAAU,cAAe,CAC9B,MAAA8E,EAAYlF,EAAM,OAAO,UAC5BmF,GAAMA,EAAE,WAAa/E,EAAS,UAAU,aAAA,EAEvC8E,GAAa,GACH5D,EAAAiC,EAAA,QAAA,MAAAjC,EAAO,UAAU4D,IAE7B,QAAQ,IAAI,8BAA8B/B,EAAkB,KAAK,EAAE,EACnEA,EAAkB,MAAQ,OAE9B,CAAA,EAGF,OAAAiC,GACE,IAAMhF,EAAS,UAAU,cACxBiF,GAAQ,OACOrF,EAAM,OAAO,UAAWmF,GAAMA,EAAE,WAAaE,CAAG,EAElD,KACV/D,EAAAiC,EAAY,QAAZ,MAAAjC,EAAmB,gBAEvB,CAAA"}