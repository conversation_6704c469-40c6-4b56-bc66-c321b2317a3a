{"version": 3, "file": "TradesLogChart-DDJ_fK8s.js", "sources": ["../../src/shared/charts/binCount.ts", "../../src/components/charts/ProfitDistributionChart.vue", "../../src/components/charts/CumProfitChart.vue", "../../src/components/charts/TradesLogChart.vue"], "sourcesContent": ["export function binData(data: number[], bins: number) {\n  const minimum = Math.min(...data);\n  const maximum = Math.max(...data);\n  const binSize = ((maximum - minimum) * 1.01) / bins;\n  // console.log(`data ranges from ${minimum} to ${maximum}, binsize ${binSize}`);\n  // Count occurances an array with [bucketStart, count in this bucket]\n  const baseBins = [...Array(bins).keys()].map((i) => [\n    Math.round((minimum + i * binSize) * 1000) / 1000,\n    0,\n  ]);\n\n  // console.log(baseBins);\n  for (let i = 0; i < data.length; i++) {\n    const index = Math.min(Math.floor((data[i] - minimum) / binSize), bins - 1);\n    if (!isNaN(index)) {\n      baseBins[index][1]++;\n    }\n  }\n\n  return baseBins;\n}\n", "<template>\n  <div class=\"d-flex flex-column h-100 position-relative\">\n    <div class=\"flex-grow-1\">\n      <ECharts v-if=\"trades\" :option=\"chartOptions\" autoresize :theme=\"settingsStore.chartTheme\" />\n    </div>\n    <b-form-group\n      class=\"z-2\"\n      :class=\"showTitle ? 'ms-5 ps-5' : 'position-absolute'\"\n      label=\"Bins\"\n      style=\"width: 33%; min-width: 12rem\"\n      label-for=\"input-bins\"\n      label-cols=\"6\"\n      content-cols=\"6\"\n      size=\"sm\"\n    >\n      <b-form-select\n        id=\"input-bins\"\n        v-model=\"settingsStore.profitDistributionBins\"\n        size=\"sm\"\n        class=\"mt-1\"\n        :options=\"binOptions\"\n      ></b-form-select>\n    </b-form-group>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport ECharts from 'vue-echarts';\nimport { EChartsOption } from 'echarts';\n\nimport { use } from 'echarts/core';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport { BarChart } from 'echarts/charts';\nimport {\n  DatasetComponent,\n  DataZoomComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n} from 'echarts/components';\n\nimport { ClosedTrade } from '@/types';\nimport { binData } from '@/shared/charts/binCount';\nimport { useSettingsStore } from '@/stores/settings';\n\nuse([\n  BarChart,\n\n  CanvasRenderer,\n\n  DatasetComponent,\n  DataZoomComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n]);\n\n// Define Column labels here to avoid typos\nconst CHART_PROFIT = 'Trade count';\n\nconst props = defineProps({\n  trades: { required: true, type: Array as () => ClosedTrade[] },\n  showTitle: { default: true, type: Boolean },\n});\nconst settingsStore = useSettingsStore();\n// registerTransform(ecStat.transform.histogram);\n// console.log(profits);\n// const data = [[]];\nconst binOptions = [\n  { text: '10', value: 10 },\n  { text: '15', value: 15 },\n  { text: '20', value: 20 },\n  { text: '25', value: 25 },\n  { text: '50', value: 50 },\n];\nconst data = computed(() => {\n  const profits = props.trades.map((trade) => trade.profit_ratio);\n\n  return binData(profits, settingsStore.profitDistributionBins);\n});\n\nconst chartOptions = computed((): EChartsOption => {\n  const chartOptionsLoc: EChartsOption = {\n    title: {\n      text: 'Profit distribution',\n      show: props.showTitle,\n    },\n    backgroundColor: 'rgba(0, 0, 0, 0)',\n    dataset: {\n      source: data.value,\n    },\n    tooltip: {\n      trigger: 'axis',\n      axisPointer: {\n        type: 'line',\n        label: {\n          backgroundColor: '#6a7985',\n        },\n      },\n    },\n    legend: {\n      data: [CHART_PROFIT],\n      right: '5%',\n      selectedMode: false,\n    },\n    xAxis: {\n      type: 'category',\n      name: 'Profit %',\n      nameLocation: 'middle',\n      nameGap: 25,\n    },\n    yAxis: [\n      {\n        type: 'value',\n        name: CHART_PROFIT,\n        splitLine: {\n          show: false,\n        },\n        nameRotate: 90,\n        nameLocation: 'middle',\n        nameGap: 35,\n        position: 'left',\n      },\n    ],\n    // grid: {\n    // bottom: 80,\n    // },\n\n    series: [\n      {\n        type: 'bar',\n        name: CHART_PROFIT,\n        animation: true,\n        encode: {\n          x: 'x0',\n          y: 'y0',\n        },\n\n        // symbol: 'none',\n      },\n    ],\n  };\n  return chartOptionsLoc;\n});\n</script>\n\n<style scoped>\n.echarts {\n  width: 100%;\n  height: 100%;\n  min-height: 150px;\n}\n</style>\n", "<template>\n  <ECharts\n    v-if=\"trades\"\n    ref=\"chart\"\n    :option=\"cumProfitChartOptions\"\n    :theme=\"settingsStore.chartTheme\"\n    autoresize\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport { EChartsOption } from 'echarts';\nimport ECharts from 'vue-echarts';\n\nimport { <PERSON><PERSON><PERSON>, LineChart } from 'echarts/charts';\nimport {\n  DataZoomComponent,\n  DatasetComponent,\n  GridComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n} from 'echarts/components';\nimport { use } from 'echarts/core';\nimport { CanvasRenderer } from 'echarts/renderers';\n\nimport { dataZoomPartial } from '@/shared/charts/chartZoom';\nimport { useSettingsStore } from '@/stores/settings';\nimport {\n  ClosedTrade,\n  CumProfitChartData,\n  CumProfitData,\n  CumProfitDataPerDate,\n  Trade,\n} from '@/types';\nimport type { ComputedRefWithControl } from '@vueuse/core';\n\nimport { formatPrice, timestampToDateString } from '@/shared/formatters';\n\nuse([\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n\n  Canvas<PERSON>enderer,\n\n  DatasetComponent,\n  DataZoomComponent,\n  GridComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n]);\n\n// Define Column labels here to avoid typos\nconst CHART_PROFIT = 'Profit';\n\nconst props = defineProps({\n  trades: { required: true, type: Array as () => ClosedTrade[] },\n  openTrades: { required: false, type: Array as () => Trade[], default: () => [] },\n  showTitle: { default: true, type: Boolean },\n  profitColumn: { default: 'profit_abs', type: String },\n});\nconst settingsStore = useSettingsStore();\nconst colorStore = useColorStore();\n// const botList = ref<string[]>([]);\n\nconst chart = ref<InstanceType<typeof ECharts>>();\n\nconst openProfit = computed<number>(() => {\n  return props.openTrades.reduce(\n    (a, v) => a + (v['total_profit_abs'] ?? v[props.profitColumn] ?? 0),\n    0,\n  );\n});\n\nconst cumulativeData = computed<CumProfitChartData[]>(() => {\n  const res: CumProfitData[] = [];\n  const resD: CumProfitDataPerDate = {};\n  const closedTrades = props.trades\n    .slice()\n    .sort((a, b) => (a.close_timestamp > b.close_timestamp ? 1 : -1));\n  let profit = 0.0;\n\n  for (let i = 0, len = closedTrades.length; i < len; i += 1) {\n    const trade = closedTrades[i];\n\n    if (trade.close_timestamp && trade[props.profitColumn]) {\n      profit += trade[props.profitColumn];\n      if (!resD[trade.close_timestamp]) {\n        // New timestamp\n        resD[trade.close_timestamp] = { profit, [trade.botId]: profit };\n      } else {\n        // Add to existing profit\n        resD[trade.close_timestamp].profit += trade[props.profitColumn];\n        if (resD[trade.close_timestamp][trade.botId]) {\n          resD[trade.close_timestamp][trade.botId] += trade[props.profitColumn];\n        } else {\n          resD[trade.close_timestamp][trade.botId] = profit;\n        }\n      }\n      res.push({ date: trade.close_timestamp, profit, [trade.botId]: profit });\n    }\n  }\n\n  const valueArray: CumProfitChartData[] = Object.entries(resD).map(\n    ([k, v]: [string, CumProfitData]) => {\n      const obj = { date: parseInt(k, 10), profit: v.profit };\n      // TODO: The below could allow \"lines\" per bot\"\n      // this.botList.forEach((botId) => {\n      // obj[botId] = v[botId];\n      // });\n      return obj;\n    },\n  );\n\n  if (props.openTrades.length > 0) {\n    let lastProfit = 0;\n    let lastDate = 0;\n    if (valueArray.length > 0) {\n      const lastPoint = valueArray[valueArray.length - 1];\n      lastProfit = lastPoint.profit ?? 0;\n      lastDate = lastPoint.date ?? 0;\n    } else {\n      lastDate = props.openTrades[0].open_timestamp;\n    }\n    const resultWitHOpen = (lastProfit ?? 0) + openProfit.value;\n    valueArray.push({ date: lastDate, currentProfit: lastProfit });\n    // Add one day to date to ensure it's showing properly\n    const tomorrow = Date.now() + 24 * 60 * 60 * 1000;\n    valueArray.push({ date: tomorrow, currentProfit: resultWitHOpen });\n  }\n  return valueArray;\n});\n\nfunction generateChart(initial = false) {\n  const { colorProfit, colorLoss } = colorStore;\n  const chartOptionsLoc: EChartsOption = {\n    dataset: {\n      dimensions: ['date', 'profit', 'currentProfit'],\n      source: cumulativeData.value,\n    },\n\n    series: [\n      {\n        // Keep  current-profit before profit, so the starting symbol is behind\n        type: 'line',\n        name: 'currentProfit',\n\n        animation: initial,\n\n        lineStyle: {\n          color: openProfit.value > 0 ? colorProfit : colorLoss,\n          type: 'dotted',\n        },\n        itemStyle: {\n          color: openProfit.value > 0 ? colorProfit : colorLoss,\n        },\n        encode: {\n          x: 'date',\n          y: 'currentProfit',\n        },\n      },\n      {\n        type: 'line',\n        name: CHART_PROFIT,\n        animation: initial,\n        step: 'end',\n        lineStyle: {\n          color: settingsStore.chartTheme === 'dark' ? '#c2c2c2' : 'black',\n        },\n        itemStyle: {\n          color: settingsStore.chartTheme === 'dark' ? '#c2c2c2' : 'black',\n        },\n        encode: {\n          x: 'date',\n          y: 'profit',\n        },\n        // symbol: 'none',\n      },\n    ],\n  };\n  // TODO: maybe have profit lines per bot?\n  // this.botList.forEach((botId: string) => {\n  //   console.log('bot', botId);\n  //   chartOptionsLoc.series.push({\n  //     type: 'line',\n  //     name: botId,\n  //     animation: true,\n  //     step: 'end',\n  //     lineStyle: {\n  //       color: settingsStore.chartTheme === 'dark' ? '#c2c2c2' : 'black',\n  //     },\n  //     itemStylesettingsStore.chartTheme === 'dark' ? '#c2c2c2' : 'black',\n  //     },\n  //     // symbol: 'none',\n  //   });\n  // });\n  return chartOptionsLoc;\n}\nfunction updateChart(initial = false) {\n  const chartOptionsLoc = generateChart(initial);\n  chart.value?.setOption(chartOptionsLoc, {\n    replaceMerge: ['series', 'dataset'],\n  });\n}\n\nconst cumProfitChartOptions: ComputedRefWithControl<EChartsOption> = computedWithControl(\n  () => props.trades,\n  () => {\n    const chartOptionsLoc: EChartsOption = {\n      title: {\n        text: 'Cumulative Profit',\n        show: props.showTitle,\n      },\n      backgroundColor: 'rgba(0, 0, 0, 0)',\n      tooltip: {\n        trigger: 'axis',\n        formatter: (params) => {\n          const profit = params[0].data.profit;\n          const currentProfit = params[0].data['currentProfit'];\n          const profitText = currentProfit\n            ? `Projected profit (incl. unrealized): ${formatPrice(currentProfit, 3)}`\n            : `Profit: ${formatPrice(profit, 3)}`;\n          return `${timestampToDateString(params[1].data.date)}<br />${\n            params[1].marker\n          }${profitText}`;\n        },\n        axisPointer: {\n          type: 'line',\n          label: {\n            backgroundColor: '#6a7985',\n          },\n        },\n      },\n      legend: {\n        data: [CHART_PROFIT],\n        right: '5%',\n        selectedMode: false,\n      },\n      useUTC: false,\n      xAxis: {\n        type: 'time',\n      },\n      yAxis: [\n        {\n          type: 'value',\n          name: CHART_PROFIT,\n          splitLine: {\n            show: false,\n          },\n          nameRotate: 90,\n          nameLocation: 'middle',\n          nameGap: 40,\n        },\n      ],\n      grid: {\n        bottom: 80,\n      },\n      dataZoom: [\n        {\n          type: 'inside',\n          // xAxisIndex: [0],\n          start: 0,\n\n          end: 100,\n        },\n        {\n          // xAxisIndex: [0],\n          bottom: 10,\n          start: 0,\n          end: 100,\n          ...dataZoomPartial,\n        },\n      ],\n    };\n\n    const chartOptionsLoc1 = generateChart(false);\n    // Merge the series and dataset, but not the rest\n    chartOptionsLoc.series = chartOptionsLoc1.series;\n    chartOptionsLoc.dataset = chartOptionsLoc1.dataset;\n    // console.log('computed chartOptionsLoc', chartOptionsLoc);\n    return chartOptionsLoc;\n  },\n);\n\nonMounted(() => {\n  // initializeChart();\n});\n\nwatchThrottled(\n  () => props.openTrades,\n  () => {\n    // cumProfitChartOptions.trigger();\n    updateChart();\n  },\n  { throttle: 60 * 1000 },\n);\nwatch(\n  () => settingsStore.chartTheme,\n  () => {\n    cumProfitChartOptions.trigger();\n  },\n);\n</script>\n\n<style scoped>\n.echarts {\n  width: 100%;\n  height: 100%;\n  min-height: 150px;\n}\n</style>\n", "<template>\n  <ECharts\n    v-if=\"trades.length > 0\"\n    :option=\"chartOptions\"\n    autoresize\n    :theme=\"settingsStore.chartTheme\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport ECharts from 'vue-echarts';\nimport { EChartsOption } from 'echarts';\n\nimport { use } from 'echarts/core';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport { LineChart, BarChart } from 'echarts/charts';\nimport {\n  DatasetComponent,\n  DataZoomComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n  VisualMapComponent,\n  VisualMapPiecewiseComponent,\n} from 'echarts/components';\n\nimport { ClosedTrade } from '@/types';\nimport { useSettingsStore } from '@/stores/settings';\n\nimport { timestampms } from '@/shared/formatters';\nimport { dataZoomPartial } from '@/shared/charts/chartZoom';\nimport { useColorStore } from '@/stores/colors';\n\nuse([\n  <PERSON><PERSON><PERSON>,\n  <PERSON>C<PERSON>,\n\n  <PERSON>vas<PERSON><PERSON>er,\n\n  DatasetComponent,\n  DataZoomComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n  VisualMapComponent,\n  VisualMapPiecewiseComponent,\n]);\n\n// Define Column labels here to avoid typos\nconst CHART_PROFIT = 'Profit %';\nconst CHART_COLOR = '#9be0a8';\n\nconst props = defineProps({\n  trades: { required: true, type: Array as () => ClosedTrade[] },\n  showTitle: { default: true, type: Boolean },\n});\nconst settingsStore = useSettingsStore();\nconst colorStore = useColorStore();\nconst chartData = computed(() => {\n  const res: (number | string)[][] = [];\n  const sortedTrades = props.trades\n    .slice(0)\n    .sort((a, b) => (a.close_timestamp > b.close_timestamp ? 1 : -1));\n  for (let i = 0, len = sortedTrades.length; i < len; i += 1) {\n    const trade = sortedTrades[i];\n    const entry = [\n      i,\n      (trade.profit_ratio * 100).toFixed(2),\n      trade.pair,\n      trade.botName,\n      timestampms(trade.close_timestamp),\n      trade.is_short === undefined || !trade.is_short ? 'Long' : 'Short',\n    ];\n    res.push(entry);\n  }\n  return res;\n});\n\nconst chartOptions = computed((): EChartsOption => {\n  // const { chartData } = this;\n  // Show a maximum of 50 trades by default - allowing to zoom out further.\n  const datazoomStart = chartData.value.length > 0 ? (1 - 50 / chartData.value.length) * 100 : 100;\n  return {\n    title: {\n      text: 'Trades log',\n      show: props.showTitle,\n    },\n    backgroundColor: 'rgba(0, 0, 0, 0)',\n    dataset: {\n      dimensions: ['date', 'profit'],\n      source: chartData.value,\n    },\n    tooltip: {\n      trigger: 'axis',\n      formatter: (params) => {\n        const botName = params[0].data[3] ? ` | ${params[0].data[3]}` : '';\n        return `${params[0].data[2]} | ${params[0].data[5]} ${botName}<br>${params[0].data[4]}<br>Profit ${params[0].data[1]} %`;\n      },\n      axisPointer: {\n        type: 'line',\n        label: {\n          backgroundColor: '#6a7985',\n        },\n      },\n    },\n    xAxis: {\n      type: 'value',\n      show: false,\n    },\n    yAxis: [\n      {\n        type: 'value',\n        name: CHART_PROFIT,\n        splitLine: {\n          show: false,\n        },\n        nameRotate: 90,\n        nameLocation: 'middle',\n        nameGap: 30,\n      },\n    ],\n    grid: {\n      bottom: 80,\n    },\n    dataZoom: [\n      {\n        type: 'inside',\n        start: datazoomStart,\n        end: 100,\n      },\n      {\n        bottom: 10,\n        start: datazoomStart,\n        end: 100,\n        ...dataZoomPartial,\n      },\n    ],\n    visualMap: [\n      {\n        show: true,\n        seriesIndex: 0,\n        pieces: [\n          {\n            max: 0.0,\n            color: colorStore.colorLoss,\n          },\n          {\n            min: 0.0,\n            color: colorStore.colorProfit,\n          },\n        ],\n      },\n    ],\n    series: [\n      {\n        type: 'bar',\n        name: CHART_PROFIT,\n        barGap: '0%',\n        barCategoryGap: '0%',\n        animation: false,\n        label: {\n          show: true,\n          position: 'top',\n          rotate: 90,\n          offset: [7.5, 7.5],\n          formatter: '{@[1]} %',\n          color: settingsStore.chartTheme === 'dark' ? '#c2c2c2' : '#3c3c3c',\n        },\n        encode: {\n          x: 0,\n          y: 1,\n        },\n\n        itemStyle: {\n          color: CHART_COLOR,\n        },\n      },\n    ],\n  };\n});\n</script>\n\n<style scoped>\n.echarts {\n  width: 100%;\n  height: 100%;\n  min-height: 150px;\n}\n</style>\n"], "names": ["binData", "data", "bins", "minimum", "binSize", "baseBins", "i", "index", "CHART_PROFIT", "use", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DatasetComponent", "DataZoomComponent", "LegendComponent", "TitleComponent", "TooltipComponent", "props", "__props", "settingsStore", "useSettingsStore", "binOptions", "computed", "profits", "trade", "chartOptions", "Line<PERSON>hart", "GridComponent", "colorStore", "useColorStore", "chart", "ref", "openProfit", "a", "v", "cumulativeData", "res", "resD", "closedTrades", "b", "profit", "len", "valueArray", "k", "lastProfit", "lastDate", "lastPoint", "resultWitHOpen", "tomorrow", "generateChart", "initial", "colorProfit", "colorLoss", "updateChart", "chartOptionsLoc", "_a", "cumProfitChartOptions", "computedWithControl", "params", "currentProfit", "profitText", "formatPrice", "timestampToDateString", "dataZoomPartial", "chartOptionsLoc1", "onMounted", "watchThrottled", "watch", "CHART_COLOR", "VisualMapComponent", "VisualMapPiecewiseComponent", "chartData", "sortedTrades", "entry", "timestampms", "datazoomStart", "botName"], "mappings": "iZAAgB,SAAAA,GAAQC,EAAgBC,EAAc,CACpD,MAAMC,EAAU,KAAK,IAAI,GAAGF,CAAI,EAE1BG,GADU,KAAK,IAAI,GAAGH,CAAI,EACJE,GAAW,KAAQD,EAGzCG,EAAW,CAAC,GAAG,MAAMH,CAAI,EAAE,MAAM,EAAE,IAAKI,GAAM,CAClD,KAAK,OAAOH,EAAUG,EAAIF,GAAW,GAAI,EAAI,IAC7C,CAAA,CACD,EAGD,QAASE,EAAI,EAAGA,EAAIL,EAAK,OAAQK,IAAK,CACpC,MAAMC,EAAQ,KAAK,IAAI,KAAK,OAAON,EAAKK,CAAC,EAAIH,GAAWC,CAAO,EAAGF,EAAO,CAAC,EACrE,MAAMK,CAAK,GACLF,EAAAE,CAAK,EAAE,CAAC,GAErB,CAEO,OAAAF,CACT,wFCsCMG,EAAe,0IAbjBC,EAAA,CACFC,EAEAC,EAEAC,EACAC,EACAC,EACAC,EACAC,CAAA,CACD,EAKD,MAAMC,EAAQC,EAIRC,EAAgBC,IAIhBC,EAAa,CACjB,CAAE,KAAM,KAAM,MAAO,EAAG,EACxB,CAAE,KAAM,KAAM,MAAO,EAAG,EACxB,CAAE,KAAM,KAAM,MAAO,EAAG,EACxB,CAAE,KAAM,KAAM,MAAO,EAAG,EACxB,CAAE,KAAM,KAAM,MAAO,EAAG,CAAA,EAEpBpB,EAAOqB,EAAS,IAAM,CAC1B,MAAMC,EAAUN,EAAM,OAAO,IAAKO,GAAUA,EAAM,YAAY,EAEvD,OAAAxB,GAAQuB,EAASJ,EAAc,sBAAsB,CAAA,CAC7D,EAEKM,EAAeH,EAAS,KACW,CACrC,MAAO,CACL,KAAM,sBACN,KAAML,EAAM,SACd,EACA,gBAAiB,mBACjB,QAAS,CACP,OAAQhB,EAAK,KACf,EACA,QAAS,CACP,QAAS,OACT,YAAa,CACX,KAAM,OACN,MAAO,CACL,gBAAiB,SACnB,CACF,CACF,EACA,OAAQ,CACN,KAAM,CAACO,CAAY,EACnB,MAAO,KACP,aAAc,EAChB,EACA,MAAO,CACL,KAAM,WACN,KAAM,WACN,aAAc,SACd,QAAS,EACX,EACA,MAAO,CACL,CACE,KAAM,QACN,KAAMA,EACN,UAAW,CACT,KAAM,EACR,EACA,WAAY,GACZ,aAAc,SACd,QAAS,GACT,SAAU,MACZ,CACF,EAKA,OAAQ,CACN,CACE,KAAM,MACN,KAAMA,EACN,UAAW,GACX,OAAQ,CACN,EAAG,KACH,EAAG,IACL,CAGF,CACF,CAAA,EAGH,koBCzFKA,EAAe,+NAfjBC,EAAA,CACFC,EACAgB,EAEAf,EAEAC,EACAC,EACAc,GACAb,EACAC,EACAC,CAAA,CACD,EAKD,MAAMC,EAAQC,EAMRC,EAAgBC,IAChBQ,EAAaC,IAGbC,EAAQC,IAERC,EAAaV,EAAiB,IAC3BL,EAAM,WAAW,OACtB,CAACgB,EAAGC,IAAMD,GAAKC,EAAE,kBAAuBA,EAAEjB,EAAM,YAAY,GAAK,GACjE,CAAA,CAEH,EAEKkB,EAAiBb,EAA+B,IAAM,CAC1D,MAAMc,EAAuB,CAAA,EACvBC,EAA6B,CAAA,EAC7BC,EAAerB,EAAM,OACxB,QACA,KAAK,CAACgB,EAAGM,IAAON,EAAE,gBAAkBM,EAAE,gBAAkB,EAAI,EAAG,EAClE,IAAIC,EAAS,EAEJ,QAAAlC,EAAI,EAAGmC,EAAMH,EAAa,OAAQhC,EAAImC,EAAKnC,GAAK,EAAG,CACpD,MAAAkB,EAAQc,EAAahC,CAAC,EAExBkB,EAAM,iBAAmBA,EAAMP,EAAM,YAAY,IACzCuB,GAAAhB,EAAMP,EAAM,YAAY,EAC7BoB,EAAKb,EAAM,eAAe,GAK7Ba,EAAKb,EAAM,eAAe,EAAE,QAAUA,EAAMP,EAAM,YAAY,EAC1DoB,EAAKb,EAAM,eAAe,EAAEA,EAAM,KAAK,EACpCa,EAAAb,EAAM,eAAe,EAAEA,EAAM,KAAK,GAAKA,EAAMP,EAAM,YAAY,EAEpEoB,EAAKb,EAAM,eAAe,EAAEA,EAAM,KAAK,EAAIgB,GAPxCH,EAAAb,EAAM,eAAe,EAAI,CAAE,OAAAgB,EAAQ,CAAChB,EAAM,KAAK,EAAGgB,GAUrDJ,EAAA,KAAK,CAAE,KAAMZ,EAAM,gBAAiB,OAAAgB,EAAQ,CAAChB,EAAM,KAAK,EAAGgB,CAAQ,CAAA,EAE3E,CAEA,MAAME,EAAmC,OAAO,QAAQL,CAAI,EAAE,IAC5D,CAAC,CAACM,EAAGT,CAAC,KACQ,CAAE,KAAM,SAASS,EAAG,EAAE,EAAG,OAAQT,EAAE,QAMjD,EAGE,GAAAjB,EAAM,WAAW,OAAS,EAAG,CAC/B,IAAI2B,EAAa,EACbC,EAAW,EACX,GAAAH,EAAW,OAAS,EAAG,CACzB,MAAMI,EAAYJ,EAAWA,EAAW,OAAS,CAAC,EAClDE,EAAaE,EAAU,QAAU,EACjCD,EAAWC,EAAU,MAAQ,CAAA,MAElBD,EAAA5B,EAAM,WAAW,CAAC,EAAE,eAE3B,MAAA8B,GAAkBH,GAAc,GAAKZ,EAAW,MACtDU,EAAW,KAAK,CAAE,KAAMG,EAAU,cAAeD,EAAY,EAE7D,MAAMI,EAAW,KAAK,IAAA,EAAQ,GAAK,GAAK,GAAK,IAC7CN,EAAW,KAAK,CAAE,KAAMM,EAAU,cAAeD,EAAgB,CACnE,CACO,OAAAL,CAAA,CACR,EAEQ,SAAAO,EAAcC,EAAU,GAAO,CAChC,KAAA,CAAE,YAAAC,EAAa,UAAAC,CAAc,EAAAxB,EA8D5B,MA7DgC,CACrC,QAAS,CACP,WAAY,CAAC,OAAQ,SAAU,eAAe,EAC9C,OAAQO,EAAe,KACzB,EAEA,OAAQ,CACN,CAEE,KAAM,OACN,KAAM,gBAEN,UAAWe,EAEX,UAAW,CACT,MAAOlB,EAAW,MAAQ,EAAImB,EAAcC,EAC5C,KAAM,QACR,EACA,UAAW,CACT,MAAOpB,EAAW,MAAQ,EAAImB,EAAcC,CAC9C,EACA,OAAQ,CACN,EAAG,OACH,EAAG,eACL,CACF,EACA,CACE,KAAM,OACN,KAAM5C,EACN,UAAW0C,EACX,KAAM,MACN,UAAW,CACT,MAAO/B,EAAc,aAAe,OAAS,UAAY,OAC3D,EACA,UAAW,CACT,MAAOA,EAAc,aAAe,OAAS,UAAY,OAC3D,EACA,OAAQ,CACN,EAAG,OACH,EAAG,QACL,CAEF,CACF,CAAA,CAmBJ,CACS,SAAAkC,EAAYH,EAAU,GAAO,OAC9B,MAAAI,EAAkBL,EAAcC,CAAO,GACvCK,EAAAzB,EAAA,QAAA,MAAAyB,EAAO,UAAUD,EAAiB,CACtC,aAAc,CAAC,SAAU,SAAS,CAAA,EAEtC,CAEA,MAAME,EAA+DC,EACnE,IAAMxC,EAAM,OACZ,IAAM,CACJ,MAAMqC,EAAiC,CACrC,MAAO,CACL,KAAM,oBACN,KAAMrC,EAAM,SACd,EACA,gBAAiB,mBACjB,QAAS,CACP,QAAS,OACT,UAAYyC,GAAW,CACrB,MAAMlB,EAASkB,EAAO,CAAC,EAAE,KAAK,OACxBC,EAAgBD,EAAO,CAAC,EAAE,KAAK,cAC/BE,EAAaD,EACf,wCAAwCE,EAAYF,EAAe,CAAC,CAAC,GACrE,WAAWE,EAAYrB,EAAQ,CAAC,CAAC,GACrC,MAAO,GAAGsB,GAAsBJ,EAAO,CAAC,EAAE,KAAK,IAAI,CAAC,SAClDA,EAAO,CAAC,EAAE,MACZ,GAAGE,CAAU,EACf,EACA,YAAa,CACX,KAAM,OACN,MAAO,CACL,gBAAiB,SACnB,CACF,CACF,EACA,OAAQ,CACN,KAAM,CAACpD,CAAY,EACnB,MAAO,KACP,aAAc,EAChB,EACA,OAAQ,GACR,MAAO,CACL,KAAM,MACR,EACA,MAAO,CACL,CACE,KAAM,QACN,KAAMA,EACN,UAAW,CACT,KAAM,EACR,EACA,WAAY,GACZ,aAAc,SACd,QAAS,EACX,CACF,EACA,KAAM,CACJ,OAAQ,EACV,EACA,SAAU,CACR,CACE,KAAM,SAEN,MAAO,EAEP,IAAK,GACP,EACA,CAEE,OAAQ,GACR,MAAO,EACP,IAAK,IACL,GAAGuD,CACL,CACF,CAAA,EAGIC,EAAmBf,EAAc,EAAK,EAE5C,OAAAK,EAAgB,OAASU,EAAiB,OAC1CV,EAAgB,QAAUU,EAAiB,QAEpCV,CACT,CAAA,EAGF,OAAAW,EAAU,IAAM,CAAA,CAEf,EAEDC,GACE,IAAMjD,EAAM,WACZ,IAAM,CAEQoC,GACd,EACA,CAAE,SAAU,GAAK,GAAK,CAAA,EAExBc,GACE,IAAMhD,EAAc,WACpB,IAAM,CACJqC,EAAsB,QAAQ,CAChC,CAAA,+LC5PIhD,EAAe,WACf4D,GAAc,6HAjBhB3D,EAAA,CACFC,EACAgB,EAEAf,EAEAC,EACAC,EACAC,EACAC,EACAC,EACAqD,GACAC,EAAA,CACD,EAMD,MAAMrD,EAAQC,EAIRC,EAAgBC,IAChBQ,EAAaC,IACb0C,EAAYjD,EAAS,IAAM,CAC/B,MAAMc,EAA6B,CAAA,EAC7BoC,EAAevD,EAAM,OACxB,MAAM,CAAC,EACP,KAAK,CAACgB,EAAG,IAAOA,EAAE,gBAAkB,EAAE,gBAAkB,EAAI,EAAG,EACzD,QAAA3B,EAAI,EAAGmC,EAAM+B,EAAa,OAAQlE,EAAImC,EAAKnC,GAAK,EAAG,CACpD,MAAAkB,EAAQgD,EAAalE,CAAC,EACtBmE,EAAQ,CACZnE,GACCkB,EAAM,aAAe,KAAK,QAAQ,CAAC,EACpCA,EAAM,KACNA,EAAM,QACNkD,GAAYlD,EAAM,eAAe,EACjCA,EAAM,WAAa,QAAa,CAACA,EAAM,SAAW,OAAS,OAAA,EAE7DY,EAAI,KAAKqC,CAAK,CAChB,CACO,OAAArC,CAAA,CACR,EAEKX,EAAeH,EAAS,IAAqB,CAG3C,MAAAqD,EAAgBJ,EAAU,MAAM,OAAS,GAAK,EAAI,GAAKA,EAAU,MAAM,QAAU,IAAM,IACtF,MAAA,CACL,MAAO,CACL,KAAM,aACN,KAAMtD,EAAM,SACd,EACA,gBAAiB,mBACjB,QAAS,CACP,WAAY,CAAC,OAAQ,QAAQ,EAC7B,OAAQsD,EAAU,KACpB,EACA,QAAS,CACP,QAAS,OACT,UAAYb,GAAW,CACrB,MAAMkB,EAAUlB,EAAO,CAAC,EAAE,KAAK,CAAC,EAAI,MAAMA,EAAO,CAAC,EAAE,KAAK,CAAC,CAAC,GAAK,GAChE,MAAO,GAAGA,EAAO,CAAC,EAAE,KAAK,CAAC,CAAC,MAAMA,EAAO,CAAC,EAAE,KAAK,CAAC,CAAC,IAAIkB,CAAO,OAAOlB,EAAO,CAAC,EAAE,KAAK,CAAC,CAAC,cAAcA,EAAO,CAAC,EAAE,KAAK,CAAC,CAAC,IACtH,EACA,YAAa,CACX,KAAM,OACN,MAAO,CACL,gBAAiB,SACnB,CACF,CACF,EACA,MAAO,CACL,KAAM,QACN,KAAM,EACR,EACA,MAAO,CACL,CACE,KAAM,QACN,KAAMlD,EACN,UAAW,CACT,KAAM,EACR,EACA,WAAY,GACZ,aAAc,SACd,QAAS,EACX,CACF,EACA,KAAM,CACJ,OAAQ,EACV,EACA,SAAU,CACR,CACE,KAAM,SACN,MAAOmE,EACP,IAAK,GACP,EACA,CACE,OAAQ,GACR,MAAOA,EACP,IAAK,IACL,GAAGZ,CACL,CACF,EACA,UAAW,CACT,CACE,KAAM,GACN,YAAa,EACb,OAAQ,CACN,CACE,IAAK,EACL,MAAOnC,EAAW,SACpB,EACA,CACE,IAAK,EACL,MAAOA,EAAW,WACpB,CACF,CACF,CACF,EACA,OAAQ,CACN,CACE,KAAM,MACN,KAAMpB,EACN,OAAQ,KACR,eAAgB,KAChB,UAAW,GACX,MAAO,CACL,KAAM,GACN,SAAU,MACV,OAAQ,GACR,OAAQ,CAAC,IAAK,GAAG,EACjB,UAAW,WACX,MAAOW,EAAc,aAAe,OAAS,UAAY,SAC3D,EACA,OAAQ,CACN,EAAG,EACH,EAAG,CACL,EAEA,UAAW,CACT,MAAOiD,EACT,CACF,CACF,CAAA,CACF,CACD"}