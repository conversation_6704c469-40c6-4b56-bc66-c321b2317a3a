import{_ as _t}from"./CandleChartContainer-DXl2ryLC.js";import{_ as ut}from"./TradeDetail-09aMAmmS.js";import{_ as mt,a as pt,b as ft,c as Z}from"./TradeList-BP6RUI29.js";import{_ as vt,a as yt}from"./TimePeriodChart-Dgeb6RQ3.js";import{g as L,u as V,o as r,c as h,a as n,b as a,w as c,h as e,t as H,s as ht,i as j,v as G,j as bt,l as U,k as f,T as D,r as q,m as W,n as $,q as K,x as _,y as x,z as I,A as b,$ as Q,B as O,F as gt,e as g,C as Bt,D as M,E as R,G as J,H as E,I as kt,J as xt}from"./index-B2p78N-x.js";import wt from"./PairListLive-CbIbYasi.js";import{_ as St}from"./BotBalance.vue_vue_type_script_setup_true_lang-CmliqZTF.js";import{_ as X}from"./InfoBox.vue_vue_type_script_setup_true_lang-Dr_hBaFv.js";import{_ as $t,a as Tt}from"./MessageBox.vue_vue_type_script_setup_true_lang-t_g1ZU8z.js";import"./EditValue.vue_vue_type_script_setup_true_lang-CWpIX5x3.js";import"./plus-box-outline-DIIEsppr.js";import"./installCanvasRenderer-DFpQ5KDo.js";import"./install-DosLD5hS.js";import"./createSeriesDataSimply-Bq9ycDaP.js";const Ct={class:"mb-2"},Lt=n("label",{class:"me-auto h3"},"Pair Locks",-1),Pt=L({__name:"PairLockList",setup(k){const t=V(),i=[{key:"pair",label:"Pair"},{key:"lock_end_timestamp",label:"Until",formatter:l=>H(l)},{key:"reason",label:"Reason"},{key:"actions"}],u=l=>{console.log(l),l.id!==void 0?t.activeBot.deleteLock(l.id):ht("This Freqtrade version does not support deleting locks.")};return(l,y)=>{const B=j,v=G,d=bt,s=U;return r(),h("div",null,[n("div",Ct,[Lt,a(v,{class:"float-end",size:"sm",onClick:e(t).activeBot.getLocks},{default:c(()=>[a(B)]),_:1},8,["onClick"])]),n("div",null,[a(s,{class:"table-sm",items:e(t).activeBot.activeLocks,fields:i},{"cell(actions)":c(o=>[a(v,{class:"btn-xs ms-1",size:"sm",title:"Delete trade",onClick:m=>u(o.item)},{default:c(()=>[a(d)]),_:2},1032,["onClick"])]),_:1},8,["items"])])])}}}),Dt={class:"mb-2"},Vt={class:"me-auto d-inline"},zt={class:"ps-1"},At=L({__name:"PeriodBreakdown",setup(k){const t=V(),i=f(()=>t.activeBot.botApiVersion>=2.33),u=f(()=>{const s=[{value:D.daily,text:"Days"}];return i.value&&(s.push({value:D.weekly,text:"Weeks"}),s.push({value:D.monthly,text:"Months"})),s}),l=q(D.daily),y=f(()=>{switch(l.value){case D.weekly:return t.activeBot.weeklyStats;case D.monthly:return t.activeBot.monthlyStats}return t.activeBot.dailyStats}),B=f(()=>({...y.value,data:y.value.data?Object.values(y.value.data).sort((s,o)=>s.date>o.date?1:-1):[]})),v=f(()=>{const s=[{key:"date",label:"Day"},{key:"abs_profit",label:"Profit",formatter:o=>W(o,t.activeBot.stakeCurrencyDecimals)},{key:"fiat_value",label:`In ${t.activeBot.dailyStats.fiat_display_currency}`,formatter:o=>W(o,2)},{key:"trade_count",label:"Trades"}];return t.activeBot.botApiVersion>=2.16&&s.push({key:"rel_profit",label:"Profit%",formatter:o=>$(o,2)}),s});function d(){t.activeBot.getTimeSummary(l.value)}return K(()=>{d()}),(s,o)=>{const m=j,p=G,T=Q,S=vt,P=U;return r(),h("div",null,[n("div",Dt,[n("h3",Vt,_(e(i)?"Period":"Daily")+" Breakdown",1),a(p,{class:"float-end",size:"sm",onClick:d},{default:c(()=>[a(m)]),_:1})]),e(i)?(r(),x(T,{key:0,id:"order-direction",modelValue:e(l),"onUpdate:modelValue":o[0]||(o[0]=z=>I(l)?l.value=z:null),options:e(u),name:"radios-btn-default",size:"sm",buttons:"",style:{"min-width":"10em"},"button-variant":"outline-primary",onChange:d},null,8,["modelValue","options"])):b("",!0),n("div",zt,[e(y)?(r(),x(S,{key:0,"daily-stats":e(B),"show-title":!1},null,8,["daily-stats"])):b("",!0)]),n("div",null,[a(P,{class:"table-sm",items:e(y).data,fields:e(v)},null,8,["items","fields"])])])}}}),Ft={class:"mb-2"},Nt=n("h3",{class:"me-auto d-inline"},"Performance",-1),Mt=L({__name:"BotPerformance",setup(k){const t=V(),i=q("performance");function u(s,o){return s.length>o?s.substring(0,o)+"...":s}const l=f(()=>{var m;return[{performance:{key:"pair",label:"Pair"},entryStats:{key:"enter_tag",label:"Enter tag",formatter:p=>u(p,17)},exitStats:{key:"exit_reason",label:"Exit Reason",formatter:p=>u(p,17)},mixTagStats:{key:"mix_tag",label:"Mix Tag",formatter:p=>u(p,17)}}[i.value],{key:"profit",label:"Profit %"},{key:"profit_abs",label:`Profit ${(m=t.activeBot.botState)==null?void 0:m.stake_currency}`,formatter:p=>W(p,5)},{key:"count",label:"Count"}]}),y=f(()=>i.value==="performance"?t.activeBot.performanceStats:i.value==="entryStats"?t.activeBot.entryStats:i.value==="exitStats"?t.activeBot.exitStats:i.value==="mixTagStats"?t.activeBot.mixTagStats:[]),B=f(()=>t.activeBot.botApiVersion>=2.34),v=[{value:"performance",text:"Performance"},{value:"entryStats",text:"Entries"},{value:"exitStats",text:"Exits"},{value:"mixTagStats",text:"Mix Tag"}];function d(){i.value==="performance"&&t.activeBot.getPerformance(),i.value==="entryStats"&&t.activeBot.getEntryStats(),i.value==="exitStats"&&t.activeBot.getExitStats(),i.value==="mixTagStats"&&t.activeBot.getMixTagStats()}return K(()=>{d()}),(s,o)=>{const m=j,p=G,T=Q,S=U;return r(),h("div",null,[n("div",Ft,[Nt,a(p,{class:"float-end",size:"sm",onClick:d},{default:c(()=>[a(m)]),_:1})]),e(B)?(r(),x(T,{key:0,id:"order-direction",modelValue:e(i),"onUpdate:modelValue":o[0]||(o[0]=P=>I(i)?i.value=P:null),options:v,name:"radios-btn-default",size:"sm",buttons:"",style:{"min-width":"10em"},"button-variant":"outline-primary",onChange:d},null,8,["modelValue"])):b("",!0),a(S,{class:"table-sm",items:e(y),fields:e(l)},null,8,["items","fields"])])}}}),Rt=L({__name:"BotProfit",props:{profit:{required:!0,type:Object},stakeCurrency:{required:!0,type:String},stakeCurrencyDecimals:{required:!0,type:Number}},setup(k){const t=k,i=[{key:"metric",label:"Metric"},{key:"value",label:"Value"}],u=f(()=>t.profit?[{metric:"ROI closed trades",value:t.profit.profit_closed_coin?`${O(t.profit.profit_closed_coin,t.stakeCurrency,t.stakeCurrencyDecimals)} (${$(t.profit.profit_closed_ratio_mean,2)})`:"N/A"},{metric:"ROI all trades",value:t.profit.profit_all_coin?`${O(t.profit.profit_all_coin,t.stakeCurrency,t.stakeCurrencyDecimals)} (${$(t.profit.profit_all_ratio_mean,2)})`:"N/A"},{metric:"Total Trade count",value:`${t.profit.trade_count??0}`},{metric:"Bot started",value:t.profit.bot_start_timestamp,isTs:!0},{metric:"First Trade opened",value:t.profit.first_trade_timestamp,isTs:!0},{metric:"Latest Trade opened",value:t.profit.latest_trade_timestamp,isTs:!0},{metric:"Win / Loss",value:`${t.profit.winning_trades??0} / ${t.profit.losing_trades??0}`},{metric:"Winrate",value:`${t.profit.winrate?$(t.profit.winrate):"N/A"}`},{metric:"Expectancy (ratio)",value:`${t.profit.expectancy?t.profit.expectancy.toFixed(2):"N/A"} (${t.profit.expectancy_ratio?t.profit.expectancy_ratio.toFixed(2):"N/A"})`},{metric:"Avg. Duration",value:`${t.profit.avg_duration??"N/A"}`},{metric:"Best performing",value:t.profit.best_pair?`${t.profit.best_pair}: ${$(t.profit.best_pair_profit_ratio,2)}`:"N/A"},{metric:"Trading volume",value:`${O(t.profit.trading_volume??0,t.stakeCurrency,t.stakeCurrencyDecimals)}`},{metric:"Profit factor",value:`${t.profit.profit_factor?t.profit.profit_factor.toFixed(2):"N/A"}`},{metric:"Max Drawdown",value:`${t.profit.max_drawdown?$(t.profit.max_drawdown,2):"N/A"} (${t.profit.max_drawdown_abs?O(t.profit.max_drawdown_abs,t.stakeCurrency,t.stakeCurrencyDecimals):"N/A"}) ${t.profit.max_drawdown_start_timestamp&&t.profit.max_drawdown_end_timestamp?"from "+H(t.profit.max_drawdown_start_timestamp)+" to "+H(t.profit.max_drawdown_end_timestamp):""}`}]:[]);return(l,y)=>{const B=X,v=U;return r(),x(v,{class:"text-start",small:"",borderless:"",items:e(u),fields:i},{"cell(value)":c(d=>[d.item.isTs&&d.value?(r(),x(B,{key:0,date:d.value},null,8,["date"])):(r(),h(gt,{key:1},[g(_(d.value),1)],64))]),_:1},8,["items"])}}}),Et={key:0},Ot={key:0},qt=n("hr",null,null,-1),Ut={key:1},Ht={key:0,class:"d-block"},Wt={class:"d-block"},jt={class:"d-block"},Gt={key:0,class:"d-block"},It={key:1,class:"d-block"},Zt=L({__name:"BotStatus",setup(k){const t=V();return(i,u)=>{const l=X,y=Rt;return e(t).activeBot.botState?(r(),h("div",Et,[n("p",null,[g(" Running Freqtrade "),n("strong",null,_(e(t).activeBot.version),1)]),n("p",null,[g(" Running with "),n("strong",null,_(e(t).activeBot.botState.max_open_trades)+"x"+_(e(t).activeBot.botState.stake_amount)+" "+_(e(t).activeBot.botState.stake_currency),1),g(" on "),n("strong",null,_(e(t).activeBot.botState.exchange),1),g(" in "),n("strong",null,_(e(t).activeBot.botState.trading_mode||"spot"),1),g(" markets, with Strategy "),n("strong",null,_(e(t).activeBot.botState.strategy),1),g(". ")]),"stoploss_on_exchange"in e(t).activeBot.botState?(r(),h("p",Ot,[g(" Stoploss on exchange is "),n("strong",null,_(e(t).activeBot.botState.stoploss_on_exchange?"enabled":"disabled"),1),g(". ")])):b("",!0),n("p",null,[g(" Currently "),n("strong",null,_(e(t).activeBot.botState.state),1),g(", "),n("strong",null,"force entry: "+_(e(t).activeBot.botState.force_entry_enable),1)]),n("p",null,[n("strong",null,_(e(t).activeBot.botState.dry_run?"Dry-Run":"Live"),1)]),qt,n("p",null," Avg Profit "+_(e($)(e(t).activeBot.profit.profit_all_ratio_mean))+" (∑ "+_(e($)(e(t).activeBot.profit.profit_all_ratio_sum))+") in "+_(e(t).activeBot.profit.trade_count)+" Trades, with an average duration of "+_(e(t).activeBot.profit.avg_duration)+". Best pair: "+_(e(t).activeBot.profit.best_pair)+". ",1),e(t).activeBot.profit.first_trade_timestamp?(r(),h("p",Ut,[e(t).activeBot.profit.bot_start_timestamp?(r(),h("span",Ht,[g(" Bot start date: "),n("strong",null,[a(l,{date:e(t).activeBot.profit.bot_start_timestamp,"show-timezone":""},null,8,["date"])])])):b("",!0),n("span",Wt,[g(" First trade opened: "),n("strong",null,[a(l,{date:e(t).activeBot.profit.first_trade_timestamp,"show-timezone":""},null,8,["date"])])]),n("span",jt,[g(" Last trade opened: "),n("strong",null,[a(l,{date:e(t).activeBot.profit.latest_trade_timestamp,"show-timezone":""},null,8,["date"])])])])):b("",!0),n("p",null,[e(t).activeBot.profit.profit_factor?(r(),h("span",Gt," Profit factor: "+_(e(t).activeBot.profit.profit_factor.toFixed(2)),1)):b("",!0),e(t).activeBot.profit.trading_volume?(r(),h("span",It," Trading volume: "+_(e(O)(e(t).activeBot.profit.trading_volume,e(t).activeBot.botState.stake_currency,e(t).activeBot.botState.stake_currency_decimals??3)),1)):b("",!0)]),a(y,{class:"mx-1",profit:e(t).activeBot.profit,"stake-currency":e(t).activeBot.botState.stake_currency??"USDT","stake-currency-decimals":e(t).activeBot.botState.stake_currency_decimals??3},null,8,["profit","stake-currency","stake-currency-decimals"])])):b("",!0)}}}),Jt={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Kt=n("path",{fill:"currentColor",d:"M2 12a9 9 0 0 0 9 9c2.39 0 4.68-.94 6.4-2.6l-1.5-1.5A6.7 6.7 0 0 1 11 19c-6.24 0-9.36-7.54-4.95-11.95S18 5.77 18 12h-3l4 4h.1l3.9-4h-3a9 9 0 0 0-18 0"},null,-1),Qt=[Kt];function Xt(k,t){return r(),h("svg",Jt,[...Qt])}const Yt={name:"mdi-reload",render:Xt},te={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},ee=n("path",{fill:"currentColor",d:"M14 19h4V5h-4M6 19h4V5H6z"},null,-1),oe=[ee];function ae(k,t){return r(),h("svg",te,[...oe])}const ne={name:"mdi-pause",render:ae},se={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},ie=n("path",{fill:"currentColor",d:"M18 18H6V6h12z"},null,-1),re=[ie];function le(k,t){return r(),h("svg",se,[...re])}const ce={name:"mdi-stop",render:le},de={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},_e=n("path",{fill:"currentColor",d:"M8 5.14v14l11-7z"},null,-1),ue=[_e];function me(k,t){return r(),h("svg",de,[...ue])}const pe={name:"mdi-play",render:me},fe=["disabled"],ve=["disabled"],ye=["disabled"],he=["disabled"],be=["disabled"],ge=["disabled"];const Be=L({__name:"BotControls",setup(k){const t=V(),i=q(!1),u=q(),l=f(()=>{var s;return((s=t.activeBot.botState)==null?void 0:s.state)==="running"}),y=()=>{var o;const s={title:"Stop Bot",message:"Stop the bot loop from running?",accept:()=>{t.activeBot.stopBot()}};(o=u.value)==null||o.show(s)},B=()=>{var o;const s={title:"Stop Buying",message:"Freqtrade will continue to handle open trades.",accept:()=>{t.activeBot.stopBuy()}};(o=u.value)==null||o.show(s)},v=()=>{var o;const s={title:"Reload",message:"Reload configuration (including strategy)?",accept:()=>{console.log("reload..."),t.activeBot.reloadConfig()}};(o=u.value)==null||o.show(s)},d=()=>{var o;const s={title:"ForceExit all",message:"Really forceexit ALL trades?",accept:()=>{const m={tradeid:"all"};t.activeBot.forceexit(m)}};(o=u.value)==null||o.show(s)};return(s,o)=>{const m=pe,p=ce,T=ne,S=Yt,P=pt,z=ft;return r(),h("div",null,[n("button",{class:"btn btn-secondary btn-sm ms-1",disabled:!e(t).activeBot.isTrading||e(l),title:"Start Trading",onClick:o[0]||(o[0]=w=>e(t).activeBot.startBot())},[a(m,{height:"24",width:"24"})],8,fe),n("button",{class:"btn btn-secondary btn-sm ms-1",disabled:!e(t).activeBot.isTrading||!e(l),title:"Stop Trading - Also stops handling open trades.",onClick:o[1]||(o[1]=w=>y())},[a(p,{height:"24",width:"24"})],8,ve),n("button",{class:"btn btn-secondary btn-sm ms-1",disabled:!e(t).activeBot.isTrading||!e(l),title:"StopBuy - Stops buying, but still handles open trades",onClick:o[2]||(o[2]=w=>B())},[a(T,{height:"24",width:"24"})],8,ye),n("button",{class:"btn btn-secondary btn-sm ms-1",disabled:!e(t).activeBot.isTrading,title:"Reload Config - reloads configuration including strategy, resetting all settings changed on the fly.",onClick:o[3]||(o[3]=w=>v())},[a(S,{height:"24",width:"24"})],8,he),n("button",{class:"btn btn-secondary btn-sm ms-1",disabled:!e(t).activeBot.isTrading,title:"Force exit all",onClick:o[4]||(o[4]=w=>d())},[a(P,{height:"24",width:"24"})],8,be),e(t).activeBot.botState&&e(t).activeBot.botState.force_entry_enable?(r(),h("button",{key:0,class:"btn btn-secondary btn-sm ms-1",disabled:!e(t).activeBot.isTrading||!e(l),title:"Force enter - Immediately enter a trade at an optional price. Exits are then handled according to strategy rules.",onClick:o[5]||(o[5]=w=>i.value=!0)},[a(z,{style:{"font-size":"20px"}})],8,ge)):b("",!0),(e(t).activeBot.isWebserverMode,b("",!0)),a(mt,{modelValue:e(i),"onUpdate:modelValue":o[7]||(o[7]=w=>I(i)?i.value=w:null),pair:e(t).activeBot.selectedPair},null,8,["modelValue","pair"]),a($t,{ref_key:"msgBox",ref:u},null,512)])}}}),ke={class:"mt-1 d-flex justify-content-center"},Me=L({__name:"TradingView",setup(k){const t=V(),i=Bt(),u=q(""),l=S=>{u.value=S},y=f(()=>["","sm","md","lg","xl"].includes(u.value)),B=f(()=>i.layoutLocked||!y.value),v=f(()=>y.value?i.tradingLayout:[...i.getTradingLayoutSm]),d=f(()=>M(v.value,R.multiPane)),s=f(()=>M(v.value,R.openTrades)),o=f(()=>M(v.value,R.tradeHistory)),m=f(()=>M(v.value,R.tradeDetail)),p=f(()=>M(v.value,R.chartView)),T=f(()=>({sm:i.getTradingLayoutSm}));return(S,P)=>{const z=Be,w=Tt,C=kt,Y=Zt,tt=Mt,et=St,ot=At,at=wt,nt=Pt,st=xt,A=yt,F=J("grid-item"),it=Z,rt=Z,lt=ut,ct=_t,dt=J("grid-layout");return r(),x(dt,{class:"h-100 w-100","row-height":50,layout:e(v),"vertical-compact":!1,margin:[5,5],"responsive-layouts":e(T),"is-resizable":!e(B),"is-draggable":!e(B),responsive:!0,cols:{lg:12,md:12,sm:12,xs:4,xxs:2},"col-num":12,"onUpdate:breakpoint":l},{default:c(({gridItemProps:N})=>[e(d).h!=0?(r(),x(F,E({key:0},N,{i:e(d).i,x:e(d).x,y:e(d).y,w:e(d).w,h:e(d).h,"drag-allow-from":".card-header"}),{default:c(()=>[a(A,{header:"Multi Pane"},{default:c(()=>[n("div",ke,[a(z,{class:"mt-1 mb-2"})]),a(st,{"content-class":"mt-3",class:"mt-1"},{default:c(()=>[a(C,{title:"Pairs combined",active:""},{default:c(()=>[a(w,{pairlist:e(t).activeBot.whitelist,"current-locks":e(t).activeBot.activeLocks,trades:e(t).activeBot.openTrades},null,8,["pairlist","current-locks","trades"])]),_:1}),a(C,{title:"General"},{default:c(()=>[a(Y)]),_:1}),a(C,{title:"Performance",lazy:""},{default:c(()=>[a(tt)]),_:1}),a(C,{title:"Balance",lazy:""},{default:c(()=>[a(et)]),_:1}),a(C,{title:"Time Breakdown",lazy:""},{default:c(()=>[a(ot)]),_:1}),a(C,{title:"Pairlist",lazy:""},{default:c(()=>[a(at)]),_:1}),a(C,{title:"Pair Locks",lazy:""},{default:c(()=>[a(nt)]),_:1})]),_:1})]),_:1})]),_:2},1040,["i","x","y","w","h"])):b("",!0),e(s).h!=0?(r(),x(F,E({key:1},N,{i:e(s).i,x:e(s).x,y:e(s).y,w:e(s).w,h:e(s).h,"drag-allow-from":".card-header"}),{default:c(()=>[a(A,{header:"Open Trades"},{default:c(()=>[a(it,{class:"open-trades",trades:e(t).activeBot.openTrades,title:"Open trades","active-trades":!0,"empty-text":"Currently no open trades."},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):b("",!0),e(o).h!=0?(r(),x(F,E({key:2},N,{i:e(o).i,x:e(o).x,y:e(o).y,w:e(o).w,h:e(o).h,"drag-allow-from":".card-header"}),{default:c(()=>[a(A,{header:"Closed Trades"},{default:c(()=>[a(rt,{class:"trade-history",trades:e(t).activeBot.closedTrades,title:"Trade history","show-filter":!0,"empty-text":"No closed trades so far."},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):b("",!0),e(t).activeBot.detailTradeId&&e(t).activeBot.tradeDetail&&e(m).h!=0?(r(),x(F,E({key:3},N,{i:e(m).i,x:e(m).x,y:e(m).y,w:e(m).w,h:e(m).h,"min-h":4,"drag-allow-from":".card-header"}),{default:c(()=>[a(A,{header:"Trade Detail"},{default:c(()=>[a(lt,{trade:e(t).activeBot.tradeDetail,"stake-currency":e(t).activeBot.stakeCurrency},null,8,["trade","stake-currency"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):b("",!0),e(m).h!=0?(r(),x(F,E({key:4},N,{i:e(p).i,x:e(p).x,y:e(p).y,w:e(p).w,h:e(p).h,"min-h":6,"drag-allow-from":".card-header"}),{default:c(()=>[a(A,{header:"Chart"},{default:c(()=>[a(ct,{"available-pairs":e(t).activeBot.whitelist,"historic-view":!1,timeframe:e(t).activeBot.timeframe,trades:e(t).activeBot.allTrades},null,8,["available-pairs","timeframe","trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):b("",!0)]),_:1},8,["layout","responsive-layouts","is-resizable","is-draggable"])}}});export{Me as default};
//# sourceMappingURL=TradingView-Bks13uR8.js.map
