import{o as e,c as o,a as t}from"./index-B2p78N-x.js";const n={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},a=t("path",{fill:"currentColor",d:"M19 19V5H5v14zm0-16a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm-8 4h2v4h4v2h-4v4h-2v-4H7v-2h4z"},null,-1),c=[a];function s(h,_){return e(),o("svg",n,[...c])}const r={name:"mdi-plus-box-outline",render:s};export{r as _};
//# sourceMappingURL=plus-box-outline-DIIEsppr.js.map
