import{g as m,o as y,c as x,a as u,at as _,e as C,x as v,_ as h,aN as T,b0 as $,r as k,b5 as w,k as i,y as A,h as r,ay as B,A as R}from"./index-B2p78N-x.js";import{u as I,bf as N,be as D,ba as L,bb as O,bc as P,bd as z}from"./installCanvasRenderer-DFpQ5KDo.js";import{k as E,l as H,g as M,h as V}from"./install-DosLD5hS.js";const q={class:"card h-100 w-100"},G={class:"drag-header card-header"},j={class:"card-body h-100 w-100 overflow-auto"},F=m({__name:"DraggableContainer",props:{header:{required:!1,type:String,default:""}},setup(o){return(e,d)=>(y(),x("div",q,[u("div",G,[_(e.$slots,"header",{},()=>[C(v(o.header),1)],!0)]),u("div",j,[_(e.$slots,"default",{},void 0,!0)])]))}}),W=h(F,[["__scopeId","data-v-9757edc5"]]),n="Absolute profit",l="Trade Count",U=m({__name:"TimePeriodChart",props:{dailyStats:{type:Object,required:!0},showTitle:{type:Boolean,default:!0}},setup(o){I([E,H,D,M,L,O,P,z,V]);const e=o,d=T(),c=$(),p=k(null),{width:f}=w(p),b=i(()=>{var a;return e.dailyStats.data.reduce((t,s)=>s.abs_profit<t?s.abs_profit:t,(a=e.dailyStats.data[0])==null?void 0:a.abs_profit)}),g=i(()=>{var a;return e.dailyStats.data.reduce((t,s)=>s.abs_profit>t?s.abs_profit:t,(a=e.dailyStats.data[0])==null?void 0:a.abs_profit)}),S=i(()=>({title:{text:"Daily profit",show:e.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",dataset:{dimensions:["date","abs_profit","trade_count"],source:e.dailyStats.data},tooltip:{trigger:"axis",axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},legend:{data:[n,l],right:"5%"},xAxis:[{type:"category"}],visualMap:[{dimension:1,seriesIndex:0,show:!1,pieces:[{max:0,min:b.value,color:c.colorLoss},{min:0,max:g.value,color:c.colorProfit}]}],yAxis:[{type:"value",name:n,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:35},{type:"value",name:l,nameRotate:90,nameLocation:"middle",nameGap:30}],series:[{type:"line",name:n},{type:"bar",name:l,itemStyle:{color:"rgba(150,150,150,0.3)"},yAxisIndex:1}]}));return(a,t)=>o.dailyStats.data?(y(),A(r(N),{key:0,ref_key:"dailyChart",ref:p,option:r(S),theme:r(d).chartTheme,style:B({height:r(f)*.6+"px"}),autoresize:""},null,8,["option","theme","style"])):R("",!0)}}),X=h(U,[["__scopeId","data-v-a7c27840"]]);export{X as _,W as a};
//# sourceMappingURL=TimePeriodChart-Dgeb6RQ3.js.map
