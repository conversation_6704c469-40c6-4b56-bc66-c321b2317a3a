{"version": 3, "file": "HomeView-qAZq3Gaw.js", "sources": ["../../src/views/HomeView.vue"], "sourcesContent": ["<template>\n  <div class=\"home\">\n    <div class=\"d-flex justify-content-center\">\n      <bot-list />\n    </div>\n    <hr />\n    <!-- <img alt=\"Freqtrade logo\" src=\"../assets/freqtrade-logo.png\" width=\"450px\" class=\"my-5\" /> -->\n    <div title=\"Freqtrade logo\" class=\"logo-svg my-5 mx-auto\" />\n    <div>\n      <h1>Welcome to the Freqtrade UI</h1>\n    </div>\n    <div>This page allows you to control your trading bot.</div>\n    <br />\n    <p>\n      If you need any help, please refer to the\n      <a href=\"https://www.freqtrade.io/en/latest/\">Freqtrade Documentation</a>.\n    </p>\n\n    <p>Have fun - <i>wishes you the Freqtrade team</i></p>\n  </div>\n</template>\n\n<script setup lang=\"ts\"></script>\n\n<style lang=\"scss\" scoped>\n.home {\n  margin-top: 1.5em;\n}\n.logo-svg {\n  -webkit-mask: url(../assets/freqtrade-logo-mask.png) no-repeat center;\n  -webkit-mask-size: 240px 240px;\n  mask: url(../assets/freqtrade-logo-mask.png) no-repeat center;\n  mask-size: contain;\n  width: 250px;\n  height: 250px;\n}\n</style>\n"], "names": ["_withScopeId", "n", "_popScopeId", "_hoisted_1", "_createElementVNode", "_hoisted_4", "_hoisted_5", "_hoisted_8", "_createTextVNode", "_hoisted_9", "_openBlock", "_createElementBlock", "_hoisted_2", "_hoisted_3", "_hoisted_6", "_hoisted_7"], "mappings": "2GACOA,EAAAC,MAAY,iBAAA,EAAAA,EAAAA,EAAA,EAAAC,EAAA,EAAAD,GACVE,EAAA,CAAA,MAAM,MAA+B,oDAK1CC,EAA4D,KAAA,KAAA,KAAA,EAAA,CAAA,EAAvDC,EAAsBL,EAAA,IAAAI,EAAA,MAAA,CAAC,MAAM,0DAEhCE,EAAAN,MAA+BI,EAAA,MAAA,KAAA,2CAEjC,EAAA,EAAA,CAAA,UACAA,EAAM,MAAA,KAAA,oDAAA,EAAA,CAAA,UACNA,EAGI,KAAA,KAAA,KAAA,EAAA,CAAA,EAhBRG,EAeMP,EAAA,IAAAI,EAAA,IAAA,KAAA,CAAAI,EAAA,6CAAQ,EAfdJ,EAgBI,IAAA,CAAA,KAAA,qCAAA,EAAA,yBAAA,gBAhBJK,EAkBkBT,EAAA,IAAAI,EAAA,IAAA,KAAA,CAAAI,EAAA,2FAhBd,OAAAE,EAAA,EAEMC,EAAA,MAAAR,EAAA,GADQ,MAAAS,EAAA,MAER,EAENC,EACAR,EAGAC,EACAQ,EACAC,EAKAR"}