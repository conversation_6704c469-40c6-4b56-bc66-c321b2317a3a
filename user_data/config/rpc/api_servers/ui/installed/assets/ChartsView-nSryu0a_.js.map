{"version": 3, "file": "ChartsView-nSryu0a_.js", "sources": ["../../src/views/ChartsView.vue"], "sourcesContent": ["<template>\n  <div class=\"d-flex flex-column h-100\">\n    <!-- <div v-if=\"isWebserverMode\" class=\"me-auto ms-3\"> -->\n    <!-- Currently only available in Webserver mode -->\n    <!-- <b-form-checkbox v-model=\"historicView\">HistoricData</b-form-checkbox> -->\n    <!-- </div> -->\n    <div v-if=\"botStore.activeBot.isWebserverMode\" class=\"mx-md-3 mt-2\">\n      <div class=\"d-flex flex-wrap mx-1 gap-1 gap-md-2\">\n        <div class=\"col-12 col-md-3 text-start me-md-1\">\n          <span>Strategy</span>\n          <StrategySelect v-model=\"strategy\" class=\"mt-1\"></StrategySelect>\n        </div>\n        <div class=\"col-12 col-md-3 text-start\">\n          <span>Timeframe</span>\n          <TimeframeSelect v-model=\"selectedTimeframe\" class=\"mt-1\" />\n        </div>\n        <TimeRangeSelect v-model=\"timerange\"></TimeRangeSelect>\n      </div>\n    </div>\n\n    <div class=\"mx-md-2 mt-2 pb-1 h-100\">\n      <CandleChartContainer\n        :available-pairs=\"availablePairs\"\n        :historic-view=\"botStore.activeBot.isWebserverMode\"\n        :timeframe=\"finalTimeframe\"\n        :trades=\"botStore.activeBot.trades\"\n        :timerange=\"botStore.activeBot.isWebserverMode ? timerange : ''\"\n        :strategy=\"botStore.activeBot.isWebserverMode ? strategy : ''\"\n        :plot-config-modal=\"false\"\n      >\n      </CandleChartContainer>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst botStore = useBotStore();\nconst strategy = ref('');\nconst timerange = ref('');\nconst selectedTimeframe = ref('');\n\nconst finalTimeframe = computed<string>(() => {\n  return botStore.activeBot.isWebserverMode\n    ? selectedTimeframe.value || botStore.activeBot.strategy.timeframe || ''\n    : botStore.activeBot.timeframe;\n});\n\nconst availablePairs = computed<string[]>(() => {\n  if (botStore.activeBot.isWebserverMode) {\n    if (finalTimeframe.value && finalTimeframe.value !== '') {\n      const tf = finalTimeframe.value;\n      return botStore.activeBot.pairlistWithTimeframe\n        .filter(([_, timeframe]) => {\n          // console.log(pair, timeframe, tf);\n          return timeframe === tf;\n        })\n        .map(([pair]) => pair);\n    }\n    return botStore.activeBot.pairlist;\n  }\n  return botStore.activeBot.whitelist;\n});\n\nonMounted(() => {\n  if (botStore.activeBot.isWebserverMode) {\n    // this.refresh();\n    botStore.activeBot.getAvailablePairs({ timeframe: botStore.activeBot.timeframe });\n    // .then((val) => {\n    // console.log(val);\n    // });\n  } else if (!botStore.activeBot.whitelist || botStore.activeBot.whitelist.length === 0) {\n    botStore.activeBot.getWhitelist();\n  }\n});\n</script>\n\n<style scoped></style>\n"], "names": ["botStore", "useBotStore", "strategy", "ref", "timerange", "selectedTimeframe", "finalTimeframe", "computed", "availablePairs", "tf", "_", "timeframe", "pair", "onMounted"], "mappings": "u0BAsCA,MAAMA,EAAWC,IACXC,EAAWC,EAAI,EAAE,EACjBC,EAAYD,EAAI,EAAE,EAClBE,EAAoBF,EAAI,EAAE,EAE1BG,EAAiBC,EAAiB,IAC/BP,EAAS,UAAU,gBACtBK,EAAkB,OAASL,EAAS,UAAU,SAAS,WAAa,GACpEA,EAAS,UAAU,SACxB,EAEKQ,EAAiBD,EAAmB,IAAM,CAC1C,GAAAP,EAAS,UAAU,gBAAiB,CACtC,GAAIM,EAAe,OAASA,EAAe,QAAU,GAAI,CACvD,MAAMG,EAAKH,EAAe,MACnB,OAAAN,EAAS,UAAU,sBACvB,OAAO,CAAC,CAACU,EAAGC,CAAS,IAEbA,IAAcF,CACtB,EACA,IAAI,CAAC,CAACG,CAAI,IAAMA,CAAI,CACzB,CACA,OAAOZ,EAAS,UAAU,QAC5B,CACA,OAAOA,EAAS,UAAU,SAAA,CAC3B,EAED,OAAAa,EAAU,IAAM,CACVb,EAAS,UAAU,gBAErBA,EAAS,UAAU,kBAAkB,CAAE,UAAWA,EAAS,UAAU,UAAW,GAIvE,CAACA,EAAS,UAAU,WAAaA,EAAS,UAAU,UAAU,SAAW,IAClFA,EAAS,UAAU,cACrB,CACD"}