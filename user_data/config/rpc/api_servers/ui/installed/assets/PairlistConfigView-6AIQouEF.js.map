{"version": 3, "file": "PairlistConfigView-6AIQouEF.js", "sources": ["../../src/types/pairlists.ts", "../../src/components/general/CopyableTextfield.vue", "../../src/stores/pairlistConfig.ts", "../../src/components/ftbot/PairlistConfigParameter.vue", "../../src/components/ftbot/PairlistConfigItem.vue", "../../src/components/ftbot/PairlistConfigBlacklist.vue", "../../src/components/ftbot/PairlistConfigActions.vue", "../../src/components/ftbot/ExchangeSelect.vue", "../../src/components/ftbot/PairlistConfigurator.vue"], "sourcesContent": ["import { BackgroundTaskResult } from './backgroundtasks';\nimport { WhitelistResponse } from './blacklist';\nimport { MarginMode, TradingMode } from './types';\n\nexport interface PairlistsResponse {\n  pairlists: Pairlist[];\n}\n\nexport interface PairlistEvalResponse extends BackgroundTaskResult {\n  result: WhitelistResponse;\n}\n\nexport interface Pairlist {\n  id?: string;\n  is_pairlist_generator: boolean;\n  name: string;\n  description: string;\n  showParameters: boolean;\n  params: Record<string, PairlistParameter>;\n}\n\nexport interface PairlistConfig {\n  name: string;\n  blacklist: string[];\n  pairlists: Pairlist[];\n}\n\nexport enum PairlistParamType {\n  string = 'string',\n  number = 'number',\n  boolean = 'boolean',\n  option = 'option',\n}\n\nexport type PairlistParamValue = string | number | boolean;\n\ninterface PairlistParameterBase {\n  description: string;\n  help: string;\n  type: PairlistParamType;\n}\n\nexport interface StringPairlistParameter extends PairlistParameterBase {\n  type: PairlistParamType.string;\n  value?: string;\n  default: string;\n}\n\nexport interface NumberPairlistParameter extends PairlistParameterBase {\n  type: PairlistParamType.number;\n  value?: number;\n  default: number;\n}\n\nexport interface BooleanPairlistParameter extends PairlistParameterBase {\n  type: PairlistParamType.boolean;\n  value?: boolean;\n  default: boolean;\n}\n\nexport interface OptionPairlistParameter extends PairlistParameterBase {\n  type: PairlistParamType.option;\n  options: string[];\n  value?: string;\n  default: string;\n}\n\nexport type PairlistParameter =\n  | StringPairlistParameter\n  | NumberPairlistParameter\n  | BooleanPairlistParameter\n  | OptionPairlistParameter;\n\nexport interface PairlistPayloadItem {\n  method: string;\n  [key: string]: string | number | boolean;\n}\n\nexport interface PairlistsPayload {\n  pairlists: PairlistPayloadItem[];\n  blacklist: string[];\n  stake_currency: string;\n  exchange?: string;\n  trading_mode?: TradingMode;\n  margin_mode?: MarginMode;\n}\n", "<template>\n  <div class=\"copy-container position-relative\">\n    <i-mdi-content-copy\n      v-if=\"isSupported && isValid\"\n      role=\"button\"\n      class=\"copy-button position-absolute end-0 mt-1 me-2\"\n      @click=\"copy(typeof content === 'string' ? content : JSON.stringify(content))\"\n    />\n    <pre class=\"text-start border p-1 mb-0\"><code>{{ content }}</code></pre>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useClipboard } from '@vueuse/core';\n\ndefineProps({\n  content: { type: [String, Array<string>], required: true },\n  isValid: { type: Boolean, default: true },\n});\n\nconst { copy, isSupported } = useClipboard();\n</script>\n\n<style lang=\"scss\" scoped>\n.copy-container {\n  .copy-button {\n    opacity: 0;\n  }\n\n  &:hover {\n    .copy-button {\n      opacity: 1;\n    }\n  }\n}\n</style>\n", "import { defineStore } from 'pinia';\nimport { useBotStore } from './ftbotwrapper';\n\nimport {\n  ExchangeSelection,\n  MarginMode,\n  Pairlist,\n  PairlistConfig,\n  PairlistParamType,\n  PairlistParamValue,\n  PairlistPayloadItem,\n  PairlistsPayload,\n  TradingMode,\n} from '@/types';\n\nimport { showAlert } from '../shared/alerts';\nimport { isNotUndefined } from '@/shared/formatters';\n\nexport const usePairlistConfigStore = defineStore(\n  'pairlistConfig',\n  () => {\n    const botStore = useBotStore();\n\n    const evaluating = ref<boolean>(false);\n    const intervalId = ref<number>();\n    const stakeCurrency = ref<string>(botStore.activeBot?.stakeCurrency ?? 'USDT');\n    const whitelist = ref<string[]>([]);\n    const customExchange = ref<boolean>(false);\n    const selectedExchange = ref<ExchangeSelection>({\n      exchange: botStore.activeBot?.botState.exchange ?? '',\n      trade_mode: {\n        trading_mode: botStore.activeBot?.botState.trading_mode ?? TradingMode.SPOT,\n        margin_mode:\n          botStore.activeBot?.botState.trading_mode === TradingMode.FUTURES\n            ? MarginMode.ISOLATED\n            : MarginMode.NONE,\n      },\n    });\n\n    const config = ref<PairlistConfig>(makeConfig());\n    const savedConfigs = ref<PairlistConfig[]>([]);\n    const configName = ref<string>('');\n\n    const firstPairlistIsGenerator = computed<boolean>(() => {\n      // First pairlist must be a generator\n      if (config.value.pairlists[0]?.is_pairlist_generator) {\n        return true;\n      }\n      return false;\n    });\n\n    const pairlistValid = computed<boolean>(() => {\n      return firstPairlistIsGenerator.value && config.value.pairlists.length > 0;\n    });\n\n    const configJSON = computed(() => {\n      return JSON.stringify(configToPayloadItems(), null, 2);\n    });\n\n    const isSavedConfig = (name: string) =>\n      savedConfigs.value.findIndex((c) => c.name === name) > -1;\n\n    function addToConfig(pairlist: Pairlist, index: number) {\n      pairlist = structuredClone(toRaw(pairlist));\n      pairlist.showParameters = false;\n      if (!pairlist.id) {\n        pairlist.id = Date.now().toString(36) + Math.random().toString(36).substring(2);\n      }\n      for (const param in pairlist.params) {\n        pairlist.params[param].value = isNotUndefined(pairlist.params[param].default)\n          ? pairlist.params[param].default\n          : '';\n      }\n      config.value.pairlists.splice(index, 0, pairlist);\n    }\n\n    function removeFromConfig(index: number) {\n      config.value.pairlists.splice(index, 1);\n    }\n\n    function saveConfig(name = '') {\n      const i = savedConfigs.value.findIndex((c) => c.name === config.value.name);\n      config.value.name = name;\n\n      if (i > -1) {\n        savedConfigs.value[i] = structuredClone(toRaw(config.value));\n      } else {\n        savedConfigs.value.push(structuredClone(toRaw(config.value)));\n      }\n    }\n\n    function newConfig(name: string) {\n      const c = makeConfig({ name });\n      savedConfigs.value.push(c);\n      config.value = structuredClone(c);\n    }\n\n    function duplicateConfig(name = '') {\n      const c = makeConfig({\n        name,\n        pairlists: toRaw(config.value.pairlists) as [],\n        blacklist: toRaw(config.value.blacklist) as [],\n      });\n      savedConfigs.value.push(c);\n      config.value = structuredClone(c);\n    }\n\n    function deleteConfig() {\n      const i = savedConfigs.value.findIndex((c) => c.name === config.value.name);\n      if (i > -1) {\n        savedConfigs.value.splice(i, 1);\n        selectOrCreateConfig(\n          savedConfigs.value.length > 0 ? savedConfigs.value[0].name : 'default',\n        );\n      }\n    }\n\n    function selectOrCreateConfig(name: string) {\n      const c = savedConfigs.value.find((c) => name === c.name);\n      if (c) {\n        config.value = structuredClone(toRaw(c));\n      } else {\n        newConfig(name);\n      }\n    }\n\n    function makeConfig({ name = '', pairlists = [], blacklist = [] } = {}): PairlistConfig {\n      return { name, pairlists, blacklist };\n    }\n\n    function addToBlacklist() {\n      config.value.blacklist.push('');\n    }\n\n    function removeFromBlacklist(index: number) {\n      config.value.blacklist.splice(index, 1);\n    }\n\n    function duplicateBlacklist(configName: string) {\n      const conf = savedConfigs.value.find((c) => c.name === configName);\n      if (conf) {\n        config.value.blacklist = structuredClone(toRaw(conf.blacklist));\n      }\n    }\n\n    async function startPairlistEvaluation() {\n      const payload: PairlistsPayload = configToPayload();\n\n      evaluating.value = true;\n      try {\n        const { job_id: jobId } = await botStore.activeBot.evaluatePairlist(payload);\n        console.log('jobId', jobId);\n\n        intervalId.value = window.setInterval(async () => {\n          const res = await botStore.activeBot.getBackgroundJobStatus(jobId);\n          if (!res.running) {\n            clearInterval(intervalId.value);\n            const wl = await botStore.activeBot.getPairlistEvalResult(jobId);\n            evaluating.value = false;\n            if (wl.status === 'success') {\n              whitelist.value = wl.result.whitelist;\n            } else if (wl.error) {\n              showAlert(wl.error, 'danger');\n              evaluating.value = false;\n            }\n          }\n        }, 1000);\n      } catch (error) {\n        showAlert('Evaluation failed', 'danger');\n        evaluating.value = false;\n      }\n    }\n\n    function convertToParamType(type: PairlistParamType, value: PairlistParamValue) {\n      if (type === PairlistParamType.number) {\n        return Number(value);\n      } else if (type === PairlistParamType.boolean) {\n        return Boolean(value);\n      } else {\n        return String(value);\n      }\n    }\n\n    function configToPayload(): PairlistsPayload {\n      const pairlists: PairlistPayloadItem[] = configToPayloadItems();\n      const c: PairlistsPayload = {\n        pairlists: pairlists,\n        stake_currency: stakeCurrency.value,\n        blacklist: config.value.blacklist,\n      };\n      if (customExchange.value) {\n        console.log('setting custom exchange props');\n        c.exchange = selectedExchange.value.exchange;\n        c.trading_mode = selectedExchange.value.trade_mode.trading_mode;\n        c.margin_mode = selectedExchange.value.trade_mode.margin_mode;\n      }\n      return c;\n    }\n\n    function configToPayloadItems() {\n      const pairlists: PairlistPayloadItem[] = [];\n      config.value.pairlists.forEach((config) => {\n        const pairlist = {\n          method: config.name,\n        };\n        for (const key in config.params) {\n          const param = config.params[key];\n          if (param.value) {\n            pairlist[key] = convertToParamType(param.type, param.value);\n          }\n        }\n        pairlists.push(pairlist);\n      });\n\n      return pairlists;\n    }\n\n    watch(\n      () => config.value,\n      () => {\n        configName.value = config.value.name;\n      },\n      {\n        deep: true,\n      },\n    );\n\n    return {\n      evaluating,\n      whitelist,\n      config,\n      configJSON,\n      savedConfigs,\n      configName,\n      startPairlistEvaluation,\n      addToConfig,\n      removeFromConfig,\n      saveConfig,\n      duplicateConfig,\n      deleteConfig,\n      newConfig,\n      selectOrCreateConfig,\n      addToBlacklist,\n      removeFromBlacklist,\n      duplicateBlacklist,\n      isSavedConfig,\n      firstPairlistIsGenerator,\n      pairlistValid,\n      stakeCurrency,\n      customExchange,\n      selectedExchange,\n    };\n  },\n  {\n    persist: {\n      key: 'ftPairlistConfig',\n      paths: ['savedConfigs', 'configName'],\n    },\n  },\n);\n", "<template>\n  <b-form-group label-cols=\"4\" label-size=\"md\" class=\"pb-1 text-start\" :description=\"param.help\">\n    <b-form-input\n      v-if=\"param.type === PairlistParamType.string || param.type === PairlistParamType.number\"\n      v-model=\"paramValue\"\n      size=\"sm\"\n    ></b-form-input>\n\n    <b-form-checkbox\n      v-if=\"param.type === PairlistParamType.boolean\"\n      v-model=\"paramValue\"\n    ></b-form-checkbox>\n\n    <b-form-select\n      v-if=\"param.type === PairlistParamType.option\"\n      v-model=\"paramValue\"\n      :options=\"param.options\"\n    ></b-form-select>\n\n    <template #label>\n      <label> {{ param.description }}</label>\n    </template>\n  </b-form-group>\n</template>\n\n<script setup lang=\"ts\">\nimport { PairlistParameter, PairlistParamType } from '@/types';\n\ndefineProps<{\n  param: PairlistParameter;\n}>();\n\n// TODO: type should really be PairlistParamValue\nconst paramValue = defineModel<any>();\n</script>\n", "<template>\n  <b-card no-body class=\"mb-2\">\n    <template #header>\n      <div class=\"d-flex text-start align-items-center\">\n        <div class=\"d-flex flex-grow-1 align-items-center\">\n          <i-mdi-reorder-horizontal\n            role=\"button\"\n            class=\"handle me-2 fs-4 flex-shrink-0\"\n            width=\"24\"\n            height=\"24\"\n          />\n          <div\n            role=\"button\"\n            class=\"d-flex flex-grow-1 align-items-start flex-column user-select-none\"\n            @click=\"toggleVisible\"\n          >\n            <span class=\"fw-bold\">{{ pairlist.name }}</span>\n            <span class=\"text-small\">{{ pairlist.description }}</span>\n          </div>\n        </div>\n        <i-mdi-close\n          role=\"button\"\n          width=\"24\"\n          height=\"24\"\n          class=\"mx-2\"\n          @click=\"pairlistStore.removeFromConfig(index)\"\n        />\n        <i-mdi-chevron-down\n          v-if=\"!pairlist.showParameters\"\n          :class=\"hasParameters && !pairlist.showParameters ? 'visible' : 'invisible'\"\n          role=\"button\"\n          class=\"fs-4\"\n          @click=\"toggleVisible\"\n        />\n        <i-mdi-chevron-up\n          v-if=\"pairlist.showParameters\"\n          :class=\"hasParameters && pairlist.showParameters ? 'visible' : 'invisible'\"\n          role=\"button\"\n          class=\"fs-4\"\n          @click=\"toggleVisible\"\n        />\n      </div>\n    </template>\n    <b-collapse v-model=\"pairlist.showParameters\">\n      <b-card-body>\n        <PairlistConfigParameter\n          v-for=\"(parameter, key) in pairlist.params\"\n          :key=\"key\"\n          v-model=\"pairlist.params[key].value\"\n          :param=\"parameter\"\n        />\n      </b-card-body>\n    </b-collapse>\n  </b-card>\n</template>\n\n<script setup lang=\"ts\">\nimport { usePairlistConfigStore } from '@/stores/pairlistConfig';\nimport { Pairlist } from '@/types';\n\nconst pairlistStore = usePairlistConfigStore();\n\ndefineProps<{\n  index: number;\n}>();\n\nconst pairlist = defineModel<Pairlist>({ required: true });\n\nconst hasParameters = computed(() => Object.keys(pairlist.value.params).length > 0);\n\nfunction toggleVisible() {\n  if (hasParameters.value) {\n    pairlist.value.showParameters = !pairlist.value.showParameters;\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped></style>\n", "<template>\n  <b-card no-body class=\"mb-2\">\n    <template #header>\n      <div\n        class=\"d-flex flex-row align-items-center justify-content-between\"\n        role=\"button\"\n        @click=\"visible = !visible\"\n      >\n        <span class=\"fw-bold fd-italic\">Blacklist</span>\n        <i-mdi-chevron-down\n          v-if=\"!visible\"\n          :class=\"!visible ? 'visible' : 'invisible'\"\n          role=\"button\"\n          class=\"fs-4\"\n        />\n        <i-mdi-chevron-up\n          v-if=\"visible\"\n          :class=\"visible ? 'visible' : 'invisible'\"\n          role=\"button\"\n          class=\"fs-4\"\n        />\n      </div>\n    </template>\n    <b-collapse v-model=\"visible\">\n      <b-card-body>\n        <div class=\"d-flex mb-4 align-items-center gap-2\">\n          <span class=\"col-auto\">Copy from:</span\n          ><b-form-select v-model=\"copyFromConfig\" size=\"sm\" :options=\"configNames\" />\n          <b-button title=\"Copy\" size=\"sm\" @click=\"pairlistStore.duplicateBlacklist(copyFromConfig)\"\n            ><i-mdi-content-copy\n          /></b-button>\n        </div>\n        <b-input-group\n          v-for=\"(item, i) in pairlistStore.config.blacklist\"\n          :key=\"i\"\n          class=\"mb-2\"\n          size=\"sm\"\n        >\n          <b-form-input v-model=\"pairlistStore.config.blacklist[i]\" />\n          <template #append>\n            <b-button size=\"sm\" @click=\"pairlistStore.removeFromBlacklist(i)\"\n              ><i-mdi-close\n            /></b-button>\n          </template>\n        </b-input-group>\n        <b-button size=\"sm\" @click=\"pairlistStore.addToBlacklist()\">Add</b-button>\n      </b-card-body>\n    </b-collapse>\n  </b-card>\n</template>\n<script setup lang=\"ts\">\nimport { usePairlistConfigStore } from '@/stores/pairlistConfig';\nconst pairlistStore = usePairlistConfigStore();\nconst copyFromConfig = ref('');\nconst visible = ref(false);\n\nconst configNames = computed(() =>\n  pairlistStore.savedConfigs.filter((c) => c.name !== pairlistStore.config.name).map((c) => c.name),\n);\n</script>\n<style lang=\"scss\" scoped></style>\n", "<template>\n  <div class=\"d-flex flex-column flex-sm-row mb-2 gap-2\">\n    <b-button\n      title=\"Save configuration\"\n      size=\"sm\"\n      variant=\"primary\"\n      @click=\"pairlistStore.saveConfig(pairlistStore.config.name)\"\n    >\n      <i-mdi-content-save />\n    </b-button>\n    <EditValue\n      v-model=\"pairlistStore.config.name\"\n      editable-name=\"config\"\n      :allow-add=\"true\"\n      :allow-duplicate=\"true\"\n      :allow-edit=\"true\"\n      class=\"d-flex flex-grow-1\"\n      @delete=\"pairlistStore.deleteConfig\"\n      @duplicate=\"(oldName: string, newName: string) => pairlistStore.duplicateConfig(newName)\"\n      @new=\"(name: string) => pairlistStore.newConfig(name)\"\n      @rename=\"(oldName: string, newName: string) => pairlistStore.saveConfig(newName)\"\n    >\n      <b-form-select\n        v-model=\"pairlistStore.configName\"\n        size=\"sm\"\n        :options=\"pairlistStore.savedConfigs.map((c) => c.name)\"\n        @update:model-value=\"(config) => pairlistStore.selectOrCreateConfig(config as string)\"\n      />\n    </EditValue>\n    <b-button\n      title=\"Evaluate pairlist\"\n      :disabled=\"pairlistStore.evaluating || !pairlistStore.pairlistValid\"\n      variant=\"primary\"\n      class=\"px-5\"\n      size=\"sm\"\n      @click=\"pairlistStore.startPairlistEvaluation()\"\n    >\n      <b-spinner v-if=\"pairlistStore.evaluating\" small></b-spinner>\n      <span>{{ pairlistStore.evaluating ? '' : 'Evaluate' }}</span>\n    </b-button>\n  </div>\n</template>\n<script setup lang=\"ts\">\nimport { usePairlistConfigStore } from '@/stores/pairlistConfig';\nimport EditValue from '../general/EditValue.vue';\nconst pairlistStore = usePairlistConfigStore();\n</script>\n", "<template>\n  <div class=\"w-100 d-flex\">\n    <b-form-select\n      id=\"exchange-select\"\n      v-model=\"exchangeModel.exchange\"\n      size=\"sm\"\n      :options=\"exchangeList\"\n    >\n    </b-form-select>\n    <b-form-select\n      id=\"tradeMode-select\"\n      v-model=\"exchangeModel.trade_mode\"\n      size=\"sm\"\n      :options=\"tradeModes\"\n      :disabled=\"tradeModes.length < 2\"\n    >\n    </b-form-select>\n    <b-button class=\"ms-2 no-min-w\" size=\"sm\" @click=\"botStore.activeBot.getExchangeList\">\n      <i-mdi-refresh />\n    </b-button>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nimport { ExchangeSelection } from '@/types';\n\nconst exchangeModel = defineModel({\n  type: Object as () => ExchangeSelection,\n  required: true,\n});\nconst botStore = useBotStore();\n\nconst exchangeList = computed(() => {\n  const supported = botStore.activeBot.exchangeList\n    .filter((ex) => ex.valid && ex.supported)\n    .sort((a, b) => a.name.localeCompare(b.name));\n\n  const unsupported = botStore.activeBot.exchangeList\n    .filter((ex) => ex.valid && !ex.supported)\n    .sort((a, b) => a.name.localeCompare(b.name));\n\n  return [\n    { label: 'Supported', options: supported.map((e) => e.name) },\n    { label: 'Unsupported', options: unsupported.map((e) => e.name) },\n  ];\n});\n\nconst tradeModesTyped = computed(() => {\n  const val = botStore.activeBot.exchangeList.find(\n    (ex) => ex.name === exchangeModel.value.exchange,\n  )?.trade_modes;\n  return val ?? [];\n});\n\nconst tradeModes = computed(() => {\n  return tradeModesTyped.value.map((tm) => {\n    return {\n      text: `${tm.margin_mode} ${tm.trading_mode}`,\n      value: tm,\n    };\n  });\n});\n\nwatch(\n  () => exchangeModel.value.exchange,\n  () => {\n    if (tradeModesTyped.value.length < 2) {\n      exchangeModel.value.trade_mode = tradeModesTyped.value[0];\n    }\n  },\n);\n\nonMounted(() => {\n  if (botStore.activeBot.exchangeList.length === 0) {\n    botStore.activeBot.getExchangeList();\n  }\n});\n</script>\n", "<template>\n  <div class=\"d-flex px-3 mb-3 gap-3 flex-column flex-lg-row\">\n    <b-list-group ref=\"availablePairlistsEl\" class=\"available-pairlists\">\n      <b-list-group-item\n        v-for=\"pairlist in availablePairlists\"\n        :key=\"pairlist.name\"\n        :class=\"{\n          'no-drag': pairlistStore.config.pairlists.length == 0 && !pairlist.is_pairlist_generator,\n        }\"\n        class=\"pairlist d-flex text-start align-items-center py-2 px-3\"\n      >\n        <div class=\"d-flex flex-grow-1 align-items-start flex-column\">\n          <span class=\"fw-bold\">{{ pairlist.name }}</span>\n          <span class=\"text-small\">{{ pairlist.description }}</span>\n        </div>\n        <b-button\n          class=\"p-0 add-pairlist\"\n          style=\"border: none\"\n          variant=\"outline-light\"\n          :disabled=\"pairlistStore.config.pairlists.length == 0 && !pairlist.is_pairlist_generator\"\n          @click=\"pairlistStore.addToConfig(pairlist, pairlistStore.config.pairlists.length)\"\n        >\n          <i-mdi-arrow-right-bold-box-outline class=\"fs-4\" />\n        </b-button>\n      </b-list-group-item>\n    </b-list-group>\n    <div class=\"d-flex flex-column flex-fill\">\n      <PairlistConfigActions />\n      <div class=\"border rounded-1 p-2 mb-2\">\n        <div class=\"d-flex align-items-center gap-2 my-2\">\n          <span class=\"col-auto\">Stake currency: </span>\n          <b-form-input v-model=\"pairlistStore.stakeCurrency\" size=\"sm\" />\n        </div>\n\n        <div class=\"mb-2 border rounded-1 p-2 text-start\">\n          <b-form-checkbox v-model=\"pairlistStore.customExchange\" class=\"mb-2\">\n            Custom Exchange\n          </b-form-checkbox>\n          <ExchangeSelect\n            v-if=\"pairlistStore.customExchange\"\n            v-model=\"pairlistStore.selectedExchange\"\n          />\n        </div>\n      </div>\n      <PairlistConfigBlacklist />\n      <b-alert\n        :model-value=\"\n          pairlistStore.config.pairlists.length > 0 && !pairlistStore.firstPairlistIsGenerator\n        \"\n        variant=\"warning\"\n      >\n        First entry in the pairlist must be a Generating pairlist, like StaticPairList or\n        VolumePairList.\n      </b-alert>\n      <div\n        ref=\"pairlistConfigsEl\"\n        class=\"d-flex flex-column flex-grow-1 position-relative border rounded-1 p-1\"\n        :class=\"{ empty: configEmpty }\"\n      >\n        <PairlistConfigItem\n          v-for=\"(pairlist, i) in pairlistStore.config.pairlists\"\n          :key=\"pairlist.id\"\n          v-model=\"pairlistStore.config.pairlists[i]\"\n          :index=\"i\"\n          @remove=\"pairlistStore.removeFromConfig\"\n        />\n      </div>\n    </div>\n    <div class=\"d-flex flex-column col-12 col-lg-3\">\n      <b-form-radio-group v-model=\"selectedView\" class=\"mb-2\" size=\"sm\" buttons>\n        <b-form-radio button value=\"Config\"> Config</b-form-radio>\n        <b-form-radio button value=\"Results\" :disabled=\"pairlistStore.whitelist.length === 0\">\n          Results</b-form-radio\n        >\n      </b-form-radio-group>\n      <div class=\"position-relative flex-fill overflow-auto\">\n        <CopyableTextfield\n          v-if=\"selectedView === 'Config'\"\n          class=\"position-lg-absolute w-100\"\n          :content=\"pairlistStore.configJSON\"\n          :is-valid=\"pairlistStore.pairlistValid\"\n        />\n        <CopyableTextfield\n          v-if=\"selectedView === 'Results'\"\n          class=\"position-lg-absolute w-100\"\n          :content=\"pairlistStore.whitelist\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { usePairlistConfigStore } from '@/stores/pairlistConfig';\nimport PairlistConfigItem from './PairlistConfigItem.vue';\nimport PairlistConfigBlacklist from './PairlistConfigBlacklist.vue';\nimport PairlistConfigActions from './PairlistConfigActions.vue';\nimport { Pairlist } from '@/types';\nimport { useSortable, moveArrayElement } from '@vueuse/integrations/useSortable';\nimport ExchangeSelect from './ExchangeSelect.vue';\n\nconst botStore = useBotStore();\nconst pairlistStore = usePairlistConfigStore();\n\nconst availablePairlists = ref<Pairlist[]>([]);\nconst pairlistConfigsEl = ref<HTMLElement | null>(null);\nconst availablePairlistsEl = ref<HTMLElement | null>(null);\nconst selectedView = ref<'Config' | 'Results'>('Config');\n\nconst configEmpty = computed(() => {\n  return pairlistStore.config.pairlists.length == 0;\n});\n\nuseSortable(availablePairlistsEl, availablePairlists.value, {\n  group: {\n    name: 'configurator',\n    pull: 'clone',\n    put: false,\n  },\n  sort: false,\n  filter: '.no-drag',\n  dragClass: 'dragging',\n});\n\nuseSortable(pairlistConfigsEl, pairlistStore.config.pairlists, {\n  handle: '.handle',\n  group: 'configurator',\n  onUpdate: async (e) => {\n    moveArrayElement(pairlistStore.config.pairlists, e.oldIndex, e.newIndex);\n  },\n  onAdd: (e) => {\n    const pairlist = availablePairlists.value[e.oldIndex];\n    pairlistStore.addToConfig(pairlist, e.newIndex);\n    // quick fix from: https://github.com/SortableJS/Sortable/issues/1515\n    e.clone.replaceWith(e.item);\n    e.clone.remove();\n  },\n});\n\nonMounted(async () => {\n  availablePairlists.value = (await botStore.activeBot.getPairlists()).pairlists.sort((a, b) =>\n    // Sort by is_pairlist_generator (by name), then by name.\n    // TODO: this might need to be improved\n    a.is_pairlist_generator === b.is_pairlist_generator\n      ? a.name.localeCompare(b.name)\n      : a.is_pairlist_generator\n        ? -1\n        : 1,\n  );\n  pairlistStore.selectOrCreateConfig(\n    pairlistStore.isSavedConfig(pairlistStore.configName) ? pairlistStore.configName : 'default',\n  );\n});\n\nwatch(\n  () => pairlistStore.whitelist,\n  () => {\n    selectedView.value = 'Results';\n  },\n);\n</script>\n\n<style lang=\"scss\" scoped>\n.pairlist {\n  &:hover {\n    cursor: grab;\n  }\n  &.no-drag {\n    color: gray;\n  }\n  &.no-drag:hover {\n    cursor: default;\n  }\n  &.dragging {\n    border: 1px solid white;\n    border-radius: 0;\n  }\n}\n\n[data-bs-theme='light'] .add-pairlist {\n  color: black;\n}\n\n.empty {\n  &:after {\n    content: 'Drag pairlist here';\n    position: absolute;\n    align-self: center;\n    font-size: 1.1rem;\n    text-transform: uppercase;\n    line-height: 0;\n    top: 50%;\n  }\n}\n</style>\n"], "names": ["PairlistParamType", "copy", "isSupported", "useClipboard", "usePairlistConfigStore", "defineStore", "botStore", "useBotStore", "evaluating", "ref", "intervalId", "stakeCurrency", "_a", "whitelist", "customExchange", "selectedExchange", "_b", "_c", "TradingMode", "_d", "MarginMode", "config", "makeConfig", "savedConfigs", "config<PERSON><PERSON>", "firstPairlistIsGenerator", "computed", "pairlist<PERSON><PERSON><PERSON>", "configJSON", "configToPayloadItems", "isSavedConfig", "name", "c", "addToConfig", "pairlist", "index", "toRaw", "param", "isNotUndefined", "removeFromConfig", "saveConfig", "i", "newConfig", "duplicateConfig", "deleteConfig", "selectOrCreateConfig", "pairlists", "blacklist", "addToBlacklist", "removeFromBlacklist", "duplicateBlacklist", "conf", "startPairlistEvaluation", "payload", "configToPayload", "jobId", "wl", "show<PERSON><PERSON><PERSON>", "convertToParamType", "type", "value", "key", "watch", "paramValue", "_useModel", "__props", "pairlistStore", "hasParameters", "toggleVisible", "copyFromConfig", "visible", "config<PERSON><PERSON><PERSON>", "exchangeModel", "exchangeList", "supported", "ex", "a", "b", "unsupported", "e", "tradeModesTyped", "tradeModes", "tm", "onMounted", "availablePairlists", "pairlistConfigsEl", "availablePairlistsEl", "<PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON>y", "useSortable", "moveArrayElement"], "mappings": "ymBA2BY,IAAAA,GAAAA,IACVA,EAAA,OAAS,SACTA,EAAA,OAAS,SACTA,EAAA,QAAU,UACVA,EAAA,OAAS,SAJCA,IAAAA,GAAA,CAAA,CAAA,4NCPZ,KAAM,CAAE,KAAAC,EAAM,YAAAC,CAAY,EAAIC,GAAa,kqBCF9BC,EAAyBC,GACpC,iBACA,IAAM,iBACJ,MAAMC,EAAWC,IAEXC,EAAaC,EAAa,EAAK,EAC/BC,EAAaD,IACbE,EAAgBF,IAAYG,GAAAN,EAAS,YAAT,YAAAM,GAAoB,gBAAiB,MAAM,EACvEC,EAAYJ,EAAc,CAAA,CAAE,EAC5BK,EAAiBL,EAAa,EAAK,EACnCM,EAAmBN,EAAuB,CAC9C,WAAUO,GAAAV,EAAS,YAAT,YAAAU,GAAoB,SAAS,WAAY,GACnD,WAAY,CACV,eAAcC,GAAAX,EAAS,YAAT,YAAAW,GAAoB,SAAS,eAAgBC,GAAY,KACvE,cACEC,GAAAb,EAAS,YAAT,YAAAa,GAAoB,SAAS,gBAAiBD,GAAY,QACtDE,GAAW,SACXA,GAAW,IACnB,CAAA,CACD,EAEKC,EAASZ,EAAoBa,EAAA,CAAY,EACzCC,EAAed,EAAsB,CAAA,CAAE,EACvCe,EAAaf,EAAY,EAAE,EAE3BgB,EAA2BC,EAAkB,IAAM,OAEvD,MAAI,IAAAd,EAAAS,EAAO,MAAM,UAAU,CAAC,IAAxB,MAAAT,EAA2B,sBAGxB,CACR,EAEKe,EAAgBD,EAAkB,IAC/BD,EAAyB,OAASJ,EAAO,MAAM,UAAU,OAAS,CAC1E,EAEKO,EAAaF,EAAS,IACnB,KAAK,UAAUG,GAAqB,EAAG,KAAM,CAAC,CACtD,EAEKC,EAAiBC,GACrBR,EAAa,MAAM,UAAWS,GAAMA,EAAE,OAASD,CAAI,EAAI,GAEhD,SAAAE,EAAYC,EAAoBC,EAAe,CAC3CD,EAAA,gBAAgBE,EAAMF,CAAQ,CAAC,EAC1CA,EAAS,eAAiB,GACrBA,EAAS,KACZA,EAAS,GAAK,KAAK,IAAI,EAAE,SAAS,EAAE,EAAI,KAAK,OAAA,EAAS,SAAS,EAAE,EAAE,UAAU,CAAC,GAErE,UAAAG,KAASH,EAAS,OAC3BA,EAAS,OAAOG,CAAK,EAAE,MAAQC,GAAeJ,EAAS,OAAOG,CAAK,EAAE,OAAO,EACxEH,EAAS,OAAOG,CAAK,EAAE,QACvB,GAENhB,EAAO,MAAM,UAAU,OAAOc,EAAO,EAAGD,CAAQ,CAClD,CAEA,SAASK,EAAiBJ,EAAe,CACvCd,EAAO,MAAM,UAAU,OAAOc,EAAO,CAAC,CACxC,CAES,SAAAK,EAAWT,EAAO,GAAI,CACvB,MAAAU,EAAIlB,EAAa,MAAM,UAAWS,GAAMA,EAAE,OAASX,EAAO,MAAM,IAAI,EAC1EA,EAAO,MAAM,KAAOU,EAEhBU,EAAI,GACNlB,EAAa,MAAMkB,CAAC,EAAI,gBAAgBL,EAAMf,EAAO,KAAK,CAAC,EAE3DE,EAAa,MAAM,KAAK,gBAAgBa,EAAMf,EAAO,KAAK,CAAC,CAAC,CAEhE,CAEA,SAASqB,EAAUX,EAAc,CAC/B,MAAMC,EAAIV,EAAW,CAAE,KAAAS,CAAM,CAAA,EAChBR,EAAA,MAAM,KAAKS,CAAC,EAClBX,EAAA,MAAQ,gBAAgBW,CAAC,CAClC,CAES,SAAAW,EAAgBZ,EAAO,GAAI,CAClC,MAAMC,EAAIV,EAAW,CACnB,KAAAS,EACA,UAAWK,EAAMf,EAAO,MAAM,SAAS,EACvC,UAAWe,EAAMf,EAAO,MAAM,SAAS,CAAA,CACxC,EACYE,EAAA,MAAM,KAAKS,CAAC,EAClBX,EAAA,MAAQ,gBAAgBW,CAAC,CAClC,CAEA,SAASY,GAAe,CAChB,MAAAH,EAAIlB,EAAa,MAAM,UAAWS,GAAMA,EAAE,OAASX,EAAO,MAAM,IAAI,EACtEoB,EAAI,KACOlB,EAAA,MAAM,OAAOkB,EAAG,CAAC,EAC9BI,EACEtB,EAAa,MAAM,OAAS,EAAIA,EAAa,MAAM,CAAC,EAAE,KAAO,SAAA,EAGnE,CAEA,SAASsB,EAAqBd,EAAc,CACpC,MAAAC,EAAIT,EAAa,MAAM,KAAMS,GAAMD,IAASC,EAAE,IAAI,EACpDA,EACFX,EAAO,MAAQ,gBAAgBe,EAAMJ,CAAC,CAAC,EAEvCU,EAAUX,CAAI,CAElB,CAEA,SAAST,EAAW,CAAE,KAAAS,EAAO,GAAI,UAAAe,EAAY,CAAA,EAAI,UAAAC,EAAY,EAAO,EAAA,GAAoB,CAC/E,MAAA,CAAE,KAAAhB,EAAM,UAAAe,EAAW,UAAAC,EAC5B,CAEA,SAASC,GAAiB,CACjB3B,EAAA,MAAM,UAAU,KAAK,EAAE,CAChC,CAEA,SAAS4B,GAAoBd,EAAe,CAC1Cd,EAAO,MAAM,UAAU,OAAOc,EAAO,CAAC,CACxC,CAEA,SAASe,GAAmB1B,EAAoB,CACxC,MAAA2B,EAAO5B,EAAa,MAAM,KAAMS,GAAMA,EAAE,OAASR,CAAU,EAC7D2B,IACF9B,EAAO,MAAM,UAAY,gBAAgBe,EAAMe,EAAK,SAAS,CAAC,EAElE,CAEA,eAAeC,IAA0B,CACvC,MAAMC,EAA4BC,KAElC9C,EAAW,MAAQ,GACf,GAAA,CACI,KAAA,CAAE,OAAQ+C,GAAU,MAAMjD,EAAS,UAAU,iBAAiB+C,CAAO,EACnE,QAAA,IAAI,QAASE,CAAK,EAEf7C,EAAA,MAAQ,OAAO,YAAY,SAAY,CAE5C,GAAA,EADQ,MAAMJ,EAAS,UAAU,uBAAuBiD,CAAK,GACxD,QAAS,CAChB,cAAc7C,EAAW,KAAK,EAC9B,MAAM8C,EAAK,MAAMlD,EAAS,UAAU,sBAAsBiD,CAAK,EAC/D/C,EAAW,MAAQ,GACfgD,EAAG,SAAW,UACN3C,EAAA,MAAQ2C,EAAG,OAAO,UACnBA,EAAG,QACFC,GAAAD,EAAG,MAAO,QAAQ,EAC5BhD,EAAW,MAAQ,GAEvB,GACC,GAAI,OACO,CACdiD,GAAU,oBAAqB,QAAQ,EACvCjD,EAAW,MAAQ,EACrB,CACF,CAES,SAAAkD,GAAmBC,EAAyBC,EAA2B,CAC1E,OAAAD,IAAS3D,EAAkB,OACtB,OAAO4D,CAAK,EACVD,IAAS3D,EAAkB,QAC7B,EAAQ4D,EAER,OAAOA,CAAK,CAEvB,CAEA,SAASN,IAAoC,CAE3C,MAAMtB,EAAsB,CAC1B,UAFuCH,KAGvC,eAAgBlB,EAAc,MAC9B,UAAWU,EAAO,MAAM,SAAA,EAE1B,OAAIP,EAAe,QACjB,QAAQ,IAAI,+BAA+B,EACzCkB,EAAA,SAAWjB,EAAiB,MAAM,SAClCiB,EAAA,aAAejB,EAAiB,MAAM,WAAW,aACjDiB,EAAA,YAAcjB,EAAiB,MAAM,WAAW,aAE7CiB,CACT,CAEA,SAASH,IAAuB,CAC9B,MAAMiB,EAAmC,CAAA,EACzC,OAAAzB,EAAO,MAAM,UAAU,QAASA,GAAW,CACzC,MAAMa,EAAW,CACf,OAAQb,EAAO,IAAA,EAEN,UAAAwC,KAAOxC,EAAO,OAAQ,CACzB,MAAAgB,EAAQhB,EAAO,OAAOwC,CAAG,EAC3BxB,EAAM,QACRH,EAAS2B,CAAG,EAAIH,GAAmBrB,EAAM,KAAMA,EAAM,KAAK,EAE9D,CACAS,EAAU,KAAKZ,CAAQ,CAAA,CACxB,EAEMY,CACT,CAEA,OAAAgB,EACE,IAAMzC,EAAO,MACb,IAAM,CACOG,EAAA,MAAQH,EAAO,MAAM,IAClC,EACA,CACE,KAAM,EACR,CAAA,EAGK,CACL,WAAAb,EACA,UAAAK,EACA,OAAAQ,EACA,WAAAO,EACA,aAAAL,EACA,WAAAC,EACA,wBAAA4B,GACA,YAAAnB,EACA,iBAAAM,EACA,WAAAC,EACA,gBAAAG,EACA,aAAAC,EACA,UAAAF,EACA,qBAAAG,EACA,eAAAG,EACA,oBAAAC,GACA,mBAAAC,GACA,cAAApB,EACA,yBAAAL,EACA,cAAAE,EACA,cAAAhB,EACA,eAAAG,EACA,iBAAAC,CAAA,CAEJ,EACA,CACE,QAAS,CACP,IAAK,mBACL,MAAO,CAAC,eAAgB,YAAY,CACtC,CACF,CACF,qIClOM,MAAAgD,EAAaC,EAAiBC,EAAA,YAAA,gwCC2BpC,MAAMC,EAAgB9D,IAMhB8B,EAAW8B,EAAqBC,EAAA,YAAmB,EAEnDE,EAAgBzC,EAAS,IAAM,OAAO,KAAKQ,EAAS,MAAM,MAAM,EAAE,OAAS,CAAC,EAElF,SAASkC,GAAgB,CACnBD,EAAc,QAChBjC,EAAS,MAAM,eAAiB,CAACA,EAAS,MAAM,eAEpD,y7CCtBA,MAAMgC,EAAgB9D,IAChBiE,EAAiB5D,EAAI,EAAE,EACvB6D,EAAU7D,EAAI,EAAK,EAEnB8D,EAAc7C,EAAS,IAC3BwC,EAAc,aAAa,OAAQlC,GAAMA,EAAE,OAASkC,EAAc,OAAO,IAAI,EAAE,IAAKlC,GAAMA,EAAE,IAAI,CAAA,qzDCZlG,MAAMkC,EAAgB9D,kzCCjBhB,MAAAoE,EAAgBR,EAGrBC,EAAA,YAAA,EACK3D,EAAWC,IAEXkE,EAAe/C,EAAS,IAAM,CAC5B,MAAAgD,EAAYpE,EAAS,UAAU,aAClC,OAAQqE,GAAOA,EAAG,OAASA,EAAG,SAAS,EACvC,KAAK,CAACC,EAAGC,IAAMD,EAAE,KAAK,cAAcC,EAAE,IAAI,CAAC,EAExCC,EAAcxE,EAAS,UAAU,aACpC,OAAQqE,GAAOA,EAAG,OAAS,CAACA,EAAG,SAAS,EACxC,KAAK,CAACC,EAAGC,IAAMD,EAAE,KAAK,cAAcC,EAAE,IAAI,CAAC,EAEvC,MAAA,CACL,CAAE,MAAO,YAAa,QAASH,EAAU,IAAKK,GAAMA,EAAE,IAAI,CAAE,EAC5D,CAAE,MAAO,cAAe,QAASD,EAAY,IAAKC,GAAMA,EAAE,IAAI,CAAE,CAAA,CAClE,CACD,EAEKC,EAAkBtD,EAAS,IAAM,OAIrC,QAHYd,EAAAN,EAAS,UAAU,aAAa,KACzCqE,GAAOA,EAAG,OAASH,EAAc,MAAM,QACvC,IAFS,YAAA5D,EAET,cACW,CAAA,CAAC,CAChB,EAEKqE,EAAavD,EAAS,IACnBsD,EAAgB,MAAM,IAAKE,IACzB,CACL,KAAM,GAAGA,EAAG,WAAW,IAAIA,EAAG,YAAY,GAC1C,MAAOA,CAAA,EAEV,CACF,EAED,OAAApB,EACE,IAAMU,EAAc,MAAM,SAC1B,IAAM,CACAQ,EAAgB,MAAM,OAAS,IACjCR,EAAc,MAAM,WAAaQ,EAAgB,MAAM,CAAC,EAE5D,CAAA,EAGFG,GAAU,IAAM,CACV7E,EAAS,UAAU,aAAa,SAAW,GAC7CA,EAAS,UAAU,iBACrB,CACD,koCCwBD,MAAMA,EAAWC,IACX2D,EAAgB9D,IAEhBgF,EAAqB3E,EAAgB,CAAA,CAAE,EACvC4E,EAAoB5E,EAAwB,IAAI,EAChD6E,EAAuB7E,EAAwB,IAAI,EACnD8E,EAAe9E,EAA0B,QAAQ,EAEjD+E,EAAc9D,EAAS,IACpBwC,EAAc,OAAO,UAAU,QAAU,CACjD,EAEW,OAAAuB,GAAAH,EAAsBF,EAAmB,MAAO,CAC1D,MAAO,CACL,KAAM,eACN,KAAM,QACN,IAAK,EACP,EACA,KAAM,GACN,OAAQ,WACR,UAAW,UAAA,CACZ,EAEWK,GAAAJ,EAAmBnB,EAAc,OAAO,UAAW,CAC7D,OAAQ,UACR,MAAO,eACP,SAAU,MAAOa,GAAM,CACrBW,GAAiBxB,EAAc,OAAO,UAAWa,EAAE,SAAUA,EAAE,QAAQ,CACzE,EACA,MAAQA,GAAM,CACZ,MAAM7C,EAAWkD,EAAmB,MAAML,EAAE,QAAQ,EACtCb,EAAA,YAAYhC,EAAU6C,EAAE,QAAQ,EAE5CA,EAAA,MAAM,YAAYA,EAAE,IAAI,EAC1BA,EAAE,MAAM,QACV,CAAA,CACD,EAEDI,GAAU,SAAY,CACpBC,EAAmB,OAAS,MAAM9E,EAAS,UAAU,aAAA,GAAgB,UAAU,KAAK,CAACsE,EAAGC,IAGtFD,EAAE,wBAA0BC,EAAE,sBAC1BD,EAAE,KAAK,cAAcC,EAAE,IAAI,EAC3BD,EAAE,sBACA,GACA,CAAA,EAEMV,EAAA,qBACZA,EAAc,cAAcA,EAAc,UAAU,EAAIA,EAAc,WAAa,SAAA,CACrF,CACD,EAEDJ,EACE,IAAMI,EAAc,UACpB,IAAM,CACJqB,EAAa,MAAQ,SACvB,CAAA"}