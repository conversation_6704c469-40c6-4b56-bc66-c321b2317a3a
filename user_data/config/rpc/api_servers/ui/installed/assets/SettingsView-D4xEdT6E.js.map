{"version": 3, "file": "SettingsView-D4xEdT6E.js", "sources": ["../../src/views/SettingsView.vue"], "sourcesContent": ["<template>\n  <div class=\"container mt-3\">\n    <b-card header=\"FreqUI Settings\">\n      <div class=\"text-start d-flex flex-column gap-2\">\n        <p>UI Version: {{ settingsStore.uiVersion }}</p>\n        <div class=\"d-flex flex-column border rounded p-2 mb-2 gap-2\">\n          <h4>UI settings</h4>\n          <b-form-group\n            description=\"Lock dynamic layouts, so they cannot move anymore. Can also be set from the navbar at the top.\"\n          >\n            <b-form-checkbox v-model=\"layoutStore.layoutLocked\">Lock layout</b-form-checkbox>\n          </b-form-group>\n          <b-form-group description=\"Reset dynamic layouts to how they were.\">\n            <b-button size=\"sm\" class=\"me-1\" @click=\"resetDynamicLayout\">Reset layout</b-button>\n          </b-form-group>\n          <b-form-group\n            label=\"Show open trades in header\"\n            description=\"Decide if open trades should be visualized\"\n          >\n            <b-form-select\n              v-model=\"settingsStore.openTradesInTitle\"\n              :options=\"openTradesOptions\"\n            ></b-form-select>\n          </b-form-group>\n          <b-form-group\n            label=\"UTC Timezone\"\n            description=\"Select timezone (we recommend UTC is recommended as exchanges usually work in UTC)\"\n          >\n            <b-form-select\n              v-model=\"settingsStore.timezone\"\n              :options=\"timezoneOptions\"\n            ></b-form-select>\n          </b-form-group>\n          <b-form-group description=\"Keep background sync running while other bots are selected.\">\n            <b-form-checkbox v-model=\"settingsStore.backgroundSync\"\n              >Background sync</b-form-checkbox\n            >\n          </b-form-group>\n          <b-form-group description=\"Use confirmation dialogs when force-exiting a trade.\">\n            <b-form-checkbox v-model=\"settingsStore.confirmDialog\"\n              >Show Confirm Dialog for Trade Exits</b-form-checkbox\n            >\n          </b-form-group>\n        </div>\n\n        <div class=\"d-flex flex-column border rounded p-2 mb-2 gap-2\">\n          <h4>Candle settings</h4>\n          <b-form-group description=\"Use Heikin Ashi candles in your charts\">\n            <b-form-checkbox v-model=\"settingsStore.useHeikinAshiCandles\"\n              >Use Heikin Ashi candles.</b-form-checkbox\n            >\n          </b-form-group>\n          <b-form-group\n            description=\"Can reduce the transfer size for large dataframes. May require additional calls if the plot config changes.\"\n          >\n            <b-form-checkbox v-model=\"settingsStore.useReducedPairCalls\"\n              >Only request necessary columns (recommended to be checked).</b-form-checkbox\n            >\n          </b-form-group>\n          <b-form-group description=\"Candle Color Preference\">\n            <b-form-radio-group\n              id=\"settings-color-preference-radio-group\"\n              v-model=\"colorStore.colorPreference\"\n              name=\"color-preference-options\"\n              @change=\"colorStore.updateProfitLossColor\"\n            >\n              <b-form-radio\n                v-for=\"option in colorPreferenceOptions\"\n                :key=\"option.value\"\n                :value=\"option.value\"\n              >\n                <div class=\"d-flex\">\n                  <span class=\"me-2\">{{ option.text }}</span>\n                  <i-mdi-arrow-up-thin\n                    :color=\"\n                      option.value === ColorPreferences.GREEN_UP\n                        ? colorStore.colorProfit\n                        : colorStore.colorLoss\n                    \"\n                    class=\"color-candle-arrows\"\n                  />\n                  <i-mdi-arrow-down-thin\n                    :color=\"\n                      option.value === ColorPreferences.GREEN_UP\n                        ? colorStore.colorLoss\n                        : colorStore.colorProfit\n                    \"\n                    class=\"color-candle-arrows\"\n                  />\n                </div>\n              </b-form-radio>\n            </b-form-radio-group>\n          </b-form-group>\n        </div>\n        <div class=\"d-flex flex-column border rounded p-2 mb-2 gap-2\">\n          <b-form-group description=\"Notifications\">\n            <h4>Notification Settings</h4>\n            <b-form-checkbox v-model=\"settingsStore.notifications[FtWsMessageTypes.entryFill]\"\n              >Entry notifications</b-form-checkbox\n            >\n            <b-form-checkbox v-model=\"settingsStore.notifications[FtWsMessageTypes.exitFill]\"\n              >Exit notifications</b-form-checkbox\n            >\n            <b-form-checkbox v-model=\"settingsStore.notifications[FtWsMessageTypes.entryCancel]\"\n              >Entry Cancel notifications</b-form-checkbox\n            >\n            <b-form-checkbox v-model=\"settingsStore.notifications[FtWsMessageTypes.exitCancel]\"\n              >Exit Cancel notifications</b-form-checkbox\n            >\n          </b-form-group>\n        </div>\n      </div>\n    </b-card>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { OpenTradeVizOptions, useSettingsStore } from '@/stores/settings';\nimport { useLayoutStore } from '@/stores/layout';\nimport { showAlert } from '@/shared/alerts';\nimport { FtWsMessageTypes } from '@/types/wsMessageTypes';\nimport { ColorPreferences, useColorStore } from '@/stores/colors';\n\nconst settingsStore = useSettingsStore();\nconst colorStore = useColorStore();\nconst layoutStore = useLayoutStore();\n\nconst timezoneOptions = ['UTC', Intl.DateTimeFormat().resolvedOptions().timeZone];\nconst openTradesOptions = [\n  { value: OpenTradeVizOptions.showPill, text: 'Show pill in icon' },\n  { value: OpenTradeVizOptions.asTitle, text: 'Show in title' },\n  { value: OpenTradeVizOptions.noOpenTrades, text: \"Don't show open trades in header\" },\n];\nconst colorPreferenceOptions = [\n  { value: ColorPreferences.GREEN_UP, text: 'Green Up/Red Down' },\n  { value: ColorPreferences.RED_UP, text: 'Green Down/Red Up' },\n];\n\nconst resetDynamicLayout = () => {\n  layoutStore.resetTradingLayout();\n  layoutStore.resetDashboardLayout();\n  showAlert('Layouts have been reset.');\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.color-candle-arrows {\n  margin-left: -0.5rem;\n  margin-top: 2px;\n}\n</style>\n"], "names": ["settingsStore", "useSettingsStore", "colorStore", "useColorStore", "layoutStore", "useLayoutStore", "timezoneOptions", "openTradesOptions", "OpenTradeVizOptions", "colorPreferenceOptions", "ColorPreferences", "resetDynamicLayout", "show<PERSON><PERSON><PERSON>"], "mappings": "ktCA2HA,MAAMA,EAAgBC,IAChBC,EAAaC,IACbC,EAAcC,IAEdC,EAAkB,CAAC,MAAO,KAAK,iBAAiB,gBAAA,EAAkB,QAAQ,EAC1EC,EAAoB,CACxB,CAAE,MAAOC,EAAoB,SAAU,KAAM,mBAAoB,EACjE,CAAE,MAAOA,EAAoB,QAAS,KAAM,eAAgB,EAC5D,CAAE,MAAOA,EAAoB,aAAc,KAAM,kCAAmC,CAAA,EAEhFC,EAAyB,CAC7B,CAAE,MAAOC,EAAiB,SAAU,KAAM,mBAAoB,EAC9D,CAAE,MAAOA,EAAiB,OAAQ,KAAM,mBAAoB,CAAA,EAGxDC,EAAqB,IAAM,CAC/BP,EAAY,mBAAmB,EAC/BA,EAAY,qBAAqB,EACjCQ,EAAU,0BAA0B,CAAA"}