{"version": 3, "file": "MessageBox.vue_vue_type_script_setup_true_lang-t_g1ZU8z.js", "sources": ["../../src/components/ftbot/PairSummary.vue", "../../src/components/general/MessageBox.vue"], "sourcesContent": ["<template>\n  <div>\n    <b-form-group\n      label-for=\"trade-filter\"\n      class=\"mb-2 ms-2\"\n      :class=\"{\n        'me-4': backtestMode,\n        'me-2': !backtestMode,\n      }\"\n    >\n      <b-form-input id=\"trade-filter\" v-model=\"filterText\" type=\"text\" placeholder=\"Filter\" />\n    </b-form-group>\n    <b-list-group>\n      <b-list-group-item\n        v-for=\"comb in combinedPairList\"\n        :key=\"comb.pair\"\n        button\n        class=\"d-flex justify-content-between align-items-center py-1\"\n        :active=\"comb.pair === botStore.activeBot.selectedPair\"\n        :title=\"`${comb.pair} - ${comb.tradeCount} trades`\"\n        @click=\"botStore.activeBot.selectedPair = comb.pair\"\n      >\n        <div>\n          {{ comb.pair }}\n          <span v-if=\"comb.locks\" :title=\"comb.lockReason\"> <i-mdi-lock /> </span>\n        </div>\n\n        <TradeProfit v-if=\"comb.trade && !backtestMode\" :trade=\"comb.trade\" />\n        <ProfitPill\n          v-if=\"backtestMode && comb.tradeCount > 0\"\n          :profit-ratio=\"comb.profit\"\n          :stake-currency=\"botStore.activeBot.stakeCurrency\"\n        />\n      </b-list-group-item>\n    </b-list-group>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent, timestampms } from '@/shared/formatters';\nimport { Lock, Trade } from '@/types';\n\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\ninterface CombinedPairList {\n  pair: string;\n  lockReason: string;\n  profitString: string;\n  trade?: Trade;\n  locks?: Lock;\n  profit: number;\n  profitAbs: number;\n  tradeCount: number;\n}\nconst filterText = ref('');\n\nconst props = defineProps({\n  // TOOD: Should be string list\n  pairlist: { required: true, type: Array as () => string[] },\n  currentLocks: { required: false, type: Array as () => Lock[], default: () => [] },\n  trades: { required: true, type: Array as () => Trade[] },\n  sortMethod: { default: 'normal', type: String },\n  backtestMode: { required: false, default: false, type: Boolean },\n});\nconst botStore = useBotStore();\nconst combinedPairList = computed(() => {\n  const comb: CombinedPairList[] = [];\n\n  props.pairlist.forEach((pair) => {\n    const trades: Trade[] = props.trades.filter((el) => el.pair === pair);\n    const allLocks = props.currentLocks.filter((el) => el.pair === pair);\n    let lockReason = '';\n    let locks;\n\n    // Sort to have longer timeframe in front\n    allLocks.sort((a, b) => (a.lock_end_timestamp > b.lock_end_timestamp ? -1 : 1));\n    if (allLocks.length > 0) {\n      [locks] = allLocks;\n      lockReason = `${timestampms(locks.lock_end_timestamp)} - ${locks.reason}`;\n    }\n    let profitString = '';\n    let profit = 0;\n    let profitAbs = 0;\n    trades.forEach((trade) => {\n      profit += trade.profit_ratio;\n      profitAbs += trade.profit_abs ?? 0;\n    });\n    const tradeCount = trades.length;\n    const trade = tradeCount ? trades[0] : undefined;\n    if (trades.length > 0) {\n      profitString = `Current profit: ${formatPercent(profit)}`;\n    }\n    if (trade) {\n      profitString += `\\nOpen since: ${timestampms(trade.open_timestamp)}`;\n    }\n    if (\n      filterText.value === '' ||\n      pair.toLocaleLowerCase().includes(filterText.value.toLocaleLowerCase())\n    ) {\n      comb.push({ pair, trade, locks, lockReason, profitString, profit, profitAbs, tradeCount });\n    }\n  });\n\n  if (props.sortMethod === 'profit') {\n    comb.sort((a, b) => {\n      if (a.profit > b.profit) {\n        return -1;\n      }\n      return 1;\n    });\n  } else {\n    // sort Pairs: \"with open trade\" -> available -> locked\n    comb.sort((a, b) => {\n      if (a.trade && !b.trade) {\n        return -1;\n      }\n      if (a.trade && b.trade) {\n        // 2 open trade pairs\n        return a.trade.trade_id > b.trade.trade_id ? 1 : -1;\n      }\n      if (!a.locks && b.locks) {\n        return -1;\n      }\n      if (a.locks && b.locks) {\n        // Both have locks\n        return a.locks.lock_end_timestamp > b.locks.lock_end_timestamp ? 1 : -1;\n      }\n      return 1;\n    });\n  }\n  return comb;\n});\n</script>\n\n<style scoped>\n.list-group {\n  text-align: left;\n}\n</style>\n", "<template>\n  <b-modal\n    id=\"MsgBoxModal\"\n    ref=\"removeTradeModal\"\n    v-model=\"showRef\"\n    :title=\"title\"\n    @ok=\"msgBoxOK\"\n    @cancel=\"showRef = false\"\n    @keyup.esc=\"showRef = false\"\n    @keyup.enter=\"msgBoxOK\"\n  >\n    {{ message }}\n  </b-modal>\n</template>\n\n<script setup lang=\"ts\">\nexport interface MsgBoxObject {\n  title: string;\n  message: string;\n  accept: () => void;\n}\nconst showRef = ref<boolean>(false);\nconst title = ref<string>('');\nconst message = ref<string>('');\nconst accept = ref<() => void>(() => {\n  console.warn('Accepted not set.');\n});\n\nconst msgBoxOK = () => {\n  accept.value();\n  showRef.value = false;\n};\n\nconst show = (msg: MsgBoxObject) => {\n  title.value = msg.title;\n  message.value = msg.message;\n  showRef.value = true;\n  accept.value = msg.accept;\n};\n\ndefineExpose({ show });\n</script>\n\n<style scoped></style>\n"], "names": ["filterText", "ref", "props", "__props", "botStore", "useBotStore", "combinedPairList", "computed", "comb", "pair", "trades", "el", "allLocks", "lockReason", "locks", "a", "b", "timestampms", "profitString", "profit", "profitAbs", "trade", "tradeCount", "formatPercent", "showRef", "title", "message", "accept", "msgBoxOK", "__expose", "msg"], "mappings": "48BAsDM,MAAAA,EAAaC,EAAI,EAAE,EAEnBC,EAAQC,EAQRC,EAAWC,IACXC,EAAmBC,EAAS,IAAM,CACtC,MAAMC,EAA2B,CAAA,EAE3B,OAAAN,EAAA,SAAS,QAASO,GAAS,CACzB,MAAAC,EAAkBR,EAAM,OAAO,OAAQS,GAAOA,EAAG,OAASF,CAAI,EAC9DG,EAAWV,EAAM,aAAa,OAAQS,GAAOA,EAAG,OAASF,CAAI,EACnE,IAAII,EAAa,GACbC,EAGKF,EAAA,KAAK,CAACG,EAAGC,IAAOD,EAAE,mBAAqBC,EAAE,mBAAqB,GAAK,CAAE,EAC1EJ,EAAS,OAAS,IACpB,CAACE,CAAK,EAAIF,EACVC,EAAa,GAAGI,EAAYH,EAAM,kBAAkB,CAAC,MAAMA,EAAM,MAAM,IAEzE,IAAII,EAAe,GACfC,EAAS,EACTC,EAAY,EACTV,EAAA,QAASW,GAAU,CACxBF,GAAUE,EAAM,aAChBD,GAAaC,EAAM,YAAc,CAAA,CAClC,EACD,MAAMC,EAAaZ,EAAO,OACpBW,EAAQC,EAAaZ,EAAO,CAAC,EAAI,OACnCA,EAAO,OAAS,IACHQ,EAAA,mBAAmBK,EAAcJ,CAAM,CAAC,IAErDE,IACcH,GAAA;AAAA,cAAiBD,EAAYI,EAAM,cAAc,CAAC,KAGlErB,EAAW,QAAU,IACrBS,EAAK,oBAAoB,SAAST,EAAW,MAAM,kBAAmB,CAAA,IAEjEQ,EAAA,KAAK,CAAE,KAAAC,EAAM,MAAAY,EAAO,MAAAP,EAAO,WAAAD,EAAY,aAAAK,EAAc,OAAAC,EAAQ,UAAAC,EAAW,WAAAE,CAAY,CAAA,CAC3F,CACD,EAEGpB,EAAM,aAAe,SAClBM,EAAA,KAAK,CAACO,EAAGC,IACRD,EAAE,OAASC,EAAE,OACR,GAEF,CACR,EAGIR,EAAA,KAAK,CAACO,EAAGC,IACRD,EAAE,OAAS,CAACC,EAAE,MACT,GAELD,EAAE,OAASC,EAAE,MAERD,EAAE,MAAM,SAAWC,EAAE,MAAM,SAAW,EAAI,GAE/C,CAACD,EAAE,OAASC,EAAE,MACT,GAELD,EAAE,OAASC,EAAE,MAERD,EAAE,MAAM,mBAAqBC,EAAE,MAAM,mBAAqB,EAAI,GAEhE,CACR,EAEIR,CAAA,CACR,upCC9GK,MAAAgB,EAAUvB,EAAa,EAAK,EAC5BwB,EAAQxB,EAAY,EAAE,EACtByB,EAAUzB,EAAY,EAAE,EACxB0B,EAAS1B,EAAgB,IAAM,CACnC,QAAQ,KAAK,mBAAmB,CAAA,CACjC,EAEK2B,EAAW,IAAM,CACrBD,EAAO,MAAM,EACbH,EAAQ,MAAQ,EAAA,EAUL,OAAAK,EAAA,CAAE,KAPDC,GAAsB,CAClCL,EAAM,MAAQK,EAAI,MAClBJ,EAAQ,MAAQI,EAAI,QACpBN,EAAQ,MAAQ,GAChBG,EAAO,MAAQG,EAAI,MAAA,EAGA"}