import{a as ce,_ as ye}from"./EditValue.vue_vue_type_script_setup_true_lang-CWpIX5x3.js";import{g as R,br as $e,o as d,c as k,h as t,y as b,A as V,a as _,x as O,_ as X,X as Se,u as Y,r as x,bs as se,bt as ie,k as E,M as Z,bu as T,R as Be,s as re,bv as de,bw as Q,w as p,Z as ee,a0 as me,V as J,Y as Pe,b as c,L as F,F as j,K as D,bx as Ee,aU as _e,by as pe,O as fe,W as ge,z as K,e as A,v as H,aX as Ue,b2 as ze,q as ve,i as Ie,bz as ue,bA as Ne,N as Me,P as Te,bB as Le,af as Oe,$ as Re,p as Fe,d as Ae}from"./index-B2p78N-x.js";import{_ as be}from"./chevron-down-BTtjNJA7.js";import"./plus-box-outline-DIIEsppr.js";var L=(u=>(u.string="string",u.number="number",u.boolean="boolean",u.option="option",u))(L||{});const je={class:"copy-container position-relative"},De={class:"text-start border p-1 mb-0"},Je=R({__name:"CopyableTextfield",props:{content:{type:[String,Array],required:!0},isValid:{type:Boolean,default:!0}},setup(u){const{copy:n,isSupported:e}=$e();return(a,f)=>{const g=ce;return d(),k("div",je,[t(e)&&u.isValid?(d(),b(g,{key:0,role:"button",class:"copy-button position-absolute end-0 mt-1 me-2",onClick:f[0]||(f[0]=i=>t(n)(typeof u.content=="string"?u.content:JSON.stringify(u.content)))})):V("",!0),_("pre",De,[_("code",null,O(u.content),1)])])}}}),He=X(Je,[["__scopeId","data-v-1609c93b"]]),qe={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Ge=_("path",{fill:"currentColor",d:"m17 12l-5 5v-3H8v-4h4V7zM3 19V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2m2 0h14V5H5z"},null,-1),We=[Ge];function Ke(u,n){return d(),k("svg",qe,[...We])}const Xe={name:"mdi-arrow-right-bold-box-outline",render:Ke},q=Se("pairlistConfig",()=>{var oe,ne,ae,le;const u=Y(),n=x(!1),e=x(),a=x(((oe=u.activeBot)==null?void 0:oe.stakeCurrency)??"USDT"),f=x([]),g=x(!1),i=x({exchange:((ne=u.activeBot)==null?void 0:ne.botState.exchange)??"",trade_mode:{trading_mode:((ae=u.activeBot)==null?void 0:ae.botState.trading_mode)??se.SPOT,margin_mode:((le=u.activeBot)==null?void 0:le.botState.trading_mode)===se.FUTURES?ie.ISOLATED:ie.NONE}}),s=x(U()),o=x([]),m=x(""),y=E(()=>{var l;return!!((l=s.value.pairlists[0])!=null&&l.is_pairlist_generator)}),C=E(()=>y.value&&s.value.pairlists.length>0),z=E(()=>JSON.stringify(te(),null,2)),I=l=>o.value.findIndex(r=>r.name===l)>-1;function N(l,r){l=structuredClone(T(l)),l.showParameters=!1,l.id||(l.id=Date.now().toString(36)+Math.random().toString(36).substring(2));for(const h in l.params)l.params[h].value=Be(l.params[h].default)?l.params[h].default:"";s.value.pairlists.splice(r,0,l)}function $(l){s.value.pairlists.splice(l,1)}function S(l=""){const r=o.value.findIndex(h=>h.name===s.value.name);s.value.name=l,r>-1?o.value[r]=structuredClone(T(s.value)):o.value.push(structuredClone(T(s.value)))}function B(l){const r=U({name:l});o.value.push(r),s.value=structuredClone(r)}function w(l=""){const r=U({name:l,pairlists:T(s.value.pairlists),blacklist:T(s.value.blacklist)});o.value.push(r),s.value=structuredClone(r)}function P(){const l=o.value.findIndex(r=>r.name===s.value.name);l>-1&&(o.value.splice(l,1),v(o.value.length>0?o.value[0].name:"default"))}function v(l){const r=o.value.find(h=>l===h.name);r?s.value=structuredClone(T(r)):B(l)}function U({name:l="",pairlists:r=[],blacklist:h=[]}={}){return{name:l,pairlists:r,blacklist:h}}function G(){s.value.blacklist.push("")}function xe(l){s.value.blacklist.splice(l,1)}function Ce(l){const r=o.value.find(h=>h.name===l);r&&(s.value.blacklist=structuredClone(T(r.blacklist)))}async function Ve(){const l=ke();n.value=!0;try{const{job_id:r}=await u.activeBot.evaluatePairlist(l);console.log("jobId",r),e.value=window.setInterval(async()=>{if(!(await u.activeBot.getBackgroundJobStatus(r)).running){clearInterval(e.value);const M=await u.activeBot.getPairlistEvalResult(r);n.value=!1,M.status==="success"?f.value=M.result.whitelist:M.error&&(re(M.error,"danger"),n.value=!1)}},1e3)}catch{re("Evaluation failed","danger"),n.value=!1}}function we(l,r){return l===L.number?Number(r):l===L.boolean?!!r:String(r)}function ke(){const r={pairlists:te(),stake_currency:a.value,blacklist:s.value.blacklist};return g.value&&(console.log("setting custom exchange props"),r.exchange=i.value.exchange,r.trading_mode=i.value.trade_mode.trading_mode,r.margin_mode=i.value.trade_mode.margin_mode),r}function te(){const l=[];return s.value.pairlists.forEach(r=>{const h={method:r.name};for(const M in r.params){const W=r.params[M];W.value&&(h[M]=we(W.type,W.value))}l.push(h)}),l}return Z(()=>s.value,()=>{m.value=s.value.name},{deep:!0}),{evaluating:n,whitelist:f,config:s,configJSON:z,savedConfigs:o,configName:m,startPairlistEvaluation:Ve,addToConfig:N,removeFromConfig:$,saveConfig:S,duplicateConfig:w,deleteConfig:P,newConfig:B,selectOrCreateConfig:v,addToBlacklist:G,removeFromBlacklist:xe,duplicateBlacklist:Ce,isSavedConfig:I,firstPairlistIsGenerator:y,pairlistValid:C,stakeCurrency:a,customExchange:g,selectedExchange:i}},{persist:{key:"ftPairlistConfig",paths:["savedConfigs","configName"]}}),Ye=R({__name:"PairlistConfigParameter",props:de({param:{}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(u){const n=Q(u,"modelValue");return(e,a)=>{const f=ee,g=me,i=J,s=Pe;return d(),b(s,{"label-cols":"4","label-size":"md",class:"pb-1 text-start",description:e.param.help},{label:p(()=>[_("label",null,O(e.param.description),1)]),default:p(()=>[e.param.type===t(L).string||e.param.type===t(L).number?(d(),b(f,{key:0,modelValue:n.value,"onUpdate:modelValue":a[0]||(a[0]=o=>n.value=o),size:"sm"},null,8,["modelValue"])):V("",!0),e.param.type===t(L).boolean?(d(),b(g,{key:1,modelValue:n.value,"onUpdate:modelValue":a[1]||(a[1]=o=>n.value=o)},null,8,["modelValue"])):V("",!0),e.param.type===t(L).option?(d(),b(i,{key:2,modelValue:n.value,"onUpdate:modelValue":a[2]||(a[2]=o=>n.value=o),options:e.param.options},null,8,["modelValue","options"])):V("",!0)]),_:1},8,["description"])}}}),Ze={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Qe=_("path",{fill:"currentColor",d:"M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6l-6 6z"},null,-1),et=[Qe];function tt(u,n){return d(),k("svg",Ze,[...et])}const he={name:"mdi-chevron-up",render:tt},ot={class:"d-flex text-start align-items-center"},nt={class:"d-flex flex-grow-1 align-items-center"},at={class:"fw-bold"},lt={class:"text-small"},st=R({__name:"PairlistConfigItem",props:de({index:{}},{modelValue:{required:!0},modelModifiers:{}}),emits:["update:modelValue"],setup(u){const n=q(),e=Q(u,"modelValue"),a=E(()=>Object.keys(e.value.params).length>0);function f(){a.value&&(e.value.showParameters=!e.value.showParameters)}return(g,i)=>{const s=Ee,o=_e,m=be,y=he,C=Ye,z=pe,I=fe,N=ge;return d(),b(N,{"no-body":"",class:"mb-2"},{header:p(()=>[_("div",ot,[_("div",nt,[c(s,{role:"button",class:"handle me-2 fs-4 flex-shrink-0",width:"24",height:"24"}),_("div",{role:"button",class:"d-flex flex-grow-1 align-items-start flex-column user-select-none",onClick:f},[_("span",at,O(e.value.name),1),_("span",lt,O(e.value.description),1)])]),c(o,{role:"button",width:"24",height:"24",class:"mx-2",onClick:i[0]||(i[0]=$=>t(n).removeFromConfig(g.index))}),e.value.showParameters?V("",!0):(d(),b(m,{key:0,class:F([t(a)&&!e.value.showParameters?"visible":"invisible","fs-4"]),role:"button",onClick:f},null,8,["class"])),e.value.showParameters?(d(),b(y,{key:1,class:F([t(a)&&e.value.showParameters?"visible":"invisible","fs-4"]),role:"button",onClick:f},null,8,["class"])):V("",!0)])]),default:p(()=>[c(I,{modelValue:e.value.showParameters,"onUpdate:modelValue":i[1]||(i[1]=$=>e.value.showParameters=$)},{default:p(()=>[c(z,null,{default:p(()=>[(d(!0),k(j,null,D(e.value.params,($,S)=>(d(),b(C,{key:S,modelValue:e.value.params[S].value,"onUpdate:modelValue":B=>e.value.params[S].value=B,param:$},null,8,["modelValue","onUpdate:modelValue","param"]))),128))]),_:1})]),_:1},8,["modelValue"])]),_:1})}}}),it=_("span",{class:"fw-bold fd-italic"},"Blacklist",-1),rt={class:"d-flex mb-4 align-items-center gap-2"},ut=_("span",{class:"col-auto"},"Copy from:",-1),ct=R({__name:"PairlistConfigBlacklist",setup(u){const n=q(),e=x(""),a=x(!1),f=E(()=>n.savedConfigs.filter(g=>g.name!==n.config.name).map(g=>g.name));return(g,i)=>{const s=be,o=he,m=J,y=ce,C=H,z=ee,I=_e,N=Ue,$=pe,S=fe,B=ge;return d(),b(B,{"no-body":"",class:"mb-2"},{header:p(()=>[_("div",{class:"d-flex flex-row align-items-center justify-content-between",role:"button",onClick:i[0]||(i[0]=w=>a.value=!t(a))},[it,t(a)?V("",!0):(d(),b(s,{key:0,class:F([t(a)?"invisible":"visible","fs-4"]),role:"button"},null,8,["class"])),t(a)?(d(),b(o,{key:1,class:F([t(a)?"visible":"invisible","fs-4"]),role:"button"},null,8,["class"])):V("",!0)])]),default:p(()=>[c(S,{modelValue:t(a),"onUpdate:modelValue":i[4]||(i[4]=w=>K(a)?a.value=w:null)},{default:p(()=>[c($,null,{default:p(()=>[_("div",rt,[ut,c(m,{modelValue:t(e),"onUpdate:modelValue":i[1]||(i[1]=w=>K(e)?e.value=w:null),size:"sm",options:t(f)},null,8,["modelValue","options"]),c(C,{title:"Copy",size:"sm",onClick:i[2]||(i[2]=w=>t(n).duplicateBlacklist(t(e)))},{default:p(()=>[c(y)]),_:1})]),(d(!0),k(j,null,D(t(n).config.blacklist,(w,P)=>(d(),b(N,{key:P,class:"mb-2",size:"sm"},{append:p(()=>[c(C,{size:"sm",onClick:v=>t(n).removeFromBlacklist(P)},{default:p(()=>[c(I)]),_:2},1032,["onClick"])]),default:p(()=>[c(z,{modelValue:t(n).config.blacklist[P],"onUpdate:modelValue":v=>t(n).config.blacklist[P]=v},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024))),128)),c(C,{size:"sm",onClick:i[3]||(i[3]=w=>t(n).addToBlacklist())},{default:p(()=>[A("Add")]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}}),dt={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},mt=_("path",{fill:"currentColor",d:"M15 9H5V5h10m-3 14a3 3 0 0 1-3-3a3 3 0 0 1 3-3a3 3 0 0 1 3 3a3 3 0 0 1-3 3m5-16H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V7z"},null,-1),_t=[mt];function pt(u,n){return d(),k("svg",dt,[..._t])}const ft={name:"mdi-content-save",render:pt},gt={class:"d-flex flex-column flex-sm-row mb-2 gap-2"},vt=R({__name:"PairlistConfigActions",setup(u){const n=q();return(e,a)=>{const f=ft,g=H,i=J,s=ze;return d(),k("div",gt,[c(g,{title:"Save configuration",size:"sm",variant:"primary",onClick:a[0]||(a[0]=o=>t(n).saveConfig(t(n).config.name))},{default:p(()=>[c(f)]),_:1}),c(ye,{modelValue:t(n).config.name,"onUpdate:modelValue":a[3]||(a[3]=o=>t(n).config.name=o),"editable-name":"config","allow-add":!0,"allow-duplicate":!0,"allow-edit":!0,class:"d-flex flex-grow-1",onDelete:t(n).deleteConfig,onDuplicate:a[4]||(a[4]=(o,m)=>t(n).duplicateConfig(m)),onNew:a[5]||(a[5]=o=>t(n).newConfig(o)),onRename:a[6]||(a[6]=(o,m)=>t(n).saveConfig(m))},{default:p(()=>[c(i,{modelValue:t(n).configName,"onUpdate:modelValue":[a[1]||(a[1]=o=>t(n).configName=o),a[2]||(a[2]=o=>t(n).selectOrCreateConfig(o))],size:"sm",options:t(n).savedConfigs.map(o=>o.name)},null,8,["modelValue","options"])]),_:1},8,["modelValue","onDelete"]),c(g,{title:"Evaluate pairlist",disabled:t(n).evaluating||!t(n).pairlistValid,variant:"primary",class:"px-5",size:"sm",onClick:a[7]||(a[7]=o=>t(n).startPairlistEvaluation())},{default:p(()=>[t(n).evaluating?(d(),b(s,{key:0,small:""})):V("",!0),_("span",null,O(t(n).evaluating?"":"Evaluate"),1)]),_:1},8,["disabled"])])}}}),bt={class:"w-100 d-flex"},ht=R({__name:"ExchangeSelect",props:{modelValue:{type:Object,required:!0},modelModifiers:{}},emits:["update:modelValue"],setup(u){const n=Q(u,"modelValue"),e=Y(),a=E(()=>{const i=e.activeBot.exchangeList.filter(o=>o.valid&&o.supported).sort((o,m)=>o.name.localeCompare(m.name)),s=e.activeBot.exchangeList.filter(o=>o.valid&&!o.supported).sort((o,m)=>o.name.localeCompare(m.name));return[{label:"Supported",options:i.map(o=>o.name)},{label:"Unsupported",options:s.map(o=>o.name)}]}),f=E(()=>{var s;return((s=e.activeBot.exchangeList.find(o=>o.name===n.value.exchange))==null?void 0:s.trade_modes)??[]}),g=E(()=>f.value.map(i=>({text:`${i.margin_mode} ${i.trading_mode}`,value:i})));return Z(()=>n.value.exchange,()=>{f.value.length<2&&(n.value.trade_mode=f.value[0])}),ve(()=>{e.activeBot.exchangeList.length===0&&e.activeBot.getExchangeList()}),(i,s)=>{const o=J,m=Ie,y=H;return d(),k("div",bt,[c(o,{id:"exchange-select",modelValue:n.value.exchange,"onUpdate:modelValue":s[0]||(s[0]=C=>n.value.exchange=C),size:"sm",options:t(a)},null,8,["modelValue","options"]),c(o,{id:"tradeMode-select",modelValue:n.value.trade_mode,"onUpdate:modelValue":s[1]||(s[1]=C=>n.value.trade_mode=C),size:"sm",options:t(g),disabled:t(g).length<2},null,8,["modelValue","options","disabled"]),c(y,{class:"ms-2 no-min-w",size:"sm",onClick:t(e).activeBot.getExchangeList},{default:p(()=>[c(m)]),_:1},8,["onClick"])])}}}),xt=u=>(Fe("data-v-29c41eaf"),u=u(),Ae(),u),Ct={class:"d-flex px-3 mb-3 gap-3 flex-column flex-lg-row"},Vt={class:"d-flex flex-grow-1 align-items-start flex-column"},wt={class:"fw-bold"},kt={class:"text-small"},yt={class:"d-flex flex-column flex-fill"},$t={class:"border rounded-1 p-2 mb-2"},St={class:"d-flex align-items-center gap-2 my-2"},Bt=xt(()=>_("span",{class:"col-auto"},"Stake currency: ",-1)),Pt={class:"mb-2 border rounded-1 p-2 text-start"},Et={class:"d-flex flex-column col-12 col-lg-3"},Ut={class:"position-relative flex-fill overflow-auto"},zt=R({__name:"PairlistConfigurator",setup(u){const n=Y(),e=q(),a=x([]),f=x(null),g=x(null),i=x("Config"),s=E(()=>e.config.pairlists.length==0);return ue(g,a.value,{group:{name:"configurator",pull:"clone",put:!1},sort:!1,filter:".no-drag",dragClass:"dragging"}),ue(f,e.config.pairlists,{handle:".handle",group:"configurator",onUpdate:async o=>{Ne(e.config.pairlists,o.oldIndex,o.newIndex)},onAdd:o=>{const m=a.value[o.oldIndex];e.addToConfig(m,o.newIndex),o.clone.replaceWith(o.item),o.clone.remove()}}),ve(async()=>{a.value=(await n.activeBot.getPairlists()).pairlists.sort((o,m)=>o.is_pairlist_generator===m.is_pairlist_generator?o.name.localeCompare(m.name):o.is_pairlist_generator?-1:1),e.selectOrCreateConfig(e.isSavedConfig(e.configName)?e.configName:"default")}),Z(()=>e.whitelist,()=>{i.value="Results"}),(o,m)=>{const y=Xe,C=H,z=Me,I=Te,N=ee,$=me,S=Le,B=Oe,w=Re,P=He;return d(),k("div",Ct,[c(I,{ref_key:"availablePairlistsEl",ref:g,class:"available-pairlists"},{default:p(()=>[(d(!0),k(j,null,D(t(a),v=>(d(),b(z,{key:v.name,class:F([{"no-drag":t(e).config.pairlists.length==0&&!v.is_pairlist_generator},"pairlist d-flex text-start align-items-center py-2 px-3"])},{default:p(()=>[_("div",Vt,[_("span",wt,O(v.name),1),_("span",kt,O(v.description),1)]),c(C,{class:"p-0 add-pairlist",style:{border:"none"},variant:"outline-light",disabled:t(e).config.pairlists.length==0&&!v.is_pairlist_generator,onClick:U=>t(e).addToConfig(v,t(e).config.pairlists.length)},{default:p(()=>[c(y,{class:"fs-4"})]),_:2},1032,["disabled","onClick"])]),_:2},1032,["class"]))),128))]),_:1},512),_("div",yt,[c(vt),_("div",$t,[_("div",St,[Bt,c(N,{modelValue:t(e).stakeCurrency,"onUpdate:modelValue":m[0]||(m[0]=v=>t(e).stakeCurrency=v),size:"sm"},null,8,["modelValue"])]),_("div",Pt,[c($,{modelValue:t(e).customExchange,"onUpdate:modelValue":m[1]||(m[1]=v=>t(e).customExchange=v),class:"mb-2"},{default:p(()=>[A(" Custom Exchange ")]),_:1},8,["modelValue"]),t(e).customExchange?(d(),b(ht,{key:0,modelValue:t(e).selectedExchange,"onUpdate:modelValue":m[2]||(m[2]=v=>t(e).selectedExchange=v)},null,8,["modelValue"])):V("",!0)])]),c(ct),c(S,{"model-value":t(e).config.pairlists.length>0&&!t(e).firstPairlistIsGenerator,variant:"warning"},{default:p(()=>[A(" First entry in the pairlist must be a Generating pairlist, like StaticPairList or VolumePairList. ")]),_:1},8,["model-value"]),_("div",{ref_key:"pairlistConfigsEl",ref:f,class:F(["d-flex flex-column flex-grow-1 position-relative border rounded-1 p-1",{empty:t(s)}])},[(d(!0),k(j,null,D(t(e).config.pairlists,(v,U)=>(d(),b(st,{key:v.id,modelValue:t(e).config.pairlists[U],"onUpdate:modelValue":G=>t(e).config.pairlists[U]=G,index:U,onRemove:t(e).removeFromConfig},null,8,["modelValue","onUpdate:modelValue","index","onRemove"]))),128))],2)]),_("div",Et,[c(w,{modelValue:t(i),"onUpdate:modelValue":m[3]||(m[3]=v=>K(i)?i.value=v:null),class:"mb-2",size:"sm",buttons:""},{default:p(()=>[c(B,{button:"",value:"Config"},{default:p(()=>[A(" Config")]),_:1}),c(B,{button:"",value:"Results",disabled:t(e).whitelist.length===0},{default:p(()=>[A(" Results")]),_:1},8,["disabled"])]),_:1},8,["modelValue"]),_("div",Ut,[t(i)==="Config"?(d(),b(P,{key:0,class:"position-lg-absolute w-100",content:t(e).configJSON,"is-valid":t(e).pairlistValid},null,8,["content","is-valid"])):V("",!0),t(i)==="Results"?(d(),b(P,{key:1,class:"position-lg-absolute w-100",content:t(e).whitelist},null,8,["content"])):V("",!0)])])])}}}),It=X(zt,[["__scopeId","data-v-29c41eaf"]]),Nt={};function Mt(u,n){const e=It;return d(),b(e,{class:"pt-4"})}const Ft=X(Nt,[["render",Mt]]);export{Ft as default};
//# sourceMappingURL=PairlistConfigView-6AIQouEF.js.map
