{"version": 3, "file": "TradingView-Bks13uR8.js", "sources": ["../../src/components/ftbot/PairLockList.vue", "../../src/components/ftbot/PeriodBreakdown.vue", "../../src/components/ftbot/BotPerformance.vue", "../../src/components/ftbot/BotProfit.vue", "../../src/components/ftbot/BotStatus.vue", "../../src/components/ftbot/BotControls.vue", "../../src/views/TradingView.vue"], "sourcesContent": ["<template>\n  <div>\n    <div class=\"mb-2\">\n      <label class=\"me-auto h3\">Pair Locks</label>\n      <b-button class=\"float-end\" size=\"sm\" @click=\"botStore.activeBot.getLocks\">\n        <i-mdi-refresh />\n      </b-button>\n    </div>\n    <div>\n      <b-table class=\"table-sm\" :items=\"botStore.activeBot.activeLocks\" :fields=\"tableFields\">\n        <template #cell(actions)=\"row\">\n          <b-button\n            class=\"btn-xs ms-1\"\n            size=\"sm\"\n            title=\"Delete trade\"\n            @click=\"removePairLock(row.item as unknown as Lock)\"\n          >\n            <i-mdi-delete />\n          </b-button>\n        </template>\n      </b-table>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { timestampms } from '@/shared/formatters';\nimport { Lock } from '@/types';\n\nimport { showAlert } from '@/shared/alerts';\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { TableField } from 'bootstrap-vue-next';\nconst botStore = useBotStore();\n\nconst tableFields: TableField[] = [\n  { key: 'pair', label: 'Pair' },\n  { key: 'lock_end_timestamp', label: 'Until', formatter: (value) => timestampms(value as number) },\n  { key: 'reason', label: 'Reason' },\n  { key: 'actions' },\n];\n\nconst removePairLock = (item: Lock) => {\n  console.log(item);\n  if (item.id !== undefined) {\n    botStore.activeBot.deleteLock(item.id);\n  } else {\n    showAlert('This Freqtrade version does not support deleting locks.');\n  }\n};\n</script>\n\n<style scoped></style>\n", "<template>\n  <div>\n    <div class=\"mb-2\">\n      <h3 class=\"me-auto d-inline\">{{ hasWeekly ? 'Period' : 'Daily' }} Breakdown</h3>\n      <b-button class=\"float-end\" size=\"sm\" @click=\"refreshSummary\">\n        <i-mdi-refresh />\n      </b-button>\n    </div>\n    <b-form-radio-group\n      v-if=\"hasWeekly\"\n      id=\"order-direction\"\n      v-model=\"periodicBreakdownPeriod\"\n      :options=\"periodicBreakdownSelections\"\n      name=\"radios-btn-default\"\n      size=\"sm\"\n      buttons\n      style=\"min-width: 10em\"\n      button-variant=\"outline-primary\"\n      @change=\"refreshSummary\"\n    ></b-form-radio-group>\n\n    <div class=\"ps-1\">\n      <TimePeriodChart\n        v-if=\"selectedStats\"\n        :daily-stats=\"selectedStatsSorted\"\n        :show-title=\"false\"\n      />\n    </div>\n    <div>\n      <b-table class=\"table-sm\" :items=\"selectedStats.data\" :fields=\"dailyFields\"> </b-table>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent, formatPrice } from '@/shared/formatters';\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { TableField } from 'bootstrap-vue-next';\n\nimport { TimeSummaryOptions } from '@/types';\n\nconst botStore = useBotStore();\n\nconst hasWeekly = computed(() => botStore.activeBot.botApiVersion >= 2.33);\n\nconst periodicBreakdownSelections = computed(() => {\n  const vals = [{ value: TimeSummaryOptions.daily, text: 'Days' }];\n  if (hasWeekly.value) {\n    vals.push({ value: TimeSummaryOptions.weekly, text: 'Weeks' });\n    vals.push({ value: TimeSummaryOptions.monthly, text: 'Months' });\n  }\n  return vals;\n});\nconst periodicBreakdownPeriod = ref<TimeSummaryOptions>(TimeSummaryOptions.daily);\n\nconst selectedStats = computed(() => {\n  switch (periodicBreakdownPeriod.value) {\n    case TimeSummaryOptions.weekly:\n      return botStore.activeBot.weeklyStats;\n    case TimeSummaryOptions.monthly:\n      return botStore.activeBot.monthlyStats;\n  }\n  return botStore.activeBot.dailyStats;\n});\n\nconst selectedStatsSorted = computed(() => {\n  // Sorted version for chart\n  return {\n    ...selectedStats.value,\n    data: selectedStats.value.data\n      ? Object.values(selectedStats.value.data).sort((a, b) => (a.date > b.date ? 1 : -1))\n      : [],\n  };\n});\n\nconst dailyFields = computed<TableField[]>(() => {\n  const res: TableField[] = [\n    { key: 'date', label: 'Day' },\n    {\n      key: 'abs_profit',\n      label: 'Profit',\n      formatter: (value: unknown) =>\n        formatPrice(value as number, botStore.activeBot.stakeCurrencyDecimals),\n    },\n    {\n      key: 'fiat_value',\n      label: `In ${botStore.activeBot.dailyStats.fiat_display_currency}`,\n      formatter: (value: unknown) => formatPrice(value as number, 2),\n    },\n    { key: 'trade_count', label: 'Trades' },\n  ];\n  if (botStore.activeBot.botApiVersion >= 2.16)\n    res.push({\n      key: 'rel_profit',\n      label: 'Profit%',\n      formatter: (value: unknown) => formatPercent(value as number, 2),\n    });\n  return res;\n});\n\nfunction refreshSummary() {\n  botStore.activeBot.getTimeSummary(periodicBreakdownPeriod.value);\n}\n\nonMounted(() => {\n  refreshSummary();\n});\n</script>\n", "<script setup lang=\"ts\">\nimport { formatPrice } from '@/shared/formatters';\n\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { TableField } from 'bootstrap-vue-next';\n\nconst botStore = useBotStore();\nenum PerformanceOptions {\n  performance = 'performance',\n  entryStats = 'entryStats',\n  exitStats = 'exitStats',\n  mixTagStats = 'mixTagStats',\n}\nconst selectedOption = ref<PerformanceOptions>(PerformanceOptions.performance);\n\nfunction formatTextLen(text: string, len: number) {\n  if (text.length > len) {\n    return text.substring(0, len) + '...';\n  }\n  return text;\n}\n\nconst performanceTable = computed<TableField[]>(() => {\n  const textLength = 17;\n  const initialCol = {\n    [PerformanceOptions.performance]: { key: 'pair', label: 'Pair' },\n    [PerformanceOptions.entryStats]: {\n      key: 'enter_tag',\n      label: 'Enter tag',\n      formatter: (v: unknown) => formatTextLen(v as string, textLength),\n    },\n    [PerformanceOptions.exitStats]: {\n      key: 'exit_reason',\n      label: 'Exit Reason',\n      formatter: (v: unknown) => formatTextLen(v as string, textLength),\n    },\n    [PerformanceOptions.mixTagStats]: {\n      key: 'mix_tag',\n      label: 'Mix Tag',\n      formatter: (v: unknown) => formatTextLen(v as string, textLength),\n    },\n  };\n  return [\n    initialCol[selectedOption.value],\n    { key: 'profit', label: 'Profit %' },\n    {\n      key: 'profit_abs',\n      label: `Profit ${botStore.activeBot.botState?.stake_currency}`,\n      formatter: (v: unknown) => formatPrice(v as number, 5),\n    },\n    { key: 'count', label: 'Count' },\n  ];\n});\n\nconst performanceData = computed(() => {\n  if (selectedOption.value === PerformanceOptions.performance) {\n    return botStore.activeBot.performanceStats;\n  }\n  if (selectedOption.value === PerformanceOptions.entryStats) {\n    return botStore.activeBot.entryStats;\n  }\n  if (selectedOption.value === PerformanceOptions.exitStats) {\n    return botStore.activeBot.exitStats;\n  }\n  if (selectedOption.value === PerformanceOptions.mixTagStats) {\n    return botStore.activeBot.mixTagStats;\n  }\n  return [];\n});\n\nconst hasAdvancedStats = computed(() => botStore.activeBot.botApiVersion >= 2.34);\n\nconst options = [\n  { value: PerformanceOptions.performance, text: 'Performance' },\n  { value: PerformanceOptions.entryStats, text: 'Entries' },\n  { value: PerformanceOptions.exitStats, text: 'Exits' },\n  { value: PerformanceOptions.mixTagStats, text: 'Mix Tag' },\n];\n\nfunction refreshSummary() {\n  if (selectedOption.value === PerformanceOptions.performance) {\n    botStore.activeBot.getPerformance();\n  }\n  if (selectedOption.value === PerformanceOptions.entryStats) {\n    botStore.activeBot.getEntryStats();\n  }\n  if (selectedOption.value === PerformanceOptions.exitStats) {\n    botStore.activeBot.getExitStats();\n  }\n  if (selectedOption.value === PerformanceOptions.mixTagStats) {\n    botStore.activeBot.getMixTagStats();\n  }\n}\n\nonMounted(() => {\n  refreshSummary();\n});\n</script>\n<template>\n  <div>\n    <div class=\"mb-2\">\n      <h3 class=\"me-auto d-inline\">Performance</h3>\n      <b-button class=\"float-end\" size=\"sm\" @click=\"refreshSummary\">\n        <i-mdi-refresh />\n      </b-button>\n    </div>\n    <b-form-radio-group\n      v-if=\"hasAdvancedStats\"\n      id=\"order-direction\"\n      v-model=\"selectedOption\"\n      :options=\"options\"\n      name=\"radios-btn-default\"\n      size=\"sm\"\n      buttons\n      style=\"min-width: 10em\"\n      button-variant=\"outline-primary\"\n      @change=\"refreshSummary\"\n    ></b-form-radio-group>\n    <b-table class=\"table-sm\" :items=\"performanceData\" :fields=\"performanceTable\"></b-table>\n  </div>\n</template>\n", "<template>\n  <b-table class=\"text-start\" small borderless :items=\"profitItems\" :fields=\"profitFields\">\n    <template #cell(value)=\"row\">\n      <DateTimeTZ v-if=\"row.item.isTs && row.value\" :date=\"row.value as number\"></DateTimeTZ>\n      <template v-else>{{ row.value }}</template>\n    </template>\n  </b-table>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent, formatPriceCurrency, timestampms } from '@/shared/formatters';\n\nimport { ProfitInterface } from '@/types';\nimport { TableField, TableItem } from 'bootstrap-vue-next';\n\nconst props = defineProps({\n  profit: { required: true, type: Object as () => ProfitInterface },\n  stakeCurrency: { required: true, type: String },\n  stakeCurrencyDecimals: { required: true, type: Number },\n});\n\nconst profitFields: TableField[] = [\n  { key: 'metric', label: 'Metric' },\n  { key: 'value', label: 'Value' },\n];\n\nconst profitItems = computed<TableItem[]>(() => {\n  if (!props.profit) return [];\n  return [\n    {\n      metric: 'ROI closed trades',\n      value: props.profit.profit_closed_coin\n        ? `${formatPriceCurrency(\n            props.profit.profit_closed_coin,\n            props.stakeCurrency,\n            props.stakeCurrencyDecimals,\n          )} (${formatPercent(props.profit.profit_closed_ratio_mean, 2)})`\n        : 'N/A',\n      // (&sum; ${formatPercent(props.profit.profit_closed_ratio_sum,  2,)})`\n    },\n    {\n      metric: 'ROI all trades',\n      value: props.profit.profit_all_coin\n        ? `${formatPriceCurrency(\n            props.profit.profit_all_coin,\n            props.stakeCurrency,\n            props.stakeCurrencyDecimals,\n          )} (${formatPercent(props.profit.profit_all_ratio_mean, 2)})`\n        : 'N/A',\n      //  (&sum; ${formatPercent(props.profit.profit_all_ratio_sum,2,)})`\n    },\n\n    {\n      metric: 'Total Trade count',\n      value: `${props.profit.trade_count ?? 0}`,\n    },\n    {\n      metric: 'Bot started',\n      value: props.profit.bot_start_timestamp,\n      isTs: true,\n    },\n    {\n      metric: 'First Trade opened',\n      value: props.profit.first_trade_timestamp,\n      isTs: true,\n    },\n    {\n      metric: 'Latest Trade opened',\n      value: props.profit.latest_trade_timestamp,\n      isTs: true,\n    },\n    {\n      metric: 'Win / Loss',\n      value: `${props.profit.winning_trades ?? 0} / ${props.profit.losing_trades ?? 0}`,\n    },\n    {\n      metric: 'Winrate',\n      value: `${props.profit.winrate ? formatPercent(props.profit.winrate) : 'N/A'}`,\n    },\n    {\n      metric: 'Expectancy (ratio)',\n      value: `${props.profit.expectancy ? props.profit.expectancy.toFixed(2) : 'N/A'} (${\n        props.profit.expectancy_ratio ? props.profit.expectancy_ratio.toFixed(2) : 'N/A'\n      })`,\n    },\n    {\n      metric: 'Avg. Duration',\n      value: `${props.profit.avg_duration ?? 'N/A'}`,\n    },\n    {\n      metric: 'Best performing',\n      value: props.profit.best_pair\n        ? `${props.profit.best_pair}: ${formatPercent(props.profit.best_pair_profit_ratio, 2)}`\n        : 'N/A',\n    },\n    {\n      metric: 'Trading volume',\n      value: `${formatPriceCurrency(\n        props.profit.trading_volume ?? 0,\n        props.stakeCurrency,\n        props.stakeCurrencyDecimals,\n      )}`,\n    },\n    {\n      metric: 'Profit factor',\n      value: `${props.profit.profit_factor ? props.profit.profit_factor.toFixed(2) : 'N/A'}`,\n    },\n    {\n      metric: 'Max Drawdown',\n      value: `${props.profit.max_drawdown ? formatPercent(props.profit.max_drawdown, 2) : 'N/A'} (${\n        props.profit.max_drawdown_abs\n          ? formatPriceCurrency(\n              props.profit.max_drawdown_abs,\n              props.stakeCurrency,\n              props.stakeCurrencyDecimals,\n            )\n          : 'N/A'\n      }) ${\n        props.profit.max_drawdown_start_timestamp && props.profit.max_drawdown_end_timestamp\n          ? 'from ' +\n            timestampms(props.profit.max_drawdown_start_timestamp) +\n            ' to ' +\n            timestampms(props.profit.max_drawdown_end_timestamp)\n          : ''\n      }`,\n    },\n  ];\n});\n</script>\n", "<template>\n  <div v-if=\"botStore.activeBot.botState\">\n    <p>\n      Running Freqtrade <strong>{{ botStore.activeBot.version }}</strong>\n    </p>\n    <p>\n      Running with\n      <strong>\n        {{ botStore.activeBot.botState.max_open_trades }}x{{\n          botStore.activeBot.botState.stake_amount\n        }}\n        {{ botStore.activeBot.botState.stake_currency }}\n      </strong>\n      on\n      <strong>{{ botStore.activeBot.botState.exchange }}</strong> in\n      <strong>{{ botStore.activeBot.botState.trading_mode || 'spot' }}</strong> markets, with\n      Strategy <strong>{{ botStore.activeBot.botState.strategy }}</strong\n      >.\n    </p>\n    <p v-if=\"'stoploss_on_exchange' in botStore.activeBot.botState\">\n      Stoploss on exchange is\n      <strong>{{\n        botStore.activeBot.botState.stoploss_on_exchange ? 'enabled' : 'disabled'\n      }}</strong\n      >.\n    </p>\n    <p>\n      Currently <strong>{{ botStore.activeBot.botState.state }}</strong\n      >,\n      <strong>force entry: {{ botStore.activeBot.botState.force_entry_enable }}</strong>\n    </p>\n    <p>\n      <strong>{{ botStore.activeBot.botState.dry_run ? 'Dry-Run' : 'Live' }}</strong>\n    </p>\n    <hr />\n    <p>\n      Avg Profit {{ formatPercent(botStore.activeBot.profit.profit_all_ratio_mean) }} (&sum;\n      {{ formatPercent(botStore.activeBot.profit.profit_all_ratio_sum) }}) in\n      {{ botStore.activeBot.profit.trade_count }} Trades, with an average duration of\n      {{ botStore.activeBot.profit.avg_duration }}. Best pair:\n      {{ botStore.activeBot.profit.best_pair }}.\n    </p>\n    <p v-if=\"botStore.activeBot.profit.first_trade_timestamp\">\n      <span v-if=\"botStore.activeBot.profit.bot_start_timestamp\" class=\"d-block\">\n        Bot start date:\n        <strong>\n          <DateTimeTZ :date=\"botStore.activeBot.profit.bot_start_timestamp\" show-timezone />\n        </strong>\n      </span>\n      <span class=\"d-block\">\n        First trade opened:\n        <strong>\n          <DateTimeTZ :date=\"botStore.activeBot.profit.first_trade_timestamp\" show-timezone />\n        </strong>\n      </span>\n      <span class=\"d-block\">\n        Last trade opened:\n        <strong>\n          <DateTimeTZ :date=\"botStore.activeBot.profit.latest_trade_timestamp\" show-timezone />\n        </strong>\n      </span>\n    </p>\n    <p>\n      <span v-if=\"botStore.activeBot.profit.profit_factor\" class=\"d-block\">\n        Profit factor:\n        {{ botStore.activeBot.profit.profit_factor.toFixed(2) }}\n      </span>\n      <span v-if=\"botStore.activeBot.profit.trading_volume\" class=\"d-block\">\n        Trading volume:\n        {{\n          formatPriceCurrency(\n            botStore.activeBot.profit.trading_volume,\n            botStore.activeBot.botState.stake_currency,\n            botStore.activeBot.botState.stake_currency_decimals ?? 3,\n          )\n        }}\n      </span>\n    </p>\n    <BotProfit\n      class=\"mx-1\"\n      :profit=\"botStore.activeBot.profit\"\n      :stake-currency=\"botStore.activeBot.botState.stake_currency ?? 'USDT'\"\n      :stake-currency-decimals=\"botStore.activeBot.botState.stake_currency_decimals ?? 3\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent, formatPriceCurrency } from '@/shared/formatters';\n\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst botStore = useBotStore();\n</script>\n", "forceexit\n<template>\n  <div>\n    <button\n      class=\"btn btn-secondary btn-sm ms-1\"\n      :disabled=\"!botStore.activeBot.isTrading || isRunning\"\n      title=\"Start Trading\"\n      @click=\"botStore.activeBot.startBot()\"\n    >\n      <i-mdi-play height=\"24\" width=\"24\" />\n    </button>\n    <button\n      class=\"btn btn-secondary btn-sm ms-1\"\n      :disabled=\"!botStore.activeBot.isTrading || !isRunning\"\n      title=\"Stop Trading - Also stops handling open trades.\"\n      @click=\"handleStopBot()\"\n    >\n      <i-mdi-stop height=\"24\" width=\"24\" />\n    </button>\n    <button\n      class=\"btn btn-secondary btn-sm ms-1\"\n      :disabled=\"!botStore.activeBot.isTrading || !isRunning\"\n      title=\"StopBuy - Stops buying, but still handles open trades\"\n      @click=\"handleStopBuy()\"\n    >\n      <i-mdi-pause height=\"24\" width=\"24\" />\n    </button>\n    <button\n      class=\"btn btn-secondary btn-sm ms-1\"\n      :disabled=\"!botStore.activeBot.isTrading\"\n      title=\"Reload Config - reloads configuration including strategy, resetting all settings changed on the fly.\"\n      @click=\"handleReloadConfig()\"\n    >\n      <i-mdi-reload height=\"24\" width=\"24\" />\n    </button>\n    <button\n      class=\"btn btn-secondary btn-sm ms-1\"\n      :disabled=\"!botStore.activeBot.isTrading\"\n      title=\"Force exit all\"\n      @click=\"handleForceExit()\"\n    >\n      <i-mdi-close-box-multiple height=\"24\" width=\"24\" />\n    </button>\n    <button\n      v-if=\"botStore.activeBot.botState && botStore.activeBot.botState.force_entry_enable\"\n      class=\"btn btn-secondary btn-sm ms-1\"\n      :disabled=\"!botStore.activeBot.isTrading || !isRunning\"\n      title=\"Force enter - Immediately enter a trade at an optional price. Exits are then handled according to strategy rules.\"\n      @click=\"forceEnter = true\"\n    >\n      <i-mdi-plus-box-multiple-outline style=\"font-size: 20px\" />\n    </button>\n    <button\n      v-if=\"botStore.activeBot.isWebserverMode && false\"\n      :disabled=\"botStore.activeBot.isTrading\"\n      class=\"btn btn-secondary btn-sm ms-1\"\n      title=\"Start Trading mode\"\n      @click=\"botStore.activeBot.startTrade()\"\n    >\n      <i-mdi-play class=\"fs-4\" />\n    </button>\n    <ForceEntryForm v-model=\"forceEnter\" :pair=\"botStore.activeBot.selectedPair\" />\n    <MessageBox ref=\"msgBox\" />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport MessageBox, { MsgBoxObject } from '@/components/general/MessageBox.vue';\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { ForceSellPayload } from '@/types';\n\nimport ForceEntryForm from './ForceEntryForm.vue';\n\nconst botStore = useBotStore();\nconst forceEnter = ref<boolean>(false);\nconst msgBox = ref<typeof MessageBox>();\n\nconst isRunning = computed((): boolean => {\n  return botStore.activeBot.botState?.state === 'running';\n});\n\nconst handleStopBot = () => {\n  const msg: MsgBoxObject = {\n    title: 'Stop Bot',\n    message: 'Stop the bot loop from running?',\n    accept: () => {\n      botStore.activeBot.stopBot();\n    },\n  };\n  msgBox.value?.show(msg);\n};\n\nconst handleStopBuy = () => {\n  const msg: MsgBoxObject = {\n    title: 'Stop Buying',\n    message: 'Freqtrade will continue to handle open trades.',\n    accept: () => {\n      botStore.activeBot.stopBuy();\n    },\n  };\n  msgBox.value?.show(msg);\n};\n\nconst handleReloadConfig = () => {\n  const msg: MsgBoxObject = {\n    title: 'Reload',\n    message: 'Reload configuration (including strategy)?',\n    accept: () => {\n      console.log('reload...');\n      botStore.activeBot.reloadConfig();\n    },\n  };\n  msgBox.value?.show(msg);\n};\n\nconst handleForceExit = () => {\n  const msg: MsgBoxObject = {\n    title: 'ForceExit all',\n    message: 'Really forceexit ALL trades?',\n    accept: () => {\n      const payload: ForceSellPayload = {\n        tradeid: 'all',\n        // TODO: support ordertype (?)\n      };\n      botStore.activeBot.forceexit(payload);\n    },\n  };\n  msgBox.value?.show(msg);\n};\n</script>\n", "<template>\n  <grid-layout\n    class=\"h-100 w-100\"\n    :row-height=\"50\"\n    :layout=\"gridLayoutData\"\n    :vertical-compact=\"false\"\n    :margin=\"[5, 5]\"\n    :responsive-layouts=\"responsiveGridLayouts\"\n    :is-resizable=\"!isLayoutLocked\"\n    :is-draggable=\"!isLayoutLocked\"\n    :responsive=\"true\"\n    :cols=\"{ lg: 12, md: 12, sm: 12, xs: 4, xxs: 2 }\"\n    :col-num=\"12\"\n    @update:breakpoint=\"breakpointChanged\"\n  >\n    <template #default=\"{ gridItemProps }\">\n      <grid-item\n        v-if=\"gridLayoutMultiPane.h != 0\"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutMultiPane.i\"\n        :x=\"gridLayoutMultiPane.x\"\n        :y=\"gridLayoutMultiPane.y\"\n        :w=\"gridLayoutMultiPane.w\"\n        :h=\"gridLayoutMultiPane.h\"\n        drag-allow-from=\".card-header\"\n      >\n        <DraggableContainer header=\"Multi Pane\">\n          <div class=\"mt-1 d-flex justify-content-center\">\n            <BotControls class=\"mt-1 mb-2\" />\n          </div>\n          <b-tabs content-class=\"mt-3\" class=\"mt-1\">\n            <b-tab title=\"Pairs combined\" active>\n              <PairSummary\n                :pairlist=\"botStore.activeBot.whitelist\"\n                :current-locks=\"botStore.activeBot.activeLocks\"\n                :trades=\"botStore.activeBot.openTrades\"\n              />\n            </b-tab>\n            <b-tab title=\"General\">\n              <BotStatus />\n            </b-tab>\n            <b-tab title=\"Performance\" lazy>\n              <BotPerformance />\n            </b-tab>\n            <b-tab title=\"Balance\" lazy>\n              <BotBalance />\n            </b-tab>\n            <b-tab title=\"Time Breakdown\" lazy>\n              <PeriodBreakdown />\n            </b-tab>\n\n            <b-tab title=\"Pairlist\" lazy>\n              <PairListLive />\n            </b-tab>\n            <b-tab title=\"Pair Locks\" lazy>\n              <PairLockList />\n            </b-tab>\n          </b-tabs>\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-if=\"gridLayoutOpenTrades.h != 0\"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutOpenTrades.i\"\n        :x=\"gridLayoutOpenTrades.x\"\n        :y=\"gridLayoutOpenTrades.y\"\n        :w=\"gridLayoutOpenTrades.w\"\n        :h=\"gridLayoutOpenTrades.h\"\n        drag-allow-from=\".card-header\"\n      >\n        <DraggableContainer header=\"Open Trades\">\n          <TradeList\n            class=\"open-trades\"\n            :trades=\"botStore.activeBot.openTrades\"\n            title=\"Open trades\"\n            :active-trades=\"true\"\n            empty-text=\"Currently no open trades.\"\n          />\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-if=\"gridLayoutTradeHistory.h != 0\"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutTradeHistory.i\"\n        :x=\"gridLayoutTradeHistory.x\"\n        :y=\"gridLayoutTradeHistory.y\"\n        :w=\"gridLayoutTradeHistory.w\"\n        :h=\"gridLayoutTradeHistory.h\"\n        drag-allow-from=\".card-header\"\n      >\n        <DraggableContainer header=\"Closed Trades\">\n          <trade-list\n            class=\"trade-history\"\n            :trades=\"botStore.activeBot.closedTrades\"\n            title=\"Trade history\"\n            :show-filter=\"true\"\n            empty-text=\"No closed trades so far.\"\n          />\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-if=\"\n          botStore.activeBot.detailTradeId &&\n          botStore.activeBot.tradeDetail &&\n          gridLayoutTradeDetail.h != 0\n        \"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutTradeDetail.i\"\n        :x=\"gridLayoutTradeDetail.x\"\n        :y=\"gridLayoutTradeDetail.y\"\n        :w=\"gridLayoutTradeDetail.w\"\n        :h=\"gridLayoutTradeDetail.h\"\n        :min-h=\"4\"\n        drag-allow-from=\".card-header\"\n      >\n        <DraggableContainer header=\"Trade Detail\">\n          <TradeDetail\n            :trade=\"botStore.activeBot.tradeDetail\"\n            :stake-currency=\"botStore.activeBot.stakeCurrency\"\n          />\n        </DraggableContainer>\n      </grid-item>\n      <grid-item\n        v-if=\"gridLayoutTradeDetail.h != 0\"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutChartView.i\"\n        :x=\"gridLayoutChartView.x\"\n        :y=\"gridLayoutChartView.y\"\n        :w=\"gridLayoutChartView.w\"\n        :h=\"gridLayoutChartView.h\"\n        :min-h=\"6\"\n        drag-allow-from=\".card-header\"\n      >\n        <DraggableContainer header=\"Chart\">\n          <CandleChartContainer\n            :available-pairs=\"botStore.activeBot.whitelist\"\n            :historic-view=\"!!false\"\n            :timeframe=\"botStore.activeBot.timeframe\"\n            :trades=\"botStore.activeBot.allTrades\"\n          >\n          </CandleChartContainer>\n        </DraggableContainer>\n      </grid-item>\n    </template>\n  </grid-layout>\n</template>\n\n<script setup lang=\"ts\">\nimport { GridItemData } from '@/types';\n\nimport { useLayoutStore, findGridLayout, TradeLayout } from '@/stores/layout';\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst botStore = useBotStore();\nconst layoutStore = useLayoutStore();\nconst currentBreakpoint = ref('');\n\nconst breakpointChanged = (newBreakpoint: string) => {\n  // console.log('breakpoint:', newBreakpoint);\n  currentBreakpoint.value = newBreakpoint;\n};\nconst isResizableLayout = computed(() =>\n  ['', 'sm', 'md', 'lg', 'xl'].includes(currentBreakpoint.value),\n);\nconst isLayoutLocked = computed(() => {\n  return layoutStore.layoutLocked || !isResizableLayout.value;\n});\nconst gridLayoutData = computed((): GridItemData[] => {\n  if (isResizableLayout.value) {\n    return layoutStore.tradingLayout;\n  }\n  return [...layoutStore.getTradingLayoutSm];\n});\n\nconst gridLayoutMultiPane = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.multiPane);\n});\n\nconst gridLayoutOpenTrades = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.openTrades);\n});\n\nconst gridLayoutTradeHistory = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.tradeHistory);\n});\n\nconst gridLayoutTradeDetail = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.tradeDetail);\n});\n\nconst gridLayoutChartView = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.chartView);\n});\n\nconst responsiveGridLayouts = computed(() => {\n  return {\n    sm: layoutStore.getTradingLayoutSm,\n  };\n});\n</script>\n\n<style scoped></style>\n"], "names": ["botStore", "useBotStore", "tableFields", "value", "timestampms", "removePairLock", "item", "show<PERSON><PERSON><PERSON>", "hasWeekly", "computed", "periodicBreakdownSelections", "vals", "TimeSummaryOptions", "periodicBreakdownPeriod", "ref", "selectedStats", "selectedStatsSorted", "a", "b", "dailyFields", "res", "formatPrice", "formatPercent", "refreshSummary", "onMounted", "selectedOption", "formatTextLen", "text", "len", "performanceTable", "v", "_a", "performanceData", "hasAdvancedStats", "options", "props", "__props", "profitFields", "profitItems", "formatPriceCurrency", "forceEnter", "msgBox", "isRunning", "handleStopBot", "msg", "handleStopBuy", "handleReloadConfig", "handleForceExit", "payload", "layoutStore", "useLayoutStore", "currentBreakpoint", "breakpointChanged", "newBreakpoint", "isResizableLayout", "isLayoutLocked", "gridLayoutData", "gridLayoutMultiPane", "findGridLayout", "TradeLayout", "gridLayoutOpenTrades", "gridLayoutTradeHistory", "gridLayoutTradeDetail", "gridLayoutChartView", "responsiveGridLayouts"], "mappings": "ynCAgCA,MAAMA,EAAWC,IAEXC,EAA4B,CAChC,CAAE,IAAK,OAAQ,MAAO,MAAO,EAC7B,CAAE,IAAK,qBAAsB,MAAO,QAAS,UAAYC,GAAUC,EAAYD,CAAe,CAAE,EAChG,CAAE,IAAK,SAAU,MAAO,QAAS,EACjC,CAAE,IAAK,SAAU,CAAA,EAGbE,EAAkBC,GAAe,CACrC,QAAQ,IAAIA,CAAI,EACZA,EAAK,KAAO,OACLN,EAAA,UAAU,WAAWM,EAAK,EAAE,EAErCC,GAAU,yDAAyD,CACrE,siBCNF,MAAMP,EAAWC,IAEXO,EAAYC,EAAS,IAAMT,EAAS,UAAU,eAAiB,IAAI,EAEnEU,EAA8BD,EAAS,IAAM,CAC3C,MAAAE,EAAO,CAAC,CAAE,MAAOC,EAAmB,MAAO,KAAM,OAAQ,EAC/D,OAAIJ,EAAU,QACZG,EAAK,KAAK,CAAE,MAAOC,EAAmB,OAAQ,KAAM,QAAS,EAC7DD,EAAK,KAAK,CAAE,MAAOC,EAAmB,QAAS,KAAM,SAAU,GAE1DD,CAAA,CACR,EACKE,EAA0BC,EAAwBF,EAAmB,KAAK,EAE1EG,EAAgBN,EAAS,IAAM,CACnC,OAAQI,EAAwB,MAAO,CACrC,KAAKD,EAAmB,OACtB,OAAOZ,EAAS,UAAU,YAC5B,KAAKY,EAAmB,QACtB,OAAOZ,EAAS,UAAU,YAC9B,CACA,OAAOA,EAAS,UAAU,UAAA,CAC3B,EAEKgB,EAAsBP,EAAS,KAE5B,CACL,GAAGM,EAAc,MACjB,KAAMA,EAAc,MAAM,KACtB,OAAO,OAAOA,EAAc,MAAM,IAAI,EAAE,KAAK,CAACE,EAAGC,IAAOD,EAAE,KAAOC,EAAE,KAAO,EAAI,EAAG,EACjF,CAAC,CAAA,EAER,EAEKC,EAAcV,EAAuB,IAAM,CAC/C,MAAMW,EAAoB,CACxB,CAAE,IAAK,OAAQ,MAAO,KAAM,EAC5B,CACE,IAAK,aACL,MAAO,SACP,UAAYjB,GACVkB,EAAYlB,EAAiBH,EAAS,UAAU,qBAAqB,CACzE,EACA,CACE,IAAK,aACL,MAAO,MAAMA,EAAS,UAAU,WAAW,qBAAqB,GAChE,UAAYG,GAAmBkB,EAAYlB,EAAiB,CAAC,CAC/D,EACA,CAAE,IAAK,cAAe,MAAO,QAAS,CAAA,EAEpC,OAAAH,EAAS,UAAU,eAAiB,MACtCoB,EAAI,KAAK,CACP,IAAK,aACL,MAAO,UACP,UAAYjB,GAAmBmB,EAAcnB,EAAiB,CAAC,CAAA,CAChE,EACIiB,CAAA,CACR,EAED,SAASG,GAAiB,CACfvB,EAAA,UAAU,eAAea,EAAwB,KAAK,CACjE,CAEA,OAAAW,EAAU,IAAM,CACCD,GAAA,CAChB,0yBCpGD,MAAMvB,EAAWC,IAOXwB,EAAiBX,EAAwB,eAEtC,SAAAY,EAAcC,EAAcC,EAAa,CAC5C,OAAAD,EAAK,OAASC,EACTD,EAAK,UAAU,EAAGC,CAAG,EAAI,MAE3BD,CACT,CAEM,MAAAE,EAAmBpB,EAAuB,IAAM,OAoB7C,MAAA,CAlBY,CAChB,YAAiC,CAAE,IAAK,OAAQ,MAAO,MAAO,EAC9D,WAAgC,CAC/B,IAAK,YACL,MAAO,YACP,UAAYqB,GAAeJ,EAAcI,EAAa,EAAU,CAClE,EACC,UAA+B,CAC9B,IAAK,cACL,MAAO,cACP,UAAYA,GAAeJ,EAAcI,EAAa,EAAU,CAClE,EACC,YAAiC,CAChC,IAAK,UACL,MAAO,UACP,UAAYA,GAAeJ,EAAcI,EAAa,EAAU,CAClE,CAAA,EAGWL,EAAe,KAAK,EAC/B,CAAE,IAAK,SAAU,MAAO,UAAW,EACnC,CACE,IAAK,aACL,MAAO,WAAUM,EAAA/B,EAAS,UAAU,WAAnB,YAAA+B,EAA6B,cAAc,GAC5D,UAAYD,GAAeT,EAAYS,EAAa,CAAC,CACvD,EACA,CAAE,IAAK,QAAS,MAAO,OAAQ,CAAA,CACjC,CACD,EAEKE,EAAkBvB,EAAS,IAC3BgB,EAAe,QAAU,cACpBzB,EAAS,UAAU,iBAExByB,EAAe,QAAU,aACpBzB,EAAS,UAAU,WAExByB,EAAe,QAAU,YACpBzB,EAAS,UAAU,UAExByB,EAAe,QAAU,cACpBzB,EAAS,UAAU,YAErB,EACR,EAEKiC,EAAmBxB,EAAS,IAAMT,EAAS,UAAU,eAAiB,IAAI,EAE1EkC,EAAU,CACd,CAAE,MAAO,cAAgC,KAAM,aAAc,EAC7D,CAAE,MAAO,aAA+B,KAAM,SAAU,EACxD,CAAE,MAAO,YAA8B,KAAM,OAAQ,EACrD,CAAE,MAAO,cAAgC,KAAM,SAAU,CAAA,EAG3D,SAASX,GAAiB,CACpBE,EAAe,QAAU,eAC3BzB,EAAS,UAAU,iBAEjByB,EAAe,QAAU,cAC3BzB,EAAS,UAAU,gBAEjByB,EAAe,QAAU,aAC3BzB,EAAS,UAAU,eAEjByB,EAAe,QAAU,eAC3BzB,EAAS,UAAU,gBAEvB,CAEA,OAAAwB,EAAU,IAAM,CACCD,GAAA,CAChB,6pBCjFD,MAAMY,EAAQC,EAMRC,EAA6B,CACjC,CAAE,IAAK,SAAU,MAAO,QAAS,EACjC,CAAE,IAAK,QAAS,MAAO,OAAQ,CAAA,EAG3BC,EAAc7B,EAAsB,IACnC0B,EAAM,OACJ,CACL,CACE,OAAQ,oBACR,MAAOA,EAAM,OAAO,mBAChB,GAAGI,EACDJ,EAAM,OAAO,mBACbA,EAAM,cACNA,EAAM,qBAAA,CACP,KAAKb,EAAca,EAAM,OAAO,yBAA0B,CAAC,CAAC,IAC7D,KAEN,EACA,CACE,OAAQ,iBACR,MAAOA,EAAM,OAAO,gBAChB,GAAGI,EACDJ,EAAM,OAAO,gBACbA,EAAM,cACNA,EAAM,qBAAA,CACP,KAAKb,EAAca,EAAM,OAAO,sBAAuB,CAAC,CAAC,IAC1D,KAEN,EAEA,CACE,OAAQ,oBACR,MAAO,GAAGA,EAAM,OAAO,aAAe,CAAC,EACzC,EACA,CACE,OAAQ,cACR,MAAOA,EAAM,OAAO,oBACpB,KAAM,EACR,EACA,CACE,OAAQ,qBACR,MAAOA,EAAM,OAAO,sBACpB,KAAM,EACR,EACA,CACE,OAAQ,sBACR,MAAOA,EAAM,OAAO,uBACpB,KAAM,EACR,EACA,CACE,OAAQ,aACR,MAAO,GAAGA,EAAM,OAAO,gBAAkB,CAAC,MAAMA,EAAM,OAAO,eAAiB,CAAC,EACjF,EACA,CACE,OAAQ,UACR,MAAO,GAAGA,EAAM,OAAO,QAAUb,EAAca,EAAM,OAAO,OAAO,EAAI,KAAK,EAC9E,EACA,CACE,OAAQ,qBACR,MAAO,GAAGA,EAAM,OAAO,WAAaA,EAAM,OAAO,WAAW,QAAQ,CAAC,EAAI,KAAK,KAC5EA,EAAM,OAAO,iBAAmBA,EAAM,OAAO,iBAAiB,QAAQ,CAAC,EAAI,KAC7E,GACF,EACA,CACE,OAAQ,gBACR,MAAO,GAAGA,EAAM,OAAO,cAAgB,KAAK,EAC9C,EACA,CACE,OAAQ,kBACR,MAAOA,EAAM,OAAO,UAChB,GAAGA,EAAM,OAAO,SAAS,KAAKb,EAAca,EAAM,OAAO,uBAAwB,CAAC,CAAC,GACnF,KACN,EACA,CACE,OAAQ,iBACR,MAAO,GAAGI,EACRJ,EAAM,OAAO,gBAAkB,EAC/BA,EAAM,cACNA,EAAM,qBAAA,CACP,EACH,EACA,CACE,OAAQ,gBACR,MAAO,GAAGA,EAAM,OAAO,cAAgBA,EAAM,OAAO,cAAc,QAAQ,CAAC,EAAI,KAAK,EACtF,EACA,CACE,OAAQ,eACR,MAAO,GAAGA,EAAM,OAAO,aAAeb,EAAca,EAAM,OAAO,aAAc,CAAC,EAAI,KAAK,KACvFA,EAAM,OAAO,iBACTI,EACEJ,EAAM,OAAO,iBACbA,EAAM,cACNA,EAAM,qBAAA,EAER,KACN,KACEA,EAAM,OAAO,8BAAgCA,EAAM,OAAO,2BACtD,QACA/B,EAAY+B,EAAM,OAAO,4BAA4B,EACrD,OACA/B,EAAY+B,EAAM,OAAO,0BAA0B,EACnD,EACN,EACF,CAAA,EAlGwB,EAoG3B,8dCnCD,MAAMnC,EAAWC,+nHCnBjB,MAAMD,EAAWC,IACXuC,EAAa1B,EAAa,EAAK,EAC/B2B,EAAS3B,IAET4B,EAAYjC,EAAS,IAAe,OACjC,QAAAsB,EAAA/B,EAAS,UAAU,WAAnB,YAAA+B,EAA6B,SAAU,SAAA,CAC/C,EAEKY,EAAgB,IAAM,OAC1B,MAAMC,EAAoB,CACxB,MAAO,WACP,QAAS,kCACT,OAAQ,IAAM,CACZ5C,EAAS,UAAU,SACrB,CAAA,GAEK+B,EAAAU,EAAA,QAAA,MAAAV,EAAO,KAAKa,EAAG,EAGlBC,EAAgB,IAAM,OAC1B,MAAMD,EAAoB,CACxB,MAAO,cACP,QAAS,iDACT,OAAQ,IAAM,CACZ5C,EAAS,UAAU,SACrB,CAAA,GAEK+B,EAAAU,EAAA,QAAA,MAAAV,EAAO,KAAKa,EAAG,EAGlBE,EAAqB,IAAM,OAC/B,MAAMF,EAAoB,CACxB,MAAO,SACP,QAAS,6CACT,OAAQ,IAAM,CACZ,QAAQ,IAAI,WAAW,EACvB5C,EAAS,UAAU,cACrB,CAAA,GAEK+B,EAAAU,EAAA,QAAA,MAAAV,EAAO,KAAKa,EAAG,EAGlBG,EAAkB,IAAM,OAC5B,MAAMH,EAAoB,CACxB,MAAO,gBACP,QAAS,+BACT,OAAQ,IAAM,CACZ,MAAMI,EAA4B,CAChC,QAAS,KAAA,EAGFhD,EAAA,UAAU,UAAUgD,CAAO,CACtC,CAAA,GAEKjB,EAAAU,EAAA,QAAA,MAAAV,EAAO,KAAKa,EAAG,wzDC0BxB,MAAM5C,EAAWC,IACXgD,EAAcC,KACdC,EAAoBrC,EAAI,EAAE,EAE1BsC,EAAqBC,GAA0B,CAEnDF,EAAkB,MAAQE,CAAA,EAEtBC,EAAoB7C,EAAS,IACjC,CAAC,GAAI,KAAM,KAAM,KAAM,IAAI,EAAE,SAAS0C,EAAkB,KAAK,CAAA,EAEzDI,EAAiB9C,EAAS,IACvBwC,EAAY,cAAgB,CAACK,EAAkB,KACvD,EACKE,EAAiB/C,EAAS,IAC1B6C,EAAkB,MACbL,EAAY,cAEd,CAAC,GAAGA,EAAY,kBAAkB,CAC1C,EAEKQ,EAAsBhD,EAAS,IAC5BiD,EAAeF,EAAe,MAAOG,EAAY,SAAS,CAClE,EAEKC,EAAuBnD,EAAS,IAC7BiD,EAAeF,EAAe,MAAOG,EAAY,UAAU,CACnE,EAEKE,EAAyBpD,EAAS,IAC/BiD,EAAeF,EAAe,MAAOG,EAAY,YAAY,CACrE,EAEKG,EAAwBrD,EAAS,IAC9BiD,EAAeF,EAAe,MAAOG,EAAY,WAAW,CACpE,EAEKI,EAAsBtD,EAAS,IAC5BiD,EAAeF,EAAe,MAAOG,EAAY,SAAS,CAClE,EAEKK,EAAwBvD,EAAS,KAC9B,CACL,GAAIwC,EAAY,kBAAA,EAEnB"}