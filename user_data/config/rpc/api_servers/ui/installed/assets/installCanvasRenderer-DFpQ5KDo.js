import{g as Eg,be as En,bf as On,bg as Og,k as Hr,bh as kg,M as kn,bi as nf,q as Bg,bj as Fg,az as af,ar as Ng,z as zg,h as Hg}from"./index-B2p78N-x.js";/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var qs=function(r,t){return qs=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])},qs(r,t)};function F(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");qs(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var Gg=function(){function r(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return r}(),Wg=function(){function r(){this.browser=new Gg,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return r}(),W=new Wg;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(W.wxa=!0,W.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?W.worker=!0:typeof navigator>"u"||navigator.userAgent.indexOf("Node.js")===0?(W.node=!0,W.svgSupported=!0):Vg(navigator.userAgent,W);function Vg(r,t){var e=t.browser,i=r.match(/Firefox\/([\d.]+)/),n=r.match(/MSIE\s([\d.]+)/)||r.match(/Trident\/.+?rv:(([\d.]+))/),a=r.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(r);i&&(e.firefox=!0,e.version=i[1]),n&&(e.ie=!0,e.version=n[1]),a&&(e.edge=!0,e.version=a[1],e.newEdge=+a[1].split(".")[0]>18),o&&(e.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!e.ie&&!e.edge,t.pointerEventsSupported="onpointerdown"in window&&(e.edge||e.ie&&+e.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(e.ie&&"transition"in s||e.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||e.ie&&+e.version>=9}var qu=12,Ug="sans-serif",kr=qu+"px "+Ug,Yg=20,Xg=100,$g="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function qg(r){var t={};if(typeof JSON>"u")return t;for(var e=0;e<r.length;e++){var i=String.fromCharCode(e+32),n=(r.charCodeAt(e)-Yg)/Xg;t[i]=n}return t}var Zg=qg($g),_i={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var r,t;return function(e,i){if(!r){var n=_i.createCanvas();r=n&&n.getContext("2d")}if(r)return t!==i&&(t=r.font=i||kr),r.measureText(e);e=e||"",i=i||kr;var a=/(\d+)px/.exec(i),o=a&&+a[1]||qu,s=0;if(i.indexOf("mono")>=0)s=o*e.length;else for(var u=0;u<e.length;u++){var l=Zg[e[u]];s+=l==null?o:l*o}return{width:s}}}(),loadImage:function(r,t,e){var i=new Image;return i.onload=t,i.onerror=e,i.src=r,i}},yc=Ke(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(r,t){return r["[object "+t+"]"]=!0,r},{}),mc=Ke(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(r,t){return r["[object "+t+"Array]"]=!0,r},{}),Dn=Object.prototype.toString,ao=Array.prototype,Kg=ao.forEach,Qg=ao.filter,Zu=ao.slice,Jg=ao.map,of=(function(){}).constructor,Bn=of?of.prototype:null,Ku="__proto__",jg=2311;function wc(){return jg++}function Qu(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];typeof console<"u"&&console.error.apply(console,r)}function K(r){if(r==null||typeof r!="object")return r;var t=r,e=Dn.call(r);if(e==="[object Array]"){if(!Ji(r)){t=[];for(var i=0,n=r.length;i<n;i++)t[i]=K(r[i])}}else if(mc[e]){if(!Ji(r)){var a=r.constructor;if(a.from)t=a.from(r);else{t=new a(r.length);for(var i=0,n=r.length;i<n;i++)t[i]=r[i]}}}else if(!yc[e]&&!Ji(r)&&!hn(r)){t={};for(var o in r)r.hasOwnProperty(o)&&o!==Ku&&(t[o]=K(r[o]))}return t}function ut(r,t,e){if(!H(t)||!H(r))return e?K(t):r;for(var i in t)if(t.hasOwnProperty(i)&&i!==Ku){var n=r[i],a=t[i];H(a)&&H(n)&&!k(a)&&!k(n)&&!hn(a)&&!hn(n)&&!sf(a)&&!sf(n)&&!Ji(a)&&!Ji(n)?ut(n,a,e):(e||!(i in r))&&(r[i]=K(t[i]))}return r}function O(r,t){if(Object.assign)Object.assign(r,t);else for(var e in t)t.hasOwnProperty(e)&&e!==Ku&&(r[e]=t[e]);return r}function ot(r,t,e){for(var i=lt(t),n=0;n<i.length;n++){var a=i[n];(e?t[a]!=null:r[a]==null)&&(r[a]=t[a])}return r}function nt(r,t){if(r){if(r.indexOf)return r.indexOf(t);for(var e=0,i=r.length;e<i;e++)if(r[e]===t)return e}return-1}function t_(r,t){var e=r.prototype;function i(){}i.prototype=t.prototype,r.prototype=new i;for(var n in e)e.hasOwnProperty(n)&&(r.prototype[n]=e[n]);r.prototype.constructor=r,r.superClass=t}function ke(r,t,e){if(r="prototype"in r?r.prototype:r,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),n=0;n<i.length;n++){var a=i[n];a!=="constructor"&&(e?t[a]!=null:r[a]==null)&&(r[a]=t[a])}else ot(r,t,e)}function Ft(r){return!r||typeof r=="string"?!1:typeof r.length=="number"}function D(r,t,e){if(r&&t)if(r.forEach&&r.forEach===Kg)r.forEach(t,e);else if(r.length===+r.length)for(var i=0,n=r.length;i<n;i++)t.call(e,r[i],i,r);else for(var a in r)r.hasOwnProperty(a)&&t.call(e,r[a],a,r)}function Y(r,t,e){if(!r)return[];if(!t)return Ju(r);if(r.map&&r.map===Jg)return r.map(t,e);for(var i=[],n=0,a=r.length;n<a;n++)i.push(t.call(e,r[n],n,r));return i}function Ke(r,t,e,i){if(r&&t){for(var n=0,a=r.length;n<a;n++)e=t.call(i,e,r[n],n,r);return e}}function Et(r,t,e){if(!r)return[];if(!t)return Ju(r);if(r.filter&&r.filter===Qg)return r.filter(t,e);for(var i=[],n=0,a=r.length;n<a;n++)t.call(e,r[n],n,r)&&i.push(r[n]);return i}function lt(r){if(!r)return[];if(Object.keys)return Object.keys(r);var t=[];for(var e in r)r.hasOwnProperty(e)&&t.push(e);return t}function e_(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return function(){return r.apply(t,e.concat(Zu.call(arguments)))}}var st=Bn&&Z(Bn.bind)?Bn.call.bind(Bn.bind):e_;function _t(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){return r.apply(this,t.concat(Zu.call(arguments)))}}function k(r){return Array.isArray?Array.isArray(r):Dn.call(r)==="[object Array]"}function Z(r){return typeof r=="function"}function B(r){return typeof r=="string"}function Zs(r){return Dn.call(r)==="[object String]"}function ht(r){return typeof r=="number"}function H(r){var t=typeof r;return t==="function"||!!r&&t==="object"}function sf(r){return!!yc[Dn.call(r)]}function Nt(r){return!!mc[Dn.call(r)]}function hn(r){return typeof r=="object"&&typeof r.nodeType=="number"&&typeof r.ownerDocument=="object"}function oo(r){return r.colorStops!=null}function r_(r){return r.image!=null}function ka(r){return r!==r}function vn(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var e=0,i=r.length;e<i;e++)if(r[e]!=null)return r[e]}function X(r,t){return r??t}function wa(r,t,e){return r??t??e}function Ju(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return Zu.apply(r,t)}function Sc(r){if(typeof r=="number")return[r,r,r,r];var t=r.length;return t===2?[r[0],r[1],r[0],r[1]]:t===3?[r[0],r[1],r[2],r[1]]:r}function xe(r,t){if(!r)throw new Error(t)}function we(r){return r==null?null:typeof r.trim=="function"?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var bc="__ec_primitive__";function Ba(r){r[bc]=!0}function Ji(r){return r[bc]}var i_=function(){function r(){this.data={}}return r.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},r.prototype.has=function(t){return this.data.hasOwnProperty(t)},r.prototype.get=function(t){return this.data[t]},r.prototype.set=function(t,e){return this.data[t]=e,this},r.prototype.keys=function(){return lt(this.data)},r.prototype.forEach=function(t){var e=this.data;for(var i in e)e.hasOwnProperty(i)&&t(e[i],i)},r}(),Tc=typeof Map=="function";function n_(){return Tc?new Map:new i_}var a_=function(){function r(t){var e=k(t);this.data=n_();var i=this;t instanceof r?t.each(n):t&&D(t,n);function n(a,o){e?i.set(a,o):i.set(o,a)}}return r.prototype.hasKey=function(t){return this.data.has(t)},r.prototype.get=function(t){return this.data.get(t)},r.prototype.set=function(t,e){return this.data.set(t,e),e},r.prototype.each=function(t,e){this.data.forEach(function(i,n){t.call(e,i,n)})},r.prototype.keys=function(){var t=this.data.keys();return Tc?Array.from(t):t},r.prototype.removeKey=function(t){this.data.delete(t)},r}();function $(r){return new a_(r)}function o_(r,t){for(var e=new r.constructor(r.length+t.length),i=0;i<r.length;i++)e[i]=r[i];for(var n=r.length,i=0;i<t.length;i++)e[i+n]=t[i];return e}function so(r,t){var e;if(Object.create)e=Object.create(r);else{var i=function(){};i.prototype=r,e=new i}return t&&O(e,t),e}function xc(r){var t=r.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function di(r,t){return r.hasOwnProperty(t)}function Bt(){}var s_=180/Math.PI;function yi(r,t){return r==null&&(r=0),t==null&&(t=0),[r,t]}function u_(r){return[r[0],r[1]]}function uf(r,t,e){return r[0]=t[0]+e[0],r[1]=t[1]+e[1],r}function l_(r,t,e){return r[0]=t[0]-e[0],r[1]=t[1]-e[1],r}function f_(r){return Math.sqrt(h_(r))}function h_(r){return r[0]*r[0]+r[1]*r[1]}function Oo(r,t,e){return r[0]=t[0]*e,r[1]=t[1]*e,r}function v_(r,t){var e=f_(t);return e===0?(r[0]=0,r[1]=0):(r[0]=t[0]/e,r[1]=t[1]/e),r}function Ks(r,t){return Math.sqrt((r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1]))}var c_=Ks;function d_(r,t){return(r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1])}var oi=d_;function nC(r,t,e,i){return r[0]=t[0]+i*(e[0]-t[0]),r[1]=t[1]+i*(e[1]-t[1]),r}function be(r,t,e){var i=t[0],n=t[1];return r[0]=e[0]*i+e[2]*n+e[4],r[1]=e[1]*i+e[3]*n+e[5],r}function ri(r,t,e){return r[0]=Math.min(t[0],e[0]),r[1]=Math.min(t[1],e[1]),r}function ii(r,t,e){return r[0]=Math.max(t[0],e[0]),r[1]=Math.max(t[1],e[1]),r}var Gr=function(){function r(t,e){this.target=t,this.topTarget=e&&e.topTarget}return r}(),p_=function(){function r(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return r.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Gr(e,t),"dragstart",t.event))},r.prototype._drag=function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,a=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(a,o,t),this.handler.dispatchToElement(new Gr(e,t),"drag",t.event);var s=this.handler.findHover(i,n,e).target,u=this._dropTarget;this._dropTarget=s,e!==s&&(u&&s!==u&&this.handler.dispatchToElement(new Gr(u,t),"dragleave",t.event),s&&s!==u&&this.handler.dispatchToElement(new Gr(s,t),"dragenter",t.event))}},r.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Gr(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Gr(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},r}(),Ce=function(){function r(t){t&&(this._$eventProcessor=t)}return r.prototype.on=function(t,e,i,n){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof e=="function"&&(n=i,i=e,e=null),!i||!t)return this;var o=this._$eventProcessor;e!=null&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===i)return this;var u={h:i,query:e,ctx:n||this,callAtLast:i.zrEventfulCallAtLast},l=a[t].length-1,f=a[t][l];return f&&f.callAtLast?a[t].splice(l,0,u):a[t].push(u),this},r.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},r.prototype.off=function(t,e){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],a=0,o=i[t].length;a<o;a++)i[t][a].h!==e&&n.push(i[t][a]);i[t]=n}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},r.prototype.trigger=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=n.length,u=0;u<s;u++){var l=n[u];if(!(a&&a.filter&&l.query!=null&&!a.filter(t,l.query)))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r.prototype.triggerWithContext=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=e[o-1],u=n.length,l=0;l<u;l++){var f=n[l];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(s);break;case 1:f.h.call(s,e[0]);break;case 2:f.h.call(s,e[0],e[1]);break;default:f.h.apply(s,e.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r}(),g_=Math.log(2);function Qs(r,t,e,i,n,a){var o=i+"-"+n,s=r.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var u=Math.round(Math.log((1<<s)-1&~n)/g_);return r[e][u]}for(var l=i|1<<e,f=e+1;i&1<<f;)f++;for(var h=0,c=0,v=0;c<s;c++){var d=1<<c;d&n||(h+=(v%2?-1:1)*r[e][c]*Qs(r,t-1,f,l,n|d,a),v++)}return a[o]=h,h}function lf(r,t){var e=[[r[0],r[1],1,0,0,0,-t[0]*r[0],-t[0]*r[1]],[0,0,0,r[0],r[1],1,-t[1]*r[0],-t[1]*r[1]],[r[2],r[3],1,0,0,0,-t[2]*r[2],-t[2]*r[3]],[0,0,0,r[2],r[3],1,-t[3]*r[2],-t[3]*r[3]],[r[4],r[5],1,0,0,0,-t[4]*r[4],-t[4]*r[5]],[0,0,0,r[4],r[5],1,-t[5]*r[4],-t[5]*r[5]],[r[6],r[7],1,0,0,0,-t[6]*r[6],-t[6]*r[7]],[0,0,0,r[6],r[7],1,-t[7]*r[6],-t[7]*r[7]]],i={},n=Qs(e,8,0,0,0,i);if(n!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*Qs(e,7,o===0?1:0,1<<o,1<<s,i)/n*t[o];return function(u,l,f){var h=l*a[6]+f*a[7]+1;u[0]=(l*a[0]+f*a[1]+a[2])/h,u[1]=(l*a[3]+f*a[4]+a[5])/h}}}var ff="___zrEVENTSAVED",ko=[];function __(r,t,e,i,n){return Js(ko,t,i,n,!0)&&Js(r,e,ko[0],ko[1])}function Js(r,t,e,i,n){if(t.getBoundingClientRect&&W.domSupported&&!Cc(t)){var a=t[ff]||(t[ff]={}),o=y_(t,a),s=m_(o,a,n);if(s)return s(r,e,i),!0}return!1}function y_(r,t){var e=t.markers;if(e)return e;e=t.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,u=a%2,l=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[u]+":0",n[l]+":0",i[1-u]+":auto",n[1-l]+":auto",""].join("!important;"),r.appendChild(o),e.push(o)}return e}function m_(r,t,e){for(var i=e?"invTrans":"trans",n=t[i],a=t.srcCoords,o=[],s=[],u=!0,l=0;l<4;l++){var f=r[l].getBoundingClientRect(),h=2*l,c=f.left,v=f.top;o.push(c,v),u=u&&a&&c===a[h]&&v===a[h+1],s.push(r[l].offsetLeft,r[l].offsetTop)}return u&&n?n:(t.srcCoords=o,t[i]=e?lf(s,o):lf(o,s))}function Cc(r){return r.nodeName.toUpperCase()==="CANVAS"}var w_=/([&<>"'])/g,S_={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Zt(r){return r==null?"":(r+"").replace(w_,function(t,e){return S_[e]})}var b_=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Bo=[],T_=W.browser.firefox&&+W.browser.version.split(".")[0]<39;function js(r,t,e,i){return e=e||{},i?hf(r,t,e):T_&&t.layerX!=null&&t.layerX!==t.offsetX?(e.zrX=t.layerX,e.zrY=t.layerY):t.offsetX!=null?(e.zrX=t.offsetX,e.zrY=t.offsetY):hf(r,t,e),e}function hf(r,t,e){if(W.domSupported&&r.getBoundingClientRect){var i=t.clientX,n=t.clientY;if(Cc(r)){var a=r.getBoundingClientRect();e.zrX=i-a.left,e.zrY=n-a.top;return}else if(Js(Bo,r,i,n)){e.zrX=Bo[0],e.zrY=Bo[1];return}}e.zrX=e.zrY=0}function ju(r){return r||window.event}function Xt(r,t,e){if(t=ju(t),t.zrX!=null)return t;var i=t.type,n=i&&i.indexOf("touch")>=0;if(n){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&js(r,o,t,e)}else{js(r,t,t,e);var a=x_(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&b_.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function x_(r){var t=r.wheelDelta;if(t)return t;var e=r.deltaX,i=r.deltaY;if(e==null||i==null)return t;var n=Math.abs(i!==0?i:e),a=i>0?-1:i<0?1:e>0?-1:1;return 3*n*a}function C_(r,t,e,i){r.addEventListener(t,e,i)}function D_(r,t,e,i){r.removeEventListener(t,e,i)}var Dc=function(r){r.preventDefault(),r.stopPropagation(),r.cancelBubble=!0};function aC(r){return r.which===2||r.which===3}var M_=function(){function r(){this._track=[]}return r.prototype.recognize=function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},r.prototype.clear=function(){return this._track.length=0,this},r.prototype._doTrack=function(t,e,i){var n=t.touches;if(n){for(var a={points:[],touches:[],target:e,event:t},o=0,s=n.length;o<s;o++){var u=n[o],l=js(i,u,{});a.points.push([l.zrX,l.zrY]),a.touches.push(u)}this._track.push(a)}},r.prototype._recognize=function(t){for(var e in Fo)if(Fo.hasOwnProperty(e)){var i=Fo[e](this._track,t);if(i)return i}},r}();function vf(r){var t=r[1][0]-r[0][0],e=r[1][1]-r[0][1];return Math.sqrt(t*t+e*e)}function A_(r){return[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]}var Fo={pinch:function(r,t){var e=r.length;if(e){var i=(r[e-1]||{}).points,n=(r[e-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=vf(i)/vf(n);!isFinite(a)&&(a=1),t.pinchScale=a;var o=A_(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:r[0].target,event:t}}}}};function si(){return[1,0,0,1,0,0]}function tl(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r[4]=0,r[5]=0,r}function P_(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r}function ui(r,t,e){var i=t[0]*e[0]+t[2]*e[1],n=t[1]*e[0]+t[3]*e[1],a=t[0]*e[2]+t[2]*e[3],o=t[1]*e[2]+t[3]*e[3],s=t[0]*e[4]+t[2]*e[5]+t[4],u=t[1]*e[4]+t[3]*e[5]+t[5];return r[0]=i,r[1]=n,r[2]=a,r[3]=o,r[4]=s,r[5]=u,r}function tu(r,t,e){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4]+e[0],r[5]=t[5]+e[1],r}function el(r,t,e,i){i===void 0&&(i=[0,0]);var n=t[0],a=t[2],o=t[4],s=t[1],u=t[3],l=t[5],f=Math.sin(e),h=Math.cos(e);return r[0]=n*h+s*f,r[1]=-n*f+s*h,r[2]=a*h+u*f,r[3]=-a*f+h*u,r[4]=h*(o-i[0])+f*(l-i[1])+i[0],r[5]=h*(l-i[1])-f*(o-i[0])+i[1],r}function L_(r,t,e){var i=e[0],n=e[1];return r[0]=t[0]*i,r[1]=t[1]*n,r[2]=t[2]*i,r[3]=t[3]*n,r[4]=t[4]*i,r[5]=t[5]*n,r}function Mc(r,t){var e=t[0],i=t[2],n=t[4],a=t[1],o=t[3],s=t[5],u=e*o-a*i;return u?(u=1/u,r[0]=o*u,r[1]=-a*u,r[2]=-i*u,r[3]=e*u,r[4]=(i*s-o*n)*u,r[5]=(a*n-e*s)*u,r):null}var et=function(){function r(t,e){this.x=t||0,this.y=e||0}return r.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},r.prototype.clone=function(){return new r(this.x,this.y)},r.prototype.set=function(t,e){return this.x=t,this.y=e,this},r.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},r.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},r.prototype.scale=function(t){this.x*=t,this.y*=t},r.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},r.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},r.prototype.dot=function(t){return this.x*t.x+this.y*t.y},r.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},r.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},r.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},r.prototype.distance=function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},r.prototype.distanceSquare=function(t){var e=this.x-t.x,i=this.y-t.y;return e*e+i*i},r.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},r.prototype.transform=function(t){if(t){var e=this.x,i=this.y;return this.x=t[0]*e+t[2]*i+t[4],this.y=t[1]*e+t[3]*i+t[5],this}},r.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},r.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},r.set=function(t,e,i){t.x=e,t.y=i},r.copy=function(t,e){t.x=e.x,t.y=e.y},r.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},r.lenSquare=function(t){return t.x*t.x+t.y*t.y},r.dot=function(t,e){return t.x*e.x+t.y*e.y},r.add=function(t,e,i){t.x=e.x+i.x,t.y=e.y+i.y},r.sub=function(t,e,i){t.x=e.x-i.x,t.y=e.y-i.y},r.scale=function(t,e,i){t.x=e.x*i,t.y=e.y*i},r.scaleAndAdd=function(t,e,i,n){t.x=e.x+i.x*n,t.y=e.y+i.y*n},r.lerp=function(t,e,i,n){var a=1-n;t.x=a*e.x+n*i.x,t.y=a*e.y+n*i.y},r}(),Fn=Math.min,Nn=Math.max,ir=new et,nr=new et,ar=new et,or=new et,Ti=new et,xi=new et,J=function(){function r(t,e,i,n){i<0&&(t=t+i,i=-i),n<0&&(e=e+n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}return r.prototype.union=function(t){var e=Fn(t.x,this.x),i=Fn(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Nn(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Nn(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=e,this.y=i},r.prototype.applyTransform=function(t){r.applyTransform(this,this,t)},r.prototype.calculateTransform=function(t){var e=this,i=t.width/e.width,n=t.height/e.height,a=si();return tu(a,a,[-e.x,-e.y]),L_(a,a,[i,n]),tu(a,a,[t.x,t.y]),a},r.prototype.intersect=function(t,e){if(!t)return!1;t instanceof r||(t=r.create(t));var i=this,n=i.x,a=i.x+i.width,o=i.y,s=i.y+i.height,u=t.x,l=t.x+t.width,f=t.y,h=t.y+t.height,c=!(a<u||l<n||s<f||h<o);if(e){var v=1/0,d=0,_=Math.abs(a-u),p=Math.abs(l-n),g=Math.abs(s-f),y=Math.abs(h-o),m=Math.min(_,p),w=Math.min(g,y);a<u||l<n?m>d&&(d=m,_<p?et.set(xi,-_,0):et.set(xi,p,0)):m<v&&(v=m,_<p?et.set(Ti,_,0):et.set(Ti,-p,0)),s<f||h<o?w>d&&(d=w,g<y?et.set(xi,0,-g):et.set(xi,0,y)):m<v&&(v=m,g<y?et.set(Ti,0,g):et.set(Ti,0,-y))}return e&&et.copy(e,c?Ti:xi),c},r.prototype.contain=function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},r.prototype.clone=function(){return new r(this.x,this.y,this.width,this.height)},r.prototype.copy=function(t){r.copy(this,t)},r.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},r.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},r.prototype.isZero=function(){return this.width===0||this.height===0},r.create=function(t){return new r(t.x,t.y,t.width,t.height)},r.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},r.applyTransform=function(t,e,i){if(!i){t!==e&&r.copy(t,e);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],a=i[3],o=i[4],s=i[5];t.x=e.x*n+o,t.y=e.y*a+s,t.width=e.width*n,t.height=e.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}ir.x=ar.x=e.x,ir.y=or.y=e.y,nr.x=or.x=e.x+e.width,nr.y=ar.y=e.y+e.height,ir.transform(i),or.transform(i),nr.transform(i),ar.transform(i),t.x=Fn(ir.x,nr.x,ar.x,or.x),t.y=Fn(ir.y,nr.y,ar.y,or.y);var u=Nn(ir.x,nr.x,ar.x,or.x),l=Nn(ir.y,nr.y,ar.y,or.y);t.width=u-t.x,t.height=l-t.y},r}(),Ac="silent";function I_(r,t,e){return{type:r,event:e,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:e.zrX,offsetY:e.zrY,gestureEvent:e.gestureEvent,pinchX:e.pinchX,pinchY:e.pinchY,pinchScale:e.pinchScale,wheelDelta:e.zrDelta,zrByTouch:e.zrByTouch,which:e.which,stop:R_}}function R_(){Dc(this.event)}var E_=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.handler=null,e}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(Ce),Ci=function(){function r(t,e){this.x=t,this.y=e}return r}(),O_=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],No=new J(0,0,0,0),Pc=function(r){F(t,r);function t(e,i,n,a,o){var s=r.call(this)||this;return s._hovered=new Ci(0,0),s.storage=e,s.painter=i,s.painterRoot=a,s._pointerSize=o,n=n||new E_,s.proxy=null,s.setHandlerProxy(n),s._draggingMgr=new p_(s),s}return t.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(D(O_,function(i){e.on&&e.on(i,this[i],this)},this),e.handler=this),this.proxy=e},t.prototype.mousemove=function(e){var i=e.zrX,n=e.zrY,a=Lc(this,i,n),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var u=this._hovered=a?new Ci(i,n):this.findHover(i,n),l=u.target,f=this.proxy;f.setCursor&&f.setCursor(l?l.cursor:"default"),s&&l!==s&&this.dispatchToElement(o,"mouseout",e),this.dispatchToElement(u,"mousemove",e),l&&l!==s&&this.dispatchToElement(u,"mouseover",e)},t.prototype.mouseout=function(e){var i=e.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",e),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:e})},t.prototype.resize=function(){this._hovered=new Ci(0,0)},t.prototype.dispatch=function(e,i){var n=this[e];n&&n.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(e){var i=this.proxy;i.setCursor&&i.setCursor(e)},t.prototype.dispatchToElement=function(e,i,n){e=e||{};var a=e.target;if(!(a&&a.silent)){for(var o="on"+i,s=I_(i,e,n);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(i,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(u){typeof u[o]=="function"&&u[o].call(u,s),u.trigger&&u.trigger(i,s)}))}},t.prototype.findHover=function(e,i,n){var a=this.storage.getDisplayList(),o=new Ci(e,i);if(cf(a,o,e,i,n),this._pointerSize&&!o.target){for(var s=[],u=this._pointerSize,l=u/2,f=new J(e-l,i-l,u,u),h=a.length-1;h>=0;h--){var c=a[h];c!==n&&!c.ignore&&!c.ignoreCoarsePointer&&(!c.parent||!c.parent.ignoreCoarsePointer)&&(No.copy(c.getBoundingRect()),c.transform&&No.applyTransform(c.transform),No.intersect(f)&&s.push(c))}if(s.length)for(var v=4,d=Math.PI/12,_=Math.PI*2,p=0;p<l;p+=v)for(var g=0;g<_;g+=d){var y=e+p*Math.cos(g),m=i+p*Math.sin(g);if(cf(s,o,y,m,n),o.target)return o}}return o},t.prototype.processGesture=function(e,i){this._gestureMgr||(this._gestureMgr=new M_);var n=this._gestureMgr;i==="start"&&n.clear();var a=n.recognize(e,this.findHover(e.zrX,e.zrY,null).target,this.proxy.dom);if(i==="end"&&n.clear(),a){var o=a.type;e.gestureEvent=o;var s=new Ci;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(Ce);D(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(r){Pc.prototype[r]=function(t){var e=t.zrX,i=t.zrY,n=Lc(this,e,i),a,o;if((r!=="mouseup"||!n)&&(a=this.findHover(e,i),o=a.target),r==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(r==="mouseup")this._upEl=o;else if(r==="click"){if(this._downEl!==this._upEl||!this._downPoint||c_(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,r,t)}});function k_(r,t,e){if(r[r.rectHover?"rectContain":"contain"](t,e)){for(var i=r,n=void 0,a=!1;i;){if(i.ignoreClip&&(a=!0),!a){var o=i.getClipPath();if(o&&!o.contain(t,e))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return n?Ac:!0}return!1}function cf(r,t,e,i,n){for(var a=r.length-1;a>=0;a--){var o=r[a],s=void 0;if(o!==n&&!o.ignore&&(s=k_(o,e,i))&&(!t.topTarget&&(t.topTarget=o),s!==Ac)){t.target=o;break}}}function Lc(r,t,e){var i=r.painter;return t<0||t>i.getWidth()||e<0||e>i.getHeight()}var Ic=32,Di=7;function B_(r){for(var t=0;r>=Ic;)t|=r&1,r>>=1;return r+t}function df(r,t,e,i){var n=t+1;if(n===e)return 1;if(i(r[n++],r[t])<0){for(;n<e&&i(r[n],r[n-1])<0;)n++;F_(r,t,n)}else for(;n<e&&i(r[n],r[n-1])>=0;)n++;return n-t}function F_(r,t,e){for(e--;t<e;){var i=r[t];r[t++]=r[e],r[e--]=i}}function pf(r,t,e,i,n){for(i===t&&i++;i<e;i++){for(var a=r[i],o=t,s=i,u;o<s;)u=o+s>>>1,n(a,r[u])<0?s=u:o=u+1;var l=i-o;switch(l){case 3:r[o+3]=r[o+2];case 2:r[o+2]=r[o+1];case 1:r[o+1]=r[o];break;default:for(;l>0;)r[o+l]=r[o+l-1],l--}r[o]=a}}function zo(r,t,e,i,n,a){var o=0,s=0,u=1;if(a(r,t[e+n])>0){for(s=i-n;u<s&&a(r,t[e+n+u])>0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=n,u+=n}else{for(s=n+1;u<s&&a(r,t[e+n-u])<=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=n-u,u=n-l}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])>0?o=f+1:u=f}return u}function Ho(r,t,e,i,n,a){var o=0,s=0,u=1;if(a(r,t[e+n])<0){for(s=n+1;u<s&&a(r,t[e+n-u])<0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=n-u,u=n-l}else{for(s=i-n;u<s&&a(r,t[e+n+u])>=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=n,u+=n}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])<0?u=f:o=f+1}return u}function N_(r,t){var e=Di,i,n,a=0,o=[];i=[],n=[];function s(v,d){i[a]=v,n[a]=d,a+=1}function u(){for(;a>1;){var v=a-2;if(v>=1&&n[v-1]<=n[v]+n[v+1]||v>=2&&n[v-2]<=n[v]+n[v-1])n[v-1]<n[v+1]&&v--;else if(n[v]>n[v+1])break;f(v)}}function l(){for(;a>1;){var v=a-2;v>0&&n[v-1]<n[v+1]&&v--,f(v)}}function f(v){var d=i[v],_=n[v],p=i[v+1],g=n[v+1];n[v]=_+g,v===a-3&&(i[v+1]=i[v+2],n[v+1]=n[v+2]),a--;var y=Ho(r[p],r,d,_,0,t);d+=y,_-=y,_!==0&&(g=zo(r[d+_-1],r,p,g,g-1,t),g!==0&&(_<=g?h(d,_,p,g):c(d,_,p,g)))}function h(v,d,_,p){var g=0;for(g=0;g<d;g++)o[g]=r[v+g];var y=0,m=_,w=v;if(r[w++]=r[m++],--p===0){for(g=0;g<d;g++)r[w+g]=o[y+g];return}if(d===1){for(g=0;g<p;g++)r[w+g]=r[m+g];r[w+p]=o[y];return}for(var b=e,S,x,C;;){S=0,x=0,C=!1;do if(t(r[m],o[y])<0){if(r[w++]=r[m++],x++,S=0,--p===0){C=!0;break}}else if(r[w++]=o[y++],S++,x=0,--d===1){C=!0;break}while((S|x)<b);if(C)break;do{if(S=Ho(r[m],o,y,d,0,t),S!==0){for(g=0;g<S;g++)r[w+g]=o[y+g];if(w+=S,y+=S,d-=S,d<=1){C=!0;break}}if(r[w++]=r[m++],--p===0){C=!0;break}if(x=zo(o[y],r,m,p,0,t),x!==0){for(g=0;g<x;g++)r[w+g]=r[m+g];if(w+=x,m+=x,p-=x,p===0){C=!0;break}}if(r[w++]=o[y++],--d===1){C=!0;break}b--}while(S>=Di||x>=Di);if(C)break;b<0&&(b=0),b+=2}if(e=b,e<1&&(e=1),d===1){for(g=0;g<p;g++)r[w+g]=r[m+g];r[w+p]=o[y]}else{if(d===0)throw new Error;for(g=0;g<d;g++)r[w+g]=o[y+g]}}function c(v,d,_,p){var g=0;for(g=0;g<p;g++)o[g]=r[_+g];var y=v+d-1,m=p-1,w=_+p-1,b=0,S=0;if(r[w--]=r[y--],--d===0){for(b=w-(p-1),g=0;g<p;g++)r[b+g]=o[g];return}if(p===1){for(w-=d,y-=d,S=w+1,b=y+1,g=d-1;g>=0;g--)r[S+g]=r[b+g];r[w]=o[m];return}for(var x=e;;){var C=0,M=0,A=!1;do if(t(o[m],r[y])<0){if(r[w--]=r[y--],C++,M=0,--d===0){A=!0;break}}else if(r[w--]=o[m--],M++,C=0,--p===1){A=!0;break}while((C|M)<x);if(A)break;do{if(C=d-Ho(o[m],r,v,d,d-1,t),C!==0){for(w-=C,y-=C,d-=C,S=w+1,b=y+1,g=C-1;g>=0;g--)r[S+g]=r[b+g];if(d===0){A=!0;break}}if(r[w--]=o[m--],--p===1){A=!0;break}if(M=p-zo(r[y],o,0,p,p-1,t),M!==0){for(w-=M,m-=M,p-=M,S=w+1,b=m+1,g=0;g<M;g++)r[S+g]=o[b+g];if(p<=1){A=!0;break}}if(r[w--]=r[y--],--d===0){A=!0;break}x--}while(C>=Di||M>=Di);if(A)break;x<0&&(x=0),x+=2}if(e=x,e<1&&(e=1),p===1){for(w-=d,y-=d,S=w+1,b=y+1,g=d-1;g>=0;g--)r[S+g]=r[b+g];r[w]=o[m]}else{if(p===0)throw new Error;for(b=w-(p-1),g=0;g<p;g++)r[b+g]=o[g]}}return{mergeRuns:u,forceMergeRuns:l,pushRun:s}}function Sa(r,t,e,i){e||(e=0),i||(i=r.length);var n=i-e;if(!(n<2)){var a=0;if(n<Ic){a=df(r,e,i,t),pf(r,e,i,e+a,t);return}var o=N_(r,t),s=B_(n);do{if(a=df(r,e,i,t),a<s){var u=n;u>s&&(u=s),pf(r,e,e+u,e+a,t),a=u}o.pushRun(e,a),o.mergeRuns(),n-=a,e+=a}while(n!==0);o.forceMergeRuns()}}var Gt=1,Yi=2,ti=4,gf=!1;function Go(){gf||(gf=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function _f(r,t){return r.zlevel===t.zlevel?r.z===t.z?r.z2-t.z2:r.z-t.z:r.zlevel-t.zlevel}var z_=function(){function r(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=_f}return r.prototype.traverse=function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},r.prototype.getDisplayList=function(t,e){e=e||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(e),i},r.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,a=e.length;n<a;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,Sa(i,_f)},r.prototype._updateAndAddDisplayable=function(t,e,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)e=null;else if(n){e?e=e.slice():e=[];for(var a=n,o=t;a;)a.parent=o,a.updateTransform(),e.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),u=0;u<s.length;u++){var l=s[u];t.__dirty&&(l.__dirty|=Gt),this._updateAndAddDisplayable(l,e,i)}t.__dirty=0}else{var f=t;e&&e.length?f.__clipPaths=e:f.__clipPaths&&f.__clipPaths.length>0&&(f.__clipPaths=[]),isNaN(f.z)&&(Go(),f.z=0),isNaN(f.z2)&&(Go(),f.z2=0),isNaN(f.zlevel)&&(Go(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,i);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,i);var v=t.getTextContent();v&&this._updateAndAddDisplayable(v,e,i)}},r.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},r.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);return}var n=nt(this._roots,t);n>=0&&this._roots.splice(n,1)},r.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},r.prototype.getRoots=function(){return this._roots},r.prototype.dispose=function(){this._displayList=null,this._roots=null},r}(),Fa;Fa=W.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(r){return setTimeout(r,16)};var ji={linear:function(r){return r},quadraticIn:function(r){return r*r},quadraticOut:function(r){return r*(2-r)},quadraticInOut:function(r){return(r*=2)<1?.5*r*r:-.5*(--r*(r-2)-1)},cubicIn:function(r){return r*r*r},cubicOut:function(r){return--r*r*r+1},cubicInOut:function(r){return(r*=2)<1?.5*r*r*r:.5*((r-=2)*r*r+2)},quarticIn:function(r){return r*r*r*r},quarticOut:function(r){return 1- --r*r*r*r},quarticInOut:function(r){return(r*=2)<1?.5*r*r*r*r:-.5*((r-=2)*r*r*r-2)},quinticIn:function(r){return r*r*r*r*r},quinticOut:function(r){return--r*r*r*r*r+1},quinticInOut:function(r){return(r*=2)<1?.5*r*r*r*r*r:.5*((r-=2)*r*r*r*r+2)},sinusoidalIn:function(r){return 1-Math.cos(r*Math.PI/2)},sinusoidalOut:function(r){return Math.sin(r*Math.PI/2)},sinusoidalInOut:function(r){return .5*(1-Math.cos(Math.PI*r))},exponentialIn:function(r){return r===0?0:Math.pow(1024,r-1)},exponentialOut:function(r){return r===1?1:1-Math.pow(2,-10*r)},exponentialInOut:function(r){return r===0?0:r===1?1:(r*=2)<1?.5*Math.pow(1024,r-1):.5*(-Math.pow(2,-10*(r-1))+2)},circularIn:function(r){return 1-Math.sqrt(1-r*r)},circularOut:function(r){return Math.sqrt(1- --r*r)},circularInOut:function(r){return(r*=2)<1?-.5*(Math.sqrt(1-r*r)-1):.5*(Math.sqrt(1-(r-=2)*r)+1)},elasticIn:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),-(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)))},elasticOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*r)*Math.sin((r-t)*(2*Math.PI)/i)+1)},elasticInOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),(r*=2)<1?-.5*(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)):e*Math.pow(2,-10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)*.5+1)},backIn:function(r){var t=1.70158;return r*r*((t+1)*r-t)},backOut:function(r){var t=1.70158;return--r*r*((t+1)*r+t)+1},backInOut:function(r){var t=2.5949095;return(r*=2)<1?.5*(r*r*((t+1)*r-t)):.5*((r-=2)*r*((t+1)*r+t)+2)},bounceIn:function(r){return 1-ji.bounceOut(1-r)},bounceOut:function(r){return r<1/2.75?7.5625*r*r:r<2/2.75?7.5625*(r-=1.5/2.75)*r+.75:r<2.5/2.75?7.5625*(r-=2.25/2.75)*r+.9375:7.5625*(r-=2.625/2.75)*r+.984375},bounceInOut:function(r){return r<.5?ji.bounceIn(r*2)*.5:ji.bounceOut(r*2-1)*.5+.5}},zn=Math.pow,qe=Math.sqrt,Na=1e-8,Rc=1e-4,yf=qe(3),Hn=1/3,me=yi(),Kt=yi(),li=yi();function Ye(r){return r>-Na&&r<Na}function Ec(r){return r>Na||r<-Na}function gt(r,t,e,i,n){var a=1-n;return a*a*(a*r+3*n*t)+n*n*(n*i+3*a*e)}function mf(r,t,e,i,n){var a=1-n;return 3*(((t-r)*a+2*(e-t)*n)*a+(i-e)*n*n)}function Oc(r,t,e,i,n,a){var o=i+3*(t-e)-r,s=3*(e-t*2+r),u=3*(t-r),l=r-n,f=s*s-3*o*u,h=s*u-9*o*l,c=u*u-3*s*l,v=0;if(Ye(f)&&Ye(h))if(Ye(s))a[0]=0;else{var d=-u/s;d>=0&&d<=1&&(a[v++]=d)}else{var _=h*h-4*f*c;if(Ye(_)){var p=h/f,d=-s/o+p,g=-p/2;d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g)}else if(_>0){var y=qe(_),m=f*s+1.5*o*(-h+y),w=f*s+1.5*o*(-h-y);m<0?m=-zn(-m,Hn):m=zn(m,Hn),w<0?w=-zn(-w,Hn):w=zn(w,Hn);var d=(-s-(m+w))/(3*o);d>=0&&d<=1&&(a[v++]=d)}else{var b=(2*f*s-3*o*h)/(2*qe(f*f*f)),S=Math.acos(b)/3,x=qe(f),C=Math.cos(S),d=(-s-2*x*C)/(3*o),g=(-s+x*(C+yf*Math.sin(S)))/(3*o),M=(-s+x*(C-yf*Math.sin(S)))/(3*o);d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g),M>=0&&M<=1&&(a[v++]=M)}}return v}function kc(r,t,e,i,n){var a=6*e-12*t+6*r,o=9*t+3*i-3*r-9*e,s=3*t-3*r,u=0;if(Ye(o)){if(Ec(a)){var l=-s/a;l>=0&&l<=1&&(n[u++]=l)}}else{var f=a*a-4*o*s;if(Ye(f))n[0]=-a/(2*o);else if(f>0){var h=qe(f),l=(-a+h)/(2*o),c=(-a-h)/(2*o);l>=0&&l<=1&&(n[u++]=l),c>=0&&c<=1&&(n[u++]=c)}}return u}function za(r,t,e,i,n,a){var o=(t-r)*n+r,s=(e-t)*n+t,u=(i-e)*n+e,l=(s-o)*n+o,f=(u-s)*n+s,h=(f-l)*n+l;a[0]=r,a[1]=o,a[2]=l,a[3]=h,a[4]=h,a[5]=f,a[6]=u,a[7]=i}function H_(r,t,e,i,n,a,o,s,u,l,f){var h,c=.005,v=1/0,d,_,p,g;me[0]=u,me[1]=l;for(var y=0;y<1;y+=.05)Kt[0]=gt(r,e,n,o,y),Kt[1]=gt(t,i,a,s,y),p=oi(me,Kt),p<v&&(h=y,v=p);v=1/0;for(var m=0;m<32&&!(c<Rc);m++)d=h-c,_=h+c,Kt[0]=gt(r,e,n,o,d),Kt[1]=gt(t,i,a,s,d),p=oi(Kt,me),d>=0&&p<v?(h=d,v=p):(li[0]=gt(r,e,n,o,_),li[1]=gt(t,i,a,s,_),g=oi(li,me),_<=1&&g<v?(h=_,v=g):c*=.5);return f&&(f[0]=gt(r,e,n,o,h),f[1]=gt(t,i,a,s,h)),qe(v)}function G_(r,t,e,i,n,a,o,s,u){for(var l=r,f=t,h=0,c=1/u,v=1;v<=u;v++){var d=v*c,_=gt(r,e,n,o,d),p=gt(t,i,a,s,d),g=_-l,y=p-f;h+=Math.sqrt(g*g+y*y),l=_,f=p}return h}function St(r,t,e,i){var n=1-i;return n*(n*r+2*i*t)+i*i*e}function wf(r,t,e,i){return 2*((1-i)*(t-r)+i*(e-t))}function W_(r,t,e,i,n){var a=r-2*t+e,o=2*(t-r),s=r-i,u=0;if(Ye(a)){if(Ec(o)){var l=-s/o;l>=0&&l<=1&&(n[u++]=l)}}else{var f=o*o-4*a*s;if(Ye(f)){var l=-o/(2*a);l>=0&&l<=1&&(n[u++]=l)}else if(f>0){var h=qe(f),l=(-o+h)/(2*a),c=(-o-h)/(2*a);l>=0&&l<=1&&(n[u++]=l),c>=0&&c<=1&&(n[u++]=c)}}return u}function Bc(r,t,e){var i=r+e-2*t;return i===0?.5:(r-t)/i}function Ha(r,t,e,i,n){var a=(t-r)*i+r,o=(e-t)*i+t,s=(o-a)*i+a;n[0]=r,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=e}function V_(r,t,e,i,n,a,o,s,u){var l,f=.005,h=1/0;me[0]=o,me[1]=s;for(var c=0;c<1;c+=.05){Kt[0]=St(r,e,n,c),Kt[1]=St(t,i,a,c);var v=oi(me,Kt);v<h&&(l=c,h=v)}h=1/0;for(var d=0;d<32&&!(f<Rc);d++){var _=l-f,p=l+f;Kt[0]=St(r,e,n,_),Kt[1]=St(t,i,a,_);var v=oi(Kt,me);if(_>=0&&v<h)l=_,h=v;else{li[0]=St(r,e,n,p),li[1]=St(t,i,a,p);var g=oi(li,me);p<=1&&g<h?(l=p,h=g):f*=.5}}return u&&(u[0]=St(r,e,n,l),u[1]=St(t,i,a,l)),qe(h)}function U_(r,t,e,i,n,a,o){for(var s=r,u=t,l=0,f=1/o,h=1;h<=o;h++){var c=h*f,v=St(r,e,n,c),d=St(t,i,a,c),_=v-s,p=d-u;l+=Math.sqrt(_*_+p*p),s=v,u=d}return l}var Y_=/cubic-bezier\(([0-9,\.e ]+)\)/;function Fc(r){var t=r&&Y_.exec(r);if(t){var e=t[1].split(","),i=+we(e[0]),n=+we(e[1]),a=+we(e[2]),o=+we(e[3]);if(isNaN(i+n+a+o))return;var s=[];return function(u){return u<=0?0:u>=1?1:Oc(0,i,a,1,u,s)&&gt(0,n,o,1,s[0])}}}var X_=function(){function r(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Bt,this.ondestroy=t.ondestroy||Bt,this.onrestart=t.onrestart||Bt,t.easing&&this.setEasing(t.easing)}return r.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var i=this._life,n=t-this._startTime-this._pausedTime,a=n/i;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var u=n%i;this._startTime=t-u,this._pausedTime=0,this.onrestart()}else return!0;return!1},r.prototype.pause=function(){this._paused=!0},r.prototype.resume=function(){this._paused=!1},r.prototype.setEasing=function(t){this.easing=t,this.easingFunc=Z(t)?t:ji[t]||Fc(t)},r}(),Nc=function(){function r(t){this.value=t}return r}(),$_=function(){function r(){this._len=0}return r.prototype.insert=function(t){var e=new Nc(t);return this.insertEntry(e),e},r.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.prototype.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.prototype.len=function(){return this._len},r.prototype.clear=function(){this.head=this.tail=null,this._len=0},r}(),Mn=function(){function r(t){this._list=new $_,this._maxSize=10,this._map={},this._maxSize=t}return r.prototype.put=function(t,e){var i=this._list,n=this._map,a=null;if(n[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var u=i.head;i.remove(u),delete n[u.key],a=u.value,this._lastRemovedEntry=u}s?s.value=e:s=new Nc(e),s.key=t,i.insertEntry(s),n[t]=s}return a},r.prototype.get=function(t){var e=this._map[t],i=this._list;if(e!=null)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},r.prototype.clear=function(){this._list.clear(),this._map={}},r.prototype.len=function(){return this._list.len()},r}(),Sf={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function oe(r){return r=Math.round(r),r<0?0:r>255?255:r}function q_(r){return r=Math.round(r),r<0?0:r>360?360:r}function cn(r){return r<0?0:r>1?1:r}function Wo(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?oe(parseFloat(t)/100*255):oe(parseInt(t,10))}function Lr(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?cn(parseFloat(t)/100):cn(parseFloat(t))}function Vo(r,t,e){return e<0?e+=1:e>1&&(e-=1),e*6<1?r+(t-r)*e*6:e*2<1?t:e*3<2?r+(t-r)*(2/3-e)*6:r}function Xe(r,t,e){return r+(t-r)*e}function Yt(r,t,e,i,n){return r[0]=t,r[1]=e,r[2]=i,r[3]=n,r}function eu(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r}var zc=new Mn(20),Gn=null;function Wr(r,t){Gn&&eu(Gn,t),Gn=zc.put(r,Gn||t.slice())}function se(r,t){if(r){t=t||[];var e=zc.get(r);if(e)return eu(t,e);r=r+"";var i=r.replace(/ /g,"").toLowerCase();if(i in Sf)return eu(t,Sf[i]),Wr(r,t),t;var n=i.length;if(i.charAt(0)==="#"){if(n===4||n===5){var a=parseInt(i.slice(1,4),16);if(!(a>=0&&a<=4095)){Yt(t,0,0,0,1);return}return Yt(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,n===5?parseInt(i.slice(4),16)/15:1),Wr(r,t),t}else if(n===7||n===9){var a=parseInt(i.slice(1,7),16);if(!(a>=0&&a<=16777215)){Yt(t,0,0,0,1);return}return Yt(t,(a&16711680)>>16,(a&65280)>>8,a&255,n===9?parseInt(i.slice(7),16)/255:1),Wr(r,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===n){var u=i.substr(0,o),l=i.substr(o+1,s-(o+1)).split(","),f=1;switch(u){case"rgba":if(l.length!==4)return l.length===3?Yt(t,+l[0],+l[1],+l[2],1):Yt(t,0,0,0,1);f=Lr(l.pop());case"rgb":if(l.length>=3)return Yt(t,Wo(l[0]),Wo(l[1]),Wo(l[2]),l.length===3?f:Lr(l[3])),Wr(r,t),t;Yt(t,0,0,0,1);return;case"hsla":if(l.length!==4){Yt(t,0,0,0,1);return}return l[3]=Lr(l[3]),ru(l,t),Wr(r,t),t;case"hsl":if(l.length!==3){Yt(t,0,0,0,1);return}return ru(l,t),Wr(r,t),t;default:return}}Yt(t,0,0,0,1)}}function ru(r,t){var e=(parseFloat(r[0])%360+360)%360/360,i=Lr(r[1]),n=Lr(r[2]),a=n<=.5?n*(i+1):n+i-n*i,o=n*2-a;return t=t||[],Yt(t,oe(Vo(o,a,e+1/3)*255),oe(Vo(o,a,e)*255),oe(Vo(o,a,e-1/3)*255),1),r.length===4&&(t[3]=r[3]),t}function Z_(r){if(r){var t=r[0]/255,e=r[1]/255,i=r[2]/255,n=Math.min(t,e,i),a=Math.max(t,e,i),o=a-n,s=(a+n)/2,u,l;if(o===0)u=0,l=0;else{s<.5?l=o/(a+n):l=o/(2-a-n);var f=((a-t)/6+o/2)/o,h=((a-e)/6+o/2)/o,c=((a-i)/6+o/2)/o;t===a?u=c-h:e===a?u=1/3+f-c:i===a&&(u=2/3+h-f),u<0&&(u+=1),u>1&&(u-=1)}var v=[u*360,l,s];return r[3]!=null&&v.push(r[3]),v}}function bf(r,t){var e=se(r);if(e){for(var i=0;i<3;i++)e[i]=e[i]*(1-t)|0,e[i]>255?e[i]=255:e[i]<0&&(e[i]=0);return mi(e,e.length===4?"rgba":"rgb")}}function oC(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){e=e||[];var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=t[n],s=t[a],u=i-n;return e[0]=oe(Xe(o[0],s[0],u)),e[1]=oe(Xe(o[1],s[1],u)),e[2]=oe(Xe(o[2],s[2],u)),e[3]=cn(Xe(o[3],s[3],u)),e}}function sC(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=se(t[n]),s=se(t[a]),u=i-n,l=mi([oe(Xe(o[0],s[0],u)),oe(Xe(o[1],s[1],u)),oe(Xe(o[2],s[2],u)),cn(Xe(o[3],s[3],u))],"rgba");return e?{color:l,leftIndex:n,rightIndex:a,value:i}:l}}function uC(r,t,e,i){var n=se(r);if(r)return n=Z_(n),t!=null&&(n[0]=q_(t)),e!=null&&(n[1]=Lr(e)),i!=null&&(n[2]=Lr(i)),mi(ru(n),"rgba")}function lC(r,t){var e=se(r);if(e&&t!=null)return e[3]=cn(t),mi(e,"rgba")}function mi(r,t){if(!(!r||!r.length)){var e=r[0]+","+r[1]+","+r[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(e+=","+r[3]),t+"("+e+")"}}function Ga(r,t){var e=se(r);return e?(.299*e[0]+.587*e[1]+.114*e[2])*e[3]/255+(1-e[3])*t:0}var Tf=new Mn(100);function xf(r){if(B(r)){var t=Tf.get(r);return t||(t=bf(r,-.1),Tf.put(r,t)),t}else if(oo(r)){var e=O({},r);return e.colorStops=Y(r.colorStops,function(i){return{offset:i.offset,color:bf(i.color,-.1)}}),e}return r}function K_(r){return r.type==="linear"}function Q_(r){return r.type==="radial"}(function(){return W.hasGlobalWindow&&Z(window.btoa)?function(r){return window.btoa(unescape(encodeURIComponent(r)))}:typeof Buffer<"u"?function(r){return Buffer.from(r).toString("base64")}:function(r){return null}})();var iu=Array.prototype.slice;function Ie(r,t,e){return(t-r)*e+r}function Uo(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=Ie(t[a],e[a],i);return r}function J_(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=Ie(t[o][s],e[o][s],i)}return r}function Wn(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=t[a]+e[a]*i;return r}function Cf(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=t[o][s]+e[o][s]*i}return r}function j_(r,t){for(var e=r.length,i=t.length,n=e>i?t:r,a=Math.min(e,i),o=n[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(e,i);s++)n.push({offset:o.offset,color:o.color.slice()})}function ty(r,t,e){var i=r,n=t;if(!(!i.push||!n.push)){var a=i.length,o=n.length;if(a!==o){var s=a>o;if(s)i.length=o;else for(var u=a;u<o;u++)i.push(e===1?n[u]:iu.call(n[u]))}for(var l=i[0]&&i[0].length,u=0;u<i.length;u++)if(e===1)isNaN(i[u])&&(i[u]=n[u]);else for(var f=0;f<l;f++)isNaN(i[u][f])&&(i[u][f]=n[u][f])}}function ba(r){if(Ft(r)){var t=r.length;if(Ft(r[0])){for(var e=[],i=0;i<t;i++)e.push(iu.call(r[i]));return e}return iu.call(r)}return r}function Ta(r){return r[0]=Math.floor(r[0])||0,r[1]=Math.floor(r[1])||0,r[2]=Math.floor(r[2])||0,r[3]=r[3]==null?1:r[3],"rgba("+r.join(",")+")"}function ey(r){return Ft(r&&r[0])?2:1}var Vn=0,xa=1,Hc=2,Xi=3,nu=4,au=5,Df=6;function Mf(r){return r===nu||r===au}function Un(r){return r===xa||r===Hc}var Mi=[0,0,0,0],ry=function(){function r(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return r.prototype.isFinished=function(){return this._finished},r.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},r.prototype.needsAnimate=function(){return this.keyframes.length>=1},r.prototype.getAdditiveTrack=function(){return this._additiveTrack},r.prototype.addKeyframe=function(t,e,i){this._needsSort=!0;var n=this.keyframes,a=n.length,o=!1,s=Df,u=e;if(Ft(e)){var l=ey(e);s=l,(l===1&&!ht(e[0])||l===2&&!ht(e[0][0]))&&(o=!0)}else if(ht(e)&&!ka(e))s=Vn;else if(B(e))if(!isNaN(+e))s=Vn;else{var f=se(e);f&&(u=f,s=Xi)}else if(oo(e)){var h=O({},u);h.colorStops=Y(e.colorStops,function(v){return{offset:v.offset,color:se(v.color)}}),K_(e)?s=nu:Q_(e)&&(s=au),u=h}a===0?this.valType=s:(s!==this.valType||s===Df)&&(o=!0),this.discrete=this.discrete||o;var c={time:t,value:u,rawValue:e,percent:0};return i&&(c.easing=i,c.easingFunc=Z(i)?i:ji[i]||Fc(i)),n.push(c),c},r.prototype.prepare=function(t,e){var i=this.keyframes;this._needsSort&&i.sort(function(_,p){return _.time-p.time});for(var n=this.valType,a=i.length,o=i[a-1],s=this.discrete,u=Un(n),l=Mf(n),f=0;f<a;f++){var h=i[f],c=h.value,v=o.value;h.percent=h.time/t,s||(u&&f!==a-1?ty(c,v,n):l&&j_(c.colorStops,v.colorStops))}if(!s&&n!==au&&e&&this.needsAnimate()&&e.needsAnimate()&&n===e.valType&&!e._finished){this._additiveTrack=e;for(var d=i[0].value,f=0;f<a;f++)n===Vn?i[f].additiveValue=i[f].value-d:n===Xi?i[f].additiveValue=Wn([],i[f].value,d,-1):Un(n)&&(i[f].additiveValue=n===xa?Wn([],i[f].value,d,-1):Cf([],i[f].value,d,-1))}},r.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,n=i?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,u=this.propName,l=a===Xi,f,h=this._lastFr,c=Math.min,v,d;if(s===1)v=d=o[0];else{if(e<0)f=0;else if(e<this._lastFrP){var _=c(h+1,s-1);for(f=_;f>=0&&!(o[f].percent<=e);f--);f=c(f,s-2)}else{for(f=h;f<s&&!(o[f].percent>e);f++);f=c(f-1,s-2)}d=o[f+1],v=o[f]}if(v&&d){this._lastFr=f,this._lastFrP=e;var p=d.percent-v.percent,g=p===0?1:c((e-v.percent)/p,1);d.easingFunc&&(g=d.easingFunc(g));var y=i?this._additiveValue:l?Mi:t[u];if((Un(a)||l)&&!y&&(y=this._additiveValue=[]),this.discrete)t[u]=g<1?v.rawValue:d.rawValue;else if(Un(a))a===xa?Uo(y,v[n],d[n],g):J_(y,v[n],d[n],g);else if(Mf(a)){var m=v[n],w=d[n],b=a===nu;t[u]={type:b?"linear":"radial",x:Ie(m.x,w.x,g),y:Ie(m.y,w.y,g),colorStops:Y(m.colorStops,function(x,C){var M=w.colorStops[C];return{offset:Ie(x.offset,M.offset,g),color:Ta(Uo([],x.color,M.color,g))}}),global:w.global},b?(t[u].x2=Ie(m.x2,w.x2,g),t[u].y2=Ie(m.y2,w.y2,g)):t[u].r=Ie(m.r,w.r,g)}else if(l)Uo(y,v[n],d[n],g),i||(t[u]=Ta(y));else{var S=Ie(v[n],d[n],g);i?this._additiveValue=S:t[u]=S}i&&this._addToTarget(t)}}},r.prototype._addToTarget=function(t){var e=this.valType,i=this.propName,n=this._additiveValue;e===Vn?t[i]=t[i]+n:e===Xi?(se(t[i],Mi),Wn(Mi,Mi,n,1),t[i]=Ta(Mi)):e===xa?Wn(t[i],t[i],n,1):e===Hc&&Cf(t[i],t[i],n,1)},r}(),rl=function(){function r(t,e,i,n){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n){Qu("Can' use additive animation on looped animation.");return}this._additiveAnimators=n,this._allowDiscrete=i}return r.prototype.getMaxTime=function(){return this._maxTime},r.prototype.getDelay=function(){return this._delay},r.prototype.getLoop=function(){return this._loop},r.prototype.getTarget=function(){return this._target},r.prototype.changeTarget=function(t){this._target=t},r.prototype.when=function(t,e,i){return this.whenWithKeys(t,e,lt(e),i)},r.prototype.whenWithKeys=function(t,e,i,n){for(var a=this._tracks,o=0;o<i.length;o++){var s=i[o],u=a[s];if(!u){u=a[s]=new ry(s);var l=void 0,f=this._getAdditiveTrack(s);if(f){var h=f.keyframes,c=h[h.length-1];l=c&&c.value,f.valType===Xi&&l&&(l=Ta(l))}else l=this._target[s];if(l==null)continue;t>0&&u.addKeyframe(0,ba(l),n),this._trackKeys.push(s)}u.addKeyframe(t,ba(e[s]),n)}return this._maxTime=Math.max(this._maxTime,t),this},r.prototype.pause=function(){this._clip.pause(),this._paused=!0},r.prototype.resume=function(){this._clip.resume(),this._paused=!1},r.prototype.isPaused=function(){return!!this._paused},r.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},r.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,i=0;i<e;i++)t[i].call(this)},r.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var i=0;i<e.length;i++)e[i].call(this)},r.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,i=0;i<e.length;i++)t[e[i]].setFinished()},r.prototype._getAdditiveTrack=function(t){var e,i=this._additiveAnimators;if(i)for(var n=0;n<i.length;n++){var a=i[n].getTrack(t);a&&(e=a)}return e},r.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,i=[],n=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],u=this._getAdditiveTrack(o),l=s.keyframes,f=l.length;if(s.prepare(n,u),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=l[f-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var c=new X_({life:n,loop:this._loop,delay:this._delay||0,onframe:function(v){e._started=2;var d=e._additiveAnimators;if(d){for(var _=!1,p=0;p<d.length;p++)if(d[p]._clip){_=!0;break}_||(e._additiveAnimators=null)}for(var p=0;p<i.length;p++)i[p].step(e._target,v);var g=e._onframeCbs;if(g)for(var p=0;p<g.length;p++)g[p](e._target,v)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},r.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},r.prototype.delay=function(t){return this._delay=t,this},r.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},r.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},r.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},r.prototype.getClip=function(){return this._clip},r.prototype.getTrack=function(t){return this._tracks[t]},r.prototype.getTracks=function(){var t=this;return Y(this._trackKeys,function(e){return t._tracks[e]})},r.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var i=this._tracks,n=this._trackKeys,a=0;a<t.length;a++){var o=i[t[a]];o&&!o.isFinished()&&(e?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<n.length;a++)if(!i[n[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},r.prototype.saveTo=function(t,e,i){if(t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var a=e[n],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,u=s[i?0:s.length-1];u&&(t[a]=ba(u.rawValue))}}}},r.prototype.__changeFinalValue=function(t,e){e=e||lt(t);for(var i=0;i<e.length;i++){var n=e[i],a=this._tracks[n];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[n]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},r}();function ni(){return new Date().getTime()}var iy=function(r){F(t,r);function t(e){var i=r.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,e=e||{},i.stage=e.stage||{},i}return t.prototype.addClip=function(e){e.animation&&this.removeClip(e),this._head?(this._tail.next=e,e.prev=this._tail,e.next=null,this._tail=e):this._head=this._tail=e,e.animation=this},t.prototype.addAnimator=function(e){e.animation=this;var i=e.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(e){if(e.animation){var i=e.prev,n=e.next;i?i.next=n:this._head=n,n?n.prev=i:this._tail=i,e.next=e.prev=e.animation=null}},t.prototype.removeAnimator=function(e){var i=e.getClip();i&&this.removeClip(i),e.animation=null},t.prototype.update=function(e){for(var i=ni()-this._pausedTime,n=i-this._time,a=this._head;a;){var o=a.next,s=a.step(i,n);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=i,e||(this.trigger("frame",n),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var e=this;this._running=!0;function i(){e._running&&(Fa(i),!e._paused&&e.update())}Fa(i)},t.prototype.start=function(){this._running||(this._time=ni(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=ni(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=ni()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var e=this._head;e;){var i=e.next;e.prev=e.next=e.animation=null,e=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(e,i){i=i||{},this.start();var n=new rl(e,i.loop);return this.addAnimator(n),n},t}(Ce),ny=300,Yo=W.domSupported,Xo=function(){var r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=Y(r,function(n){var a=n.replace("mouse","pointer");return e.hasOwnProperty(a)?a:n});return{mouse:r,touch:t,pointer:i}}(),Af={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},Pf=!1;function ou(r){var t=r.pointerType;return t==="pen"||t==="touch"}function ay(r){r.touching=!0,r.touchTimer!=null&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)}function $o(r){r&&(r.zrByTouch=!0)}function oy(r,t){return Xt(r.dom,new sy(r,t),!0)}function Gc(r,t){for(var e=t,i=!1;e&&e.nodeType!==9&&!(i=e.domBelongToZr||e!==t&&e===r.painterRoot);)e=e.parentNode;return i}var sy=function(){function r(t,e){this.stopPropagation=Bt,this.stopImmediatePropagation=Bt,this.preventDefault=Bt,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return r}(),ne={mousedown:function(r){r=Xt(this.dom,r),this.__mayPointerCapture=[r.zrX,r.zrY],this.trigger("mousedown",r)},mousemove:function(r){r=Xt(this.dom,r);var t=this.__mayPointerCapture;t&&(r.zrX!==t[0]||r.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",r)},mouseup:function(r){r=Xt(this.dom,r),this.__togglePointerCapture(!1),this.trigger("mouseup",r)},mouseout:function(r){r=Xt(this.dom,r);var t=r.toElement||r.relatedTarget;Gc(this,t)||(this.__pointerCapturing&&(r.zrEventControl="no_globalout"),this.trigger("mouseout",r))},wheel:function(r){Pf=!0,r=Xt(this.dom,r),this.trigger("mousewheel",r)},mousewheel:function(r){Pf||(r=Xt(this.dom,r),this.trigger("mousewheel",r))},touchstart:function(r){r=Xt(this.dom,r),$o(r),this.__lastTouchMoment=new Date,this.handler.processGesture(r,"start"),ne.mousemove.call(this,r),ne.mousedown.call(this,r)},touchmove:function(r){r=Xt(this.dom,r),$o(r),this.handler.processGesture(r,"change"),ne.mousemove.call(this,r)},touchend:function(r){r=Xt(this.dom,r),$o(r),this.handler.processGesture(r,"end"),ne.mouseup.call(this,r),+new Date-+this.__lastTouchMoment<ny&&ne.click.call(this,r)},pointerdown:function(r){ne.mousedown.call(this,r)},pointermove:function(r){ou(r)||ne.mousemove.call(this,r)},pointerup:function(r){ne.mouseup.call(this,r)},pointerout:function(r){ou(r)||ne.mouseout.call(this,r)}};D(["click","dblclick","contextmenu"],function(r){ne[r]=function(t){t=Xt(this.dom,t),this.trigger(r,t)}});var su={pointermove:function(r){ou(r)||su.mousemove.call(this,r)},pointerup:function(r){su.mouseup.call(this,r)},mousemove:function(r){this.trigger("mousemove",r)},mouseup:function(r){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",r),t&&(r.zrEventControl="only_globalout",this.trigger("mouseout",r))}};function uy(r,t){var e=t.domHandlers;W.pointerEventsSupported?D(Xo.pointer,function(i){Ca(t,i,function(n){e[i].call(r,n)})}):(W.touchEventsSupported&&D(Xo.touch,function(i){Ca(t,i,function(n){e[i].call(r,n),ay(t)})}),D(Xo.mouse,function(i){Ca(t,i,function(n){n=ju(n),t.touching||e[i].call(r,n)})}))}function ly(r,t){W.pointerEventsSupported?D(Af.pointer,e):W.touchEventsSupported||D(Af.mouse,e);function e(i){function n(a){a=ju(a),Gc(r,a.target)||(a=oy(r,a),t.domHandlers[i].call(r,a))}Ca(t,i,n,{capture:!0})}}function Ca(r,t,e,i){r.mounted[t]=e,r.listenerOpts[t]=i,C_(r.domTarget,t,e,i)}function qo(r){var t=r.mounted;for(var e in t)t.hasOwnProperty(e)&&D_(r.domTarget,e,t[e],r.listenerOpts[e]);r.mounted={}}var Lf=function(){function r(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return r}(),fy=function(r){F(t,r);function t(e,i){var n=r.call(this)||this;return n.__pointerCapturing=!1,n.dom=e,n.painterRoot=i,n._localHandlerScope=new Lf(e,ne),Yo&&(n._globalHandlerScope=new Lf(document,su)),uy(n,n._localHandlerScope),n}return t.prototype.dispose=function(){qo(this._localHandlerScope),Yo&&qo(this._globalHandlerScope)},t.prototype.setCursor=function(e){this.dom.style&&(this.dom.style.cursor=e||"default")},t.prototype.__togglePointerCapture=function(e){if(this.__mayPointerCapture=null,Yo&&+this.__pointerCapturing^+e){this.__pointerCapturing=e;var i=this._globalHandlerScope;e?ly(this,i):qo(i)}},t}(Ce),Wc=1;W.hasGlobalWindow&&(Wc=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Wa=Wc,uu=.4,lu="#333",fu="#ccc",hy="#eee",If=tl,Rf=5e-5;function sr(r){return r>Rf||r<-Rf}var ur=[],Vr=[],Zo=si(),Ko=Math.abs,vy=function(){function r(){}return r.prototype.getLocalTransform=function(t){return r.getLocalTransform(this,t)},r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},r.prototype.needLocalTransform=function(){return sr(this.rotation)||sr(this.x)||sr(this.y)||sr(this.scaleX-1)||sr(this.scaleY-1)||sr(this.skewX)||sr(this.skewY)},r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),i=this.transform;if(!(e||t)){i&&(If(i),this.invTransform=null);return}i=i||si(),e?this.getLocalTransform(i):If(i),t&&(e?ui(i,t,i):P_(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},r.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(e!=null&&e!==1){this.getGlobalScale(ur);var i=ur[0]<0?-1:1,n=ur[1]<0?-1:1,a=((ur[0]-i)*e+i)/ur[0]||0,o=((ur[1]-n)*e+n)/ur[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||si(),Mc(this.invTransform,t)},r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},r.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),a=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(a),e=Math.sqrt(e),this.skewX=a,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=i,this.originX=0,this.originY=0}},r.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||si(),ui(Vr,t.invTransform,e),e=Vr);var i=this.originX,n=this.originY;(i||n)&&(Zo[4]=i,Zo[5]=n,ui(Vr,e,Zo),Vr[4]-=i,Vr[5]-=n,e=Vr),this.setLocalTransform(e)}},r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},r.prototype.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&be(i,i,n),i},r.prototype.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&be(i,i,n),i},r.prototype.getLineScale=function(){var t=this.transform;return t&&Ko(t[0]-1)>1e-10&&Ko(t[3]-1)>1e-10?Math.sqrt(Ko(t[0]*t[3]-t[2]*t[1])):1},r.prototype.copyTransform=function(t){cy(this,t)},r.getLocalTransform=function(t,e){e=e||[];var i=t.originX||0,n=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,u=t.anchorY,l=t.rotation||0,f=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,v=t.skewY?Math.tan(-t.skewY):0;if(i||n||s||u){var d=i+s,_=n+u;e[4]=-d*a-c*_*o,e[5]=-_*o-v*d*a}else e[4]=e[5]=0;return e[0]=a,e[3]=o,e[1]=v*a,e[2]=c*o,l&&el(e,e,l),e[4]+=i+f,e[5]+=n+h,e},r.initDefaultProps=function(){var t=r.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),r}(),dn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function cy(r,t){for(var e=0;e<dn.length;e++){var i=dn[e];r[i]=t[i]}}const il=vy;var Ef={};function Wt(r,t){t=t||kr;var e=Ef[t];e||(e=Ef[t]=new Mn(500));var i=e.get(r);return i==null&&(i=_i.measureText(r,t).width,e.put(r,i)),i}function Of(r,t,e,i){var n=Wt(r,t),a=nl(t),o=$i(0,n,e),s=ei(0,a,i),u=new J(o,s,n,a);return u}function Vc(r,t,e,i){var n=((r||"")+"").split(`
`),a=n.length;if(a===1)return Of(n[0],t,e,i);for(var o=new J(0,0,0,0),s=0;s<n.length;s++){var u=Of(n[s],t,e,i);s===0?o.copy(u):o.union(u)}return o}function $i(r,t,e){return e==="right"?r-=t:e==="center"&&(r-=t/2),r}function ei(r,t,e){return e==="middle"?r-=t/2:e==="bottom"&&(r-=t),r}function nl(r){return Wt("国",r)}function Qe(r,t){return typeof r=="string"?r.lastIndexOf("%")>=0?parseFloat(r)/100*t:parseFloat(r):r}function Uc(r,t,e){var i=t.position||"inside",n=t.distance!=null?t.distance:5,a=e.height,o=e.width,s=a/2,u=e.x,l=e.y,f="left",h="top";if(i instanceof Array)u+=Qe(i[0],e.width),l+=Qe(i[1],e.height),f=null,h=null;else switch(i){case"left":u-=n,l+=s,f="right",h="middle";break;case"right":u+=n+o,l+=s,h="middle";break;case"top":u+=o/2,l-=n,f="center",h="bottom";break;case"bottom":u+=o/2,l+=a+n,f="center";break;case"inside":u+=o/2,l+=s,f="center",h="middle";break;case"insideLeft":u+=n,l+=s,h="middle";break;case"insideRight":u+=o-n,l+=s,f="right",h="middle";break;case"insideTop":u+=o/2,l+=n,f="center";break;case"insideBottom":u+=o/2,l+=a-n,f="center",h="bottom";break;case"insideTopLeft":u+=n,l+=n;break;case"insideTopRight":u+=o-n,l+=n,f="right";break;case"insideBottomLeft":u+=n,l+=a-n,h="bottom";break;case"insideBottomRight":u+=o-n,l+=a-n,f="right",h="bottom";break}return r=r||{},r.x=u,r.y=l,r.align=f,r.verticalAlign=h,r}var Qo="__zr_normal__",Jo=dn.concat(["ignore"]),dy=Ke(dn,function(r,t){return r[t]=!0,r},{ignore:!1}),Ur={},py=new J(0,0,0,0),uo=function(){function r(t){this.id=wc(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,i){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,n=i.local,a=e.innerTransformable,o=void 0,s=void 0,u=!1;a.parent=n?this:null;var l=!1;if(a.copyTransform(e),i.position!=null){var f=py;i.layoutRect?f.copy(i.layoutRect):f.copy(this.getBoundingRect()),n||f.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Ur,i,f):Uc(Ur,i,f),a.x=Ur.x,a.y=Ur.y,o=Ur.align,s=Ur.verticalAlign;var h=i.origin;if(h&&i.rotation!=null){var c=void 0,v=void 0;h==="center"?(c=f.width*.5,v=f.height*.5):(c=Qe(h[0],f.width),v=Qe(h[1],f.height)),l=!0,a.originX=-a.x+c+(n?0:f.x),a.originY=-a.y+v+(n?0:f.y)}}i.rotation!=null&&(a.rotation=i.rotation);var d=i.offset;d&&(a.x+=d[0],a.y+=d[1],l||(a.originX=-d[0],a.originY=-d[1]));var _=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,p=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),g=void 0,y=void 0,m=void 0;_&&this.canBeInsideText()?(g=i.insideFill,y=i.insideStroke,(g==null||g==="auto")&&(g=this.getInsideTextFill()),(y==null||y==="auto")&&(y=this.getInsideTextStroke(g),m=!0)):(g=i.outsideFill,y=i.outsideStroke,(g==null||g==="auto")&&(g=this.getOutsideFill()),(y==null||y==="auto")&&(y=this.getOutsideStroke(g),m=!0)),g=g||"#000",(g!==p.fill||y!==p.stroke||m!==p.autoStroke||o!==p.align||s!==p.verticalAlign)&&(u=!0,p.fill=g,p.stroke=y,p.autoStroke=m,p.align=o,p.verticalAlign=s,e.setDefaultTextStyle(p)),e.__dirty|=Gt,u&&e.dirtyStyle(!0)}},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?fu:lu},r.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),i=typeof e=="string"&&se(e);i||(i=[255,255,255,1]);for(var n=i[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*n+(a?0:255)*(1-n);return i[3]=1,mi(i,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){t==="textConfig"?this.setTextConfig(e):t==="textContent"?this.setTextContent(e):t==="clipPath"?this.setClipPath(e):t==="extra"?(this.extra=this.extra||{},O(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if(typeof t=="string")this.attrKV(t,e);else if(H(t))for(var i=t,n=lt(i),a=0;a<n.length;a++){var o=n[a];this.attrKV(o,t[o])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,i=0;i<this.animators.length;i++){var n=this.animators[i],a=n.__fromStateTransition;if(!(n.getLoop()||a&&a!==Qo)){var o=n.targetName,s=o?e[o]:e;n.saveTo(s)}}},r.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Jo)},r.prototype._savePrimaryToNormal=function(t,e,i){for(var n=0;n<i.length;n++){var a=i[n];t[a]!=null&&!(a in e)&&(e[a]=this[a])}},r.prototype.hasState=function(){return this.currentStates.length>0},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Qo,!1,t)},r.prototype.useState=function(t,e,i,n){var a=t===Qo,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,u=this.stateTransition;if(!(nt(s,t)>=0&&(e||s.length===1))){var l;if(this.stateProxy&&!a&&(l=this.stateProxy(t)),l||(l=this.states&&this.states[t]),!l&&!a){Qu("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(l);var f=!!(l&&l.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,l,this._normalState,e,!i&&!this.__inHover&&u&&u.duration>0,u);var h=this._textContent,c=this._textGuide;return h&&h.useState(t,e,i,f),c&&c.useState(t,e,i,f),a?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Gt),l}}},r.prototype.useStates=function(t,e,i){if(!t.length)this.clearStates();else{var n=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var u=0;u<o;u++)if(t[u]!==a[u]){s=!1;break}}if(s)return;for(var u=0;u<o;u++){var l=t[u],f=void 0;this.stateProxy&&(f=this.stateProxy(l,t)),f||(f=this.states[l]),f&&n.push(f)}var h=n[o-1],c=!!(h&&h.hoverLayer||i);c&&this._toggleHoverLayerFlag(!0);var v=this._mergeStates(n),d=this.stateTransition;this.saveCurrentToNormalState(v),this._applyStateObj(t.join(","),v,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var _=this._textContent,p=this._textGuide;_&&_.useStates(t,e,c),p&&p.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Gt)}},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e=nt(this.currentStates,t);if(e>=0){var i=this.currentStates.slice();i.splice(e,1),this.useStates(i)}},r.prototype.replaceState=function(t,e,i){var n=this.currentStates.slice(),a=nt(n,t),o=nt(n,e)>=0;a>=0?o?n.splice(a,1):n[a]=e:i&&!o&&n.push(e),this.useStates(n)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e={},i,n=0;n<t.length;n++){var a=t[n];O(e,a),a.textConfig&&(i=i||{},O(i,a.textConfig))}return i&&(e.textConfig=i),e},r.prototype._applyStateObj=function(t,e,i,n,a,o){var s=!(e&&n);e&&e.textConfig?(this.textConfig=O({},n?this.textConfig:i.textConfig),O(this.textConfig,e.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var u={},l=!1,f=0;f<Jo.length;f++){var h=Jo[f],c=a&&dy[h];e&&e[h]!=null?c?(l=!0,u[h]=e[h]):this[h]=e[h]:s&&i[h]!=null&&(c?(l=!0,u[h]=i[h]):this[h]=i[h])}if(!a)for(var f=0;f<this.animators.length;f++){var v=this.animators[f],d=v.targetName;v.getLoop()||v.__changeFinalValue(d?(e||i)[d]:e||i)}l&&this._transitionState(t,u,o)},r.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new il,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),O(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=Gt;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,i=this._textGuide;e&&(e.__inHover=t),i&&(i.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,i){var n=t?this[t]:this,a=new rl(n,e,i);return t&&(a.targetName=t),this.addAnimator(a,t),a},r.prototype.addAnimator=function(t,e){var i=this.__zr,n=this;t.during(function(){n.updateDuringAnimation(e)}).done(function(){var a=n.animators,o=nt(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var i=this.animators,n=i.length,a=[],o=0;o<n;o++){var s=i[o];!t||t===s.scope?s.stop(e):a.push(s)}return this.animators=a,this},r.prototype.animateTo=function(t,e,i){jo(this,t,e,i)},r.prototype.animateFrom=function(t,e,i){jo(this,t,e,i,!0)},r.prototype._transitionState=function(t,e,i,n){for(var a=jo(this,e,i,n),o=0;o<a.length;o++)a[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=function(){var t=r.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=Gt;function e(i,n,a,o){Object.defineProperty(t,i,{get:function(){if(!this[n]){var u=this[n]=[];s(this,u)}return this[n]},set:function(u){this[a]=u[0],this[o]=u[1],this[n]=u,s(this,u)}});function s(u,l){Object.defineProperty(l,0,{get:function(){return u[a]},set:function(f){u[a]=f}}),Object.defineProperty(l,1,{get:function(){return u[o]},set:function(f){u[o]=f}})}}Object.defineProperty&&(e("position","_legacyPos","x","y"),e("scale","_legacyScale","scaleX","scaleY"),e("origin","_legacyOrigin","originX","originY"))}(),r}();ke(uo,Ce);ke(uo,il);function jo(r,t,e,i,n){e=e||{};var a=[];Yc(r,"",r,t,e,i,a,n);var o=a.length,s=!1,u=e.done,l=e.aborted,f=function(){s=!0,o--,o<=0&&(s?u&&u():l&&l())},h=function(){o--,o<=0&&(s?u&&u():l&&l())};o||u&&u(),a.length>0&&e.during&&a[0].during(function(d,_){e.during(_)});for(var c=0;c<a.length;c++){var v=a[c];f&&v.done(f),h&&v.aborted(h),e.force&&v.duration(e.duration),v.start(e.easing)}return a}function ts(r,t,e){for(var i=0;i<e;i++)r[i]=t[i]}function gy(r){return Ft(r[0])}function _y(r,t,e){if(Ft(t[e]))if(Ft(r[e])||(r[e]=[]),Nt(t[e])){var i=t[e].length;r[e].length!==i&&(r[e]=new t[e].constructor(i),ts(r[e],t[e],i))}else{var n=t[e],a=r[e],o=n.length;if(gy(n))for(var s=n[0].length,u=0;u<o;u++)a[u]?ts(a[u],n[u],s):a[u]=Array.prototype.slice.call(n[u]);else ts(a,n,o);a.length=n.length}else r[e]=t[e]}function yy(r,t){return r===t||Ft(r)&&Ft(t)&&my(r,t)}function my(r,t){var e=r.length;if(e!==t.length)return!1;for(var i=0;i<e;i++)if(r[i]!==t[i])return!1;return!0}function Yc(r,t,e,i,n,a,o,s){for(var u=lt(i),l=n.duration,f=n.delay,h=n.additive,c=n.setToFinal,v=!H(a),d=r.animators,_=[],p=0;p<u.length;p++){var g=u[p],y=i[g];if(y!=null&&e[g]!=null&&(v||a[g]))if(H(y)&&!Ft(y)&&!oo(y)){if(t){s||(e[g]=y,r.updateDuringAnimation(t));continue}Yc(r,g,e[g],y,n,a&&a[g],o,s)}else _.push(g);else s||(e[g]=y,r.updateDuringAnimation(t),_.push(g))}var m=_.length;if(!h&&m)for(var w=0;w<d.length;w++){var b=d[w];if(b.targetName===t){var S=b.stopTracks(_);if(S){var x=nt(d,b);d.splice(x,1)}}}if(n.force||(_=Et(_,function(T){return!yy(i[T],e[T])}),m=_.length),m>0||n.force&&!o.length){var C=void 0,M=void 0,A=void 0;if(s){M={},c&&(C={});for(var w=0;w<m;w++){var g=_[w];M[g]=e[g],c?C[g]=i[g]:e[g]=i[g]}}else if(c){A={};for(var w=0;w<m;w++){var g=_[w];A[g]=ba(e[g]),_y(e,i,g)}}var b=new rl(e,!1,!1,h?Et(d,function(P){return P.targetName===t}):null);b.targetName=t,n.scope&&(b.scope=n.scope),c&&C&&b.whenWithKeys(0,C,_),A&&b.whenWithKeys(0,A,_),b.whenWithKeys(l??500,s?M:i,_).delay(f||0),r.addAnimator(b,t),o.push(b)}}var fe=function(r){F(t,r);function t(e){var i=r.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(e){return this._children[e]},t.prototype.childOfName=function(e){for(var i=this._children,n=0;n<i.length;n++)if(i[n].name===e)return i[n]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(e){return e&&e!==this&&e.parent!==this&&(this._children.push(e),this._doAdd(e)),this},t.prototype.addBefore=function(e,i){if(e&&e!==this&&e.parent!==this&&i&&i.parent===this){var n=this._children,a=n.indexOf(i);a>=0&&(n.splice(a,0,e),this._doAdd(e))}return this},t.prototype.replace=function(e,i){var n=nt(this._children,e);return n>=0&&this.replaceAt(i,n),this},t.prototype.replaceAt=function(e,i){var n=this._children,a=n[i];if(e&&e!==this&&e.parent!==this&&e!==a){n[i]=e,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(e)}return this},t.prototype._doAdd=function(e){e.parent&&e.parent.remove(e),e.parent=this;var i=this.__zr;i&&i!==e.__zr&&e.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(e){var i=this.__zr,n=this._children,a=nt(n,e);return a<0?this:(n.splice(a,1),e.parent=null,i&&e.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var e=this._children,i=this.__zr,n=0;n<e.length;n++){var a=e[n];i&&a.removeSelfFromZr(i),a.parent=null}return e.length=0,this},t.prototype.eachChild=function(e,i){for(var n=this._children,a=0;a<n.length;a++){var o=n[a];e.call(i,o,a)}return this},t.prototype.traverse=function(e,i){for(var n=0;n<this._children.length;n++){var a=this._children[n],o=e.call(i,a);a.isGroup&&!o&&a.traverse(e,i)}return this},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.addSelfToZr(e)}},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.removeSelfFromZr(e)}},t.prototype.getBoundingRect=function(e){for(var i=new J(0,0,0,0),n=e||this._children,a=[],o=null,s=0;s<n.length;s++){var u=n[s];if(!(u.ignore||u.invisible)){var l=u.getBoundingRect(),f=u.getLocalTransform(a);f?(J.applyTransform(i,l,f),o=o||i.clone(),o.union(i)):(o=o||l.clone(),o.union(l))}}return o||i},t}(uo);fe.prototype.type="group";/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Da={},Xc={};function wy(r){delete Xc[r]}function Sy(r){if(!r)return!1;if(typeof r=="string")return Ga(r,1)<uu;if(r.colorStops){for(var t=r.colorStops,e=0,i=t.length,n=0;n<i;n++)e+=Ga(t[n].color,1);return e/=i,e<uu}return!1}var by=function(){function r(t,e,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=e,this.id=t;var a=new z_,o=i.renderer||"canvas";Da[o]||(o=lt(Da)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new Da[o](e,a,i,t),u=i.ssr||s.ssrOnly;this.storage=a,this.painter=s;var l=!W.node&&!W.worker&&!u?new fy(s.getViewportRoot(),s.root):null,f=i.useCoarsePointer,h=f==null||f==="auto"?W.touchEventsSupported:!!f,c=44,v;h&&(v=X(i.pointerSize,c)),this.handler=new Pc(a,s,l,s.root,v),this.animation=new iy({stage:{update:u?null:function(){return n._flush(!0)}}}),u||this.animation.start()}return r.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},r.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=Sy(t))},r.prototype.getBackgroundColor=function(){return this._backgroundColor},r.prototype.setDarkMode=function(t){this._darkMode=t},r.prototype.isDarkMode=function(){return this._darkMode},r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},r.prototype.flush=function(){this._disposed||this._flush(!1)},r.prototype._flush=function(t){var e,i=ni();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var n=ni();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:n-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},r.prototype.refreshHover=function(){this._needsRefreshHover=!0},r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},r.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},r.prototype.on=function(t,e,i){return this._disposed||this.handler.on(t,e,i),this},r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof fe&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},r.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,wy(this.id))},r}();function kf(r,t){var e=new by(wc(),r,t);return Xc[e.id]=e,e}function Ty(r,t){Da[r]=t}var Bf=1e-4,$c=20;function xy(r){return r.replace(/^\s+|\s+$/g,"")}function fC(r,t,e,i){var n=t[0],a=t[1],o=e[0],s=e[1],u=a-n,l=s-o;if(u===0)return l===0?o:(o+s)/2;if(i)if(u>0){if(r<=n)return o;if(r>=a)return s}else{if(r>=n)return o;if(r<=a)return s}else{if(r===n)return o;if(r===a)return s}return(r-n)/u*l+o}function At(r,t){switch(r){case"center":case"middle":r="50%";break;case"left":case"top":r="0%";break;case"right":case"bottom":r="100%";break}return B(r)?xy(r).match(/%$/)?parseFloat(r)/100*t:parseFloat(r):r==null?NaN:+r}function ue(r,t,e){return t==null&&(t=10),t=Math.min(Math.max(0,t),$c),r=(+r).toFixed(t),e?r:+r}function hC(r){return r.sort(function(t,e){return t-e}),r}function Re(r){if(r=+r,isNaN(r))return 0;if(r>1e-14){for(var t=1,e=0;e<15;e++,t*=10)if(Math.round(r*t)/t===r)return e}return Cy(r)}function Cy(r){var t=r.toString().toLowerCase(),e=t.indexOf("e"),i=e>0?+t.slice(e+1):0,n=e>0?e:t.length,a=t.indexOf("."),o=a<0?0:n-1-a;return Math.max(0,o-i)}function vC(r,t){var e=Math.log,i=Math.LN10,n=Math.floor(e(r[1]-r[0])/i),a=Math.round(e(Math.abs(t[1]-t[0]))/i),o=Math.min(Math.max(-n+a,0),20);return isFinite(o)?o:20}function cC(r,t){var e=Ke(r,function(v,d){return v+(isNaN(d)?0:d)},0);if(e===0)return[];for(var i=Math.pow(10,t),n=Y(r,function(v){return(isNaN(v)?0:v)/e*i*100}),a=i*100,o=Y(n,function(v){return Math.floor(v)}),s=Ke(o,function(v,d){return v+d},0),u=Y(n,function(v,d){return v-o[d]});s<a;){for(var l=Number.NEGATIVE_INFINITY,f=null,h=0,c=u.length;h<c;++h)u[h]>l&&(l=u[h],f=h);++o[f],u[f]=0,++s}return Y(o,function(v){return v/i})}function Dy(r,t){var e=Math.max(Re(r),Re(t)),i=r+t;return e>$c?i:ue(i,e)}function qc(r){var t=Math.PI*2;return(r%t+t)%t}function Va(r){return r>-Bf&&r<Bf}var My=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Oe(r){if(r instanceof Date)return r;if(B(r)){var t=My.exec(r);if(!t)return new Date(NaN);if(t[8]){var e=+t[4]||0;return t[8].toUpperCase()!=="Z"&&(e-=+t[8].slice(0,3)),new Date(Date.UTC(+t[1],+(t[2]||1)-1,+t[3]||1,e,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0))}else return new Date(+t[1],+(t[2]||1)-1,+t[3]||1,+t[4]||0,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0)}else if(r==null)return new Date(NaN);return new Date(Math.round(r))}function Ay(r){return Math.pow(10,al(r))}function al(r){if(r===0)return 0;var t=Math.floor(Math.log(r)/Math.LN10);return r/Math.pow(10,t)>=10&&t++,t}function Zc(r,t){var e=al(r),i=Math.pow(10,e),n=r/i,a;return n<1.5?a=1:n<2.5?a=2:n<4?a=3:n<7?a=5:a=10,r=a*i,e>=-20?+r.toFixed(e<0?-e:0):r}function dC(r){r.sort(function(u,l){return s(u,l,0)?-1:1});for(var t=-1/0,e=1,i=0;i<r.length;){for(var n=r[i].interval,a=r[i].close,o=0;o<2;o++)n[o]<=t&&(n[o]=t,a[o]=o?1:1-e),t=n[o],e=a[o];n[0]===n[1]&&a[0]*a[1]!==1?r.splice(i,1):i++}return r;function s(u,l,f){return u.interval[f]<l.interval[f]||u.interval[f]===l.interval[f]&&(u.close[f]-l.close[f]===(f?-1:1)||!f&&s(u,l,1))}}function Ua(r){var t=parseFloat(r);return t==r&&(t!==0||!B(r)||r.indexOf("x")<=0)?t:NaN}function Py(r){return!isNaN(Ua(r))}function Kc(){return Math.round(Math.random()*9)}function Qc(r,t){return t===0?r:Qc(t,r%t)}function Ff(r,t){return r==null?t:t==null?r:r*t/Qc(r,t)}var Ly="[ECharts] ",Nf={},Iy=typeof console<"u"&&console.warn&&console.log;function Ry(r,t,e){if(Iy){{if(Nf[t])return;Nf[t]=!0}console[r](Ly+t)}}function pC(r,t){Ry("warn",r)}function Ot(r){throw new Error(r)}function zf(r,t,e){return(t-r)*e+r}var Jc="series\0",jc="\0_ec_\0";function Tt(r){return r instanceof Array?r:r==null?[]:[r]}function Hf(r,t,e){if(r){r[t]=r[t]||{},r.emphasis=r.emphasis||{},r.emphasis[t]=r.emphasis[t]||{};for(var i=0,n=e.length;i<n;i++){var a=e[i];!r.emphasis[t].hasOwnProperty(a)&&r[t].hasOwnProperty(a)&&(r.emphasis[t][a]=r[t][a])}}}var Gf=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function lo(r){return H(r)&&!k(r)&&!(r instanceof Date)?r.value:r}function Ey(r){return H(r)&&!(r instanceof Array)}function Oy(r,t,e){var i=e==="normalMerge",n=e==="replaceMerge",a=e==="replaceAll";r=r||[],t=(t||[]).slice();var o=$();D(t,function(u,l){if(!H(u)){t[l]=null;return}});var s=ky(r,o,e);return(i||n)&&By(s,r,o,t),i&&Fy(s,t),i||n?Ny(s,t,n):a&&zy(s,t),Hy(s),s}function ky(r,t,e){var i=[];if(e==="replaceAll")return i;for(var n=0;n<r.length;n++){var a=r[n];a&&a.id!=null&&t.set(a.id,n),i.push({existing:e==="replaceMerge"||pn(a)?null:a,newOption:null,keyInfo:null,brandNew:null})}return i}function By(r,t,e,i){D(i,function(n,a){if(!(!n||n.id==null)){var o=tn(n.id),s=e.get(o);if(s!=null){var u=r[s];xe(!u.newOption,'Duplicated option on id "'+o+'".'),u.newOption=n,u.existing=t[s],i[a]=null}}})}function Fy(r,t){D(t,function(e,i){if(!(!e||e.name==null))for(var n=0;n<r.length;n++){var a=r[n].existing;if(!r[n].newOption&&a&&(a.id==null||e.id==null)&&!pn(e)&&!pn(a)&&td("name",a,e)){r[n].newOption=e,t[i]=null;return}}})}function Ny(r,t,e){D(t,function(i){if(i){for(var n,a=0;(n=r[a])&&(n.newOption||pn(n.existing)||n.existing&&i.id!=null&&!td("id",i,n.existing));)a++;n?(n.newOption=i,n.brandNew=e):r.push({newOption:i,brandNew:e,existing:null,keyInfo:null}),a++}})}function zy(r,t){D(t,function(e){r.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})})}function Hy(r){var t=$();D(r,function(e){var i=e.existing;i&&t.set(i.id,e)}),D(r,function(e){var i=e.newOption;xe(!i||i.id==null||!t.get(i.id)||t.get(i.id)===e,"id duplicates: "+(i&&i.id)),i&&i.id!=null&&t.set(i.id,e),!e.keyInfo&&(e.keyInfo={})}),D(r,function(e,i){var n=e.existing,a=e.newOption,o=e.keyInfo;if(H(a)){if(o.name=a.name!=null?tn(a.name):n?n.name:Jc+i,n)o.id=tn(n.id);else if(a.id!=null)o.id=tn(a.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(t.get(o.id))}t.set(o.id,e)}})}function td(r,t,e){var i=le(t[r],null),n=le(e[r],null);return i!=null&&n!=null&&i===n}function tn(r){return le(r,"")}function le(r,t){return r==null?t:B(r)?r:ht(r)||Zs(r)?r+"":t}function ol(r){var t=r.name;return!!(t&&t.indexOf(Jc))}function pn(r){return r&&r.id!=null&&tn(r.id).indexOf(jc)===0}function gC(r){return jc+r}function Gy(r,t,e){D(r,function(i){var n=i.newOption;H(n)&&(i.keyInfo.mainType=t,i.keyInfo.subType=Wy(t,n,i.existing,e))})}function Wy(r,t,e,i){var n=t.type?t.type:e?e.subType:i.determineSubType(r,t);return n}function _C(r,t){var e={},i={};return n(r||[],e),n(t||[],i,e),[a(e),a(i)];function n(o,s,u){for(var l=0,f=o.length;l<f;l++){var h=le(o[l].seriesId,null);if(h==null)return;for(var c=Tt(o[l].dataIndex),v=u&&u[h],d=0,_=c.length;d<_;d++){var p=c[d];v&&v[p]?v[p]=null:(s[h]||(s[h]={}))[p]=1}}}function a(o,s){var u=[];for(var l in o)if(o.hasOwnProperty(l)&&o[l]!=null)if(s)u.push(+l);else{var f=a(o[l],!0);f.length&&u.push({seriesId:l,dataIndex:f})}return u}}function An(r,t){if(t.dataIndexInside!=null)return t.dataIndexInside;if(t.dataIndex!=null)return k(t.dataIndex)?Y(t.dataIndex,function(e){return r.indexOfRawIndex(e)}):r.indexOfRawIndex(t.dataIndex);if(t.name!=null)return k(t.name)?Y(t.name,function(e){return r.indexOfName(e)}):r.indexOfName(t.name)}function yt(){var r="__ec_inner_"+Vy++;return function(t){return t[r]||(t[r]={})}}var Vy=Kc();function es(r,t,e){var i=sl(t,e),n=i.mainTypeSpecified,a=i.queryOptionMap,o=i.others,s=o,u=e?e.defaultMainType:null;return!n&&u&&a.set(u,{}),a.each(function(l,f){var h=Pn(r,f,l,{useDefault:u===f,enableAll:e&&e.enableAll!=null?e.enableAll:!0,enableNone:e&&e.enableNone!=null?e.enableNone:!0});s[f+"Models"]=h.models,s[f+"Model"]=h.models[0]}),s}function sl(r,t){var e;if(B(r)){var i={};i[r+"Index"]=0,e=i}else e=r;var n=$(),a={},o=!1;return D(e,function(s,u){if(u==="dataIndex"||u==="dataIndexInside"){a[u]=s;return}var l=u.match(/^(\w+)(Index|Id|Name)$/)||[],f=l[1],h=(l[2]||"").toLowerCase();if(!(!f||!h||t&&t.includeMainTypes&&nt(t.includeMainTypes,f)<0)){o=o||!!f;var c=n.get(f)||n.set(f,{});c[h]=s}}),{mainTypeSpecified:o,queryOptionMap:n,others:a}}var fo={useDefault:!0,enableAll:!1,enableNone:!1},yC={useDefault:!1,enableAll:!0,enableNone:!0};function Pn(r,t,e,i){i=i||fo;var n=e.index,a=e.id,o=e.name,s={models:null,specified:n!=null||a!=null||o!=null};if(!s.specified){var u=void 0;return s.models=i.useDefault&&(u=r.getComponent(t))?[u]:[],s}return n==="none"||n===!1?(xe(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(n==="all"&&(xe(i.enableAll,'`"all"` is not a valid value on index option.'),n=a=o=null),s.models=r.queryComponents({mainType:t,index:n,id:a,name:o}),s)}function ed(r,t,e){r.setAttribute?r.setAttribute(t,e):r[t]=e}function Uy(r,t){return r.getAttribute?r.getAttribute(t):r[t]}function Yy(r){return r==="auto"?W.domSupported?"html":"richText":r||"html"}function Xy(r,t,e,i,n){var a=t==null||t==="auto";if(i==null)return i;if(ht(i)){var o=zf(e||0,i,n);return ue(o,a?Math.max(Re(e||0),Re(i)):t)}else{if(B(i))return n<1?e:i;for(var s=[],u=e,l=i,f=Math.max(u?u.length:0,l.length),h=0;h<f;++h){var c=r.getDimensionInfo(h);if(c&&c.type==="ordinal")s[h]=(n<1&&u?u:l)[h];else{var v=u&&u[h]?u[h]:0,d=l[h],o=zf(v,d,n);s[h]=ue(o,a?Math.max(Re(v),Re(d)):t)}}return s}}var $y=".",lr="___EC__COMPONENT__CONTAINER___",rd="___EC__EXTENDED_CLASS___";function Se(r){var t={main:"",sub:""};if(r){var e=r.split($y);t.main=e[0]||"",t.sub=e[1]||""}return t}function qy(r){xe(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal')}function Zy(r){return!!(r&&r[rd])}function ul(r,t){r.$constructor=r,r.extend=function(e){var i=this,n;return Ky(i)?n=function(a){F(o,a);function o(){return a.apply(this,arguments)||this}return o}(i):(n=function(){(e.$constructor||i).apply(this,arguments)},t_(n,this)),O(n.prototype,e),n[rd]=!0,n.extend=this.extend,n.superCall=jy,n.superApply=t0,n.superClass=i,n}}function Ky(r){return Z(r)&&/^class\s/.test(Function.prototype.toString.call(r))}function id(r,t){r.extend=t.extend}var Qy=Math.round(Math.random()*10);function Jy(r){var t=["__\0is_clz",Qy++].join("_");r.prototype[t]=!0,r.isInstance=function(e){return!!(e&&e[t])}}function jy(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return this.superClass.prototype[t].apply(r,e)}function t0(r,t,e){return this.superClass.prototype[t].apply(r,e)}function ho(r){var t={};r.registerClass=function(i){var n=i.type||i.prototype.type;if(n){qy(n),i.prototype.type=n;var a=Se(n);if(!a.sub)t[a.main]=i;else if(a.sub!==lr){var o=e(a);o[a.sub]=i}}return i},r.getClass=function(i,n,a){var o=t[i];if(o&&o[lr]&&(o=n?o[n]:null),a&&!o)throw new Error(n?"Component "+i+"."+(n||"")+" is used but not imported.":i+".type should be specified.");return o},r.getClassesByMainType=function(i){var n=Se(i),a=[],o=t[n.main];return o&&o[lr]?D(o,function(s,u){u!==lr&&a.push(s)}):a.push(o),a},r.hasClass=function(i){var n=Se(i);return!!t[n.main]},r.getAllClassMainTypes=function(){var i=[];return D(t,function(n,a){i.push(a)}),i},r.hasSubTypes=function(i){var n=Se(i),a=t[n.main];return a&&a[lr]};function e(i){var n=t[i.main];return(!n||!n[lr])&&(n=t[i.main]={},n[lr]=!0),n}}function gn(r,t){for(var e=0;e<r.length;e++)r[e][1]||(r[e][1]=r[e][0]);return t=t||!1,function(i,n,a){for(var o={},s=0;s<r.length;s++){var u=r[s][1];if(!(n&&nt(n,u)>=0||a&&nt(a,u)<0)){var l=i.getShallow(u,t);l!=null&&(o[r[s][0]]=l)}}return o}}var e0=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],r0=gn(e0),i0=function(){function r(){}return r.prototype.getAreaStyle=function(t,e){return r0(this,t,e)},r}(),hu=new Mn(50);function n0(r){if(typeof r=="string"){var t=hu.get(r);return t&&t.image}else return r}function nd(r,t,e,i,n){if(r)if(typeof r=="string"){if(t&&t.__zrImageSrc===r||!e)return t;var a=hu.get(r),o={hostEl:e,cb:i,cbPayload:n};return a?(t=a.image,!vo(t)&&a.pending.push(o)):(t=_i.loadImage(r,Wf,Wf),t.__zrImageSrc=r,hu.put(r,t.__cachedImgObj={image:t,pending:[o]})),t}else return r;else return t}function Wf(){var r=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<r.pending.length;t++){var e=r.pending[t],i=e.cb;i&&i(this,e.cbPayload),e.hostEl.dirty()}r.pending.length=0}function vo(r){return r&&r.width&&r.height}var rs=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function a0(r,t,e,i,n){if(!t)return"";var a=(r+"").split(`
`);n=ad(t,e,i,n);for(var o=0,s=a.length;o<s;o++)a[o]=od(a[o],n);return a.join(`
`)}function ad(r,t,e,i){i=i||{};var n=O({},i);n.font=t,e=X(e,"..."),n.maxIterations=X(i.maxIterations,2);var a=n.minChar=X(i.minChar,0);n.cnCharWidth=Wt("国",t);var o=n.ascCharWidth=Wt("a",t);n.placeholder=X(i.placeholder,"");for(var s=r=Math.max(0,r-1),u=0;u<a&&s>=o;u++)s-=o;var l=Wt(e,t);return l>s&&(e="",l=0),s=r-l,n.ellipsis=e,n.ellipsisWidth=l,n.contentWidth=s,n.containerWidth=r,n}function od(r,t){var e=t.containerWidth,i=t.font,n=t.contentWidth;if(!e)return"";var a=Wt(r,i);if(a<=e)return r;for(var o=0;;o++){if(a<=n||o>=t.maxIterations){r+=t.ellipsis;break}var s=o===0?o0(r,n,t.ascCharWidth,t.cnCharWidth):a>0?Math.floor(r.length*n/a):0;r=r.substr(0,s),a=Wt(r,i)}return r===""&&(r=t.placeholder),r}function o0(r,t,e,i){for(var n=0,a=0,o=r.length;a<o&&n<t;a++){var s=r.charCodeAt(a);n+=0<=s&&s<=127?e:i}return a}function s0(r,t){r!=null&&(r+="");var e=t.overflow,i=t.padding,n=t.font,a=e==="truncate",o=nl(n),s=X(t.lineHeight,o),u=!!t.backgroundColor,l=t.lineOverflow==="truncate",f=t.width,h;f!=null&&(e==="break"||e==="breakAll")?h=r?sd(r,t.font,f,e==="breakAll",0).lines:[]:h=r?r.split(`
`):[];var c=h.length*s,v=X(t.height,c);if(c>v&&l){var d=Math.floor(v/s);h=h.slice(0,d)}if(r&&a&&f!=null)for(var _=ad(f,n,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),p=0;p<h.length;p++)h[p]=od(h[p],_);for(var g=v,y=0,p=0;p<h.length;p++)y=Math.max(Wt(h[p],n),y);f==null&&(f=y);var m=y;return i&&(g+=i[0]+i[2],m+=i[1]+i[3],f+=i[1]+i[3]),u&&(m=f),{lines:h,height:v,outerWidth:m,outerHeight:g,lineHeight:s,calculatedLineHeight:o,contentWidth:y,contentHeight:c,width:f}}var u0=function(){function r(){}return r}(),Vf=function(){function r(t){this.tokens=[],t&&(this.tokens=t)}return r}(),l0=function(){function r(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]}return r}();function f0(r,t){var e=new l0;if(r!=null&&(r+=""),!r)return e;for(var i=t.width,n=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:a==="breakAll"}:null,s=rs.lastIndex=0,u;(u=rs.exec(r))!=null;){var l=u.index;l>s&&is(e,r.substring(s,l),t,o),is(e,u[2],t,o,u[1]),s=rs.lastIndex}s<r.length&&is(e,r.substring(s,r.length),t,o);var f=[],h=0,c=0,v=t.padding,d=a==="truncate",_=t.lineOverflow==="truncate";function p(N,z,G){N.width=z,N.lineHeight=G,h+=G,c=Math.max(c,z)}t:for(var g=0;g<e.lines.length;g++){for(var y=e.lines[g],m=0,w=0,b=0;b<y.tokens.length;b++){var S=y.tokens[b],x=S.styleName&&t.rich[S.styleName]||{},C=S.textPadding=x.padding,M=C?C[1]+C[3]:0,A=S.font=x.font||t.font;S.contentHeight=nl(A);var T=X(x.height,S.contentHeight);if(S.innerHeight=T,C&&(T+=C[0]+C[2]),S.height=T,S.lineHeight=wa(x.lineHeight,t.lineHeight,T),S.align=x&&x.align||t.align,S.verticalAlign=x&&x.verticalAlign||"middle",_&&n!=null&&h+S.lineHeight>n){b>0?(y.tokens=y.tokens.slice(0,b),p(y,w,m),e.lines=e.lines.slice(0,g+1)):e.lines=e.lines.slice(0,g);break t}var P=x.width,L=P==null||P==="auto";if(typeof P=="string"&&P.charAt(P.length-1)==="%")S.percentWidth=P,f.push(S),S.contentWidth=Wt(S.text,A);else{if(L){var I=x.backgroundColor,R=I&&I.image;R&&(R=n0(R),vo(R)&&(S.width=Math.max(S.width,R.width*T/R.height)))}var E=d&&i!=null?i-w:null;E!=null&&E<S.width?!L||E<M?(S.text="",S.width=S.contentWidth=0):(S.text=a0(S.text,E-M,A,t.ellipsis,{minChar:t.truncateMinChar}),S.width=S.contentWidth=Wt(S.text,A)):S.contentWidth=Wt(S.text,A)}S.width+=M,w+=S.width,x&&(m=Math.max(m,S.lineHeight))}p(y,w,m)}e.outerWidth=e.width=X(i,c),e.outerHeight=e.height=X(n,h),e.contentHeight=h,e.contentWidth=c,v&&(e.outerWidth+=v[1]+v[3],e.outerHeight+=v[0]+v[2]);for(var g=0;g<f.length;g++){var S=f[g],U=S.percentWidth;S.width=parseInt(U,10)/100*e.width}return e}function is(r,t,e,i,n){var a=t==="",o=n&&e.rich[n]||{},s=r.lines,u=o.font||e.font,l=!1,f,h;if(i){var c=o.padding,v=c?c[1]+c[3]:0;if(o.width!=null&&o.width!=="auto"){var d=Qe(o.width,i.width)+v;s.length>0&&d+i.accumWidth>i.width&&(f=t.split(`
`),l=!0),i.accumWidth=d}else{var _=sd(t,u,i.width,i.breakAll,i.accumWidth);i.accumWidth=_.accumWidth+v,h=_.linesWidths,f=_.lines}}else f=t.split(`
`);for(var p=0;p<f.length;p++){var g=f[p],y=new u0;if(y.styleName=n,y.text=g,y.isLineHolder=!g&&!a,typeof o.width=="number"?y.width=o.width:y.width=h?h[p]:Wt(g,u),!p&&!l){var m=(s[s.length-1]||(s[0]=new Vf)).tokens,w=m.length;w===1&&m[0].isLineHolder?m[0]=y:(g||!w||a)&&m.push(y)}else s.push(new Vf([y]))}}function h0(r){var t=r.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var v0=Ke(",&?/;] ".split(""),function(r,t){return r[t]=!0,r},{});function c0(r){return h0(r)?!!v0[r]:!0}function sd(r,t,e,i,n){for(var a=[],o=[],s="",u="",l=0,f=0,h=0;h<r.length;h++){var c=r.charAt(h);if(c===`
`){u&&(s+=u,f+=l),a.push(s),o.push(f),s="",u="",l=0,f=0;continue}var v=Wt(c,t),d=i?!1:!c0(c);if(a.length?f+v>e:n+f+v>e){f?(s||u)&&(d?(s||(s=u,u="",l=0,f=l),a.push(s),o.push(f-l),u+=c,l+=v,s="",f=l):(u&&(s+=u,u="",l=0),a.push(s),o.push(f),s=c,f=v)):d?(a.push(u),o.push(l),u=c,l=v):(a.push(c),o.push(v));continue}f+=v,d?(u+=c,l+=v):(u&&(s+=u,u="",l=0),s+=c)}return!a.length&&!s&&(s=r,u="",l=0),u&&(s+=u),s&&(a.push(s),o.push(f)),a.length===1&&(f+=n),{accumWidth:f,lines:a,linesWidths:o}}var vu="__zr_style_"+Math.round(Math.random()*10),Ir={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},co={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};Ir[vu]=!0;var Uf=["z","z2","invisible"],d0=["invisible"],Ln=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype._init=function(e){for(var i=lt(e),n=0;n<i.length;n++){var a=i[n];a==="style"?this.useStyle(e[a]):r.prototype.attrKV.call(this,a,e[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(e,i,n,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&p0(this,e,i)||o&&!o[0]&&!o[3])return!1;if(n&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var u=this.parent;u;){if(u.ignore)return!1;u=u.parent}return!0},t.prototype.contain=function(e,i){return this.rectContain(e,i)},t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.rectContain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();return a.contain(n[0],n[1])},t.prototype.getPaintRect=function(){var e=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,n=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,u=a.shadowOffsetY||0;e=this._paintRect||(this._paintRect=new J(0,0,0,0)),i?J.applyTransform(e,n,i):e.copy(n),(o||s||u)&&(e.width+=o*2+Math.abs(s),e.height+=o*2+Math.abs(u),e.x=Math.min(e.x,e.x+s-o),e.y=Math.min(e.y,e.y+u-o));var l=this.dirtyRectTolerance;e.isZero()||(e.x=Math.floor(e.x-l),e.y=Math.floor(e.y-l),e.width=Math.ceil(e.width+1+l*2),e.height=Math.ceil(e.height+1+l*2))}return e},t.prototype.setPrevPaintRect=function(e){e?(this._prevPaintRect=this._prevPaintRect||new J(0,0,0,0),this._prevPaintRect.copy(e)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(e){return this.animate("style",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(e,i){e!=="style"?r.prototype.attrKV.call(this,e,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(e,i){return typeof e=="string"?this.style[e]=i:O(this.style,e),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(e){e||this.markRedraw(),this.__dirty|=Yi,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&Yi)},t.prototype.styleUpdated=function(){this.__dirty&=~Yi},t.prototype.createStyle=function(e){return so(Ir,e)},t.prototype.useStyle=function(e){e[vu]||(e=this.createStyle(e)),this.__inHover?this.__hoverStyle=e:this.style=e,this.dirtyStyle()},t.prototype.isStyleObject=function(e){return e[vu]},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,i,Uf)},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var u=!(i&&a),l;if(i&&i.style?o?a?l=i.style:(l=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(l,i.style)):(l=this._mergeStyle(this.createStyle(),a?this.style:n.style),this._mergeStyle(l,i.style)):u&&(l=n.style),l)if(o){var f=this.style;if(this.style=this.createStyle(u?{}:f),u)for(var h=lt(f),c=0;c<h.length;c++){var v=h[c];v in l&&(l[v]=l[v],this.style[v]=f[v])}for(var d=lt(l),c=0;c<d.length;c++){var v=d[c];this.style[v]=this.style[v]}this._transitionState(e,{style:l},s,this.getAnimationStyleProps())}else this.useStyle(l);for(var _=this.__inHover?d0:Uf,c=0;c<_.length;c++){var v=_[c];i&&i[v]!=null?this[v]=i[v]:u&&n[v]!=null&&(this[v]=n[v])}},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},t.prototype._mergeStyle=function(e,i){return O(e,i),e},t.prototype.getAnimationStyleProps=function(){return co},t.initDefaultProps=function(){var e=t.prototype;e.type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=Gt|Yi}(),t}(uo),ns=new J(0,0,0,0),as=new J(0,0,0,0);function p0(r,t,e){return ns.copy(r.getBoundingRect()),r.transform&&ns.applyTransform(r.transform),as.width=t,as.height=e,!ns.intersect(as)}var Qt=Math.min,Jt=Math.max,os=Math.sin,ss=Math.cos,fr=Math.PI*2,Yn=yi(),Xn=yi(),$n=yi();function Yf(r,t,e,i,n,a){n[0]=Qt(r,e),n[1]=Qt(t,i),a[0]=Jt(r,e),a[1]=Jt(t,i)}var Xf=[],$f=[];function g0(r,t,e,i,n,a,o,s,u,l){var f=kc,h=gt,c=f(r,e,n,o,Xf);u[0]=1/0,u[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var v=0;v<c;v++){var d=h(r,e,n,o,Xf[v]);u[0]=Qt(d,u[0]),l[0]=Jt(d,l[0])}c=f(t,i,a,s,$f);for(var v=0;v<c;v++){var _=h(t,i,a,s,$f[v]);u[1]=Qt(_,u[1]),l[1]=Jt(_,l[1])}u[0]=Qt(r,u[0]),l[0]=Jt(r,l[0]),u[0]=Qt(o,u[0]),l[0]=Jt(o,l[0]),u[1]=Qt(t,u[1]),l[1]=Jt(t,l[1]),u[1]=Qt(s,u[1]),l[1]=Jt(s,l[1])}function _0(r,t,e,i,n,a,o,s){var u=Bc,l=St,f=Jt(Qt(u(r,e,n),1),0),h=Jt(Qt(u(t,i,a),1),0),c=l(r,e,n,f),v=l(t,i,a,h);o[0]=Qt(r,n,c),o[1]=Qt(t,a,v),s[0]=Jt(r,n,c),s[1]=Jt(t,a,v)}function y0(r,t,e,i,n,a,o,s,u){var l=ri,f=ii,h=Math.abs(n-a);if(h%fr<1e-4&&h>1e-4){s[0]=r-e,s[1]=t-i,u[0]=r+e,u[1]=t+i;return}if(Yn[0]=ss(n)*e+r,Yn[1]=os(n)*i+t,Xn[0]=ss(a)*e+r,Xn[1]=os(a)*i+t,l(s,Yn,Xn),f(u,Yn,Xn),n=n%fr,n<0&&(n=n+fr),a=a%fr,a<0&&(a=a+fr),n>a&&!o?a+=fr:n<a&&o&&(n+=fr),o){var c=a;a=n,n=c}for(var v=0;v<a;v+=Math.PI/2)v>n&&($n[0]=ss(v)*e+r,$n[1]=os(v)*i+t,l(s,$n,s),f(u,$n,u))}var Q={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},hr=[],vr=[],de=[],Fe=[],pe=[],ge=[],us=Math.min,ls=Math.max,cr=Math.cos,dr=Math.sin,Pe=Math.abs,cu=Math.PI,Ve=cu*2,fs=typeof Float32Array<"u",Ai=[];function hs(r){var t=Math.round(r/cu*1e8)/1e8;return t%2*cu}function m0(r,t){var e=hs(r[0]);e<0&&(e+=Ve);var i=e-r[0],n=r[1];n+=i,!t&&n-e>=Ve?n=e+Ve:t&&e-n>=Ve?n=e-Ve:!t&&e>n?n=e+(Ve-hs(e-n)):t&&e<n&&(n=e-(Ve-hs(n-e))),r[0]=e,r[1]=n}var pi=function(){function r(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return r.prototype.increaseVersion=function(){this._version++},r.prototype.getVersion=function(){return this._version},r.prototype.setScale=function(t,e,i){i=i||0,i>0&&(this._ux=Pe(i/Wa/t)||0,this._uy=Pe(i/Wa/e)||0)},r.prototype.setDPR=function(t){this.dpr=t},r.prototype.setContext=function(t){this._ctx=t},r.prototype.getContext=function(){return this._ctx},r.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},r.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},r.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Q.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},r.prototype.lineTo=function(t,e){var i=Pe(t-this._xi),n=Pe(e-this._yi),a=i>this._ux||n>this._uy;if(this.addData(Q.L,t,e),this._ctx&&a&&this._ctx.lineTo(t,e),a)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=i*i+n*n;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},r.prototype.bezierCurveTo=function(t,e,i,n,a,o){return this._drawPendingPt(),this.addData(Q.C,t,e,i,n,a,o),this._ctx&&this._ctx.bezierCurveTo(t,e,i,n,a,o),this._xi=a,this._yi=o,this},r.prototype.quadraticCurveTo=function(t,e,i,n){return this._drawPendingPt(),this.addData(Q.Q,t,e,i,n),this._ctx&&this._ctx.quadraticCurveTo(t,e,i,n),this._xi=i,this._yi=n,this},r.prototype.arc=function(t,e,i,n,a,o){this._drawPendingPt(),Ai[0]=n,Ai[1]=a,m0(Ai,o),n=Ai[0],a=Ai[1];var s=a-n;return this.addData(Q.A,t,e,i,i,n,s,0,o?0:1),this._ctx&&this._ctx.arc(t,e,i,n,a,o),this._xi=cr(a)*i+t,this._yi=dr(a)*i+e,this},r.prototype.arcTo=function(t,e,i,n,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,i,n,a),this},r.prototype.rect=function(t,e,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,i,n),this.addData(Q.R,t,e,i,n),this},r.prototype.closePath=function(){this._drawPendingPt(),this.addData(Q.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&t.closePath(),this._xi=e,this._yi=i,this},r.prototype.fill=function(t){t&&t.fill(),this.toStatic()},r.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},r.prototype.len=function(){return this._len},r.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&fs&&(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},r.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,a=0;a<e;a++)i+=t[a].len();fs&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var a=0;a<e;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[n++]=o[s];this._len=n},r.prototype.addData=function(t,e,i,n,a,o,s,u,l){if(this._saveData){var f=this.data;this._len+arguments.length>f.length&&(this._expandData(),f=this.data);for(var h=0;h<arguments.length;h++)f[this._len++]=arguments[h]}},r.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},r.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},r.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,fs&&this._len>11&&(this.data=new Float32Array(t)))}},r.prototype.getBoundingRect=function(){de[0]=de[1]=pe[0]=pe[1]=Number.MAX_VALUE,Fe[0]=Fe[1]=ge[0]=ge[1]=-Number.MAX_VALUE;var t=this.data,e=0,i=0,n=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],u=o===1;switch(u&&(e=t[o],i=t[o+1],n=e,a=i),s){case Q.M:e=n=t[o++],i=a=t[o++],pe[0]=n,pe[1]=a,ge[0]=n,ge[1]=a;break;case Q.L:Yf(e,i,t[o],t[o+1],pe,ge),e=t[o++],i=t[o++];break;case Q.C:g0(e,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],pe,ge),e=t[o++],i=t[o++];break;case Q.Q:_0(e,i,t[o++],t[o++],t[o],t[o+1],pe,ge),e=t[o++],i=t[o++];break;case Q.A:var l=t[o++],f=t[o++],h=t[o++],c=t[o++],v=t[o++],d=t[o++]+v;o+=1;var _=!t[o++];u&&(n=cr(v)*h+l,a=dr(v)*c+f),y0(l,f,h,c,v,d,_,pe,ge),e=cr(d)*h+l,i=dr(d)*c+f;break;case Q.R:n=e=t[o++],a=i=t[o++];var p=t[o++],g=t[o++];Yf(n,a,n+p,a+g,pe,ge);break;case Q.Z:e=n,i=a;break}ri(de,de,pe),ii(Fe,Fe,ge)}return o===0&&(de[0]=de[1]=Fe[0]=Fe[1]=0),new J(de[0],de[1],Fe[0]-de[0],Fe[1]-de[1])},r.prototype._calculateLength=function(){var t=this.data,e=this._len,i=this._ux,n=this._uy,a=0,o=0,s=0,u=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,f=0,h=0,c=0;c<e;){var v=t[c++],d=c===1;d&&(a=t[c],o=t[c+1],s=a,u=o);var _=-1;switch(v){case Q.M:a=s=t[c++],o=u=t[c++];break;case Q.L:{var p=t[c++],g=t[c++],y=p-a,m=g-o;(Pe(y)>i||Pe(m)>n||c===e-1)&&(_=Math.sqrt(y*y+m*m),a=p,o=g);break}case Q.C:{var w=t[c++],b=t[c++],p=t[c++],g=t[c++],S=t[c++],x=t[c++];_=G_(a,o,w,b,p,g,S,x,10),a=S,o=x;break}case Q.Q:{var w=t[c++],b=t[c++],p=t[c++],g=t[c++];_=U_(a,o,w,b,p,g,10),a=p,o=g;break}case Q.A:var C=t[c++],M=t[c++],A=t[c++],T=t[c++],P=t[c++],L=t[c++],I=L+P;c+=1,d&&(s=cr(P)*A+C,u=dr(P)*T+M),_=ls(A,T)*us(Ve,Math.abs(L)),a=cr(I)*A+C,o=dr(I)*T+M;break;case Q.R:{s=a=t[c++],u=o=t[c++];var R=t[c++],E=t[c++];_=R*2+E*2;break}case Q.Z:{var y=s-a,m=u-o;_=Math.sqrt(y*y+m*m),a=s,o=u;break}}_>=0&&(l[h++]=_,f+=_)}return this._pathLen=f,f},r.prototype.rebuildPath=function(t,e){var i=this.data,n=this._ux,a=this._uy,o=this._len,s,u,l,f,h,c,v=e<1,d,_,p=0,g=0,y,m=0,w,b;if(!(v&&(this._pathSegLen||this._calculateLength(),d=this._pathSegLen,_=this._pathLen,y=e*_,!y)))t:for(var S=0;S<o;){var x=i[S++],C=S===1;switch(C&&(l=i[S],f=i[S+1],s=l,u=f),x!==Q.L&&m>0&&(t.lineTo(w,b),m=0),x){case Q.M:s=l=i[S++],u=f=i[S++],t.moveTo(l,f);break;case Q.L:{h=i[S++],c=i[S++];var M=Pe(h-l),A=Pe(c-f);if(M>n||A>a){if(v){var T=d[g++];if(p+T>y){var P=(y-p)/T;t.lineTo(l*(1-P)+h*P,f*(1-P)+c*P);break t}p+=T}t.lineTo(h,c),l=h,f=c,m=0}else{var L=M*M+A*A;L>m&&(w=h,b=c,m=L)}break}case Q.C:{var I=i[S++],R=i[S++],E=i[S++],U=i[S++],N=i[S++],z=i[S++];if(v){var T=d[g++];if(p+T>y){var P=(y-p)/T;za(l,I,E,N,P,hr),za(f,R,U,z,P,vr),t.bezierCurveTo(hr[1],vr[1],hr[2],vr[2],hr[3],vr[3]);break t}p+=T}t.bezierCurveTo(I,R,E,U,N,z),l=N,f=z;break}case Q.Q:{var I=i[S++],R=i[S++],E=i[S++],U=i[S++];if(v){var T=d[g++];if(p+T>y){var P=(y-p)/T;Ha(l,I,E,P,hr),Ha(f,R,U,P,vr),t.quadraticCurveTo(hr[1],vr[1],hr[2],vr[2]);break t}p+=T}t.quadraticCurveTo(I,R,E,U),l=E,f=U;break}case Q.A:var G=i[S++],rt=i[S++],j=i[S++],ct=i[S++],xt=i[S++],te=i[S++],tr=i[S++],er=!i[S++],zr=j>ct?j:ct,Ht=Pe(j-ct)>.001,dt=xt+te,V=!1;if(v){var T=d[g++];p+T>y&&(dt=xt+te*(y-p)/T,V=!0),p+=T}if(Ht&&t.ellipse?t.ellipse(G,rt,j,ct,tr,xt,dt,er):t.arc(G,rt,zr,xt,dt,er),V)break t;C&&(s=cr(xt)*j+G,u=dr(xt)*ct+rt),l=cr(dt)*j+G,f=dr(dt)*ct+rt;break;case Q.R:s=l=i[S],u=f=i[S+1],h=i[S++],c=i[S++];var q=i[S++],rr=i[S++];if(v){var T=d[g++];if(p+T>y){var Ct=y-p;t.moveTo(h,c),t.lineTo(h+us(Ct,q),c),Ct-=q,Ct>0&&t.lineTo(h+q,c+us(Ct,rr)),Ct-=rr,Ct>0&&t.lineTo(h+ls(q-Ct,0),c+rr),Ct-=q,Ct>0&&t.lineTo(h,c+ls(rr-Ct,0));break t}p+=T}t.rect(h,c,q,rr);break;case Q.Z:if(v){var T=d[g++];if(p+T>y){var P=(y-p)/T;t.lineTo(l*(1-P)+s*P,f*(1-P)+u*P);break t}p+=T}t.closePath(),l=s,f=u}}},r.prototype.clone=function(){var t=new r,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},r.CMD=Q,r.initDefaultProps=function(){var t=r.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),r}();function Yr(r,t,e,i,n,a,o){if(n===0)return!1;var s=n,u=0,l=r;if(o>t+s&&o>i+s||o<t-s&&o<i-s||a>r+s&&a>e+s||a<r-s&&a<e-s)return!1;if(r!==e)u=(t-i)/(r-e),l=(r*i-e*t)/(r-e);else return Math.abs(a-r)<=s/2;var f=u*a-o+l,h=f*f/(u*u+1);return h<=s/2*s/2}function w0(r,t,e,i,n,a,o,s,u,l,f){if(u===0)return!1;var h=u;if(f>t+h&&f>i+h&&f>a+h&&f>s+h||f<t-h&&f<i-h&&f<a-h&&f<s-h||l>r+h&&l>e+h&&l>n+h&&l>o+h||l<r-h&&l<e-h&&l<n-h&&l<o-h)return!1;var c=H_(r,t,e,i,n,a,o,s,l,f,null);return c<=h/2}function S0(r,t,e,i,n,a,o,s,u){if(o===0)return!1;var l=o;if(u>t+l&&u>i+l&&u>a+l||u<t-l&&u<i-l&&u<a-l||s>r+l&&s>e+l&&s>n+l||s<r-l&&s<e-l&&s<n-l)return!1;var f=V_(r,t,e,i,n,a,s,u,null);return f<=l/2}var qf=Math.PI*2;function qn(r){return r%=qf,r<0&&(r+=qf),r}var Pi=Math.PI*2;function b0(r,t,e,i,n,a,o,s,u){if(o===0)return!1;var l=o;s-=r,u-=t;var f=Math.sqrt(s*s+u*u);if(f-l>e||f+l<e)return!1;if(Math.abs(i-n)%Pi<1e-4)return!0;if(a){var h=i;i=qn(n),n=qn(h)}else i=qn(i),n=qn(n);i>n&&(n+=Pi);var c=Math.atan2(u,s);return c<0&&(c+=Pi),c>=i&&c<=n||c+Pi>=i&&c+Pi<=n}function pr(r,t,e,i,n,a){if(a>t&&a>i||a<t&&a<i||i===t)return 0;var o=(a-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var u=o*(e-r)+r;return u===n?1/0:u>n?s:0}var Ne=pi.CMD,gr=Math.PI*2,T0=1e-4;function x0(r,t){return Math.abs(r-t)<T0}var Mt=[-1,-1,-1],qt=[-1,-1];function C0(){var r=qt[0];qt[0]=qt[1],qt[1]=r}function D0(r,t,e,i,n,a,o,s,u,l){if(l>t&&l>i&&l>a&&l>s||l<t&&l<i&&l<a&&l<s)return 0;var f=Oc(t,i,a,s,l,Mt);if(f===0)return 0;for(var h=0,c=-1,v=void 0,d=void 0,_=0;_<f;_++){var p=Mt[_],g=p===0||p===1?.5:1,y=gt(r,e,n,o,p);y<u||(c<0&&(c=kc(t,i,a,s,qt),qt[1]<qt[0]&&c>1&&C0(),v=gt(t,i,a,s,qt[0]),c>1&&(d=gt(t,i,a,s,qt[1]))),c===2?p<qt[0]?h+=v<t?g:-g:p<qt[1]?h+=d<v?g:-g:h+=s<d?g:-g:p<qt[0]?h+=v<t?g:-g:h+=s<v?g:-g)}return h}function M0(r,t,e,i,n,a,o,s){if(s>t&&s>i&&s>a||s<t&&s<i&&s<a)return 0;var u=W_(t,i,a,s,Mt);if(u===0)return 0;var l=Bc(t,i,a);if(l>=0&&l<=1){for(var f=0,h=St(t,i,a,l),c=0;c<u;c++){var v=Mt[c]===0||Mt[c]===1?.5:1,d=St(r,e,n,Mt[c]);d<o||(Mt[c]<l?f+=h<t?v:-v:f+=a<h?v:-v)}return f}else{var v=Mt[0]===0||Mt[0]===1?.5:1,d=St(r,e,n,Mt[0]);return d<o?0:a<t?v:-v}}function A0(r,t,e,i,n,a,o,s){if(s-=t,s>e||s<-e)return 0;var u=Math.sqrt(e*e-s*s);Mt[0]=-u,Mt[1]=u;var l=Math.abs(i-n);if(l<1e-4)return 0;if(l>=gr-1e-4){i=0,n=gr;var f=a?1:-1;return o>=Mt[0]+r&&o<=Mt[1]+r?f:0}if(i>n){var h=i;i=n,n=h}i<0&&(i+=gr,n+=gr);for(var c=0,v=0;v<2;v++){var d=Mt[v];if(d+r>o){var _=Math.atan2(s,d),f=a?1:-1;_<0&&(_=gr+_),(_>=i&&_<=n||_+gr>=i&&_+gr<=n)&&(_>Math.PI/2&&_<Math.PI*1.5&&(f=-f),c+=f)}}return c}function ud(r,t,e,i,n){for(var a=r.data,o=r.len(),s=0,u=0,l=0,f=0,h=0,c,v,d=0;d<o;){var _=a[d++],p=d===1;switch(_===Ne.M&&d>1&&(e||(s+=pr(u,l,f,h,i,n))),p&&(u=a[d],l=a[d+1],f=u,h=l),_){case Ne.M:f=a[d++],h=a[d++],u=f,l=h;break;case Ne.L:if(e){if(Yr(u,l,a[d],a[d+1],t,i,n))return!0}else s+=pr(u,l,a[d],a[d+1],i,n)||0;u=a[d++],l=a[d++];break;case Ne.C:if(e){if(w0(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=D0(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,n)||0;u=a[d++],l=a[d++];break;case Ne.Q:if(e){if(S0(u,l,a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=M0(u,l,a[d++],a[d++],a[d],a[d+1],i,n)||0;u=a[d++],l=a[d++];break;case Ne.A:var g=a[d++],y=a[d++],m=a[d++],w=a[d++],b=a[d++],S=a[d++];d+=1;var x=!!(1-a[d++]);c=Math.cos(b)*m+g,v=Math.sin(b)*w+y,p?(f=c,h=v):s+=pr(u,l,c,v,i,n);var C=(i-g)*w/m+g;if(e){if(b0(g,y,w,b,b+S,x,t,C,n))return!0}else s+=A0(g,y,w,b,b+S,x,C,n);u=Math.cos(b+S)*m+g,l=Math.sin(b+S)*w+y;break;case Ne.R:f=u=a[d++],h=l=a[d++];var M=a[d++],A=a[d++];if(c=f+M,v=h+A,e){if(Yr(f,h,c,h,t,i,n)||Yr(c,h,c,v,t,i,n)||Yr(c,v,f,v,t,i,n)||Yr(f,v,f,h,t,i,n))return!0}else s+=pr(c,h,c,v,i,n),s+=pr(f,v,f,h,i,n);break;case Ne.Z:if(e){if(Yr(u,l,f,h,t,i,n))return!0}else s+=pr(u,l,f,h,i,n);u=f,l=h;break}}return!e&&!x0(l,h)&&(s+=pr(u,l,f,h,i,n)||0),s!==0}function P0(r,t,e){return ud(r,0,!1,t,e)}function L0(r,t,e,i){return ud(r,t,!0,e,i)}var ld=ot({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},Ir),I0={style:ot({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},co.style)},vs=dn.concat(["invisible","culling","z","z2","zlevel","parent"]),at=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.update=function(){var e=this;r.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new t;n.buildPath===t.prototype.buildPath&&(n.buildPath=function(u){e.buildPath(u,e.shape)}),n.silent=!0;var a=n.style;for(var o in i)a[o]!==i[o]&&(a[o]=i[o]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var s=0;s<vs.length;++s)n[vs[s]]=this[vs[s]];n.__dirty|=Gt}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(e){var i=lt(e);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var a=0;a<i.length;a++){var o=i[a],s=e[o];o==="style"?this.style?O(this.style,s):this.useStyle(s):o==="shape"?O(this.shape,s):r.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var e=this.style.fill;if(e!=="none"){if(B(e)){var i=Ga(e,0);return i>.5?lu:i>.2?hy:fu}else if(e)return fu}return lu},t.prototype.getInsideTextStroke=function(e){var i=this.style.fill;if(B(i)){var n=this.__zr,a=!!(n&&n.isDarkMode()),o=Ga(e,0)<uu;if(a===o)return i}},t.prototype.buildPath=function(e,i,n){},t.prototype.pathUpdated=function(){this.__dirty&=~ti},t.prototype.getUpdatedPathProxy=function(e){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,e),this.path},t.prototype.createPathProxy=function(){this.path=new pi(!1)},t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return!(i==null||i==="none"||!(e.lineWidth>0))},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var e=this._rect,i=this.style,n=!e;if(n){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&ti)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),e=o.getBoundingRect()}if(this._rect=e,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=e.clone());if(this.__dirty||n){s.copy(e);var u=i.strokeNoScale?this.getLineScale():1,l=i.lineWidth;if(!this.hasFill()){var f=this.strokeContainThreshold;l=Math.max(l,f??4)}u>1e-10&&(s.width+=l/u,s.height+=l/u,s.x-=l/u/2,s.y-=l/u/2)}return s}return e},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect(),o=this.style;if(e=n[0],i=n[1],a.contain(e,i)){var s=this.path;if(this.hasStroke()){var u=o.lineWidth,l=o.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(this.hasFill()||(u=Math.max(u,this.strokeContainThreshold)),L0(s,u/l,e,i)))return!0}if(this.hasFill())return P0(s,e,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=ti,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(e){return this.animate("shape",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():e==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(e,i){e==="shape"?this.setShape(i):r.prototype.attrKV.call(this,e,i)},t.prototype.setShape=function(e,i){var n=this.shape;return n||(n=this.shape={}),typeof e=="string"?n[e]=i:O(n,e),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&ti)},t.prototype.createStyle=function(e){return so(ld,e)},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.shape&&!i.shape&&(i.shape=O({},this.shape))},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var u=!(i&&a),l;if(i&&i.shape?o?a?l=i.shape:(l=O({},n.shape),O(l,i.shape)):(l=O({},a?this.shape:n.shape),O(l,i.shape)):u&&(l=n.shape),l)if(o){this.shape=O({},this.shape);for(var f={},h=lt(l),c=0;c<h.length;c++){var v=h[c];typeof l[v]=="object"?this.shape[v]=l[v]:f[v]=l[v]}this._transitionState(e,{shape:f},s)}else this.shape=l,this.dirtyShape()},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},t.prototype.getAnimationStyleProps=function(){return I0},t.prototype.isZeroArea=function(){return!1},t.extend=function(e){var i=function(a){F(o,a);function o(s){var u=a.call(this,s)||this;return e.init&&e.init.call(u,s),u}return o.prototype.getDefaultStyle=function(){return K(e.style)},o.prototype.getDefaultShape=function(){return K(e.shape)},o}(t);for(var n in e)typeof e[n]=="function"&&(i.prototype[n]=e[n]);return i},t.initDefaultProps=function(){var e=t.prototype;e.type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=Gt|Yi|ti}(),t}(Ln),R0=ot({strokeFirst:!0,font:kr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},ld),Ya=function(r){F(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return i!=null&&i!=="none"&&e.lineWidth>0},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(e){return so(R0,e)},t.prototype.setBoundingRect=function(e){this._rect=e},t.prototype.getBoundingRect=function(){var e=this.style;if(!this._rect){var i=e.text;i!=null?i+="":i="";var n=Vc(i,e.font,e.textAlign,e.textBaseline);if(n.x+=e.x||0,n.y+=e.y||0,this.hasStroke()){var a=e.lineWidth;n.x-=a/2,n.y-=a/2,n.width+=a,n.height+=a}this._rect=n}return this._rect},t.initDefaultProps=function(){var e=t.prototype;e.dirtyRectTolerance=10}(),t}(Ln);Ya.prototype.type="tspan";var E0=ot({x:0,y:0},Ir),O0={style:ot({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},co.style)};function k0(r){return!!(r&&typeof r!="string"&&r.width&&r.height)}var Fr=function(r){F(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.createStyle=function(e){return so(E0,e)},t.prototype._getSize=function(e){var i=this.style,n=i[e];if(n!=null)return n;var a=k0(i.image)?i.image:this.__image;if(!a)return 0;var o=e==="width"?"height":"width",s=i[o];return s==null?a[e]:a[e]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return O0},t.prototype.getBoundingRect=function(){var e=this.style;return this._rect||(this._rect=new J(e.x||0,e.y||0,this.getWidth(),this.getHeight())),this._rect},t}(Ln);Fr.prototype.type="image";function B0(r,t){var e=t.x,i=t.y,n=t.width,a=t.height,o=t.r,s,u,l,f;n<0&&(e=e+n,n=-n),a<0&&(i=i+a,a=-a),typeof o=="number"?s=u=l=f=o:o instanceof Array?o.length===1?s=u=l=f=o[0]:o.length===2?(s=l=o[0],u=f=o[1]):o.length===3?(s=o[0],u=f=o[1],l=o[2]):(s=o[0],u=o[1],l=o[2],f=o[3]):s=u=l=f=0;var h;s+u>n&&(h=s+u,s*=n/h,u*=n/h),l+f>n&&(h=l+f,l*=n/h,f*=n/h),u+l>a&&(h=u+l,u*=a/h,l*=a/h),s+f>a&&(h=s+f,s*=a/h,f*=a/h),r.moveTo(e+s,i),r.lineTo(e+n-u,i),u!==0&&r.arc(e+n-u,i+u,u,-Math.PI/2,0),r.lineTo(e+n,i+a-l),l!==0&&r.arc(e+n-l,i+a-l,l,0,Math.PI/2),r.lineTo(e+f,i+a),f!==0&&r.arc(e+f,i+a-f,f,Math.PI/2,Math.PI),r.lineTo(e,i+s),s!==0&&r.arc(e+s,i+s,s,Math.PI,Math.PI*1.5)}var ai=Math.round;function fd(r,t,e){if(t){var i=t.x1,n=t.x2,a=t.y1,o=t.y2;r.x1=i,r.x2=n,r.y1=a,r.y2=o;var s=e&&e.lineWidth;return s&&(ai(i*2)===ai(n*2)&&(r.x1=r.x2=Dr(i,s,!0)),ai(a*2)===ai(o*2)&&(r.y1=r.y2=Dr(a,s,!0))),r}}function hd(r,t,e){if(t){var i=t.x,n=t.y,a=t.width,o=t.height;r.x=i,r.y=n,r.width=a,r.height=o;var s=e&&e.lineWidth;return s&&(r.x=Dr(i,s,!0),r.y=Dr(n,s,!0),r.width=Math.max(Dr(i+a,s,!1)-r.x,a===0?0:1),r.height=Math.max(Dr(n+o,s,!1)-r.y,o===0?0:1)),r}}function Dr(r,t,e){if(!t)return r;var i=ai(r*2);return(i+ai(t))%2===0?i/2:(i+(e?1:-1))/2}var F0=function(){function r(){this.x=0,this.y=0,this.width=0,this.height=0}return r}(),N0={},Pt=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new F0},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var u=hd(N0,i,this.style);n=u.x,a=u.y,o=u.width,s=u.height,u.r=i.r,i=u}else n=i.x,a=i.y,o=i.width,s=i.height;i.r?B0(e,i):e.rect(n,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(at);Pt.prototype.type="rect";var Zf={fill:"#000"},Kf=2,z0={style:ot({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},co.style)},Lt=function(r){F(t,r);function t(e){var i=r.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=Zf,i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){r.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var i=this._children[e];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):r.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(e){var i=this.innerTransformable;return i?i.getLocalTransform(e):r.prototype.getLocalTransform.call(this,e)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),r.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,U0(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=e},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var e=new J(0,0,0,0),i=this._children,n=[],a=null,o=0;o<i.length;o++){var s=i[o],u=s.getBoundingRect(),l=s.getLocalTransform(n);l?(e.copy(u),e.applyTransform(l),a=a||e.clone(),a.union(e)):(a=a||u.clone(),a.union(u))}this._rect=a||e}return this._rect},t.prototype.setDefaultTextStyle=function(e){this._defaultStyle=e||Zf},t.prototype.setTextContent=function(e){},t.prototype._mergeStyle=function(e,i){if(!i)return e;var n=i.rich,a=e.rich||n&&{};return O(e,i),n&&a?(this._mergeRich(a,n),e.rich=a):a&&(e.rich=a),e},t.prototype._mergeRich=function(e,i){for(var n=lt(i),a=0;a<n.length;a++){var o=n[a];e[o]=e[o]||{},O(e[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return z0},t.prototype._getOrCreateChild=function(e){var i=this._children[this._childCursor];return(!i||!(i instanceof e))&&(i=new e),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var e=this.style,i=e.font||kr,n=e.padding,a=ih(e),o=s0(a,e),s=cs(e),u=!!e.backgroundColor,l=o.outerHeight,f=o.outerWidth,h=o.contentWidth,c=o.lines,v=o.lineHeight,d=this._defaultStyle,_=e.x||0,p=e.y||0,g=e.align||d.align||"left",y=e.verticalAlign||d.verticalAlign||"top",m=_,w=ei(p,o.contentHeight,y);if(s||n){var b=$i(_,f,g),S=ei(p,l,y);s&&this._renderBackground(e,e,b,S,f,l)}w+=v/2,n&&(m=rh(_,g,n),y==="top"?w+=n[0]:y==="bottom"&&(w-=n[2]));for(var x=0,C=!1,M=eh("fill"in e?e.fill:(C=!0,d.fill)),A=th("stroke"in e?e.stroke:!u&&(!d.autoStroke||C)?(x=Kf,d.stroke):null),T=e.textShadowBlur>0,P=e.width!=null&&(e.overflow==="truncate"||e.overflow==="break"||e.overflow==="breakAll"),L=o.calculatedLineHeight,I=0;I<c.length;I++){var R=this._getOrCreateChild(Ya),E=R.createStyle();R.useStyle(E),E.text=c[I],E.x=m,E.y=w,g&&(E.textAlign=g),E.textBaseline="middle",E.opacity=e.opacity,E.strokeFirst=!0,T&&(E.shadowBlur=e.textShadowBlur||0,E.shadowColor=e.textShadowColor||"transparent",E.shadowOffsetX=e.textShadowOffsetX||0,E.shadowOffsetY=e.textShadowOffsetY||0),E.stroke=A,E.fill=M,A&&(E.lineWidth=e.lineWidth||x,E.lineDash=e.lineDash,E.lineDashOffset=e.lineDashOffset||0),E.font=i,Jf(E,e),w+=v,P&&R.setBoundingRect(new J($i(E.x,e.width,E.textAlign),ei(E.y,L,E.textBaseline),h,L))}},t.prototype._updateRichTexts=function(){var e=this.style,i=ih(e),n=f0(i,e),a=n.width,o=n.outerWidth,s=n.outerHeight,u=e.padding,l=e.x||0,f=e.y||0,h=this._defaultStyle,c=e.align||h.align,v=e.verticalAlign||h.verticalAlign,d=$i(l,o,c),_=ei(f,s,v),p=d,g=_;u&&(p+=u[3],g+=u[0]);var y=p+a;cs(e)&&this._renderBackground(e,e,d,_,o,s);for(var m=!!e.backgroundColor,w=0;w<n.lines.length;w++){for(var b=n.lines[w],S=b.tokens,x=S.length,C=b.lineHeight,M=b.width,A=0,T=p,P=y,L=x-1,I=void 0;A<x&&(I=S[A],!I.align||I.align==="left");)this._placeToken(I,e,C,g,T,"left",m),M-=I.width,T+=I.width,A++;for(;L>=0&&(I=S[L],I.align==="right");)this._placeToken(I,e,C,g,P,"right",m),M-=I.width,P-=I.width,L--;for(T+=(a-(T-p)-(y-P)-M)/2;A<=L;)I=S[A],this._placeToken(I,e,C,g,T+I.width/2,"center",m),T+=I.width,A++;g+=C}},t.prototype._placeToken=function(e,i,n,a,o,s,u){var l=i.rich[e.styleName]||{};l.text=e.text;var f=e.verticalAlign,h=a+n/2;f==="top"?h=a+e.height/2:f==="bottom"&&(h=a+n-e.height/2);var c=!e.isLineHolder&&cs(l);c&&this._renderBackground(l,i,s==="right"?o-e.width:s==="center"?o-e.width/2:o,h-e.height/2,e.width,e.height);var v=!!l.backgroundColor,d=e.textPadding;d&&(o=rh(o,s,d),h-=e.height/2-d[0]-e.innerHeight/2);var _=this._getOrCreateChild(Ya),p=_.createStyle();_.useStyle(p);var g=this._defaultStyle,y=!1,m=0,w=eh("fill"in l?l.fill:"fill"in i?i.fill:(y=!0,g.fill)),b=th("stroke"in l?l.stroke:"stroke"in i?i.stroke:!v&&!u&&(!g.autoStroke||y)?(m=Kf,g.stroke):null),S=l.textShadowBlur>0||i.textShadowBlur>0;p.text=e.text,p.x=o,p.y=h,S&&(p.shadowBlur=l.textShadowBlur||i.textShadowBlur||0,p.shadowColor=l.textShadowColor||i.textShadowColor||"transparent",p.shadowOffsetX=l.textShadowOffsetX||i.textShadowOffsetX||0,p.shadowOffsetY=l.textShadowOffsetY||i.textShadowOffsetY||0),p.textAlign=s,p.textBaseline="middle",p.font=e.font||kr,p.opacity=wa(l.opacity,i.opacity,1),Jf(p,l),b&&(p.lineWidth=wa(l.lineWidth,i.lineWidth,m),p.lineDash=X(l.lineDash,i.lineDash),p.lineDashOffset=i.lineDashOffset||0,p.stroke=b),w&&(p.fill=w);var x=e.contentWidth,C=e.contentHeight;_.setBoundingRect(new J($i(p.x,x,p.textAlign),ei(p.y,C,p.textBaseline),x,C))},t.prototype._renderBackground=function(e,i,n,a,o,s){var u=e.backgroundColor,l=e.borderWidth,f=e.borderColor,h=u&&u.image,c=u&&!h,v=e.borderRadius,d=this,_,p;if(c||e.lineHeight||l&&f){_=this._getOrCreateChild(Pt),_.useStyle(_.createStyle()),_.style.fill=null;var g=_.shape;g.x=n,g.y=a,g.width=o,g.height=s,g.r=v,_.dirtyShape()}if(c){var y=_.style;y.fill=u||null,y.fillOpacity=X(e.fillOpacity,1)}else if(h){p=this._getOrCreateChild(Fr),p.onload=function(){d.dirtyStyle()};var m=p.style;m.image=u.image,m.x=n,m.y=a,m.width=o,m.height=s}if(l&&f){var y=_.style;y.lineWidth=l,y.stroke=f,y.strokeOpacity=X(e.strokeOpacity,1),y.lineDash=e.borderDash,y.lineDashOffset=e.borderDashOffset||0,_.strokeContainThreshold=0,_.hasFill()&&_.hasStroke()&&(y.strokeFirst=!0,y.lineWidth*=2)}var w=(_||p).style;w.shadowBlur=e.shadowBlur||0,w.shadowColor=e.shadowColor||"transparent",w.shadowOffsetX=e.shadowOffsetX||0,w.shadowOffsetY=e.shadowOffsetY||0,w.opacity=wa(e.opacity,i.opacity,1)},t.makeFont=function(e){var i="";return V0(e)&&(i=[e.fontStyle,e.fontWeight,W0(e.fontSize),e.fontFamily||"sans-serif"].join(" ")),i&&we(i)||e.textFont||e.font},t}(Ln),H0={left:!0,right:1,center:1},G0={top:1,bottom:1,middle:1},Qf=["fontStyle","fontWeight","fontSize","fontFamily"];function W0(r){return typeof r=="string"&&(r.indexOf("px")!==-1||r.indexOf("rem")!==-1||r.indexOf("em")!==-1)?r:isNaN(+r)?qu+"px":r+"px"}function Jf(r,t){for(var e=0;e<Qf.length;e++){var i=Qf[e],n=t[i];n!=null&&(r[i]=n)}}function V0(r){return r.fontSize!=null||r.fontFamily||r.fontWeight}function U0(r){return jf(r),D(r.rich,jf),r}function jf(r){if(r){r.font=Lt.makeFont(r);var t=r.align;t==="middle"&&(t="center"),r.align=t==null||H0[t]?t:"left";var e=r.verticalAlign;e==="center"&&(e="middle"),r.verticalAlign=e==null||G0[e]?e:"top";var i=r.padding;i&&(r.padding=Sc(r.padding))}}function th(r,t){return r==null||t<=0||r==="transparent"||r==="none"?null:r.image||r.colorStops?"#000":r}function eh(r){return r==null||r==="none"?null:r.image||r.colorStops?"#000":r}function rh(r,t,e){return t==="right"?r-e[1]:t==="center"?r+e[3]/2-e[1]/2:r+e[3]}function ih(r){var t=r.text;return t!=null&&(t+=""),t}function cs(r){return!!(r.backgroundColor||r.lineHeight||r.borderWidth&&r.borderColor)}var it=yt(),Y0=function(r,t,e,i){if(i){var n=it(i);n.dataIndex=e,n.dataType=t,n.seriesIndex=r,n.ssrType="chart",i.type==="group"&&i.traverse(function(a){var o=it(a);o.seriesIndex=r,o.dataIndex=e,o.dataType=t,o.ssrType="chart"})}},nh=1,ah={},vd=yt(),ll=yt(),fl=0,po=1,go=2,Je=["emphasis","blur","select"],oh=["normal","emphasis","blur","select"],X0=10,$0=9,Rr="highlight",Ma="downplay",en="select",Aa="unselect",rn="toggleSelect";function Xr(r){return r!=null&&r!=="none"}function _o(r,t,e){r.onHoverStateChange&&(r.hoverState||0)!==e&&r.onHoverStateChange(t),r.hoverState=e}function cd(r){_o(r,"emphasis",go)}function dd(r){r.hoverState===go&&_o(r,"normal",fl)}function hl(r){_o(r,"blur",po)}function pd(r){r.hoverState===po&&_o(r,"normal",fl)}function q0(r){r.selected=!0}function Z0(r){r.selected=!1}function sh(r,t,e){t(r,e)}function Be(r,t,e){sh(r,t,e),r.isGroup&&r.traverse(function(i){sh(i,t,e)})}function mC(r,t){switch(t){case"emphasis":r.hoverState=go;break;case"normal":r.hoverState=fl;break;case"blur":r.hoverState=po;break;case"select":r.selected=!0}}function K0(r,t,e,i){for(var n=r.style,a={},o=0;o<t.length;o++){var s=t[o],u=n[s];a[s]=u??(i&&i[s])}for(var o=0;o<r.animators.length;o++){var l=r.animators[o];l.__fromStateTransition&&l.__fromStateTransition.indexOf(e)<0&&l.targetName==="style"&&l.saveTo(a,t)}return a}function Q0(r,t,e,i){var n=e&&nt(e,"select")>=0,a=!1;if(r instanceof at){var o=vd(r),s=n&&o.selectFill||o.normalFill,u=n&&o.selectStroke||o.normalStroke;if(Xr(s)||Xr(u)){i=i||{};var l=i.style||{};l.fill==="inherit"?(a=!0,i=O({},i),l=O({},l),l.fill=s):!Xr(l.fill)&&Xr(s)?(a=!0,i=O({},i),l=O({},l),l.fill=xf(s)):!Xr(l.stroke)&&Xr(u)&&(a||(i=O({},i),l=O({},l)),l.stroke=xf(u)),i.style=l}}if(i&&i.z2==null){a||(i=O({},i));var f=r.z2EmphasisLift;i.z2=r.z2+(f??X0)}return i}function J0(r,t,e){if(e&&e.z2==null){e=O({},e);var i=r.z2SelectLift;e.z2=r.z2+(i??$0)}return e}function j0(r,t,e){var i=nt(r.currentStates,t)>=0,n=r.style.opacity,a=i?null:K0(r,["opacity"],t,{opacity:1});e=e||{};var o=e.style||{};return o.opacity==null&&(e=O({},e),o=O({opacity:i?n:a.opacity*.1},o),e.style=o),e}function ds(r,t){var e=this.states[r];if(this.style){if(r==="emphasis")return Q0(this,r,t,e);if(r==="blur")return j0(this,r,e);if(r==="select")return J0(this,r,e)}return e}function tm(r){r.stateProxy=ds;var t=r.getTextContent(),e=r.getTextGuideLine();t&&(t.stateProxy=ds),e&&(e.stateProxy=ds)}function uh(r,t){!md(r,t)&&!r.__highByOuter&&Be(r,cd)}function lh(r,t){!md(r,t)&&!r.__highByOuter&&Be(r,dd)}function du(r,t){r.__highByOuter|=1<<(t||0),Be(r,cd)}function pu(r,t){!(r.__highByOuter&=~(1<<(t||0)))&&Be(r,dd)}function em(r){Be(r,hl)}function gd(r){Be(r,pd)}function _d(r){Be(r,q0)}function yd(r){Be(r,Z0)}function md(r,t){return r.__highDownSilentOnTouch&&t.zrByTouch}function wd(r){var t=r.getModel(),e=[],i=[];t.eachComponent(function(n,a){var o=ll(a),s=n==="series",u=s?r.getViewOfSeriesModel(a):r.getViewOfComponentModel(a);!s&&i.push(u),o.isBlured&&(u.group.traverse(function(l){pd(l)}),s&&e.push(a)),o.isBlured=!1}),D(i,function(n){n&&n.toggleBlurSeries&&n.toggleBlurSeries(e,!1,t)})}function gu(r,t,e,i){var n=i.getModel();e=e||"coordinateSystem";function a(l,f){for(var h=0;h<f.length;h++){var c=l.getItemGraphicEl(f[h]);c&&gd(c)}}if(r!=null&&!(!t||t==="none")){var o=n.getSeriesByIndex(r),s=o.coordinateSystem;s&&s.master&&(s=s.master);var u=[];n.eachSeries(function(l){var f=o===l,h=l.coordinateSystem;h&&h.master&&(h=h.master);var c=h&&s?h===s:f;if(!(e==="series"&&!f||e==="coordinateSystem"&&!c||t==="series"&&f)){var v=i.getViewOfSeriesModel(l);if(v.group.traverse(function(p){p.__highByOuter&&f&&t==="self"||hl(p)}),Ft(t))a(l.getData(),t);else if(H(t))for(var d=lt(t),_=0;_<d.length;_++)a(l.getData(d[_]),t[d[_]]);u.push(l),ll(l).isBlured=!0}}),n.eachComponent(function(l,f){if(l!=="series"){var h=i.getViewOfComponentModel(f);h&&h.toggleBlurSeries&&h.toggleBlurSeries(u,!0,n)}})}}function _u(r,t,e){if(!(r==null||t==null)){var i=e.getModel().getComponent(r,t);if(i){ll(i).isBlured=!0;var n=e.getViewOfComponentModel(i);!n||!n.focusBlurEnabled||n.group.traverse(function(a){hl(a)})}}}function rm(r,t,e){var i=r.seriesIndex,n=r.getData(t.dataType);if(n){var a=An(n,t);a=(k(a)?a[0]:a)||0;var o=n.getItemGraphicEl(a);if(!o)for(var s=n.count(),u=0;!o&&u<s;)o=n.getItemGraphicEl(u++);if(o){var l=it(o);gu(i,l.focus,l.blurScope,e)}else{var f=r.get(["emphasis","focus"]),h=r.get(["emphasis","blurScope"]);f!=null&&gu(i,f,h,e)}}}function vl(r,t,e,i){var n={focusSelf:!1,dispatchers:null};if(r==null||r==="series"||t==null||e==null)return n;var a=i.getModel().getComponent(r,t);if(!a)return n;var o=i.getViewOfComponentModel(a);if(!o||!o.findHighDownDispatchers)return n;for(var s=o.findHighDownDispatchers(e),u,l=0;l<s.length;l++)if(it(s[l]).focus==="self"){u=!0;break}return{focusSelf:u,dispatchers:s}}function im(r,t,e){var i=it(r),n=vl(i.componentMainType,i.componentIndex,i.componentHighDownName,e),a=n.dispatchers,o=n.focusSelf;a?(o&&_u(i.componentMainType,i.componentIndex,e),D(a,function(s){return uh(s,t)})):(gu(i.seriesIndex,i.focus,i.blurScope,e),i.focus==="self"&&_u(i.componentMainType,i.componentIndex,e),uh(r,t))}function nm(r,t,e){wd(e);var i=it(r),n=vl(i.componentMainType,i.componentIndex,i.componentHighDownName,e).dispatchers;n?D(n,function(a){return lh(a,t)}):lh(r,t)}function am(r,t,e){if(wu(t)){var i=t.dataType,n=r.getData(i),a=An(n,t);k(a)||(a=[a]),r[t.type===rn?"toggleSelect":t.type===en?"select":"unselect"](a,i)}}function fh(r){var t=r.getAllData();D(t,function(e){var i=e.data,n=e.type;i.eachItemGraphicEl(function(a,o){r.isSelected(o,n)?_d(a):yd(a)})})}function om(r){var t=[];return r.eachSeries(function(e){var i=e.getAllData();D(i,function(n){n.data;var a=n.type,o=e.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:e.seriesIndex};a!=null&&(s.dataType=a),t.push(s)}})}),t}function yu(r,t,e){Sd(r,!0),Be(r,tm),um(r,t,e)}function sm(r){Sd(r,!1)}function wC(r,t,e,i){i?sm(r):yu(r,t,e)}function um(r,t,e){var i=it(r);t!=null?(i.focus=t,i.blurScope=e):i.focus&&(i.focus=null)}var hh=["emphasis","blur","select"],lm={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function SC(r,t,e,i){e=e||"itemStyle";for(var n=0;n<hh.length;n++){var a=hh[n],o=t.getModel([a,e]),s=r.ensureState(a);s.style=o[lm[e]]()}}function Sd(r,t){var e=t===!1,i=r;r.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=r.highDownSilentOnTouch),(!e||i.__highDownDispatcher)&&(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!e)}function mu(r){return!!(r&&r.__highDownDispatcher)}function fm(r){var t=ah[r];return t==null&&nh<=32&&(t=ah[r]=nh++),t}function wu(r){var t=r.type;return t===en||t===Aa||t===rn}function vh(r){var t=r.type;return t===Rr||t===Ma}function hm(r){var t=vd(r);t.normalFill=r.style.fill,t.normalStroke=r.style.stroke;var e=r.states.select||{};t.selectFill=e.style&&e.style.fill||null,t.selectStroke=e.style&&e.style.stroke||null}var $r=pi.CMD,vm=[[],[],[]],ch=Math.sqrt,cm=Math.atan2;function dm(r,t){if(t){var e=r.data,i=r.len(),n,a,o,s,u,l,f=$r.M,h=$r.C,c=$r.L,v=$r.R,d=$r.A,_=$r.Q;for(o=0,s=0;o<i;){switch(n=e[o++],s=o,a=0,n){case f:a=1;break;case c:a=1;break;case h:a=3;break;case _:a=2;break;case d:var p=t[4],g=t[5],y=ch(t[0]*t[0]+t[1]*t[1]),m=ch(t[2]*t[2]+t[3]*t[3]),w=cm(-t[1]/m,t[0]/y);e[o]*=y,e[o++]+=p,e[o]*=m,e[o++]+=g,e[o++]*=y,e[o++]*=m,e[o++]+=w,e[o++]+=w,o+=2,s=o;break;case v:l[0]=e[o++],l[1]=e[o++],be(l,l,t),e[s++]=l[0],e[s++]=l[1],l[0]+=e[o++],l[1]+=e[o++],be(l,l,t),e[s++]=l[0],e[s++]=l[1]}for(u=0;u<a;u++){var b=vm[u];b[0]=e[o++],b[1]=e[o++],be(b,b,t),e[s++]=b[0],e[s++]=b[1]}}r.increaseVersion()}}var ps=Math.sqrt,Zn=Math.sin,Kn=Math.cos,Li=Math.PI;function dh(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function Su(r,t){return(r[0]*t[0]+r[1]*t[1])/(dh(r)*dh(t))}function ph(r,t){return(r[0]*t[1]<r[1]*t[0]?-1:1)*Math.acos(Su(r,t))}function gh(r,t,e,i,n,a,o,s,u,l,f){var h=u*(Li/180),c=Kn(h)*(r-e)/2+Zn(h)*(t-i)/2,v=-1*Zn(h)*(r-e)/2+Kn(h)*(t-i)/2,d=c*c/(o*o)+v*v/(s*s);d>1&&(o*=ps(d),s*=ps(d));var _=(n===a?-1:1)*ps((o*o*(s*s)-o*o*(v*v)-s*s*(c*c))/(o*o*(v*v)+s*s*(c*c)))||0,p=_*o*v/s,g=_*-s*c/o,y=(r+e)/2+Kn(h)*p-Zn(h)*g,m=(t+i)/2+Zn(h)*p+Kn(h)*g,w=ph([1,0],[(c-p)/o,(v-g)/s]),b=[(c-p)/o,(v-g)/s],S=[(-1*c-p)/o,(-1*v-g)/s],x=ph(b,S);if(Su(b,S)<=-1&&(x=Li),Su(b,S)>=1&&(x=0),x<0){var C=Math.round(x/Li*1e6)/1e6;x=Li*2+C%2*Li}f.addData(l,y,m,o,s,w,x,h,a)}var pm=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,gm=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function _m(r){var t=new pi;if(!r)return t;var e=0,i=0,n=e,a=i,o,s=pi.CMD,u=r.match(pm);if(!u)return t;for(var l=0;l<u.length;l++){for(var f=u[l],h=f.charAt(0),c=void 0,v=f.match(gm)||[],d=v.length,_=0;_<d;_++)v[_]=parseFloat(v[_]);for(var p=0;p<d;){var g=void 0,y=void 0,m=void 0,w=void 0,b=void 0,S=void 0,x=void 0,C=e,M=i,A=void 0,T=void 0;switch(h){case"l":e+=v[p++],i+=v[p++],c=s.L,t.addData(c,e,i);break;case"L":e=v[p++],i=v[p++],c=s.L,t.addData(c,e,i);break;case"m":e+=v[p++],i+=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="l";break;case"M":e=v[p++],i=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="L";break;case"h":e+=v[p++],c=s.L,t.addData(c,e,i);break;case"H":e=v[p++],c=s.L,t.addData(c,e,i);break;case"v":i+=v[p++],c=s.L,t.addData(c,e,i);break;case"V":i=v[p++],c=s.L,t.addData(c,e,i);break;case"C":c=s.C,t.addData(c,v[p++],v[p++],v[p++],v[p++],v[p++],v[p++]),e=v[p-2],i=v[p-1];break;case"c":c=s.C,t.addData(c,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i),e+=v[p-2],i+=v[p-1];break;case"S":g=e,y=i,A=t.len(),T=t.data,o===s.C&&(g+=e-T[A-4],y+=i-T[A-3]),c=s.C,C=v[p++],M=v[p++],e=v[p++],i=v[p++],t.addData(c,g,y,C,M,e,i);break;case"s":g=e,y=i,A=t.len(),T=t.data,o===s.C&&(g+=e-T[A-4],y+=i-T[A-3]),c=s.C,C=e+v[p++],M=i+v[p++],e+=v[p++],i+=v[p++],t.addData(c,g,y,C,M,e,i);break;case"Q":C=v[p++],M=v[p++],e=v[p++],i=v[p++],c=s.Q,t.addData(c,C,M,e,i);break;case"q":C=v[p++]+e,M=v[p++]+i,e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,C,M,e,i);break;case"T":g=e,y=i,A=t.len(),T=t.data,o===s.Q&&(g+=e-T[A-4],y+=i-T[A-3]),e=v[p++],i=v[p++],c=s.Q,t.addData(c,g,y,e,i);break;case"t":g=e,y=i,A=t.len(),T=t.data,o===s.Q&&(g+=e-T[A-4],y+=i-T[A-3]),e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,g,y,e,i);break;case"A":m=v[p++],w=v[p++],b=v[p++],S=v[p++],x=v[p++],C=e,M=i,e=v[p++],i=v[p++],c=s.A,gh(C,M,e,i,S,x,m,w,b,c,t);break;case"a":m=v[p++],w=v[p++],b=v[p++],S=v[p++],x=v[p++],C=e,M=i,e+=v[p++],i+=v[p++],c=s.A,gh(C,M,e,i,S,x,m,w,b,c,t);break}}(h==="z"||h==="Z")&&(c=s.Z,t.addData(c),e=n,i=a),o=c}return t.toStatic(),t}var bd=function(r){F(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.applyTransform=function(e){},t}(at);function Td(r){return r.setData!=null}function xd(r,t){var e=_m(r),i=O({},t);return i.buildPath=function(n){if(Td(n)){n.setData(e.data);var a=n.getContext();a&&n.rebuildPath(a,1)}else{var a=n;e.rebuildPath(a,1)}},i.applyTransform=function(n){dm(e,n),this.dirtyShape()},i}function ym(r,t){return new bd(xd(r,t))}function mm(r,t){var e=xd(r,t),i=function(n){F(a,n);function a(o){var s=n.call(this,o)||this;return s.applyTransform=e.applyTransform,s.buildPath=e.buildPath,s}return a}(bd);return i}function wm(r,t){for(var e=[],i=r.length,n=0;n<i;n++){var a=r[n];e.push(a.getUpdatedPathProxy(!0))}var o=new at(t);return o.createPathProxy(),o.buildPath=function(s){if(Td(s)){s.appendPath(e);var u=s.getContext();u&&s.rebuildPath(u,1)}},o}var Sm=function(){function r(){this.cx=0,this.cy=0,this.r=0}return r}(),yo=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Sm},t.prototype.buildPath=function(e,i){e.moveTo(i.cx+i.r,i.cy),e.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(at);yo.prototype.type="circle";var bm=function(){function r(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return r}(),cl=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new bm},t.prototype.buildPath=function(e,i){var n=.5522848,a=i.cx,o=i.cy,s=i.rx,u=i.ry,l=s*n,f=u*n;e.moveTo(a-s,o),e.bezierCurveTo(a-s,o-f,a-l,o-u,a,o-u),e.bezierCurveTo(a+l,o-u,a+s,o-f,a+s,o),e.bezierCurveTo(a+s,o+f,a+l,o+u,a,o+u),e.bezierCurveTo(a-l,o+u,a-s,o+f,a-s,o),e.closePath()},t}(at);cl.prototype.type="ellipse";var Cd=Math.PI,gs=Cd*2,_r=Math.sin,qr=Math.cos,Tm=Math.acos,mt=Math.atan2,_h=Math.abs,nn=Math.sqrt,qi=Math.max,_e=Math.min,ie=1e-4;function xm(r,t,e,i,n,a,o,s){var u=e-r,l=i-t,f=o-n,h=s-a,c=h*u-f*l;if(!(c*c<ie))return c=(f*(t-a)-h*(r-n))/c,[r+c*u,t+c*l]}function Qn(r,t,e,i,n,a,o){var s=r-e,u=t-i,l=(o?a:-a)/nn(s*s+u*u),f=l*u,h=-l*s,c=r+f,v=t+h,d=e+f,_=i+h,p=(c+d)/2,g=(v+_)/2,y=d-c,m=_-v,w=y*y+m*m,b=n-a,S=c*_-d*v,x=(m<0?-1:1)*nn(qi(0,b*b*w-S*S)),C=(S*m-y*x)/w,M=(-S*y-m*x)/w,A=(S*m+y*x)/w,T=(-S*y+m*x)/w,P=C-p,L=M-g,I=A-p,R=T-g;return P*P+L*L>I*I+R*R&&(C=A,M=T),{cx:C,cy:M,x0:-f,y0:-h,x1:C*(n/b-1),y1:M*(n/b-1)}}function Cm(r){var t;if(k(r)){var e=r.length;if(!e)return r;e===1?t=[r[0],r[0],0,0]:e===2?t=[r[0],r[0],r[1],r[1]]:e===3?t=r.concat(r[2]):t=r}else t=[r,r,r,r];return t}function Dm(r,t){var e,i=qi(t.r,0),n=qi(t.r0||0,0),a=i>0,o=n>0;if(!(!a&&!o)){if(a||(i=n,n=0),n>i){var s=i;i=n,n=s}var u=t.startAngle,l=t.endAngle;if(!(isNaN(u)||isNaN(l))){var f=t.cx,h=t.cy,c=!!t.clockwise,v=_h(l-u),d=v>gs&&v%gs;if(d>ie&&(v=d),!(i>ie))r.moveTo(f,h);else if(v>gs-ie)r.moveTo(f+i*qr(u),h+i*_r(u)),r.arc(f,h,i,u,l,!c),n>ie&&(r.moveTo(f+n*qr(l),h+n*_r(l)),r.arc(f,h,n,l,u,c));else{var _=void 0,p=void 0,g=void 0,y=void 0,m=void 0,w=void 0,b=void 0,S=void 0,x=void 0,C=void 0,M=void 0,A=void 0,T=void 0,P=void 0,L=void 0,I=void 0,R=i*qr(u),E=i*_r(u),U=n*qr(l),N=n*_r(l),z=v>ie;if(z){var G=t.cornerRadius;G&&(e=Cm(G),_=e[0],p=e[1],g=e[2],y=e[3]);var rt=_h(i-n)/2;if(m=_e(rt,g),w=_e(rt,y),b=_e(rt,_),S=_e(rt,p),M=x=qi(m,w),A=C=qi(b,S),(x>ie||C>ie)&&(T=i*qr(l),P=i*_r(l),L=n*qr(u),I=n*_r(u),v<Cd)){var j=xm(R,E,L,I,T,P,U,N);if(j){var ct=R-j[0],xt=E-j[1],te=T-j[0],tr=P-j[1],er=1/_r(Tm((ct*te+xt*tr)/(nn(ct*ct+xt*xt)*nn(te*te+tr*tr)))/2),zr=nn(j[0]*j[0]+j[1]*j[1]);M=_e(x,(i-zr)/(er+1)),A=_e(C,(n-zr)/(er-1))}}}if(!z)r.moveTo(f+R,h+E);else if(M>ie){var Ht=_e(g,M),dt=_e(y,M),V=Qn(L,I,R,E,i,Ht,c),q=Qn(T,P,U,N,i,dt,c);r.moveTo(f+V.cx+V.x0,h+V.cy+V.y0),M<x&&Ht===dt?r.arc(f+V.cx,h+V.cy,M,mt(V.y0,V.x0),mt(q.y0,q.x0),!c):(Ht>0&&r.arc(f+V.cx,h+V.cy,Ht,mt(V.y0,V.x0),mt(V.y1,V.x1),!c),r.arc(f,h,i,mt(V.cy+V.y1,V.cx+V.x1),mt(q.cy+q.y1,q.cx+q.x1),!c),dt>0&&r.arc(f+q.cx,h+q.cy,dt,mt(q.y1,q.x1),mt(q.y0,q.x0),!c))}else r.moveTo(f+R,h+E),r.arc(f,h,i,u,l,!c);if(!(n>ie)||!z)r.lineTo(f+U,h+N);else if(A>ie){var Ht=_e(_,A),dt=_e(p,A),V=Qn(U,N,T,P,n,-dt,c),q=Qn(R,E,L,I,n,-Ht,c);r.lineTo(f+V.cx+V.x0,h+V.cy+V.y0),A<C&&Ht===dt?r.arc(f+V.cx,h+V.cy,A,mt(V.y0,V.x0),mt(q.y0,q.x0),!c):(dt>0&&r.arc(f+V.cx,h+V.cy,dt,mt(V.y0,V.x0),mt(V.y1,V.x1),!c),r.arc(f,h,n,mt(V.cy+V.y1,V.cx+V.x1),mt(q.cy+q.y1,q.cx+q.x1),c),Ht>0&&r.arc(f+q.cx,h+q.cy,Ht,mt(q.y1,q.x1),mt(q.y0,q.x0),!c))}else r.lineTo(f+U,h+N),r.arc(f,h,n,l,u,c)}r.closePath()}}}var Mm=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return r}(),dl=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Mm},t.prototype.buildPath=function(e,i){Dm(e,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(at);dl.prototype.type="sector";var Am=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return r}(),pl=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Am},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.PI*2;e.moveTo(n+i.r,a),e.arc(n,a,i.r,0,o,!1),e.moveTo(n+i.r0,a),e.arc(n,a,i.r0,0,o,!0)},t}(at);pl.prototype.type="ring";function Pm(r,t,e,i){var n=[],a=[],o=[],s=[],u,l,f,h;if(i){f=[1/0,1/0],h=[-1/0,-1/0];for(var c=0,v=r.length;c<v;c++)ri(f,f,r[c]),ii(h,h,r[c]);ri(f,f,i[0]),ii(h,h,i[1])}for(var c=0,v=r.length;c<v;c++){var d=r[c];if(e)u=r[c?c-1:v-1],l=r[(c+1)%v];else if(c===0||c===v-1){n.push(u_(r[c]));continue}else u=r[c-1],l=r[c+1];l_(a,l,u),Oo(a,a,t);var _=Ks(d,u),p=Ks(d,l),g=_+p;g!==0&&(_/=g,p/=g),Oo(o,a,-_),Oo(s,a,p);var y=uf([],d,o),m=uf([],d,s);i&&(ii(y,y,f),ri(y,y,h),ii(m,m,f),ri(m,m,h)),n.push(y),n.push(m)}return e&&n.push(n.shift()),n}function Dd(r,t,e){var i=t.smooth,n=t.points;if(n&&n.length>=2){if(i){var a=Pm(n,i,e,t.smoothConstraint);r.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;s<(e?o:o-1);s++){var u=a[s*2],l=a[s*2+1],f=n[(s+1)%o];r.bezierCurveTo(u[0],u[1],l[0],l[1],f[0],f[1])}}else{r.moveTo(n[0][0],n[0][1]);for(var s=1,h=n.length;s<h;s++)r.lineTo(n[s][0],n[s][1])}e&&r.closePath()}}var Lm=function(){function r(){this.points=null,this.smooth=0,this.smoothConstraint=null}return r}(),gl=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Lm},t.prototype.buildPath=function(e,i){Dd(e,i,!0)},t}(at);gl.prototype.type="polygon";var Im=function(){function r(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return r}(),_l=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Im},t.prototype.buildPath=function(e,i){Dd(e,i,!1)},t}(at);_l.prototype.type="polyline";var Rm={},Em=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return r}(),wi=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Em},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var u=fd(Rm,i,this.style);n=u.x1,a=u.y1,o=u.x2,s=u.y2}else n=i.x1,a=i.y1,o=i.x2,s=i.y2;var l=i.percent;l!==0&&(e.moveTo(n,a),l<1&&(o=n*(1-l)+o*l,s=a*(1-l)+s*l),e.lineTo(o,s))},t.prototype.pointAt=function(e){var i=this.shape;return[i.x1*(1-e)+i.x2*e,i.y1*(1-e)+i.y2*e]},t}(at);wi.prototype.type="line";var It=[],Om=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return r}();function yh(r,t,e){var i=r.cpx2,n=r.cpy2;return i!=null||n!=null?[(e?mf:gt)(r.x1,r.cpx1,r.cpx2,r.x2,t),(e?mf:gt)(r.y1,r.cpy1,r.cpy2,r.y2,t)]:[(e?wf:St)(r.x1,r.cpx1,r.x2,t),(e?wf:St)(r.y1,r.cpy1,r.y2,t)]}var yl=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Om},t.prototype.buildPath=function(e,i){var n=i.x1,a=i.y1,o=i.x2,s=i.y2,u=i.cpx1,l=i.cpy1,f=i.cpx2,h=i.cpy2,c=i.percent;c!==0&&(e.moveTo(n,a),f==null||h==null?(c<1&&(Ha(n,u,o,c,It),u=It[1],o=It[2],Ha(a,l,s,c,It),l=It[1],s=It[2]),e.quadraticCurveTo(u,l,o,s)):(c<1&&(za(n,u,f,o,c,It),u=It[1],f=It[2],o=It[3],za(a,l,h,s,c,It),l=It[1],h=It[2],s=It[3]),e.bezierCurveTo(u,l,f,h,o,s)))},t.prototype.pointAt=function(e){return yh(this.shape,e,!1)},t.prototype.tangentAt=function(e){var i=yh(this.shape,e,!0);return v_(i,i)},t}(at);yl.prototype.type="bezier-curve";var km=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),mo=function(r){F(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new km},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.max(i.r,0),s=i.startAngle,u=i.endAngle,l=i.clockwise,f=Math.cos(s),h=Math.sin(s);e.moveTo(f*o+n,h*o+a),e.arc(n,a,o,s,u,!l)},t}(at);mo.prototype.type="arc";var Bm=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="compound",e}return t.prototype._updatePathDirty=function(){for(var e=this.shape.paths,i=this.shapeChanged(),n=0;n<e.length;n++)i=i||e[n].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var e=this.shape.paths||[],i=this.getGlobalScale(),n=0;n<e.length;n++)e[n].path||e[n].createPathProxy(),e[n].path.setScale(i[0],i[1],e[n].segmentIgnoreThreshold)},t.prototype.buildPath=function(e,i){for(var n=i.paths||[],a=0;a<n.length;a++)n[a].buildPath(e,n[a].shape,!0)},t.prototype.afterBrush=function(){for(var e=this.shape.paths||[],i=0;i<e.length;i++)e[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),at.prototype.getBoundingRect.call(this)},t}(at);const Fm=Bm;var Md=function(){function r(t){this.colorStops=t||[]}return r.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},r}(),Nm=function(r){F(t,r);function t(e,i,n,a,o,s){var u=r.call(this,o)||this;return u.x=e??0,u.y=i??0,u.x2=n??1,u.y2=a??0,u.type="linear",u.global=s||!1,u}return t}(Md),zm=function(r){F(t,r);function t(e,i,n,a,o){var s=r.call(this,a)||this;return s.x=e??.5,s.y=i??.5,s.r=n??.5,s.type="radial",s.global=o||!1,s}return t}(Md);const Hm=zm;var yr=[0,0],mr=[0,0],Jn=new et,jn=new et,Gm=function(){function r(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new et;for(var i=0;i<2;i++)this._axes[i]=new et;t&&this.fromBoundingRect(t,e)}return r.prototype.fromBoundingRect=function(t,e){var i=this._corners,n=this._axes,a=t.x,o=t.y,s=a+t.width,u=o+t.height;if(i[0].set(a,o),i[1].set(s,o),i[2].set(s,u),i[3].set(a,u),e)for(var l=0;l<4;l++)i[l].transform(e);et.sub(n[0],i[1],i[0]),et.sub(n[1],i[3],i[0]),n[0].normalize(),n[1].normalize();for(var l=0;l<2;l++)this._origin[l]=n[l].dot(i[0])},r.prototype.intersect=function(t,e){var i=!0,n=!e;return Jn.set(1/0,1/0),jn.set(0,0),!this._intersectCheckOneSide(this,t,Jn,jn,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,Jn,jn,n,-1)&&(i=!1,n)||n||et.copy(e,i?Jn:jn),i},r.prototype._intersectCheckOneSide=function(t,e,i,n,a,o){for(var s=!0,u=0;u<2;u++){var l=this._axes[u];if(this._getProjMinMaxOnAxis(u,t._corners,yr),this._getProjMinMaxOnAxis(u,e._corners,mr),yr[1]<mr[0]||yr[0]>mr[1]){if(s=!1,a)return s;var f=Math.abs(mr[0]-yr[1]),h=Math.abs(yr[0]-mr[1]);Math.min(f,h)>n.len()&&(f<h?et.scale(n,l,-f*o):et.scale(n,l,h*o))}else if(i){var f=Math.abs(mr[0]-yr[1]),h=Math.abs(yr[0]-mr[1]);Math.min(f,h)<i.len()&&(f<h?et.scale(i,l,f*o):et.scale(i,l,-h*o))}}return s},r.prototype._getProjMinMaxOnAxis=function(t,e,i){for(var n=this._axes[t],a=this._origin,o=e[0].dot(n)+a[t],s=o,u=o,l=1;l<e.length;l++){var f=e[l].dot(n)+a[t];s=Math.min(f,s),u=Math.max(f,u)}i[0]=s,i[1]=u},r}();const Xa=Gm;var Wm=[],Vm=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(e,i){i?this._temporaryDisplayables.push(e):this._displayables.push(e),this.markRedraw()},t.prototype.addDisplayables=function(e,i){i=i||!1;for(var n=0;n<e.length;n++)this.addDisplayable(e[n],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(e){for(var i=this._cursor;i<this._displayables.length;i++)e&&e(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)e&&e(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.parent=this,i.update(),i.parent=null}for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var e=new J(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var n=this._displayables[i],a=n.getBoundingRect().clone();n.needLocalTransform()&&a.applyTransform(n.getLocalTransform(Wm)),e.union(a)}this._rect=e}return this._rect},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();if(a.contain(n[0],n[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(e,i))return!0}return!1},t}(Ln);const Um=Vm;var Ym=yt();function Xm(r,t,e,i,n){var a;if(t&&t.ecModel){var o=t.ecModel.getUpdatePayload();a=o&&o.animation}var s=t&&t.isAnimationEnabled(),u=r==="update";if(s){var l=void 0,f=void 0,h=void 0;i?(l=X(i.duration,200),f=X(i.easing,"cubicOut"),h=0):(l=t.getShallow(u?"animationDurationUpdate":"animationDuration"),f=t.getShallow(u?"animationEasingUpdate":"animationEasing"),h=t.getShallow(u?"animationDelayUpdate":"animationDelay")),a&&(a.duration!=null&&(l=a.duration),a.easing!=null&&(f=a.easing),a.delay!=null&&(h=a.delay)),Z(h)&&(h=h(e,n)),Z(l)&&(l=l(e));var c={duration:l||0,delay:h,easing:f};return c}else return null}function ml(r,t,e,i,n,a,o){var s=!1,u;Z(n)?(o=a,a=n,n=null):H(n)&&(a=n.cb,o=n.during,s=n.isFrom,u=n.removeOpt,n=n.dataIndex);var l=r==="leave";l||t.stopAnimation("leave");var f=Xm(r,i,n,l?u||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(t,n):null);if(f&&f.duration>0){var h=f.duration,c=f.delay,v=f.easing,d={duration:h,delay:c||0,easing:v,done:a,force:!!a||!!o,setToFinal:!l,scope:r,during:o};s?t.animateFrom(e,d):t.animateTo(e,d)}else t.stopAnimation(),!s&&t.attr(e),o&&o(1),a&&a()}function In(r,t,e,i,n,a){ml("update",r,t,e,i,n,a)}function Ad(r,t,e,i,n,a){ml("enter",r,t,e,i,n,a)}function an(r){if(!r.__zr)return!0;for(var t=0;t<r.animators.length;t++){var e=r.animators[t];if(e.scope==="leave")return!0}return!1}function Pd(r,t,e,i,n,a){an(r)||ml("leave",r,t,e,i,n,a)}function mh(r,t,e,i){r.removeTextContent(),r.removeTextGuideLine(),Pd(r,{style:{opacity:0}},t,e,i)}function $m(r,t,e){function i(){r.parent&&r.parent.remove(r)}r.isGroup?r.traverse(function(n){n.isGroup||mh(n,t,e,i)}):mh(r,t,e,i)}function bC(r){Ym(r).oldStyle=r.style}var $a=Math.max,qa=Math.min,bu={};function qm(r){return at.extend(r)}var Zm=mm;function Km(r,t){return Zm(r,t)}function ve(r,t){bu[r]=t}function Qm(r){if(bu.hasOwnProperty(r))return bu[r]}function wl(r,t,e,i){var n=ym(r,t);return e&&(i==="center"&&(e=Id(e,n.getBoundingRect())),Rd(n,e)),n}function Ld(r,t,e){var i=new Fr({style:{image:r,x:t.x,y:t.y,width:t.width,height:t.height},onload:function(n){if(e==="center"){var a={width:n.width,height:n.height};i.setStyle(Id(t,a))}}});return i}function Id(r,t){var e=t.width/t.height,i=r.height*e,n;i<=r.width?n=r.height:(i=r.width,n=i/e);var a=r.x+r.width/2,o=r.y+r.height/2;return{x:a-i/2,y:o-n/2,width:i,height:n}}var Jm=wm;function Rd(r,t){if(r.applyTransform){var e=r.getBoundingRect(),i=e.calculateTransform(t);r.applyTransform(i)}}function Sl(r,t){return fd(r,r,{lineWidth:t}),r}function jm(r){return hd(r.shape,r.shape,r.style),r}var t1=Dr;function e1(r,t){for(var e=tl([]);r&&r!==t;)ui(e,r.getLocalTransform(),e),r=r.parent;return e}function bl(r,t,e){return t&&!Ft(t)&&(t=il.getLocalTransform(t)),e&&(t=Mc([],t)),be([],r,t)}function r1(r,t,e){var i=t[4]===0||t[5]===0||t[0]===0?1:Math.abs(2*t[4]/t[0]),n=t[4]===0||t[5]===0||t[2]===0?1:Math.abs(2*t[4]/t[2]),a=[r==="left"?-i:r==="right"?i:0,r==="top"?-n:r==="bottom"?n:0];return a=bl(a,t,e),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function wh(r){return!r.isGroup}function i1(r){return r.shape!=null}function n1(r,t,e){if(!r||!t)return;function i(o){var s={};return o.traverse(function(u){wh(u)&&u.anid&&(s[u.anid]=u)}),s}function n(o){var s={x:o.x,y:o.y,rotation:o.rotation};return i1(o)&&(s.shape=O({},o.shape)),s}var a=i(r);t.traverse(function(o){if(wh(o)&&o.anid){var s=a[o.anid];if(s){var u=n(o);o.attr(n(s)),In(o,u,e,it(o).dataIndex)}}})}function a1(r,t){return Y(r,function(e){var i=e[0];i=$a(i,t.x),i=qa(i,t.x+t.width);var n=e[1];return n=$a(n,t.y),n=qa(n,t.y+t.height),[i,n]})}function o1(r,t){var e=$a(r.x,t.x),i=qa(r.x+r.width,t.x+t.width),n=$a(r.y,t.y),a=qa(r.y+r.height,t.y+t.height);if(i>=e&&a>=n)return{x:e,y:n,width:i-e,height:a-n}}function Tl(r,t,e){var i=O({rectHover:!0},t),n=i.style={strokeNoScale:!0};if(e=e||{x:-1,y:-1,width:2,height:2},r)return r.indexOf("image://")===0?(n.image=r.slice(8),ot(n,e),new Fr(i)):wl(r.replace("path://",""),i,e,"center")}function s1(r,t,e,i,n){for(var a=0,o=n[n.length-1];a<n.length;a++){var s=n[a];if(Ed(r,t,e,i,s[0],s[1],o[0],o[1]))return!0;o=s}}function Ed(r,t,e,i,n,a,o,s){var u=e-r,l=i-t,f=o-n,h=s-a,c=_s(f,h,u,l);if(u1(c))return!1;var v=r-n,d=t-a,_=_s(v,d,u,l)/c;if(_<0||_>1)return!1;var p=_s(v,d,f,h)/c;return!(p<0||p>1)}function _s(r,t,e,i){return r*i-e*t}function u1(r){return r<=1e-6&&r>=-1e-6}function xl(r){var t=r.itemTooltipOption,e=r.componentModel,i=r.itemName,n=B(t)?{formatter:t}:t,a=e.mainType,o=e.componentIndex,s={componentType:a,name:i,$vars:["name"]};s[a+"Index"]=o;var u=r.formatterParamsExtra;u&&D(lt(u),function(f){di(s,f)||(s[f]=u[f],s.$vars.push(f))});var l=it(r.el);l.componentMainType=a,l.componentIndex=o,l.tooltipConfig={name:i,option:ot({content:i,formatterParams:s},n)}}function Sh(r,t){var e;r.isGroup&&(e=t(r)),e||r.traverse(t)}function Od(r,t){if(r)if(k(r))for(var e=0;e<r.length;e++)Sh(r[e],t);else Sh(r,t)}ve("circle",yo);ve("ellipse",cl);ve("sector",dl);ve("ring",pl);ve("polygon",gl);ve("polyline",_l);ve("rect",Pt);ve("line",wi);ve("bezierCurve",yl);ve("arc",mo);const l1=Object.freeze(Object.defineProperty({__proto__:null,Arc:mo,BezierCurve:yl,BoundingRect:J,Circle:yo,CompoundPath:Fm,Ellipse:cl,Group:fe,Image:Fr,IncrementalDisplayable:Um,Line:wi,LinearGradient:Nm,OrientedBoundingRect:Xa,Path:at,Point:et,Polygon:gl,Polyline:_l,RadialGradient:Hm,Rect:Pt,Ring:pl,Sector:dl,Text:Lt,applyTransform:bl,clipPointsByRect:a1,clipRectByRect:o1,createIcon:Tl,extendPath:Km,extendShape:qm,getShapeClass:Qm,getTransform:e1,groupTransition:n1,initProps:Ad,isElementRemoved:an,lineLineIntersect:Ed,linePolygonIntersect:s1,makeImage:Ld,makePath:wl,mergePath:Jm,registerShape:ve,removeElement:Pd,removeElementWithFadeOut:$m,resizePath:Rd,setTooltipConfig:xl,subPixelOptimize:t1,subPixelOptimizeLine:Sl,subPixelOptimizeRect:jm,transformDirection:r1,traverseElements:Od,updateProps:In},Symbol.toStringTag,{value:"Module"}));var wo={};function kd(r,t){for(var e=0;e<Je.length;e++){var i=Je[e],n=t[i],a=r.ensureState(i);a.style=a.style||{},a.style.text=n}var o=r.currentStates.slice();r.clearStates(!0),r.setStyle({text:t.normal}),r.useStates(o,!0)}function Tu(r,t,e){var i=r.labelFetcher,n=r.labelDataIndex,a=r.labelDimIndex,o=t.normal,s;i&&(s=i.getFormattedLabel(n,"normal",null,a,o&&o.get("formatter"),e!=null?{interpolatedValue:e}:null)),s==null&&(s=Z(r.defaultText)?r.defaultText(n,r,e):r.defaultText);for(var u={normal:s},l=0;l<Je.length;l++){var f=Je[l],h=t[f];u[f]=X(i?i.getFormattedLabel(n,f,null,a,h&&h.get("formatter")):null,s)}return u}function f1(r,t,e,i){e=e||wo;for(var n=r instanceof Lt,a=!1,o=0;o<oh.length;o++){var s=t[oh[o]];if(s&&s.getShallow("show")){a=!0;break}}var u=n?r:r.getTextContent();if(a){n||(u||(u=new Lt,r.setTextContent(u)),r.stateProxy&&(u.stateProxy=r.stateProxy));var l=Tu(e,t),f=t.normal,h=!!f.getShallow("show"),c=je(f,i&&i.normal,e,!1,!n);c.text=l.normal,n||r.setTextConfig(bh(f,e,!1));for(var o=0;o<Je.length;o++){var v=Je[o],s=t[v];if(s){var d=u.ensureState(v),_=!!X(s.getShallow("show"),h);if(_!==h&&(d.ignore=!_),d.style=je(s,i&&i[v],e,!0,!n),d.style.text=l[v],!n){var p=r.ensureState(v);p.textConfig=bh(s,e,!0)}}}u.silent=!!f.getShallow("silent"),u.style.x!=null&&(c.x=u.style.x),u.style.y!=null&&(c.y=u.style.y),u.ignore=!h,u.useStyle(c),u.dirty(),e.enableTextSetter&&(Cl(u).setLabelText=function(g){var y=Tu(e,t,g);kd(u,y)})}else u&&(u.ignore=!0);r.dirty()}function TC(r,t){t=t||"label";for(var e={normal:r.getModel(t)},i=0;i<Je.length;i++){var n=Je[i];e[n]=r.getModel([n,t])}return e}function je(r,t,e,i,n){var a={};return h1(a,r,e,i,n),t&&O(a,t),a}function bh(r,t,e){t=t||{};var i={},n,a=r.getShallow("rotate"),o=X(r.getShallow("distance"),e?null:5),s=r.getShallow("offset");return n=r.getShallow("position")||(e?null:"inside"),n==="outside"&&(n=t.defaultOutsidePosition||"top"),n!=null&&(i.position=n),s!=null&&(i.offset=s),a!=null&&(a*=Math.PI/180,i.rotation=a),o!=null&&(i.distance=o),i.outsideFill=r.get("color")==="inherit"?t.inheritColor||null:"auto",i}function h1(r,t,e,i,n){e=e||wo;var a=t.ecModel,o=a&&a.option.textStyle,s=v1(t),u;if(s){u={};for(var l in s)if(s.hasOwnProperty(l)){var f=t.getModel(["rich",l]);Dh(u[l]={},f,o,e,i,n,!1,!0)}}u&&(r.rich=u);var h=t.get("overflow");h&&(r.overflow=h);var c=t.get("minMargin");c!=null&&(r.margin=c),Dh(r,t,o,e,i,n,!0,!1)}function v1(r){for(var t;r&&r!==r.ecModel;){var e=(r.option||wo).rich;if(e){t=t||{};for(var i=lt(e),n=0;n<i.length;n++){var a=i[n];t[a]=1}}r=r.parentModel}return t}var Th=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],xh=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],Ch=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function Dh(r,t,e,i,n,a,o,s){e=!n&&e||wo;var u=i&&i.inheritColor,l=t.getShallow("color"),f=t.getShallow("textBorderColor"),h=X(t.getShallow("opacity"),e.opacity);(l==="inherit"||l==="auto")&&(u?l=u:l=null),(f==="inherit"||f==="auto")&&(u?f=u:f=null),a||(l=l||e.color,f=f||e.textBorderColor),l!=null&&(r.fill=l),f!=null&&(r.stroke=f);var c=X(t.getShallow("textBorderWidth"),e.textBorderWidth);c!=null&&(r.lineWidth=c);var v=X(t.getShallow("textBorderType"),e.textBorderType);v!=null&&(r.lineDash=v);var d=X(t.getShallow("textBorderDashOffset"),e.textBorderDashOffset);d!=null&&(r.lineDashOffset=d),!n&&h==null&&!s&&(h=i&&i.defaultOpacity),h!=null&&(r.opacity=h),!n&&!a&&r.fill==null&&i.inheritColor&&(r.fill=i.inheritColor);for(var _=0;_<Th.length;_++){var p=Th[_],g=X(t.getShallow(p),e[p]);g!=null&&(r[p]=g)}for(var _=0;_<xh.length;_++){var p=xh[_],g=t.getShallow(p);g!=null&&(r[p]=g)}if(r.verticalAlign==null){var y=t.getShallow("baseline");y!=null&&(r.verticalAlign=y)}if(!o||!i.disableBox){for(var _=0;_<Ch.length;_++){var p=Ch[_],g=t.getShallow(p);g!=null&&(r[p]=g)}var m=t.getShallow("borderType");m!=null&&(r.borderDash=m),(r.backgroundColor==="auto"||r.backgroundColor==="inherit")&&u&&(r.backgroundColor=u),(r.borderColor==="auto"||r.borderColor==="inherit")&&u&&(r.borderColor=u)}}function c1(r,t){var e=t&&t.getModel("textStyle");return we([r.fontStyle||e&&e.getShallow("fontStyle")||"",r.fontWeight||e&&e.getShallow("fontWeight")||"",(r.fontSize||e&&e.getShallow("fontSize")||12)+"px",r.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "))}var Cl=yt();function xC(r,t,e,i){if(r){var n=Cl(r);n.prevValue=n.value,n.value=e;var a=t.normal;n.valueAnimation=a.get("valueAnimation"),n.valueAnimation&&(n.precision=a.get("precision"),n.defaultInterpolatedText=i,n.statesModels=t)}}function CC(r,t,e,i,n){var a=Cl(r);if(!a.valueAnimation||a.prevValue===a.value)return;var o=a.defaultInterpolatedText,s=X(a.interpolatedValue,a.prevValue),u=a.value;function l(f){var h=Xy(e,a.precision,s,u,f);a.interpolatedValue=f===1?null:h;var c=Tu({labelDataIndex:t,labelFetcher:n,defaultText:o?o(h):h+""},a.statesModels,h);kd(r,c)}r.percent=0,(a.prevValue==null?Ad:In)(r,{percent:1},i,t,null,l)}var d1=["textStyle","color"],ys=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],ms=new Lt,p1=function(){function r(){}return r.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(d1):null)},r.prototype.getFont=function(){return c1({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},r.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},i=0;i<ys.length;i++)e[ys[i]]=this.getShallow(ys[i]);return ms.useStyle(e),ms.update(),ms.getBoundingRect()},r}(),Bd=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],g1=gn(Bd),_1=function(){function r(){}return r.prototype.getLineStyle=function(t){return g1(this,t)},r}(),Fd=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],y1=gn(Fd),m1=function(){function r(){}return r.prototype.getItemStyle=function(t,e){return y1(this,t,e)},r}(),ft=function(){function r(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}return r.prototype.init=function(t,e,i){},r.prototype.mergeOption=function(t,e){ut(this.option,t,!0)},r.prototype.get=function(t,e){return t==null?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},r.prototype.getShallow=function(t,e){var i=this.option,n=i==null?i:i[t];if(n==null&&!e){var a=this.parentModel;a&&(n=a.getShallow(t))}return n},r.prototype.getModel=function(t,e){var i=t!=null,n=i?this.parsePath(t):null,a=i?this._doGet(n):this.option;return e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(n)),new r(a,e,this.ecModel)},r.prototype.isEmpty=function(){return this.option==null},r.prototype.restoreData=function(){},r.prototype.clone=function(){var t=this.constructor;return new t(K(this.option))},r.prototype.parsePath=function(t){return typeof t=="string"?t.split("."):t},r.prototype.resolveParentPath=function(t){return t},r.prototype.isAnimationEnabled=function(){if(!W.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},r.prototype._doGet=function(t,e){var i=this.option;if(!t)return i;for(var n=0;n<t.length&&!(t[n]&&(i=i&&typeof i=="object"?i[t[n]]:null,i==null));n++);return i==null&&e&&(i=e._doGet(this.resolveParentPath(t),e.parentModel)),i},r}();ul(ft);Jy(ft);ke(ft,_1);ke(ft,m1);ke(ft,i0);ke(ft,p1);var w1=Math.round(Math.random()*10);function So(r){return[r||"",w1++].join("_")}function S1(r){var t={};r.registerSubTypeDefaulter=function(e,i){var n=Se(e);t[n.main]=i},r.determineSubType=function(e,i){var n=i.type;if(!n){var a=Se(e).main;r.hasSubTypes(e)&&t[a]&&(n=t[a](i))}return n}}function b1(r,t){r.topologicalTravel=function(a,o,s,u){if(!a.length)return;var l=e(o),f=l.graph,h=l.noEntryList,c={};for(D(a,function(y){c[y]=!0});h.length;){var v=h.pop(),d=f[v],_=!!c[v];_&&(s.call(u,v,d.originalDeps.slice()),delete c[v]),D(d.successor,_?g:p)}D(c,function(){var y="";throw new Error(y)});function p(y){f[y].entryCount--,f[y].entryCount===0&&h.push(y)}function g(y){c[y]=!0,p(y)}};function e(a){var o={},s=[];return D(a,function(u){var l=i(o,u),f=l.originalDeps=t(u),h=n(f,a);l.entryCount=h.length,l.entryCount===0&&s.push(u),D(h,function(c){nt(l.predecessor,c)<0&&l.predecessor.push(c);var v=i(o,c);nt(v.successor,c)<0&&v.successor.push(u)})}),{graph:o,noEntryList:s}}function i(a,o){return a[o]||(a[o]={predecessor:[],successor:[]}),a[o]}function n(a,o){var s=[];return D(a,function(u){nt(o,u)>=0&&s.push(u)}),s}}function T1(r,t){return ut(ut({},r,!0),t,!0)}const x1={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},C1={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var Za="ZH",Dl="EN",fi=Dl,Pa={},Ml={},Nd=W.domSupported?function(){var r=(document.documentElement.lang||navigator.language||navigator.browserLanguage||fi).toUpperCase();return r.indexOf(Za)>-1?Za:fi}():fi;function zd(r,t){r=r.toUpperCase(),Ml[r]=new ft(t),Pa[r]=t}function D1(r){if(B(r)){var t=Pa[r.toUpperCase()]||{};return r===Za||r===Dl?K(t):ut(K(t),K(Pa[fi]),!1)}else return ut(K(r),K(Pa[fi]),!1)}function M1(r){return Ml[r]}function A1(){return Ml[fi]}zd(Dl,x1);zd(Za,C1);var Al=1e3,Pl=Al*60,on=Pl*60,jt=on*24,Mh=jt*365,Zi={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},ta="{yyyy}-{MM}-{dd}",Ah={year:"{yyyy}",month:"{yyyy}-{MM}",day:ta,hour:ta+" "+Zi.hour,minute:ta+" "+Zi.minute,second:ta+" "+Zi.second,millisecond:Zi.none},ws=["year","month","day","hour","minute","second","millisecond"],Hd=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function ze(r,t){return r+="","0000".substr(0,t-r.length)+r}function hi(r){switch(r){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return r}}function P1(r){return r===hi(r)}function L1(r){switch(r){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function bo(r,t,e,i){var n=Oe(r),a=n[Ll(e)](),o=n[vi(e)]()+1,s=Math.floor((o-1)/3)+1,u=n[To(e)](),l=n["get"+(e?"UTC":"")+"Day"](),f=n[_n(e)](),h=(f-1)%12+1,c=n[xo(e)](),v=n[Co(e)](),d=n[Do(e)](),_=i instanceof ft?i:M1(i||Nd)||A1(),p=_.getModel("time"),g=p.get("month"),y=p.get("monthAbbr"),m=p.get("dayOfWeek"),w=p.get("dayOfWeekAbbr");return(t||"").replace(/{yyyy}/g,a+"").replace(/{yy}/g,ze(a%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,g[o-1]).replace(/{MMM}/g,y[o-1]).replace(/{MM}/g,ze(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,ze(u,2)).replace(/{d}/g,u+"").replace(/{eeee}/g,m[l]).replace(/{ee}/g,w[l]).replace(/{e}/g,l+"").replace(/{HH}/g,ze(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,ze(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,ze(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,ze(v,2)).replace(/{s}/g,v+"").replace(/{SSS}/g,ze(d,3)).replace(/{S}/g,d+"")}function I1(r,t,e,i,n){var a=null;if(B(e))a=e;else if(Z(e))a=e(r.value,t,{level:r.level});else{var o=O({},Zi);if(r.level>0)for(var s=0;s<ws.length;++s)o[ws[s]]="{primary|"+o[ws[s]]+"}";var u=e?e.inherit===!1?e:ot(e,o):o,l=Gd(r.value,n);if(u[l])a=u[l];else if(u.inherit){for(var f=Hd.indexOf(l),s=f-1;s>=0;--s)if(u[l]){a=u[l];break}a=a||o.none}if(k(a)){var h=r.level==null?0:r.level>=0?r.level:a.length+r.level;h=Math.min(h,a.length-1),a=a[h]}}return bo(new Date(r.value),a,n,i)}function Gd(r,t){var e=Oe(r),i=e[vi(t)]()+1,n=e[To(t)](),a=e[_n(t)](),o=e[xo(t)](),s=e[Co(t)](),u=e[Do(t)](),l=u===0,f=l&&s===0,h=f&&o===0,c=h&&a===0,v=c&&n===1,d=v&&i===1;return d?"year":v?"month":c?"day":h?"hour":f?"minute":l?"second":"millisecond"}function Ph(r,t,e){var i=ht(r)?Oe(r):r;switch(t=t||Gd(r,e),t){case"year":return i[Ll(e)]();case"half-year":return i[vi(e)]()>=6?1:0;case"quarter":return Math.floor((i[vi(e)]()+1)/4);case"month":return i[vi(e)]();case"day":return i[To(e)]();case"half-day":return i[_n(e)]()/24;case"hour":return i[_n(e)]();case"minute":return i[xo(e)]();case"second":return i[Co(e)]();case"millisecond":return i[Do(e)]()}}function Ll(r){return r?"getUTCFullYear":"getFullYear"}function vi(r){return r?"getUTCMonth":"getMonth"}function To(r){return r?"getUTCDate":"getDate"}function _n(r){return r?"getUTCHours":"getHours"}function xo(r){return r?"getUTCMinutes":"getMinutes"}function Co(r){return r?"getUTCSeconds":"getSeconds"}function Do(r){return r?"getUTCMilliseconds":"getMilliseconds"}function R1(r){return r?"setUTCFullYear":"setFullYear"}function Wd(r){return r?"setUTCMonth":"setMonth"}function Vd(r){return r?"setUTCDate":"setDate"}function Ud(r){return r?"setUTCHours":"setHours"}function Yd(r){return r?"setUTCMinutes":"setMinutes"}function Xd(r){return r?"setUTCSeconds":"setSeconds"}function $d(r){return r?"setUTCMilliseconds":"setMilliseconds"}function qd(r){if(!Py(r))return B(r)?r:"-";var t=(r+"").split(".");return t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function Zd(r,t){return r=(r||"").toLowerCase().replace(/-(.)/g,function(e,i){return i.toUpperCase()}),t&&r&&(r=r.charAt(0).toUpperCase()+r.slice(1)),r}var Mo=Sc;function xu(r,t,e){var i="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function n(f){return f&&we(f)?f:"-"}function a(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=t==="time",s=r instanceof Date;if(o||s){var u=o?Oe(r):r;if(isNaN(+u)){if(s)return"-"}else return bo(u,i,e)}if(t==="ordinal")return Zs(r)?n(r):ht(r)&&a(r)?r+"":"-";var l=Ua(r);return a(l)?qd(l):Zs(r)?n(r):typeof r=="boolean"?r+"":"-"}var Lh=["a","b","c","d","e","f","g"],Ss=function(r,t){return"{"+r+(t??"")+"}"};function Kd(r,t,e){k(t)||(t=[t]);var i=t.length;if(!i)return"";for(var n=t[0].$vars||[],a=0;a<n.length;a++){var o=Lh[a];r=r.replace(Ss(o),Ss(o,0))}for(var s=0;s<i;s++)for(var u=0;u<n.length;u++){var l=t[s][n[u]];r=r.replace(Ss(Lh[u],s),e?Zt(l):l)}return r}function DC(r,t,e){return D(t,function(i,n){r=r.replace("{"+n+"}",i)}),r}function E1(r,t){var e=B(r)?{color:r,extraCssText:t}:r||{},i=e.color,n=e.type;t=e.extraCssText;var a=e.renderMode||"html";if(!i)return"";if(a==="html")return n==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Zt(i)+";"+(t||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Zt(i)+";"+(t||"")+'"></span>';var o=e.markerId||"markerX";return{renderMode:a,content:"{"+o+"|}  ",style:n==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}}function yn(r,t){return t=t||"transparent",B(r)?r:H(r)&&r.colorStops&&(r.colorStops[0]||{}).color||t}function Ih(r,t){if(t==="_blank"||t==="blank"){var e=window.open();e.opener=null,e.location.href=r}else window.open(r,t)}var La=D,O1=["left","right","top","bottom","width","height"],Mr=[["width","left","right"],["height","top","bottom"]];function Il(r,t,e,i,n){var a=0,o=0;i==null&&(i=1/0),n==null&&(n=1/0);var s=0;t.eachChild(function(u,l){var f=u.getBoundingRect(),h=t.childAt(l+1),c=h&&h.getBoundingRect(),v,d;if(r==="horizontal"){var _=f.width+(c?-c.x+f.x:0);v=a+_,v>i||u.newline?(a=0,v=_,o+=s+e,s=f.height):s=Math.max(s,f.height)}else{var p=f.height+(c?-c.y+f.y:0);d=o+p,d>n||u.newline?(a+=s+e,o=0,d=p,s=f.width):s=Math.max(s,f.width)}u.newline||(u.x=a,u.y=o,u.markRedraw(),r==="horizontal"?a=v+e:o=d+e)})}var ci=Il;_t(Il,"vertical");_t(Il,"horizontal");function gi(r,t,e){e=Mo(e||0);var i=t.width,n=t.height,a=At(r.left,i),o=At(r.top,n),s=At(r.right,i),u=At(r.bottom,n),l=At(r.width,i),f=At(r.height,n),h=e[2]+e[0],c=e[1]+e[3],v=r.aspect;switch(isNaN(l)&&(l=i-s-c-a),isNaN(f)&&(f=n-u-h-o),v!=null&&(isNaN(l)&&isNaN(f)&&(v>i/n?l=i*.8:f=n*.8),isNaN(l)&&(l=v*f),isNaN(f)&&(f=l/v)),isNaN(a)&&(a=i-s-l-c),isNaN(o)&&(o=n-u-f-h),r.left||r.right){case"center":a=i/2-l/2-e[3];break;case"right":a=i-l-c;break}switch(r.top||r.bottom){case"middle":case"center":o=n/2-f/2-e[0];break;case"bottom":o=n-f-h;break}a=a||0,o=o||0,isNaN(l)&&(l=i-c-a-(s||0)),isNaN(f)&&(f=n-h-o-(u||0));var d=new J(a+e[3],o+e[0],l,f);return d.margin=e,d}function k1(r,t,e,i,n,a){var o=!n||!n.hv||n.hv[0],s=!n||!n.hv||n.hv[1],u=n&&n.boundingMode||"all";if(a=a||r,a.x=r.x,a.y=r.y,!o&&!s)return!1;var l;if(u==="raw")l=r.type==="group"?new J(0,0,+t.width||0,+t.height||0):r.getBoundingRect();else if(l=r.getBoundingRect(),r.needLocalTransform()){var f=r.getLocalTransform();l=l.clone(),l.applyTransform(f)}var h=gi(ot({width:l.width,height:l.height},t),e,i),c=o?h.x-l.x:0,v=s?h.y-l.y:0;return u==="raw"?(a.x=c,a.y=v):(a.x+=c,a.y+=v),a===r&&r.markRedraw(),!0}function MC(r,t){return r[Mr[t][0]]!=null||r[Mr[t][1]]!=null&&r[Mr[t][2]]!=null}function Ka(r){var t=r.layoutMode||r.constructor.layoutMode;return H(t)?t:t?{type:t}:null}function mn(r,t,e){var i=e&&e.ignoreSize;!k(i)&&(i=[i,i]);var n=o(Mr[0],0),a=o(Mr[1],1);l(Mr[0],r,n),l(Mr[1],r,a);function o(f,h){var c={},v=0,d={},_=0,p=2;if(La(f,function(m){d[m]=r[m]}),La(f,function(m){s(t,m)&&(c[m]=d[m]=t[m]),u(c,m)&&v++,u(d,m)&&_++}),i[h])return u(t,f[1])?d[f[2]]=null:u(t,f[2])&&(d[f[1]]=null),d;if(_===p||!v)return d;if(v>=p)return c;for(var g=0;g<f.length;g++){var y=f[g];if(!s(c,y)&&s(r,y)){c[y]=r[y];break}}return c}function s(f,h){return f.hasOwnProperty(h)}function u(f,h){return f[h]!=null&&f[h]!=="auto"}function l(f,h,c){La(f,function(v){h[v]=c[v]})}}function Rl(r){return B1({},r)}function B1(r,t){return t&&r&&La(O1,function(e){t.hasOwnProperty(e)&&(r[e]=t[e])}),r}var F1=yt(),tt=function(r){F(t,r);function t(e,i,n){var a=r.call(this,e,i,n)||this;return a.uid=So("ec_cpt_model"),a}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=Ka(this),a=n?Rl(e):{},o=i.getTheme();ut(e,o.get(this.mainType)),ut(e,this.getDefaultOption()),n&&mn(e,a,n)},t.prototype.mergeOption=function(e,i){ut(this.option,e,!0);var n=Ka(this);n&&mn(this.option,e,n)},t.prototype.optionUpdated=function(e,i){},t.prototype.getDefaultOption=function(){var e=this.constructor;if(!Zy(e))return e.defaultOption;var i=F1(this);if(!i.defaultOption){for(var n=[],a=e;a;){var o=a.prototype.defaultOption;o&&n.push(o),a=a.superClass}for(var s={},u=n.length-1;u>=0;u--)s=ut(s,n[u],!0);i.defaultOption=s}return i.defaultOption},t.prototype.getReferringComponents=function(e,i){var n=e+"Index",a=e+"Id";return Pn(this.ecModel,e,{index:this.get(n,!0),id:this.get(a,!0)},i)},t.prototype.getBoxLayoutParams=function(){var e=this;return{left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")}},t.prototype.getZLevelKey=function(){return""},t.prototype.setZLevel=function(e){this.option.zlevel=e},t.protoInitialize=function(){var e=t.prototype;e.type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0}(),t}(ft);id(tt,ft);ho(tt);S1(tt);b1(tt,N1);function N1(r){var t=[];return D(tt.getClassesByMainType(r),function(e){t=t.concat(e.dependencies||e.prototype.dependencies||[])}),t=Y(t,function(e){return Se(e).main}),r!=="dataset"&&nt(t,"dataset")<=0&&t.unshift("dataset"),t}var Qd="";typeof navigator<"u"&&(Qd=navigator.platform||"");var Zr="rgba(0, 0, 0, 0.2)";const z1={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Zr,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Zr,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Zr,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Zr,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Zr,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Zr,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Qd.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var Jd=$(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),De="original",zt="arrayRows",ce="objectRows",Me="keyedColumns",Ze="typedArray",jd="unknown",Te="column",Si="row",pt={Must:1,Might:2,Not:3},tp=yt();function H1(r){tp(r).datasetMap=$()}function AC(r,t,e){var i={},n=El(t);if(!n||!r)return i;var a=[],o=[],s=t.ecModel,u=tp(s).datasetMap,l=n.uid+"_"+e.seriesLayoutBy,f,h;r=r.slice(),D(r,function(_,p){var g=H(_)?_:r[p]={name:_};g.type==="ordinal"&&f==null&&(f=p,h=d(g)),i[g.name]=[]});var c=u.get(l)||u.set(l,{categoryWayDim:h,valueWayDim:0});D(r,function(_,p){var g=_.name,y=d(_);if(f==null){var m=c.valueWayDim;v(i[g],m,y),v(o,m,y),c.valueWayDim+=y}else if(f===p)v(i[g],0,y),v(a,0,y);else{var m=c.categoryWayDim;v(i[g],m,y),v(o,m,y),c.categoryWayDim+=y}});function v(_,p,g){for(var y=0;y<g;y++)_.push(p+y)}function d(_){var p=_.dimsDef;return p?p.length:1}return a.length&&(i.itemName=a),o.length&&(i.seriesName=o),i}function PC(r,t,e){var i={},n=El(r);if(!n)return i;var a=t.sourceFormat,o=t.dimensionsDefine,s;(a===ce||a===Me)&&D(o,function(f,h){(H(f)?f.name:f)==="name"&&(s=h)});var u=function(){for(var f={},h={},c=[],v=0,d=Math.min(5,e);v<d;v++){var _=rp(t.data,a,t.seriesLayoutBy,o,t.startIndex,v);c.push(_);var p=_===pt.Not;if(p&&f.v==null&&v!==s&&(f.v=v),(f.n==null||f.n===f.v||!p&&c[f.n]===pt.Not)&&(f.n=v),g(f)&&c[f.n]!==pt.Not)return f;p||(_===pt.Might&&h.v==null&&v!==s&&(h.v=v),(h.n==null||h.n===h.v)&&(h.n=v))}function g(y){return y.v!=null&&y.n!=null}return g(f)?f:g(h)?h:null}();if(u){i.value=[u.v];var l=s??u.n;i.itemName=[l],i.seriesName=[l]}return i}function El(r){var t=r.get("data",!0);if(!t)return Pn(r.ecModel,"dataset",{index:r.get("datasetIndex",!0),id:r.get("datasetId",!0)},fo).models[0]}function G1(r){return!r.get("transform",!0)&&!r.get("fromTransformResult",!0)?[]:Pn(r.ecModel,"dataset",{index:r.get("fromDatasetIndex",!0),id:r.get("fromDatasetId",!0)},fo).models}function ep(r,t){return rp(r.data,r.sourceFormat,r.seriesLayoutBy,r.dimensionsDefine,r.startIndex,t)}function rp(r,t,e,i,n,a){var o,s=5;if(Nt(r))return pt.Not;var u,l;if(i){var f=i[a];H(f)?(u=f.name,l=f.type):B(f)&&(u=f)}if(l!=null)return l==="ordinal"?pt.Must:pt.Not;if(t===zt){var h=r;if(e===Si){for(var c=h[a],v=0;v<(c||[]).length&&v<s;v++)if((o=w(c[n+v]))!=null)return o}else for(var v=0;v<h.length&&v<s;v++){var d=h[n+v];if(d&&(o=w(d[a]))!=null)return o}}else if(t===ce){var _=r;if(!u)return pt.Not;for(var v=0;v<_.length&&v<s;v++){var p=_[v];if(p&&(o=w(p[u]))!=null)return o}}else if(t===Me){var g=r;if(!u)return pt.Not;var c=g[u];if(!c||Nt(c))return pt.Not;for(var v=0;v<c.length&&v<s;v++)if((o=w(c[v]))!=null)return o}else if(t===De)for(var y=r,v=0;v<y.length&&v<s;v++){var p=y[v],m=lo(p);if(!k(m))return pt.Not;if((o=w(m[a]))!=null)return o}function w(b){var S=B(b);if(b!=null&&isFinite(b)&&b!=="")return S?pt.Might:pt.Not;if(S&&b!=="-")return pt.Must}return pt.Not}var Cu=$();function LC(r,t){xe(Cu.get(r)==null&&t),Cu.set(r,t)}function W1(r,t,e){var i=Cu.get(t);if(!i)return e;var n=i(r);return n?e.concat(n):e}var Rh=yt();yt();var Ol=function(){function r(){}return r.prototype.getColorFromPalette=function(t,e,i){var n=Tt(this.get("color",!0)),a=this.get("colorLayer",!0);return U1(this,Rh,n,a,t,e,i)},r.prototype.clearColorPalette=function(){Y1(this,Rh)},r}();function V1(r,t){for(var e=r.length,i=0;i<e;i++)if(r[i].length>t)return r[i];return r[e-1]}function U1(r,t,e,i,n,a,o){a=a||r;var s=t(a),u=s.paletteIdx||0,l=s.paletteNameMap=s.paletteNameMap||{};if(l.hasOwnProperty(n))return l[n];var f=o==null||!i?e:V1(i,o);if(f=f||e,!(!f||!f.length)){var h=f[u];return n&&(l[n]=h),s.paletteIdx=(u+1)%f.length,h}}function Y1(r,t){t(r).paletteIdx=0,t(r).paletteNameMap={}}var ea,Ii,Eh,Oh="\0_ec_inner",X1=1,kl=function(r){F(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(e,i,n,a,o,s){a=a||{},this.option=null,this._theme=new ft(a),this._locale=new ft(o),this._optionManager=s},t.prototype.setOption=function(e,i,n){var a=Fh(i);this._optionManager.setOption(e,n,a),this._resetOption(null,a)},t.prototype.resetOption=function(e,i){return this._resetOption(e,Fh(i))},t.prototype._resetOption=function(e,i){var n=!1,a=this._optionManager;if(!e||e==="recreate"){var o=a.mountOption(e==="recreate");!this.option||e==="recreate"?Eh(this,o):(this.restoreData(),this._mergeOption(o,i)),n=!0}if((e==="timeline"||e==="media")&&this.restoreData(),!e||e==="recreate"||e==="timeline"){var s=a.getTimelineOption(this);s&&(n=!0,this._mergeOption(s,i))}if(!e||e==="recreate"||e==="media"){var u=a.getMediaOption(this);u.length&&D(u,function(l){n=!0,this._mergeOption(l,i)},this)}return n},t.prototype.mergeOption=function(e){this._mergeOption(e,null)},t.prototype._mergeOption=function(e,i){var n=this.option,a=this._componentsMap,o=this._componentsCount,s=[],u=$(),l=i&&i.replaceMergeMainTypeMap;H1(this),D(e,function(h,c){h!=null&&(tt.hasClass(c)?c&&(s.push(c),u.set(c,!0)):n[c]=n[c]==null?K(h):ut(n[c],h,!0))}),l&&l.each(function(h,c){tt.hasClass(c)&&!u.get(c)&&(s.push(c),u.set(c,!0))}),tt.topologicalTravel(s,tt.getAllClassMainTypes(),f,this);function f(h){var c=W1(this,h,Tt(e[h])),v=a.get(h),d=v?l&&l.get(h)?"replaceMerge":"normalMerge":"replaceAll",_=Oy(v,c,d);Gy(_,h,tt),n[h]=null,a.set(h,null),o.set(h,0);var p=[],g=[],y=0,m;D(_,function(w,b){var S=w.existing,x=w.newOption;if(!x)S&&(S.mergeOption({},this),S.optionUpdated({},!1));else{var C=h==="series",M=tt.getClass(h,w.keyInfo.subType,!C);if(!M)return;if(h==="tooltip"){if(m)return;m=!0}if(S&&S.constructor===M)S.name=w.keyInfo.name,S.mergeOption(x,this),S.optionUpdated(x,!1);else{var A=O({componentIndex:b},w.keyInfo);S=new M(x,this,this,A),O(S,A),w.brandNew&&(S.__requireNewView=!0),S.init(x,this,this),S.optionUpdated(null,!0)}}S?(p.push(S.option),g.push(S),y++):(p.push(void 0),g.push(void 0))},this),n[h]=p,a.set(h,g),o.set(h,y),h==="series"&&ea(this)}this._seriesIndices||ea(this)},t.prototype.getOption=function(){var e=K(this.option);return D(e,function(i,n){if(tt.hasClass(n)){for(var a=Tt(i),o=a.length,s=!1,u=o-1;u>=0;u--)a[u]&&!pn(a[u])?s=!0:(a[u]=null,!s&&o--);a.length=o,e[n]=a}}),delete e[Oh],e},t.prototype.getTheme=function(){return this._theme},t.prototype.getLocaleModel=function(){return this._locale},t.prototype.setUpdatePayload=function(e){this._payload=e},t.prototype.getUpdatePayload=function(){return this._payload},t.prototype.getComponent=function(e,i){var n=this._componentsMap.get(e);if(n){var a=n[i||0];if(a)return a;if(i==null){for(var o=0;o<n.length;o++)if(n[o])return n[o]}}},t.prototype.queryComponents=function(e){var i=e.mainType;if(!i)return[];var n=e.index,a=e.id,o=e.name,s=this._componentsMap.get(i);if(!s||!s.length)return[];var u;return n!=null?(u=[],D(Tt(n),function(l){s[l]&&u.push(s[l])})):a!=null?u=kh("id",a,s):o!=null?u=kh("name",o,s):u=Et(s,function(l){return!!l}),Bh(u,e)},t.prototype.findComponents=function(e){var i=e.query,n=e.mainType,a=s(i),o=a?this.queryComponents(a):Et(this._componentsMap.get(n),function(l){return!!l});return u(Bh(o,e));function s(l){var f=n+"Index",h=n+"Id",c=n+"Name";return l&&(l[f]!=null||l[h]!=null||l[c]!=null)?{mainType:n,index:l[f],id:l[h],name:l[c]}:null}function u(l){return e.filter?Et(l,e.filter):l}},t.prototype.eachComponent=function(e,i,n){var a=this._componentsMap;if(Z(e)){var o=i,s=e;a.each(function(h,c){for(var v=0;h&&v<h.length;v++){var d=h[v];d&&s.call(o,c,d,d.componentIndex)}})}else for(var u=B(e)?a.get(e):H(e)?this.findComponents(e):null,l=0;u&&l<u.length;l++){var f=u[l];f&&i.call(n,f,f.componentIndex)}},t.prototype.getSeriesByName=function(e){var i=le(e,null);return Et(this._componentsMap.get("series"),function(n){return!!n&&i!=null&&n.name===i})},t.prototype.getSeriesByIndex=function(e){return this._componentsMap.get("series")[e]},t.prototype.getSeriesByType=function(e){return Et(this._componentsMap.get("series"),function(i){return!!i&&i.subType===e})},t.prototype.getSeries=function(){return Et(this._componentsMap.get("series"),function(e){return!!e})},t.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},t.prototype.eachSeries=function(e,i){Ii(this),D(this._seriesIndices,function(n){var a=this._componentsMap.get("series")[n];e.call(i,a,n)},this)},t.prototype.eachRawSeries=function(e,i){D(this._componentsMap.get("series"),function(n){n&&e.call(i,n,n.componentIndex)})},t.prototype.eachSeriesByType=function(e,i,n){Ii(this),D(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];o.subType===e&&i.call(n,o,a)},this)},t.prototype.eachRawSeriesByType=function(e,i,n){return D(this.getSeriesByType(e),i,n)},t.prototype.isSeriesFiltered=function(e){return Ii(this),this._seriesIndicesMap.get(e.componentIndex)==null},t.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},t.prototype.filterSeries=function(e,i){Ii(this);var n=[];D(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];e.call(i,o,a)&&n.push(a)},this),this._seriesIndices=n,this._seriesIndicesMap=$(n)},t.prototype.restoreData=function(e){ea(this);var i=this._componentsMap,n=[];i.each(function(a,o){tt.hasClass(o)&&n.push(o)}),tt.topologicalTravel(n,tt.getAllClassMainTypes(),function(a){D(i.get(a),function(o){o&&(a!=="series"||!$1(o,e))&&o.restoreData()})})},t.internalField=function(){ea=function(e){var i=e._seriesIndices=[];D(e._componentsMap.get("series"),function(n){n&&i.push(n.componentIndex)}),e._seriesIndicesMap=$(i)},Ii=function(e){},Eh=function(e,i){e.option={},e.option[Oh]=X1,e._componentsMap=$({series:[]}),e._componentsCount=$();var n=i.aria;H(n)&&n.enabled==null&&(n.enabled=!0),q1(i,e._theme.option),ut(i,z1,!1),e._mergeOption(i,null)}}(),t}(ft);function $1(r,t){if(t){var e=t.seriesIndex,i=t.seriesId,n=t.seriesName;return e!=null&&r.componentIndex!==e||i!=null&&r.id!==i||n!=null&&r.name!==n}}function q1(r,t){var e=r.color&&!r.colorLayer;D(t,function(i,n){n==="colorLayer"&&e||tt.hasClass(n)||(typeof i=="object"?r[n]=r[n]?ut(r[n],i,!1):K(i):r[n]==null&&(r[n]=i))})}function kh(r,t,e){if(k(t)){var i=$();return D(t,function(a){if(a!=null){var o=le(a,null);o!=null&&i.set(a,!0)}}),Et(e,function(a){return a&&i.get(a[r])})}else{var n=le(t,null);return Et(e,function(a){return a&&n!=null&&a[r]===n})}}function Bh(r,t){return t.hasOwnProperty("subType")?Et(r,function(e){return e&&e.subType===t.subType}):r}function Fh(r){var t=$();return r&&D(Tt(r.replaceMerge),function(e){t.set(e,!0)}),{replaceMergeMainTypeMap:t}}ke(kl,Ol);var Z1=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],ip=function(){function r(t){D(Z1,function(e){this[e]=st(t[e],t)},this)}return r}(),bs={},np=function(){function r(){this._coordinateSystems=[]}return r.prototype.create=function(t,e){var i=[];D(bs,function(n,a){var o=n.create(t,e);i=i.concat(o||[])}),this._coordinateSystems=i},r.prototype.update=function(t,e){D(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},r.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},r.register=function(t,e){bs[t]=e},r.get=function(t){return bs[t]},r}(),K1=/^(min|max)?(.+)$/,Q1=function(){function r(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return r.prototype.setOption=function(t,e,i){t&&(D(Tt(t.series),function(o){o&&o.data&&Nt(o.data)&&Ba(o.data)}),D(Tt(t.dataset),function(o){o&&o.source&&Nt(o.source)&&Ba(o.source)})),t=K(t);var n=this._optionBackup,a=J1(t,e,!n);this._newBaseOption=a.baseOption,n?(a.timelineOptions.length&&(n.timelineOptions=a.timelineOptions),a.mediaList.length&&(n.mediaList=a.mediaList),a.mediaDefault&&(n.mediaDefault=a.mediaDefault)):this._optionBackup=a},r.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],K(t?e.baseOption:this._newBaseOption)},r.prototype.getTimelineOption=function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=K(i[n.getCurrentIndex()]))}return e},r.prototype.getMediaOption=function(t){var e=this._api.getWidth(),i=this._api.getHeight(),n=this._mediaList,a=this._mediaDefault,o=[],s=[];if(!n.length&&!a)return s;for(var u=0,l=n.length;u<l;u++)j1(n[u].query,e,i)&&o.push(u);return!o.length&&a&&(o=[-1]),o.length&&!ew(o,this._currentMediaIndices)&&(s=Y(o,function(f){return K(f===-1?a.option:n[f].option)})),this._currentMediaIndices=o,s},r}();function J1(r,t,e){var i=[],n,a,o=r.baseOption,s=r.timeline,u=r.options,l=r.media,f=!!r.media,h=!!(u||s||o&&o.timeline);o?(a=o,a.timeline||(a.timeline=s)):((h||f)&&(r.options=r.media=null),a=r),f&&k(l)&&D(l,function(v){v&&v.option&&(v.query?i.push(v):n||(n=v))}),c(a),D(u,function(v){return c(v)}),D(i,function(v){return c(v.option)});function c(v){D(t,function(d){d(v,e)})}return{baseOption:a,timelineOptions:u||[],mediaDefault:n,mediaList:i}}function j1(r,t,e){var i={width:t,height:e,aspectratio:t/e},n=!0;return D(r,function(a,o){var s=o.match(K1);if(!(!s||!s[1]||!s[2])){var u=s[1],l=s[2].toLowerCase();tw(i[l],a,u)||(n=!1)}}),n}function tw(r,t,e){return e==="min"?r>=t:e==="max"?r<=t:r===t}function ew(r,t){return r.join(",")===t.join(",")}var ee=D,wn=H,Nh=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Ts(r){var t=r&&r.itemStyle;if(t)for(var e=0,i=Nh.length;e<i;e++){var n=Nh[e],a=t.normal,o=t.emphasis;a&&a[n]&&(r[n]=r[n]||{},r[n].normal?ut(r[n].normal,a[n]):r[n].normal=a[n],a[n]=null),o&&o[n]&&(r[n]=r[n]||{},r[n].emphasis?ut(r[n].emphasis,o[n]):r[n].emphasis=o[n],o[n]=null)}}function bt(r,t,e){if(r&&r[t]&&(r[t].normal||r[t].emphasis)){var i=r[t].normal,n=r[t].emphasis;i&&(e?(r[t].normal=r[t].emphasis=null,ot(r[t],i)):r[t]=i),n&&(r.emphasis=r.emphasis||{},r.emphasis[t]=n,n.focus&&(r.emphasis.focus=n.focus),n.blurScope&&(r.emphasis.blurScope=n.blurScope))}}function Ki(r){bt(r,"itemStyle"),bt(r,"lineStyle"),bt(r,"areaStyle"),bt(r,"label"),bt(r,"labelLine"),bt(r,"upperLabel"),bt(r,"edgeLabel")}function vt(r,t){var e=wn(r)&&r[t],i=wn(e)&&e.textStyle;if(i)for(var n=0,a=Gf.length;n<a;n++){var o=Gf[n];i.hasOwnProperty(o)&&(e[o]=i[o])}}function $t(r){r&&(Ki(r),vt(r,"label"),r.emphasis&&vt(r.emphasis,"label"))}function rw(r){if(wn(r)){Ts(r),Ki(r),vt(r,"label"),vt(r,"upperLabel"),vt(r,"edgeLabel"),r.emphasis&&(vt(r.emphasis,"label"),vt(r.emphasis,"upperLabel"),vt(r.emphasis,"edgeLabel"));var t=r.markPoint;t&&(Ts(t),$t(t));var e=r.markLine;e&&(Ts(e),$t(e));var i=r.markArea;i&&$t(i);var n=r.data;if(r.type==="graph"){n=n||r.nodes;var a=r.links||r.edges;if(a&&!Nt(a))for(var o=0;o<a.length;o++)$t(a[o]);D(r.categories,function(l){Ki(l)})}if(n&&!Nt(n))for(var o=0;o<n.length;o++)$t(n[o]);if(t=r.markPoint,t&&t.data)for(var s=t.data,o=0;o<s.length;o++)$t(s[o]);if(e=r.markLine,e&&e.data)for(var u=e.data,o=0;o<u.length;o++)k(u[o])?($t(u[o][0]),$t(u[o][1])):$t(u[o]);r.type==="gauge"?(vt(r,"axisLabel"),vt(r,"title"),vt(r,"detail")):r.type==="treemap"?(bt(r.breadcrumb,"itemStyle"),D(r.levels,function(l){Ki(l)})):r.type==="tree"&&Ki(r.leaves)}}function Le(r){return k(r)?r:r?[r]:[]}function zh(r){return(k(r)?r[0]:r)||{}}function iw(r,t){ee(Le(r.series),function(i){wn(i)&&rw(i)});var e=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&e.push("valueAxis","categoryAxis","logAxis","timeAxis"),ee(e,function(i){ee(Le(r[i]),function(n){n&&(vt(n,"axisLabel"),vt(n.axisPointer,"label"))})}),ee(Le(r.parallel),function(i){var n=i&&i.parallelAxisDefault;vt(n,"axisLabel"),vt(n&&n.axisPointer,"label")}),ee(Le(r.calendar),function(i){bt(i,"itemStyle"),vt(i,"dayLabel"),vt(i,"monthLabel"),vt(i,"yearLabel")}),ee(Le(r.radar),function(i){vt(i,"name"),i.name&&i.axisName==null&&(i.axisName=i.name,delete i.name),i.nameGap!=null&&i.axisNameGap==null&&(i.axisNameGap=i.nameGap,delete i.nameGap)}),ee(Le(r.geo),function(i){wn(i)&&($t(i),ee(Le(i.regions),function(n){$t(n)}))}),ee(Le(r.timeline),function(i){$t(i),bt(i,"label"),bt(i,"itemStyle"),bt(i,"controlStyle",!0);var n=i.data;k(n)&&D(n,function(a){H(a)&&(bt(a,"label"),bt(a,"itemStyle"))})}),ee(Le(r.toolbox),function(i){bt(i,"iconStyle"),ee(i.feature,function(n){bt(n,"iconStyle")})}),vt(zh(r.axisPointer),"label"),vt(zh(r.tooltip).axisPointer,"label")}function nw(r,t){for(var e=t.split(","),i=r,n=0;n<e.length&&(i=i&&i[e[n]],i!=null);n++);return i}function aw(r,t,e,i){for(var n=t.split(","),a=r,o,s=0;s<n.length-1;s++)o=n[s],a[o]==null&&(a[o]={}),a=a[o];a[n[s]]==null&&(a[n[s]]=e)}function Hh(r){r&&D(ow,function(t){t[0]in r&&!(t[1]in r)&&(r[t[1]]=r[t[0]])})}var ow=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],sw=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],xs=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Ri(r){var t=r&&r.itemStyle;if(t)for(var e=0;e<xs.length;e++){var i=xs[e][1],n=xs[e][0];t[i]!=null&&(t[n]=t[i])}}function Gh(r){r&&r.alignTo==="edge"&&r.margin!=null&&r.edgeDistance==null&&(r.edgeDistance=r.margin)}function Wh(r){r&&r.downplay&&!r.blur&&(r.blur=r.downplay)}function uw(r){r&&r.focusNodeAdjacency!=null&&(r.emphasis=r.emphasis||{},r.emphasis.focus==null&&(r.emphasis.focus="adjacency"))}function ap(r,t){if(r)for(var e=0;e<r.length;e++)t(r[e]),r[e]&&ap(r[e].children,t)}function op(r,t){iw(r,t),r.series=Tt(r.series),D(r.series,function(e){if(H(e)){var i=e.type;if(i==="line")e.clipOverflow!=null&&(e.clip=e.clipOverflow);else if(i==="pie"||i==="gauge"){e.clockWise!=null&&(e.clockwise=e.clockWise),Gh(e.label);var n=e.data;if(n&&!Nt(n))for(var a=0;a<n.length;a++)Gh(n[a]);e.hoverOffset!=null&&(e.emphasis=e.emphasis||{},(e.emphasis.scaleSize=null)&&(e.emphasis.scaleSize=e.hoverOffset))}else if(i==="gauge"){var o=nw(e,"pointer.color");o!=null&&aw(e,"itemStyle.color",o)}else if(i==="bar"){Ri(e),Ri(e.backgroundStyle),Ri(e.emphasis);var n=e.data;if(n&&!Nt(n))for(var a=0;a<n.length;a++)typeof n[a]=="object"&&(Ri(n[a]),Ri(n[a]&&n[a].emphasis))}else if(i==="sunburst"){var s=e.highlightPolicy;s&&(e.emphasis=e.emphasis||{},e.emphasis.focus||(e.emphasis.focus=s)),Wh(e),ap(e.data,Wh)}else i==="graph"||i==="sankey"?uw(e):i==="map"&&(e.mapType&&!e.map&&(e.map=e.mapType),e.mapLocation&&ot(e,e.mapLocation));e.hoverAnimation!=null&&(e.emphasis=e.emphasis||{},e.emphasis&&e.emphasis.scale==null&&(e.emphasis.scale=e.hoverAnimation)),Hh(e)}}),r.dataRange&&(r.visualMap=r.dataRange),D(sw,function(e){var i=r[e];i&&(k(i)||(i=[i]),D(i,function(n){Hh(n)}))})}function lw(r){var t=$();r.eachSeries(function(e){var i=e.get("stack");if(i){var n=t.get(i)||t.set(i,[]),a=e.getData(),o={stackResultDimension:a.getCalculationInfo("stackResultDimension"),stackedOverDimension:a.getCalculationInfo("stackedOverDimension"),stackedDimension:a.getCalculationInfo("stackedDimension"),stackedByDimension:a.getCalculationInfo("stackedByDimension"),isStackedByIndex:a.getCalculationInfo("isStackedByIndex"),data:a,seriesModel:e};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;n.length&&a.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(o)}}),t.each(fw)}function fw(r){D(r,function(t,e){var i=[],n=[NaN,NaN],a=[t.stackResultDimension,t.stackedOverDimension],o=t.data,s=t.isStackedByIndex,u=t.seriesModel.get("stackStrategy")||"samesign";o.modify(a,function(l,f,h){var c=o.get(t.stackedDimension,h);if(isNaN(c))return n;var v,d;s?d=o.getRawIndex(h):v=o.get(t.stackedByDimension,h);for(var _=NaN,p=e-1;p>=0;p--){var g=r[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,v)),d>=0){var y=g.data.getByRawIndex(g.stackResultDimension,d);if(u==="all"||u==="positive"&&y>0||u==="negative"&&y<0||u==="samesign"&&c>=0&&y>0||u==="samesign"&&c<=0&&y<0){c=Dy(c,y),_=y;break}}}return i[0]=c,i[1]=_,i})})}var Ao=function(){function r(t){this.data=t.data||(t.sourceFormat===Me?{}:[]),this.sourceFormat=t.sourceFormat||jd,this.seriesLayoutBy=t.seriesLayoutBy||Te,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var i=0;i<e.length;i++){var n=e[i];n.type==null&&ep(this,i)===pt.Must&&(n.type="ordinal")}}return r}();function Bl(r){return r instanceof Ao}function Du(r,t,e){e=e||up(r);var i=t.seriesLayoutBy,n=vw(r,e,i,t.sourceHeader,t.dimensions),a=new Ao({data:r,sourceFormat:e,seriesLayoutBy:i,dimensionsDefine:n.dimensionsDefine,startIndex:n.startIndex,dimensionsDetectedCount:n.dimensionsDetectedCount,metaRawOption:K(t)});return a}function sp(r){return new Ao({data:r,sourceFormat:Nt(r)?Ze:De})}function hw(r){return new Ao({data:r.data,sourceFormat:r.sourceFormat,seriesLayoutBy:r.seriesLayoutBy,dimensionsDefine:K(r.dimensionsDefine),startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount})}function up(r){var t=jd;if(Nt(r))t=Ze;else if(k(r)){r.length===0&&(t=zt);for(var e=0,i=r.length;e<i;e++){var n=r[e];if(n!=null){if(k(n)||Nt(n)){t=zt;break}else if(H(n)){t=ce;break}}}}else if(H(r)){for(var a in r)if(di(r,a)&&Ft(r[a])){t=Me;break}}return t}function vw(r,t,e,i,n){var a,o;if(!r)return{dimensionsDefine:Vh(n),startIndex:o,dimensionsDetectedCount:a};if(t===zt){var s=r;i==="auto"||i==null?Uh(function(l){l!=null&&l!=="-"&&(B(l)?o==null&&(o=1):o=0)},e,s,10):o=ht(i)?i:i?1:0,!n&&o===1&&(n=[],Uh(function(l,f){n[f]=l!=null?l+"":""},e,s,1/0)),a=n?n.length:e===Si?s.length:s[0]?s[0].length:null}else if(t===ce)n||(n=cw(r));else if(t===Me)n||(n=[],D(r,function(l,f){n.push(f)}));else if(t===De){var u=lo(r[0]);a=k(u)&&u.length||1}return{startIndex:o,dimensionsDefine:Vh(n),dimensionsDetectedCount:a}}function cw(r){for(var t=0,e;t<r.length&&!(e=r[t++]););if(e)return lt(e)}function Vh(r){if(r){var t=$();return Y(r,function(e,i){e=H(e)?e:{name:e};var n={name:e.name,displayName:e.displayName,type:e.type};if(n.name==null)return n;n.name+="",n.displayName==null&&(n.displayName=n.name);var a=t.get(n.name);return a?n.name+="-"+a.count++:t.set(n.name,{count:1}),n})}}function Uh(r,t,e,i){if(t===Si)for(var n=0;n<e.length&&n<i;n++)r(e[n]?e[n][0]:null,n);else for(var a=e[0]||[],n=0;n<a.length&&n<i;n++)r(a[n],n)}function lp(r){var t=r.sourceFormat;return t===ce||t===Me}var wr,Sr,br,Yh,Xh,fp=function(){function r(t,e){var i=Bl(t)?t:sp(t);this._source=i;var n=this._data=i.data;i.sourceFormat===Ze&&(this._offset=0,this._dimSize=e,this._data=n),Xh(this,n,i)}return r.prototype.getSource=function(){return this._source},r.prototype.count=function(){return 0},r.prototype.getItem=function(t,e){},r.prototype.appendData=function(t){},r.prototype.clean=function(){},r.protoInitialize=function(){var t=r.prototype;t.pure=!1,t.persistent=!0}(),r.internalField=function(){var t;Xh=function(o,s,u){var l=u.sourceFormat,f=u.seriesLayoutBy,h=u.startIndex,c=u.dimensionsDefine,v=Yh[Fl(l,f)];if(O(o,v),l===Ze)o.getItem=e,o.count=n,o.fillStorage=i;else{var d=hp(l,f);o.getItem=st(d,null,s,h,c);var _=vp(l,f);o.count=st(_,null,s,h,c)}};var e=function(o,s){o=o-this._offset,s=s||[];for(var u=this._data,l=this._dimSize,f=l*o,h=0;h<l;h++)s[h]=u[f+h];return s},i=function(o,s,u,l){for(var f=this._data,h=this._dimSize,c=0;c<h;c++){for(var v=l[c],d=v[0]==null?1/0:v[0],_=v[1]==null?-1/0:v[1],p=s-o,g=u[c],y=0;y<p;y++){var m=f[y*h+c];g[o+y]=m,m<d&&(d=m),m>_&&(_=m)}v[0]=d,v[1]=_}},n=function(){return this._data?this._data.length/this._dimSize:0};Yh=(t={},t[zt+"_"+Te]={pure:!0,appendData:a},t[zt+"_"+Si]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[ce]={pure:!0,appendData:a},t[Me]={pure:!0,appendData:function(o){var s=this._data;D(o,function(u,l){for(var f=s[l]||(s[l]=[]),h=0;h<(u||[]).length;h++)f.push(u[h])})}},t[De]={appendData:a},t[Ze]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},t);function a(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),r}(),$h=function(r,t,e,i){return r[i]},dw=(wr={},wr[zt+"_"+Te]=function(r,t,e,i){return r[i+t]},wr[zt+"_"+Si]=function(r,t,e,i,n){i+=t;for(var a=n||[],o=r,s=0;s<o.length;s++){var u=o[s];a[s]=u?u[i]:null}return a},wr[ce]=$h,wr[Me]=function(r,t,e,i,n){for(var a=n||[],o=0;o<e.length;o++){var s=e[o].name,u=r[s];a[o]=u?u[i]:null}return a},wr[De]=$h,wr);function hp(r,t){var e=dw[Fl(r,t)];return e}var qh=function(r,t,e){return r.length},pw=(Sr={},Sr[zt+"_"+Te]=function(r,t,e){return Math.max(0,r.length-t)},Sr[zt+"_"+Si]=function(r,t,e){var i=r[0];return i?Math.max(0,i.length-t):0},Sr[ce]=qh,Sr[Me]=function(r,t,e){var i=e[0].name,n=r[i];return n?n.length:0},Sr[De]=qh,Sr);function vp(r,t){var e=pw[Fl(r,t)];return e}var Cs=function(r,t,e){return r[t]},gw=(br={},br[zt]=Cs,br[ce]=function(r,t,e){return r[e]},br[Me]=Cs,br[De]=function(r,t,e){var i=lo(r);return i instanceof Array?i[t]:i},br[Ze]=Cs,br);function cp(r){var t=gw[r];return t}function Fl(r,t){return r===zt?r+"_"+t:r}function Qa(r,t,e){if(r){var i=r.getRawDataItem(t);if(i!=null){var n=r.getStore(),a=n.getSource().sourceFormat;if(e!=null){var o=r.getDimensionIndex(e),s=n.getDimensionProperty(o);return cp(a)(i,o,s)}else{var u=i;return a===De&&(u=lo(i)),u}}}}var _w=/\{@(.+?)\}/g,yw=function(){function r(){}return r.prototype.getDataParams=function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),a=i.getRawIndex(t),o=i.getName(t),s=i.getRawDataItem(t),u=i.getItemVisual(t,"style"),l=u&&u[i.getItemVisual(t,"drawType")||"fill"],f=u&&u.stroke,h=this.mainType,c=h==="series",v=i.userOutput&&i.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:a,data:s,dataType:e,value:n,color:l,borderColor:f,dimensionNames:v?v.fullDimensions:null,encode:v?v.encode:null,$vars:["seriesName","name","value"]}},r.prototype.getFormattedLabel=function(t,e,i,n,a,o){e=e||"normal";var s=this.getData(i),u=this.getDataParams(t,i);if(o&&(u.value=o.interpolatedValue),n!=null&&k(u.value)&&(u.value=u.value[n]),!a){var l=s.getItemModel(t);a=l.get(e==="normal"?["label","formatter"]:[e,"label","formatter"])}if(Z(a))return u.status=e,u.dimensionIndex=n,a(u);if(B(a)){var f=Kd(a,u);return f.replace(_w,function(h,c){var v=c.length,d=c;d.charAt(0)==="["&&d.charAt(v-1)==="]"&&(d=+d.slice(1,v-1));var _=Qa(s,t,d);if(o&&k(o.interpolatedValue)){var p=s.getDimensionIndex(d);p>=0&&(_=o.interpolatedValue[p])}return _!=null?_+"":""})}},r.prototype.getRawValue=function(t,e){return Qa(this.getData(e),t)},r.prototype.formatTooltip=function(t,e,i){},r}();function Zh(r){var t,e;return H(r)?r.type&&(e=r):t=r,{text:t,frag:e}}function sn(r){return new mw(r)}var mw=function(){function r(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return r.prototype.perform=function(t){var e=this._upstream,i=t&&t.skip;if(this._dirty&&e){var n=this.context;n.data=n.outputData=e.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!i&&(a=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,u=f(t&&t.modBy),l=t&&t.modDataCount||0;(o!==u||s!==l)&&(a="reset");function f(y){return!(y>=1)&&(y=1),y}var h;(this._dirty||a==="reset")&&(this._dirty=!1,h=this._doReset(i)),this._modBy=u,this._modDataCount=l;var c=t&&t.step;if(e?this._dueEnd=e._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var v=this._dueIndex,d=Math.min(c!=null?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(h||v<d)){var _=this._progress;if(k(_))for(var p=0;p<_.length;p++)this._doProgress(_[p],v,d,u,l);else this._doProgress(_,v,d,u,l)}this._dueIndex=d;var g=this._settedOutputEnd!=null?this._settedOutputEnd:d;this._outputDueEnd=g}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},r.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.prototype._doProgress=function(t,e,i,n,a){Kh.reset(e,i,n,a),this._callingProgress=t,this._callingProgress({start:e,end:i,count:i-e,next:Kh.next},this.context)},r.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var e,i;!t&&this._reset&&(e=this._reset(this.context),e&&e.progress&&(i=e.forceFirstProgress,e=e.progress),k(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var n=this._downstream;return n&&n.dirty(),i},r.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},r.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.prototype.getUpstream=function(){return this._upstream},r.prototype.getDownstream=function(){return this._downstream},r.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},r}(),Kh=function(){var r,t,e,i,n,a={reset:function(u,l,f,h){t=u,r=l,e=f,i=h,n=Math.ceil(i/e),a.next=e>1&&i>0?s:o}};return a;function o(){return t<r?t++:null}function s(){var u=t%n*e+Math.ceil(t/n),l=t>=r?null:u<i?u:t;return t++,l}}();function Ia(r,t){var e=t&&t.type;return e==="ordinal"?r:(e==="time"&&!ht(r)&&r!=null&&r!=="-"&&(r=+Oe(r)),r==null||r===""?NaN:+r)}$({number:function(r){return parseFloat(r)},time:function(r){return+Oe(r)},trim:function(r){return B(r)?we(r):r}});var ww=function(){function r(t,e){var i=t==="desc";this._resultLT=i?1:-1,e==null&&(e=i?"min":"max"),this._incomparable=e==="min"?-1/0:1/0}return r.prototype.evaluate=function(t,e){var i=ht(t)?t:Ua(t),n=ht(e)?e:Ua(e),a=isNaN(i),o=isNaN(n);if(a&&(i=this._incomparable),o&&(n=this._incomparable),a&&o){var s=B(t),u=B(e);s&&(i=u?t:0),u&&(n=s?e:0)}return i<n?this._resultLT:i>n?-this._resultLT:0},r}(),Sw=function(){function r(){}return r.prototype.getRawData=function(){throw new Error("not supported")},r.prototype.getRawDataItem=function(t){throw new Error("not supported")},r.prototype.cloneRawData=function(){},r.prototype.getDimensionInfo=function(t){},r.prototype.cloneAllDimensionInfo=function(){},r.prototype.count=function(){},r.prototype.retrieveValue=function(t,e){},r.prototype.retrieveValueFromItem=function(t,e){},r.prototype.convertValue=function(t,e){return Ia(t,e)},r}();function bw(r,t){var e=new Sw,i=r.data,n=e.sourceFormat=r.sourceFormat,a=r.startIndex,o="";r.seriesLayoutBy!==Te&&Ot(o);var s=[],u={},l=r.dimensionsDefine;if(l)D(l,function(_,p){var g=_.name,y={index:p,name:g,displayName:_.displayName};if(s.push(y),g!=null){var m="";di(u,g)&&Ot(m),u[g]=y}});else for(var f=0;f<r.dimensionsDetectedCount;f++)s.push({index:f});var h=hp(n,Te);t.__isBuiltIn&&(e.getRawDataItem=function(_){return h(i,a,s,_)},e.getRawData=st(Tw,null,r)),e.cloneRawData=st(xw,null,r);var c=vp(n,Te);e.count=st(c,null,i,a,s);var v=cp(n);e.retrieveValue=function(_,p){var g=h(i,a,s,_);return d(g,p)};var d=e.retrieveValueFromItem=function(_,p){if(_!=null){var g=s[p];if(g)return v(_,p,g.name)}};return e.getDimensionInfo=st(Cw,null,s,u),e.cloneAllDimensionInfo=st(Dw,null,s),e}function Tw(r){var t=r.sourceFormat;if(!Nl(t)){var e="";Ot(e)}return r.data}function xw(r){var t=r.sourceFormat,e=r.data;if(!Nl(t)){var i="";Ot(i)}if(t===zt){for(var n=[],a=0,o=e.length;a<o;a++)n.push(e[a].slice());return n}else if(t===ce){for(var n=[],a=0,o=e.length;a<o;a++)n.push(O({},e[a]));return n}}function Cw(r,t,e){if(e!=null){if(ht(e)||!isNaN(e)&&!di(t,e))return r[e];if(di(t,e))return t[e]}}function Dw(r){return K(r)}var dp=$();function Mw(r){r=K(r);var t=r.type,e="";t||Ot(e);var i=t.split(":");i.length!==2&&Ot(e);var n=!1;i[0]==="echarts"&&(t=i[1],n=!0),r.__isBuiltIn=n,dp.set(t,r)}function Aw(r,t,e){var i=Tt(r),n=i.length,a="";n||Ot(a);for(var o=0,s=n;o<s;o++){var u=i[o];t=Pw(u,t),o!==s-1&&(t.length=Math.max(t.length,1))}return t}function Pw(r,t,e,i){var n="";t.length||Ot(n),H(r)||Ot(n);var a=r.type,o=dp.get(a);o||Ot(n);var s=Y(t,function(l){return bw(l,o)}),u=Tt(o.transform({upstream:s[0],upstreamList:s,config:K(r.config)}));return Y(u,function(l,f){var h="";H(l)||Ot(h),l.data||Ot(h);var c=up(l.data);Nl(c)||Ot(h);var v,d=t[0];if(d&&f===0&&!l.dimensions){var _=d.startIndex;_&&(l.data=d.data.slice(0,_).concat(l.data)),v={seriesLayoutBy:Te,sourceHeader:_,dimensions:d.metaRawOption.dimensions}}else v={seriesLayoutBy:Te,sourceHeader:0,dimensions:l.dimensions};return Du(l.data,v,null)})}function Nl(r){return r===zt||r===ce}var Po="undefined",Lw=typeof Uint32Array===Po?Array:Uint32Array,Iw=typeof Uint16Array===Po?Array:Uint16Array,pp=typeof Int32Array===Po?Array:Int32Array,Qh=typeof Float64Array===Po?Array:Float64Array,gp={float:Qh,int:pp,ordinal:Array,number:Array,time:Qh},Ds;function Ei(r){return r>65535?Lw:Iw}function Kr(){return[1/0,-1/0]}function Rw(r){var t=r.constructor;return t===Array?r.slice():new t(r)}function Jh(r,t,e,i,n){var a=gp[e||"float"];if(n){var o=r[t],s=o&&o.length;if(s!==i){for(var u=new a(i),l=0;l<s;l++)u[l]=o[l];r[t]=u}}else r[t]=new a(i)}var Mu=function(){function r(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=$()}return r.prototype.initData=function(t,e,i){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var n=t.getSource(),a=this.defaultDimValueGetter=Ds[n.sourceFormat];this._dimValueGetter=i||a,this._rawExtent=[],lp(n),this._dimensions=Y(e,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,t.count())},r.prototype.getProvider=function(){return this._provider},r.prototype.getSource=function(){return this._provider.getSource()},r.prototype.ensureCalculationDimension=function(t,e){var i=this._calcDimNameToIdx,n=this._dimensions,a=i.get(t);if(a!=null){if(n[a].type===e)return a}else a=n.length;return n[a]={type:e},i.set(t,a),this._chunks[a]=new gp[e||"float"](this._rawCount),this._rawExtent[a]=Kr(),a},r.prototype.collectOrdinalMeta=function(t,e){var i=this._chunks[t],n=this._dimensions[t],a=this._rawExtent,o=n.ordinalOffset||0,s=i.length;o===0&&(a[t]=Kr());for(var u=a[t],l=o;l<s;l++){var f=i[l]=e.parseAndCollect(i[l]);isNaN(f)||(u[0]=Math.min(f,u[0]),u[1]=Math.max(f,u[1]))}n.ordinalMeta=e,n.ordinalOffset=s,n.type="ordinal"},r.prototype.getOrdinalMeta=function(t){var e=this._dimensions[t],i=e.ordinalMeta;return i},r.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},r.prototype.appendData=function(t){var e=this._provider,i=this.count();e.appendData(t);var n=e.count();return e.persistent||(n+=i),i<n&&this._initDataFromProvider(i,n,!0),[i,n]},r.prototype.appendValues=function(t,e){for(var i=this._chunks,n=this._dimensions,a=n.length,o=this._rawExtent,s=this.count(),u=s+Math.max(t.length,e||0),l=0;l<a;l++){var f=n[l];Jh(i,l,f.type,u,!0)}for(var h=[],c=s;c<u;c++)for(var v=c-s,d=0;d<a;d++){var f=n[d],_=Ds.arrayRows.call(this,t[v]||h,f.property,v,d);i[d][c]=_;var p=o[d];_<p[0]&&(p[0]=_),_>p[1]&&(p[1]=_)}return this._rawCount=this._count=u,{start:s,end:u}},r.prototype._initDataFromProvider=function(t,e,i){for(var n=this._provider,a=this._chunks,o=this._dimensions,s=o.length,u=this._rawExtent,l=Y(o,function(y){return y.property}),f=0;f<s;f++){var h=o[f];u[f]||(u[f]=Kr()),Jh(a,f,h.type,e,i)}if(n.fillStorage)n.fillStorage(t,e,a,u);else for(var c=[],v=t;v<e;v++){c=n.getItem(v,c);for(var d=0;d<s;d++){var _=a[d],p=this._dimValueGetter(c,l[d],v,d);_[v]=p;var g=u[d];p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}!n.persistent&&n.clean&&n.clean(),this._rawCount=this._count=e,this._extent=[]},r.prototype.count=function(){return this._count},r.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var i=this._chunks[t];return i?i[this.getRawIndex(e)]:NaN},r.prototype.getValues=function(t,e){var i=[],n=[];if(e==null){e=t,t=[];for(var a=0;a<this._dimensions.length;a++)n.push(a)}else n=t;for(var a=0,o=n.length;a<o;a++)i.push(this.get(n[a],e));return i},r.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var i=this._chunks[t];return i?i[e]:NaN},r.prototype.getSum=function(t){var e=this._chunks[t],i=0;if(e)for(var n=0,a=this.count();n<a;n++){var o=this.get(t,n);isNaN(o)||(i+=o)}return i},r.prototype.getMedian=function(t){var e=[];this.each([t],function(a){isNaN(a)||e.push(a)});var i=e.sort(function(a,o){return a-o}),n=this.count();return n===0?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},r.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(i!=null&&i<this._count&&i===t)return t;for(var n=0,a=this._count-1;n<=a;){var o=(n+a)/2|0;if(e[o]<t)n=o+1;else if(e[o]>t)a=o-1;else return o}return-1},r.prototype.indicesOfNearest=function(t,e,i){var n=this._chunks,a=n[t],o=[];if(!a)return o;i==null&&(i=1/0);for(var s=1/0,u=-1,l=0,f=0,h=this.count();f<h;f++){var c=this.getRawIndex(f),v=e-a[c],d=Math.abs(v);d<=i&&((d<s||d===s&&v>=0&&u<0)&&(s=d,u=v,l=0),v===u&&(o[l++]=f))}return o.length=l,o},r.prototype.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var a=0;a<n;a++)t[a]=e[a]}else t=new i(e.buffer,0,n)}else{var i=Ei(this._rawCount);t=new i(this.count());for(var a=0;a<t.length;a++)t[a]=a}return t},r.prototype.filter=function(t,e){if(!this._count)return this;for(var i=this.clone(),n=i.count(),a=Ei(i._rawCount),o=new a(n),s=[],u=t.length,l=0,f=t[0],h=i._chunks,c=0;c<n;c++){var v=void 0,d=i.getRawIndex(c);if(u===0)v=e(c);else if(u===1){var _=h[f][d];v=e(_,c)}else{for(var p=0;p<u;p++)s[p]=h[t[p]][d];s[p]=c,v=e.apply(null,s)}v&&(o[l++]=d)}return l<n&&(i._indices=o),i._count=l,i._extent=[],i._updateGetRawIdx(),i},r.prototype.selectRange=function(t){var e=this.clone(),i=e._count;if(!i)return this;var n=lt(t),a=n.length;if(!a)return this;var o=e.count(),s=Ei(e._rawCount),u=new s(o),l=0,f=n[0],h=t[f][0],c=t[f][1],v=e._chunks,d=!1;if(!e._indices){var _=0;if(a===1){for(var p=v[n[0]],g=0;g<i;g++){var y=p[g];(y>=h&&y<=c||isNaN(y))&&(u[l++]=_),_++}d=!0}else if(a===2){for(var p=v[n[0]],m=v[n[1]],w=t[n[1]][0],b=t[n[1]][1],g=0;g<i;g++){var y=p[g],S=m[g];(y>=h&&y<=c||isNaN(y))&&(S>=w&&S<=b||isNaN(S))&&(u[l++]=_),_++}d=!0}}if(!d)if(a===1)for(var g=0;g<o;g++){var x=e.getRawIndex(g),y=v[n[0]][x];(y>=h&&y<=c||isNaN(y))&&(u[l++]=x)}else for(var g=0;g<o;g++){for(var C=!0,x=e.getRawIndex(g),M=0;M<a;M++){var A=n[M],y=v[A][x];(y<t[A][0]||y>t[A][1])&&(C=!1)}C&&(u[l++]=e.getRawIndex(g))}return l<o&&(e._indices=u),e._count=l,e._extent=[],e._updateGetRawIdx(),e},r.prototype.map=function(t,e){var i=this.clone(t);return this._updateDims(i,t,e),i},r.prototype.modify=function(t,e){this._updateDims(this,t,e)},r.prototype._updateDims=function(t,e,i){for(var n=t._chunks,a=[],o=e.length,s=t.count(),u=[],l=t._rawExtent,f=0;f<e.length;f++)l[e[f]]=Kr();for(var h=0;h<s;h++){for(var c=t.getRawIndex(h),v=0;v<o;v++)u[v]=n[e[v]][c];u[o]=h;var d=i&&i.apply(null,u);if(d!=null){typeof d!="object"&&(a[0]=d,d=a);for(var f=0;f<d.length;f++){var _=e[f],p=d[f],g=l[_],y=n[_];y&&(y[c]=p),p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}}},r.prototype.lttbDownSample=function(t,e){var i=this.clone([t],!0),n=i._chunks,a=n[t],o=this.count(),s=0,u=Math.floor(1/e),l=this.getRawIndex(0),f,h,c,v=new(Ei(this._rawCount))(Math.min((Math.ceil(o/u)+2)*2,o));v[s++]=l;for(var d=1;d<o-1;d+=u){for(var _=Math.min(d+u,o-1),p=Math.min(d+u*2,o),g=(p+_)/2,y=0,m=_;m<p;m++){var w=this.getRawIndex(m),b=a[w];isNaN(b)||(y+=b)}y/=p-_;var S=d,x=Math.min(d+u,o),C=d-1,M=a[l];f=-1,c=S;for(var A=-1,T=0,m=S;m<x;m++){var w=this.getRawIndex(m),b=a[w];if(isNaN(b)){T++,A<0&&(A=w);continue}h=Math.abs((C-g)*(b-M)-(C-m)*(y-M)),h>f&&(f=h,c=w)}T>0&&T<x-S&&(v[s++]=Math.min(A,c),c=Math.max(A,c)),v[s++]=c,l=c}return v[s++]=this.getRawIndex(o-1),i._count=s,i._indices=v,i.getRawIndex=this._getRawIdx,i},r.prototype.downSample=function(t,e,i,n){for(var a=this.clone([t],!0),o=a._chunks,s=[],u=Math.floor(1/e),l=o[t],f=this.count(),h=a._rawExtent[t]=Kr(),c=new(Ei(this._rawCount))(Math.ceil(f/u)),v=0,d=0;d<f;d+=u){u>f-d&&(u=f-d,s.length=u);for(var _=0;_<u;_++){var p=this.getRawIndex(d+_);s[_]=l[p]}var g=i(s),y=this.getRawIndex(Math.min(d+n(s,g)||0,f-1));l[y]=g,g<h[0]&&(h[0]=g),g>h[1]&&(h[1]=g),c[v++]=y}return a._count=v,a._indices=c,a._updateGetRawIdx(),a},r.prototype.each=function(t,e){if(this._count)for(var i=t.length,n=this._chunks,a=0,o=this.count();a<o;a++){var s=this.getRawIndex(a);switch(i){case 0:e(a);break;case 1:e(n[t[0]][s],a);break;case 2:e(n[t[0]][s],n[t[1]][s],a);break;default:for(var u=0,l=[];u<i;u++)l[u]=n[t[u]][s];l[u]=a,e.apply(null,l)}}},r.prototype.getDataExtent=function(t){var e=this._chunks[t],i=Kr();if(!e)return i;var n=this.count(),a=!this._indices,o;if(a)return this._rawExtent[t].slice();if(o=this._extent[t],o)return o.slice();o=i;for(var s=o[0],u=o[1],l=0;l<n;l++){var f=this.getRawIndex(l),h=e[f];h<s&&(s=h),h>u&&(u=h)}return o=[s,u],this._extent[t]=o,o},r.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var i=[],n=this._chunks,a=0;a<n.length;a++)i.push(n[a][e]);return i},r.prototype.clone=function(t,e){var i=new r,n=this._chunks,a=t&&Ke(t,function(s,u){return s[u]=!0,s},{});if(a)for(var o=0;o<n.length;o++)i._chunks[o]=a[o]?Rw(n[o]):n[o];else i._chunks=n;return this._copyCommonProps(i),e||(i._indices=this._cloneIndices()),i._updateGetRawIdx(),i},r.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=K(this._extent),t._rawExtent=K(this._rawExtent)},r.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var i=this._indices.length;e=new t(i);for(var n=0;n<i;n++)e[n]=this._indices[n]}else e=new t(this._indices);return e}return null},r.prototype._getRawIdxIdentity=function(t){return t},r.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},r.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},r.internalField=function(){function t(e,i,n,a){return Ia(e[a],this._dimensions[a])}Ds={arrayRows:t,objectRows:function(e,i,n,a){return Ia(e[i],this._dimensions[a])},keyedColumns:t,original:function(e,i,n,a){var o=e&&(e.value==null?e:e.value);return Ia(o instanceof Array?o[a]:o,this._dimensions[a])},typedArray:function(e,i,n,a){return e[a]}}}(),r}(),_p=function(){function r(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return r.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},r.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},r.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},r.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},r.prototype._createSource=function(){this._setLocalSource([],[]);var t=this._sourceHost,e=this._getUpstreamSourceManagers(),i=!!e.length,n,a;if(ra(t)){var o=t,s=void 0,u=void 0,l=void 0;if(i){var f=e[0];f.prepareSource(),l=f.getSource(),s=l.data,u=l.sourceFormat,a=[f._getVersionSign()]}else s=o.get("data",!0),u=Nt(s)?Ze:De,a=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},v=X(h.seriesLayoutBy,c.seriesLayoutBy)||null,d=X(h.sourceHeader,c.sourceHeader),_=X(h.dimensions,c.dimensions),p=v!==c.seriesLayoutBy||!!d!=!!c.sourceHeader||_;n=p?[Du(s,{seriesLayoutBy:v,sourceHeader:d,dimensions:_},u)]:[]}else{var g=t;if(i){var y=this._applyTransform(e);n=y.sourceList,a=y.upstreamSignList}else{var m=g.get("source",!0);n=[Du(m,this._getSourceMetaRawOption(),null)],a=[]}}this._setLocalSource(n,a)},r.prototype._applyTransform=function(t){var e=this._sourceHost,i=e.get("transform",!0),n=e.get("fromTransformResult",!0);if(n!=null){var a="";t.length!==1&&tv(a)}var o,s=[],u=[];return D(t,function(l){l.prepareSource();var f=l.getSource(n||0),h="";n!=null&&!f&&tv(h),s.push(f),u.push(l._getVersionSign())}),i?o=Aw(i,s,{datasetIndex:e.componentIndex}):n!=null&&(o=[hw(s[0])]),{sourceList:o,upstreamSignList:u}},r.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var i=t[e];if(i._isDirty()||this._upstreamSignList[e]!==i._getVersionSign())return!0}},r.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var i=this._getUpstreamSourceManagers();return i[0]&&i[0].getSource(t)}return e},r.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},r.prototype._innerGetDataStore=function(t,e,i){var n=0,a=this._storeList,o=a[n];o||(o=a[n]={});var s=o[i];if(!s){var u=this._getUpstreamSourceManagers()[0];ra(this._sourceHost)&&u?s=u._innerGetDataStore(t,e,i):(s=new Mu,s.initData(new fp(e,t.length),t)),o[i]=s}return s},r.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(ra(t)){var e=El(t);return e?[e.getSourceManager()]:[]}else return Y(G1(t),function(i){return i.getSourceManager()})},r.prototype._getSourceMetaRawOption=function(){var t=this._sourceHost,e,i,n;if(ra(t))e=t.get("seriesLayoutBy",!0),i=t.get("sourceHeader",!0),n=t.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var a=t;e=a.get("seriesLayoutBy",!0),i=a.get("sourceHeader",!0),n=a.get("dimensions",!0)}return{seriesLayoutBy:e,sourceHeader:i,dimensions:n}},r}();function jh(r){var t=r.option.transform;t&&Ba(r.option.transform)}function ra(r){return r.mainType==="series"}function tv(r){throw new Error(r)}var yp="line-height:1";function mp(r,t){var e=r.color||"#6e7079",i=r.fontSize||12,n=r.fontWeight||"400",a=r.color||"#464646",o=r.fontSize||14,s=r.fontWeight||"900";return t==="html"?{nameStyle:"font-size:"+Zt(i+"")+"px;color:"+Zt(e)+";font-weight:"+Zt(n+""),valueStyle:"font-size:"+Zt(o+"")+"px;color:"+Zt(a)+";font-weight:"+Zt(s+"")}:{nameStyle:{fontSize:i,fill:e,fontWeight:n},valueStyle:{fontSize:o,fill:a,fontWeight:s}}}var Ew=[0,10,20,30],Ow=["",`
`,`

`,`


`];function Sn(r,t){return t.type=r,t}function Au(r){return r.type==="section"}function wp(r){return Au(r)?kw:Bw}function Sp(r){if(Au(r)){var t=0,e=r.blocks.length,i=e>1||e>0&&!r.noHeader;return D(r.blocks,function(n){var a=Sp(n);a>=t&&(t=a+ +(i&&(!a||Au(n)&&!n.noHeader)))}),t}return 0}function kw(r,t,e,i){var n=t.noHeader,a=Fw(Sp(t)),o=[],s=t.blocks||[];xe(!s||k(s)),s=s||[];var u=r.orderMode;if(t.sortBlocks&&u){s=s.slice();var l={valueAsc:"asc",valueDesc:"desc"};if(di(l,u)){var f=new ww(l[u],null);s.sort(function(d,_){return f.evaluate(d.sortParam,_.sortParam)})}else u==="seriesDesc"&&s.reverse()}D(s,function(d,_){var p=t.valueFormatter,g=wp(d)(p?O(O({},r),{valueFormatter:p}):r,d,_>0?a.html:0,i);g!=null&&o.push(g)});var h=r.renderMode==="richText"?o.join(a.richText):Pu(o.join(""),n?e:a.html);if(n)return h;var c=xu(t.header,"ordinal",r.useUTC),v=mp(i,r.renderMode).nameStyle;return r.renderMode==="richText"?bp(r,c,v)+a.richText+h:Pu('<div style="'+v+";"+yp+';">'+Zt(c)+"</div>"+h,e)}function Bw(r,t,e,i){var n=r.renderMode,a=t.noName,o=t.noValue,s=!t.markerType,u=t.name,l=r.useUTC,f=t.valueFormatter||r.valueFormatter||function(w){return w=k(w)?w:[w],Y(w,function(b,S){return xu(b,k(v)?v[S]:v,l)})};if(!(a&&o)){var h=s?"":r.markupStyleCreator.makeTooltipMarker(t.markerType,t.markerColor||"#333",n),c=a?"":xu(u,"ordinal",l),v=t.valueType,d=o?[]:f(t.value,t.dataIndex),_=!s||!a,p=!s&&a,g=mp(i,n),y=g.nameStyle,m=g.valueStyle;return n==="richText"?(s?"":h)+(a?"":bp(r,c,y))+(o?"":Hw(r,d,_,p,m)):Pu((s?"":h)+(a?"":Nw(c,!s,y))+(o?"":zw(d,_,p,m)),e)}}function ev(r,t,e,i,n,a){if(r){var o=wp(r),s={useUTC:n,renderMode:e,orderMode:i,markupStyleCreator:t,valueFormatter:r.valueFormatter};return o(s,r,0,a)}}function Fw(r){return{html:Ew[r],richText:Ow[r]}}function Pu(r,t){var e='<div style="clear:both"></div>',i="margin: "+t+"px 0 0";return'<div style="'+i+";"+yp+';">'+r+e+"</div>"}function Nw(r,t,e){var i=t?"margin-left:2px":"";return'<span style="'+e+";"+i+'">'+Zt(r)+"</span>"}function zw(r,t,e,i){var n=e?"10px":"20px",a=t?"float:right;margin-left:"+n:"";return r=k(r)?r:[r],'<span style="'+a+";"+i+'">'+Y(r,function(o){return Zt(o)}).join("&nbsp;&nbsp;")+"</span>"}function bp(r,t,e){return r.markupStyleCreator.wrapRichTextStyle(t,e)}function Hw(r,t,e,i,n){var a=[n],o=i?10:20;return e&&a.push({padding:[0,0,0,o],align:"right"}),r.markupStyleCreator.wrapRichTextStyle(k(t)?t.join("  "):t,a)}function Gw(r,t){var e=r.getData().getItemVisual(t,"style"),i=e[r.visualDrawType];return yn(i)}function Tp(r,t){var e=r.get("padding");return e??(t==="richText"?[8,10]:10)}var Ms=function(){function r(){this.richTextStyles={},this._nextStyleNameId=Kc()}return r.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},r.prototype.makeTooltipMarker=function(t,e,i){var n=i==="richText"?this._generateStyleName():null,a=E1({color:e,type:t,renderMode:i,markerId:n});return B(a)?a:(this.richTextStyles[n]=a.style,a.content)},r.prototype.wrapRichTextStyle=function(t,e){var i={};k(e)?D(e,function(a){return O(i,a)}):O(i,e);var n=this._generateStyleName();return this.richTextStyles[n]=i,"{"+n+"|"+t+"}"},r}();function Ww(r){var t=r.series,e=r.dataIndex,i=r.multipleSeries,n=t.getData(),a=n.mapDimensionsAll("defaultedTooltip"),o=a.length,s=t.getRawValue(e),u=k(s),l=Gw(t,e),f,h,c,v;if(o>1||u&&!o){var d=Vw(s,t,e,a,l);f=d.inlineValues,h=d.inlineValueTypes,c=d.blocks,v=d.inlineValues[0]}else if(o){var _=n.getDimensionInfo(a[0]);v=f=Qa(n,e,a[0]),h=_.type}else v=f=u?s[0]:s;var p=ol(t),g=p&&t.name||"",y=n.getName(e),m=i?g:y;return Sn("section",{header:g,noHeader:i||!p,sortParam:v,blocks:[Sn("nameValue",{markerType:"item",markerColor:l,name:m,noName:!we(m),value:f,valueType:h,dataIndex:e})].concat(c||[])})}function Vw(r,t,e,i,n){var a=t.getData(),o=Ke(r,function(h,c,v){var d=a.getDimensionInfo(v);return h=h||d&&d.tooltip!==!1&&d.displayName!=null},!1),s=[],u=[],l=[];i.length?D(i,function(h){f(Qa(a,e,h),h)}):D(r,f);function f(h,c){var v=a.getDimensionInfo(c);!v||v.otherDims.tooltip===!1||(o?l.push(Sn("nameValue",{markerType:"subItem",markerColor:n,name:v.displayName,value:h,valueType:v.type})):(s.push(h),u.push(v.type)))}return{inlineValues:s,inlineValueTypes:u,blocks:l}}var He=yt();function ia(r,t){return r.getName(t)||r.getId(t)}var Uw="__universalTransitionEnabled",Br=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return t.prototype.init=function(e,i,n){this.seriesIndex=this.componentIndex,this.dataTask=sn({count:Xw,reset:$w}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(e,n);var a=He(this).sourceManager=new _p(this);a.prepareSource();var o=this.getInitialData(e,n);iv(o,this),this.dataTask.context.data=o,He(this).dataBeforeProcessed=o,rv(this),this._initSelectedMapFromData(o)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=Ka(this),a=n?Rl(e):{},o=this.subType;tt.hasClass(o)&&(o+="Series"),ut(e,i.getTheme().get(this.subType)),ut(e,this.getDefaultOption()),Hf(e,"label",["show"]),this.fillDataTextStyle(e.data),n&&mn(e,a,n)},t.prototype.mergeOption=function(e,i){e=ut(this.option,e,!0),this.fillDataTextStyle(e.data);var n=Ka(this);n&&mn(this.option,e,n);var a=He(this).sourceManager;a.dirty(),a.prepareSource();var o=this.getInitialData(e,i);iv(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,He(this).dataBeforeProcessed=o,rv(this),this._initSelectedMapFromData(o)},t.prototype.fillDataTextStyle=function(e){if(e&&!Nt(e))for(var i=["show"],n=0;n<e.length;n++)e[n]&&e[n].label&&Hf(e[n],"label",i)},t.prototype.getInitialData=function(e,i){},t.prototype.appendData=function(e){var i=this.getRawData();i.appendData(e.data)},t.prototype.getData=function(e){var i=Lu(this);if(i){var n=i.context.data;return e==null?n:n.getLinkedData(e)}else return He(this).data},t.prototype.getAllData=function(){var e=this.getData();return e&&e.getLinkedDataAll?e.getLinkedDataAll():[{data:e}]},t.prototype.setData=function(e){var i=Lu(this);if(i){var n=i.context;n.outputData=e,i!==this.dataTask&&(n.data=e)}He(this).data=e},t.prototype.getEncode=function(){var e=this.get("encode",!0);if(e)return $(e)},t.prototype.getSourceManager=function(){return He(this).sourceManager},t.prototype.getSource=function(){return this.getSourceManager().getSource()},t.prototype.getRawData=function(){return He(this).dataBeforeProcessed},t.prototype.getColorBy=function(){var e=this.get("colorBy");return e||"series"},t.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},t.prototype.getBaseAxis=function(){var e=this.coordinateSystem;return e&&e.getBaseAxis&&e.getBaseAxis()},t.prototype.formatTooltip=function(e,i,n){return Ww({series:this,dataIndex:e,multipleSeries:i})},t.prototype.isAnimationEnabled=function(){var e=this.ecModel;if(W.node&&!(e&&e.ssr))return!1;var i=this.getShallow("animation");return i&&this.getData().count()>this.getShallow("animationThreshold")&&(i=!1),!!i},t.prototype.restoreData=function(){this.dataTask.dirty()},t.prototype.getColorFromPalette=function(e,i,n){var a=this.ecModel,o=Ol.prototype.getColorFromPalette.call(this,e,i,n);return o||(o=a.getColorFromPalette(e,i,n)),o},t.prototype.coordDimToDataDim=function(e){return this.getRawData().mapDimensionsAll(e)},t.prototype.getProgressive=function(){return this.get("progressive")},t.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},t.prototype.select=function(e,i){this._innerSelect(this.getData(i),e)},t.prototype.unselect=function(e,i){var n=this.option.selectedMap;if(n){var a=this.option.selectedMode,o=this.getData(i);if(a==="series"||n==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<e.length;s++){var u=e[s],l=ia(o,u);n[l]=!1,this._selectedDataIndicesMap[l]=-1}}},t.prototype.toggleSelect=function(e,i){for(var n=[],a=0;a<e.length;a++)n[0]=e[a],this.isSelected(e[a],i)?this.unselect(n,i):this.select(n,i)},t.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var e=this._selectedDataIndicesMap,i=lt(e),n=[],a=0;a<i.length;a++){var o=e[i[a]];o>=0&&n.push(o)}return n},t.prototype.isSelected=function(e,i){var n=this.option.selectedMap;if(!n)return!1;var a=this.getData(i);return(n==="all"||n[ia(a,e)])&&!a.getItemModel(e).get(["select","disabled"])},t.prototype.isUniversalTransitionEnabled=function(){if(this[Uw])return!0;var e=this.option.universalTransition;return e?e===!0?!0:e&&e.enabled:!1},t.prototype._innerSelect=function(e,i){var n,a,o=this.option,s=o.selectedMode,u=i.length;if(!(!s||!u)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){H(o.selectedMap)||(o.selectedMap={});for(var l=o.selectedMap,f=0;f<u;f++){var h=i[f],c=ia(e,h);l[c]=!0,this._selectedDataIndicesMap[c]=e.getRawIndex(h)}}else if(s==="single"||s===!0){var v=i[u-1],c=ia(e,v);o.selectedMap=(n={},n[c]=!0,n),this._selectedDataIndicesMap=(a={},a[c]=e.getRawIndex(v),a)}}},t.prototype._initSelectedMapFromData=function(e){if(!this.option.selectedMap){var i=[];e.hasItemOption&&e.each(function(n){var a=e.getRawDataItem(n);a&&a.selected&&i.push(n)}),i.length>0&&this._innerSelect(e,i)}},t.registerClass=function(e){return tt.registerClass(e)},t.protoInitialize=function(){var e=t.prototype;e.type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"}(),t}(tt);ke(Br,yw);ke(Br,Ol);id(Br,tt);function rv(r){var t=r.name;ol(r)||(r.name=Yw(r)||t)}function Yw(r){var t=r.getRawData(),e=t.mapDimensionsAll("seriesName"),i=[];return D(e,function(n){var a=t.getDimensionInfo(n);a.displayName&&i.push(a.displayName)}),i.join(" ")}function Xw(r){return r.model.getRawData().count()}function $w(r){var t=r.model;return t.setData(t.getRawData().cloneShallow()),qw}function qw(r,t){t.outputData&&r.end>t.outputData.count()&&t.model.getRawData().cloneShallow(t.outputData)}function iv(r,t){D(o_(r.CHANGABLE_METHODS,r.DOWNSAMPLE_METHODS),function(e){r.wrapMethod(e,_t(Zw,t))})}function Zw(r,t){var e=Lu(r);return e&&e.setOutputEnd((t||this).count()),t}function Lu(r){var t=(r.ecModel||{}).scheduler,e=t&&t.getPipeline(r.uid);if(e){var i=e.currentTask;if(i){var n=i.agentStubMap;n&&(i=n.get(r.uid))}return i}}var he=function(){function r(){this.group=new fe,this.uid=So("viewComponent")}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){},r.prototype.updateLayout=function(t,e,i,n){},r.prototype.updateVisual=function(t,e,i,n){},r.prototype.toggleBlurSeries=function(t,e,i){},r.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},r}();ul(he);ho(he);function xp(){var r=yt();return function(t){var e=r(t),i=t.pipelineContext,n=!!e.large,a=!!e.progressiveRender,o=e.large=!!(i&&i.large),s=e.progressiveRender=!!(i&&i.progressiveRender);return(n!==o||a!==s)&&"reset"}}var Cp=yt(),Kw=xp(),Er=function(){function r(){this.group=new fe,this.uid=So("viewChart"),this.renderTask=sn({plan:Qw,reset:Jw}),this.renderTask.context={view:this}}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.highlight=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&av(a,n,"emphasis")},r.prototype.downplay=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&av(a,n,"normal")},r.prototype.remove=function(t,e){this.group.removeAll()},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateLayout=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.eachRendered=function(t){Od(this.group,t)},r.markUpdateMethod=function(t,e){Cp(t).updateMethod=e},r.protoInitialize=function(){var t=r.prototype;t.type="chart"}(),r}();function nv(r,t,e){r&&mu(r)&&(t==="emphasis"?du:pu)(r,e)}function av(r,t,e){var i=An(r,t),n=t&&t.highlightKey!=null?fm(t.highlightKey):null;i!=null?D(Tt(i),function(a){nv(r.getItemGraphicEl(a),e,n)}):r.eachItemGraphicEl(function(a){nv(a,e,n)})}ul(Er);ho(Er);function Qw(r){return Kw(r.model)}function Jw(r){var t=r.model,e=r.ecModel,i=r.api,n=r.payload,a=t.pipelineContext.progressiveRender,o=r.view,s=n&&Cp(n).updateMethod,u=a?"incrementalPrepareRender":s&&o[s]?s:"render";return u!=="render"&&o[u](t,e,i,n),jw[u]}var jw={incrementalPrepareRender:{progress:function(r,t){t.view.incrementalRender(r,t.model,t.ecModel,t.api,t.payload)}},render:{forceFirstProgress:!0,progress:function(r,t){t.view.render(t.model,t.ecModel,t.api,t.payload)}}},Ja="\0__throttleOriginMethod",ov="\0__throttleRate",sv="\0__throttleType";function zl(r,t,e){var i,n=0,a=0,o=null,s,u,l,f;t=t||0;function h(){a=new Date().getTime(),o=null,r.apply(u,l||[])}var c=function(){for(var v=[],d=0;d<arguments.length;d++)v[d]=arguments[d];i=new Date().getTime(),u=this,l=v;var _=f||t,p=f||e;f=null,s=i-(p?n:a)-_,clearTimeout(o),p?o=setTimeout(h,_):s>=0?h():o=setTimeout(h,-s),n=i};return c.clear=function(){o&&(clearTimeout(o),o=null)},c.debounceNextCall=function(v){f=v},c}function Dp(r,t,e,i){var n=r[t];if(n){var a=n[Ja]||n,o=n[sv],s=n[ov];if(s!==e||o!==i){if(e==null||!i)return r[t]=a;n=r[t]=zl(a,e,i==="debounce"),n[Ja]=a,n[sv]=i,n[ov]=e}return n}}function Iu(r,t){var e=r[t];e&&e[Ja]&&(e.clear&&e.clear(),r[t]=e[Ja])}var uv=yt(),lv={itemStyle:gn(Fd,!0),lineStyle:gn(Bd,!0)},tS={lineStyle:"stroke",itemStyle:"fill"};function Mp(r,t){var e=r.visualStyleMapper||lv[t];return e||(console.warn("Unknown style type '"+t+"'."),lv.itemStyle)}function Ap(r,t){var e=r.visualDrawType||tS[t];return e||(console.warn("Unknown style type '"+t+"'."),"fill")}var eS={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=r.getModel(i),a=Mp(r,i),o=a(n),s=n.getShallow("decal");s&&(e.setVisual("decal",s),s.dirty=!0);var u=Ap(r,i),l=o[u],f=Z(l)?l:null,h=o.fill==="auto"||o.stroke==="auto";if(!o[u]||f||h){var c=r.getColorFromPalette(r.name,null,t.getSeriesCount());o[u]||(o[u]=c,e.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||Z(o.fill)?c:o.fill,o.stroke=o.stroke==="auto"||Z(o.stroke)?c:o.stroke}if(e.setVisual("style",o),e.setVisual("drawType",u),!t.isSeriesFiltered(r)&&f)return e.setVisual("colorFromPalette",!1),{dataEach:function(v,d){var _=r.getDataParams(d),p=O({},o);p[u]=f(_),v.setItemVisual(d,"style",p)}}}},Oi=new ft,rS={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!(r.ignoreStyleOnData||t.isSeriesFiltered(r))){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=Mp(r,i),a=e.getVisual("drawType");return{dataEach:e.hasItemOption?function(o,s){var u=o.getRawDataItem(s);if(u&&u[i]){Oi.option=u[i];var l=n(Oi),f=o.ensureUniqueItemVisual(s,"style");O(f,l),Oi.option.decal&&(o.setItemVisual(s,"decal",Oi.option.decal),Oi.option.decal.dirty=!0),a in l&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},iS={performRawSeries:!0,overallReset:function(r){var t=$();r.eachSeries(function(e){var i=e.getColorBy();if(!e.isColorBySeries()){var n=e.type+"-"+i,a=t.get(n);a||(a={},t.set(n,a)),uv(e).scope=a}}),r.eachSeries(function(e){if(!(e.isColorBySeries()||r.isSeriesFiltered(e))){var i=e.getRawData(),n={},a=e.getData(),o=uv(e).scope,s=e.visualStyleAccessPath||"itemStyle",u=Ap(e,s);a.each(function(l){var f=a.getRawIndex(l);n[f]=l}),i.each(function(l){var f=n[l],h=a.getItemVisual(f,"colorFromPalette");if(h){var c=a.ensureUniqueItemVisual(f,"style"),v=i.getName(l)||l+"",d=i.count();c[u]=e.getColorFromPalette(v,o,d)}})}})}},na=Math.PI;function nS(r,t){t=t||{},ot(t,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var e=new fe,i=new Pt({style:{fill:t.maskColor},zlevel:t.zlevel,z:1e4});e.add(i);var n=new Lt({style:{text:t.text,fill:t.textColor,fontSize:t.fontSize,fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:t.fontFamily},zlevel:t.zlevel,z:10001}),a=new Pt({style:{fill:"none"},textContent:n,textConfig:{position:"right",distance:10},zlevel:t.zlevel,z:10001});e.add(a);var o;return t.showSpinner&&(o=new mo({shape:{startAngle:-na/2,endAngle:-na/2+.1,r:t.spinnerRadius},style:{stroke:t.color,lineCap:"round",lineWidth:t.lineWidth},zlevel:t.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:na*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:na*3/2}).delay(300).start("circularInOut"),e.add(o)),e.resize=function(){var s=n.getBoundingRect().width,u=t.showSpinner?t.spinnerRadius:0,l=(r.getWidth()-u*2-(t.showSpinner&&s?10:0)-s)/2-(t.showSpinner&&s?0:5+s/2)+(t.showSpinner?0:s/2)+(s?0:u),f=r.getHeight()/2;t.showSpinner&&o.setShape({cx:l,cy:f}),a.setShape({x:l-u,y:f-u,width:u*2,height:u*2}),i.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},e.resize(),e}var Pp=function(){function r(t,e,i,n){this._stageTaskMap=$(),this.ecInstance=t,this.api=e,i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice(),this._allHandlers=i.concat(n)}return r.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(i){var n=i.overallTask;n&&n.dirty()})},r.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,a=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,o=a?i.step:null,s=n&&n.modDataCount,u=s!=null?Math.ceil(s/o):null;return{step:o,modBy:u,modDataCount:s}}},r.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},r.prototype.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),a=n.count(),o=i.progressiveEnabled&&e.incrementalPrepareRender&&a>=i.threshold,s=t.get("large")&&a>=t.get("largeThreshold"),u=t.get("progressiveChunkMode")==="mod"?a:null;t.pipelineContext=i.context={progressiveRender:o,modDataCount:u,large:s}},r.prototype.restorePipelines=function(t){var e=this,i=e._pipelineMap=$();t.eachSeries(function(n){var a=n.getProgressive(),o=n.uid;i.set(o,{id:o,head:null,tail:null,threshold:n.getProgressiveThreshold(),progressiveEnabled:a&&!(n.preventIncremental&&n.preventIncremental()),blockIndex:-1,step:Math.round(a||700),count:0}),e._pipe(n,n.dataTask)})},r.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),i=this.api;D(this._allHandlers,function(n){var a=t.get(n.uid)||t.set(n.uid,{}),o="";xe(!(n.reset&&n.overallReset),o),n.reset&&this._createSeriesStageTask(n,a,e,i),n.overallReset&&this._createOverallStageTask(n,a,e,i)},this)},r.prototype.prepareView=function(t,e,i,n){var a=t.renderTask,o=a.context;o.model=e,o.ecModel=i,o.api=n,a.__block=!t.incrementalPrepareRender,this._pipe(e,a)},r.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},r.prototype.performVisualTasks=function(t,e,i){this._performStageTasks(this._visualHandlers,t,e,i)},r.prototype._performStageTasks=function(t,e,i,n){n=n||{};var a=!1,o=this;D(t,function(u,l){if(!(n.visualType&&n.visualType!==u.visualType)){var f=o._stageTaskMap.get(u.uid),h=f.seriesTaskMap,c=f.overallTask;if(c){var v,d=c.agentStubMap;d.each(function(p){s(n,p)&&(p.dirty(),v=!0)}),v&&c.dirty(),o.updatePayload(c,i);var _=o.getPerformArgs(c,n.block);d.each(function(p){p.perform(_)}),c.perform(_)&&(a=!0)}else h&&h.each(function(p,g){s(n,p)&&p.dirty();var y=o.getPerformArgs(p,n.block);y.skip=!u.performRawSeries&&e.isSeriesFiltered(p.context.model),o.updatePayload(p,i),p.perform(y)&&(a=!0)})}});function s(u,l){return u.setDirty&&(!u.dirtyMap||u.dirtyMap.get(l.__pipeline.id))}this.unfinished=a||this.unfinished},r.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(i){e=i.dataTask.perform()||e}),this.unfinished=e||this.unfinished},r.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},r.prototype.updatePayload=function(t,e){e!=="remain"&&(t.context.payload=e)},r.prototype._createSeriesStageTask=function(t,e,i,n){var a=this,o=e.seriesTaskMap,s=e.seriesTaskMap=$(),u=t.seriesType,l=t.getTargetSeries;t.createOnAllSeries?i.eachRawSeries(f):u?i.eachRawSeriesByType(u,f):l&&l(i,n).each(f);function f(h){var c=h.uid,v=s.set(c,o&&o.get(c)||sn({plan:lS,reset:fS,count:vS}));v.context={model:h,ecModel:i,api:n,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(h,v)}},r.prototype._createOverallStageTask=function(t,e,i,n){var a=this,o=e.overallTask=e.overallTask||sn({reset:aS});o.context={ecModel:i,api:n,overallReset:t.overallReset,scheduler:a};var s=o.agentStubMap,u=o.agentStubMap=$(),l=t.seriesType,f=t.getTargetSeries,h=!0,c=!1,v="";xe(!t.createOnAllSeries,v),l?i.eachRawSeriesByType(l,d):f?f(i,n).each(d):(h=!1,D(i.getSeries(),d));function d(_){var p=_.uid,g=u.set(p,s&&s.get(p)||(c=!0,sn({reset:oS,onDirty:uS})));g.context={model:_,overallProgress:h},g.agent=o,g.__block=h,a._pipe(_,g)}c&&o.dirty()},r.prototype._pipe=function(t,e){var i=t.uid,n=this._pipelineMap.get(i);!n.head&&(n.head=e),n.tail&&n.tail.pipe(e),n.tail=e,e.__idxInPipeline=n.count++,e.__pipeline=n},r.wrapStageHandler=function(t,e){return Z(t)&&(t={overallReset:t,seriesType:cS(t)}),t.uid=So("stageHandler"),e&&(t.visualType=e),t},r}();function aS(r){r.overallReset(r.ecModel,r.api,r.payload)}function oS(r){return r.overallProgress&&sS}function sS(){this.agent.dirty(),this.getDownstream().dirty()}function uS(){this.agent&&this.agent.dirty()}function lS(r){return r.plan?r.plan(r.model,r.ecModel,r.api,r.payload):null}function fS(r){r.useClearVisual&&r.data.clearAllVisual();var t=r.resetDefines=Tt(r.reset(r.model,r.ecModel,r.api,r.payload));return t.length>1?Y(t,function(e,i){return Lp(i)}):hS}var hS=Lp(0);function Lp(r){return function(t,e){var i=e.data,n=e.resetDefines[r];if(n&&n.dataEach)for(var a=t.start;a<t.end;a++)n.dataEach(i,a);else n&&n.progress&&n.progress(t,i)}}function vS(r){return r.data.count()}function cS(r){ja=null;try{r(bn,Ip)}catch{}return ja}var bn={},Ip={},ja;Rp(bn,kl);Rp(Ip,ip);bn.eachSeriesByType=bn.eachRawSeriesByType=function(r){ja=r};bn.eachComponent=function(r){r.mainType==="series"&&r.subType&&(ja=r.subType)};function Rp(r,t){for(var e in t.prototype)r[e]=Bt}var fv=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const dS={color:fv,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],fv]};var Dt="#B9B8CE",hv="#100C2A",aa=function(){return{axisLine:{lineStyle:{color:Dt}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},vv=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Ep={darkMode:!0,color:vv,backgroundColor:hv,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Dt}},textStyle:{color:Dt},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Dt}},dataZoom:{borderColor:"#71708A",textStyle:{color:Dt},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Dt}},timeline:{lineStyle:{color:Dt},label:{color:Dt},controlStyle:{color:Dt,borderColor:Dt}},calendar:{itemStyle:{color:hv},dayLabel:{color:Dt},monthLabel:{color:Dt},yearLabel:{color:Dt}},timeAxis:aa(),logAxis:aa(),valueAxis:aa(),categoryAxis:aa(),line:{symbol:"circle"},graph:{color:vv},gauge:{title:{color:Dt},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Dt},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};Ep.categoryAxis.splitLine.show=!1;var pS=function(){function r(){}return r.prototype.normalizeQuery=function(t){var e={},i={},n={};if(B(t)){var a=Se(t);e.mainType=a.main||null,e.subType=a.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};D(t,function(u,l){for(var f=!1,h=0;h<o.length;h++){var c=o[h],v=l.lastIndexOf(c);if(v>0&&v===l.length-c.length){var d=l.slice(0,v);d!=="data"&&(e.mainType=d,e[c.toLowerCase()]=u,f=!0)}}s.hasOwnProperty(l)&&(i[l]=u,f=!0),f||(n[l]=u)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},r.prototype.filter=function(t,e){var i=this.eventInfo;if(!i)return!0;var n=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var u=e.cptQuery,l=e.dataQuery;return f(u,o,"mainType")&&f(u,o,"subType")&&f(u,o,"index","componentIndex")&&f(u,o,"name")&&f(u,o,"id")&&f(l,a,"name")&&f(l,a,"dataIndex")&&f(l,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,a));function f(h,c,v,d){return h[v]==null||c[d||v]===h[v]}},r.prototype.afterTrigger=function(){this.eventInfo=null},r}(),Ru=["symbol","symbolSize","symbolRotate","symbolOffset"],cv=Ru.concat(["symbolKeepAspect"]),gS={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData();if(r.legendIcon&&e.setVisual("legendIcon",r.legendIcon),!r.hasSymbolVisual)return;for(var i={},n={},a=!1,o=0;o<Ru.length;o++){var s=Ru[o],u=r.get(s);Z(u)?(a=!0,n[s]=u):i[s]=u}if(i.symbol=i.symbol||r.defaultSymbol,e.setVisual(O({legendIcon:r.legendIcon||i.symbol,symbolKeepAspect:r.get("symbolKeepAspect")},i)),t.isSeriesFiltered(r))return;var l=lt(n);function f(h,c){for(var v=r.getRawValue(c),d=r.getDataParams(c),_=0;_<l.length;_++){var p=l[_];h.setItemVisual(c,p,n[p](v,d))}}return{dataEach:a?f:null}}},_S={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!r.hasSymbolVisual||t.isSeriesFiltered(r))return;var e=r.getData();function i(n,a){for(var o=n.getItemModel(a),s=0;s<cv.length;s++){var u=cv[s],l=o.getShallow(u,!0);l!=null&&n.setItemVisual(a,u,l)}}return{dataEach:e.hasItemOption?i:null}}};function yS(r,t,e){switch(e){case"color":var i=r.getItemVisual(t,"style");return i[r.getVisual("drawType")];case"opacity":return r.getItemVisual(t,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getItemVisual(t,e)}}function mS(r,t){switch(t){case"color":var e=r.getVisual("style");return e[r.getVisual("drawType")];case"opacity":return r.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getVisual(t)}}function IC(r,t,e,i){switch(e){case"color":var n=r.ensureUniqueItemVisual(t,"style");n[r.getVisual("drawType")]=i,r.setItemVisual(t,"colorFromPalette",!1);break;case"opacity":r.ensureUniqueItemVisual(t,"style").opacity=i;break;case"symbol":case"symbolSize":case"liftZ":r.setItemVisual(t,e,i);break}}function RC(r,t){function e(i,n){var a=[];return i.eachComponent({mainType:"series",subType:r,query:n},function(o){a.push(o.seriesIndex)}),a}D([[r+"ToggleSelect","toggleSelect"],[r+"Select","select"],[r+"UnSelect","unselect"]],function(i){t(i[0],function(n,a,o){n=O({},n),o.dispatchAction(O(n,{type:i[1],seriesIndex:e(a,n)}))})})}function Qr(r,t,e,i,n){var a=r+t;e.isSilent(a)||i.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,u=o.option.selectedMap,l=n.selected,f=0;f<l.length;f++)if(l[f].seriesIndex===s){var h=o.getData(),c=An(h,n.fromActionPayload);e.trigger(a,{type:a,seriesId:o.id,name:k(c)?h.getName(c[0]):h.getName(c),selected:B(u)?u:O({},u)})}})}function wS(r,t,e){r.on("selectchanged",function(i){var n=e.getModel();i.isFromClick?(Qr("map","selectchanged",t,n,i),Qr("pie","selectchanged",t,n,i)):i.fromAction==="select"?(Qr("map","selected",t,n,i),Qr("pie","selected",t,n,i)):i.fromAction==="unselect"&&(Qr("map","unselected",t,n,i),Qr("pie","unselected",t,n,i))})}function Qi(r,t,e){for(var i;r&&!(t(r)&&(i=r,e));)r=r.__hostTarget||r.parent;return i}var SS=Math.round(Math.random()*9),bS=typeof Object.defineProperty=="function",TS=function(){function r(){this._id="__ec_inner_"+SS++}return r.prototype.get=function(t){return this._guard(t)[this._id]},r.prototype.set=function(t,e){var i=this._guard(t);return bS?Object.defineProperty(i,this._id,{value:e,enumerable:!1,configurable:!0}):i[this._id]=e,this},r.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},r.prototype.has=function(t){return!!this._guard(t)[this._id]},r.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},r}(),xS=at.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i+a),r.lineTo(e-n,i+a),r.closePath()}}),CS=at.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i),r.lineTo(e,i+a),r.lineTo(e-n,i),r.closePath()}}),DS=at.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.x,i=t.y,n=t.width/5*3,a=Math.max(n,t.height),o=n/2,s=o*o/(a-o),u=i-a+o+s,l=Math.asin(s/o),f=Math.cos(l)*o,h=Math.sin(l),c=Math.cos(l),v=o*.6,d=o*.7;r.moveTo(e-f,u+s),r.arc(e,u,o,Math.PI-l,Math.PI*2+l),r.bezierCurveTo(e+f-h*v,u+s+c*v,e,i-d,e,i),r.bezierCurveTo(e,i-d,e-f+h*v,u+s+c*v,e-f,u+s),r.closePath()}}),MS=at.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.height,i=t.width,n=t.x,a=t.y,o=i/3*2;r.moveTo(n,a),r.lineTo(n+o,a+e),r.lineTo(n,a+e/4*3),r.lineTo(n-o,a+e),r.lineTo(n,a),r.closePath()}}),AS={line:wi,rect:Pt,roundRect:Pt,square:Pt,circle:yo,diamond:CS,pin:DS,arrow:MS,triangle:xS},PS={line:function(r,t,e,i,n){n.x1=r,n.y1=t+i/2,n.x2=r+e,n.y2=t+i/2},rect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i},roundRect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i,n.r=Math.min(e,i)/4},square:function(r,t,e,i,n){var a=Math.min(e,i);n.x=r,n.y=t,n.width=a,n.height=a},circle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.r=Math.min(e,i)/2},diamond:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i},pin:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},arrow:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},triangle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i}},Eu={};D(AS,function(r,t){Eu[t]=new r});var LS=at.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(r,t,e){var i=Uc(r,t,e),n=this.shape;return n&&n.symbolType==="pin"&&t.position==="inside"&&(i.y=e.y+e.height*.4),i},buildPath:function(r,t,e){var i=t.symbolType;if(i!=="none"){var n=Eu[i];n||(i="rect",n=Eu[i]),PS[i](t.x,t.y,t.width,t.height,n.shape),n.buildPath(r,n.shape,e)}}});function IS(r,t){if(this.type!=="image"){var e=this.style;this.__isEmptyBrush?(e.stroke=r,e.fill=t||"#fff",e.lineWidth=2):this.shape.symbolType==="line"?e.stroke=r:e.fill=r,this.markRedraw()}}function Hl(r,t,e,i,n,a,o){var s=r.indexOf("empty")===0;s&&(r=r.substr(5,1).toLowerCase()+r.substr(6));var u;return r.indexOf("image://")===0?u=Ld(r.slice(8),new J(t,e,i,n),o?"center":"cover"):r.indexOf("path://")===0?u=wl(r.slice(7),{},new J(t,e,i,n),o?"center":"cover"):u=new LS({shape:{symbolType:r,x:t,y:e,width:i,height:n}}),u.__isEmptyBrush=s,u.setColor=IS,a&&u.setColor(a),u}function EC(r){return k(r)||(r=[+r,+r]),[r[0]||0,r[1]||0]}function RS(r,t){if(r!=null)return k(r)||(r=[r,r]),[At(r[0],t[0])||0,At(X(r[1],r[0]),t[1])||0]}function Ar(r){return isFinite(r)}function ES(r,t,e){var i=t.x==null?0:t.x,n=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*e.width+e.x,n=n*e.width+e.x,a=a*e.height+e.y,o=o*e.height+e.y),i=Ar(i)?i:0,n=Ar(n)?n:1,a=Ar(a)?a:0,o=Ar(o)?o:0;var s=r.createLinearGradient(i,a,n,o);return s}function OS(r,t,e){var i=e.width,n=e.height,a=Math.min(i,n),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,u=t.r==null?.5:t.r;t.global||(o=o*i+e.x,s=s*n+e.y,u=u*a),o=Ar(o)?o:.5,s=Ar(s)?s:.5,u=u>=0&&Ar(u)?u:.5;var l=r.createRadialGradient(o,s,0,o,s,u);return l}function Ou(r,t,e){for(var i=t.type==="radial"?OS(r,t,e):ES(r,t,e),n=t.colorStops,a=0;a<n.length;a++)i.addColorStop(n[a].offset,n[a].color);return i}function kS(r,t){if(r===t||!r&&!t)return!1;if(!r||!t||r.length!==t.length)return!0;for(var e=0;e<r.length;e++)if(r[e]!==t[e])return!0;return!1}function oa(r){return parseInt(r,10)}function sa(r,t,e){var i=["width","height"][t],n=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(e[i]!=null&&e[i]!=="auto")return parseFloat(e[i]);var s=document.defaultView.getComputedStyle(r);return(r[n]||oa(s[i])||oa(r.style[i]))-(oa(s[a])||0)-(oa(s[o])||0)|0}function BS(r,t){return!r||r==="solid"||!(t>0)?null:r==="dashed"?[4*t,2*t]:r==="dotted"?[t]:ht(r)?[r]:k(r)?r:null}function Op(r){var t=r.style,e=t.lineDash&&t.lineWidth>0&&BS(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(e){var n=t.strokeNoScale&&r.getLineScale?r.getLineScale():1;n&&n!==1&&(e=Y(e,function(a){return a/n}),i/=n)}return[e,i]}var FS=new pi(!0);function to(r){var t=r.stroke;return!(t==null||t==="none"||!(r.lineWidth>0))}function dv(r){return typeof r=="string"&&r!=="none"}function eo(r){var t=r.fill;return t!=null&&t!=="none"}function pv(r,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.fillOpacity*t.opacity,r.fill(),r.globalAlpha=e}else r.fill()}function gv(r,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.strokeOpacity*t.opacity,r.stroke(),r.globalAlpha=e}else r.stroke()}function ku(r,t,e){var i=nd(t.image,t.__image,e);if(vo(i)){var n=r.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&n&&n.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*s_),a.scaleSelf(t.scaleX||1,t.scaleY||1),n.setTransform(a)}return n}}function NS(r,t,e,i){var n,a=to(e),o=eo(e),s=e.strokePercent,u=s<1,l=!t.path;(!t.silent||u)&&l&&t.createPathProxy();var f=t.path||FS,h=t.__dirty;if(!i){var c=e.fill,v=e.stroke,d=o&&!!c.colorStops,_=a&&!!v.colorStops,p=o&&!!c.image,g=a&&!!v.image,y=void 0,m=void 0,w=void 0,b=void 0,S=void 0;(d||_)&&(S=t.getBoundingRect()),d&&(y=h?Ou(r,c,S):t.__canvasFillGradient,t.__canvasFillGradient=y),_&&(m=h?Ou(r,v,S):t.__canvasStrokeGradient,t.__canvasStrokeGradient=m),p&&(w=h||!t.__canvasFillPattern?ku(r,c,t):t.__canvasFillPattern,t.__canvasFillPattern=w),g&&(b=h||!t.__canvasStrokePattern?ku(r,v,t):t.__canvasStrokePattern,t.__canvasStrokePattern=w),d?r.fillStyle=y:p&&(w?r.fillStyle=w:o=!1),_?r.strokeStyle=m:g&&(b?r.strokeStyle=b:a=!1)}var x=t.getGlobalScale();f.setScale(x[0],x[1],t.segmentIgnoreThreshold);var C,M;r.setLineDash&&e.lineDash&&(n=Op(t),C=n[0],M=n[1]);var A=!0;(l||h&ti)&&(f.setDPR(r.dpr),u?f.setContext(null):(f.setContext(r),A=!1),f.reset(),t.buildPath(f,t.shape,i),f.toStatic(),t.pathUpdated()),A&&f.rebuildPath(r,u?s:1),C&&(r.setLineDash(C),r.lineDashOffset=M),i||(e.strokeFirst?(a&&gv(r,e),o&&pv(r,e)):(o&&pv(r,e),a&&gv(r,e))),C&&r.setLineDash([])}function zS(r,t,e){var i=t.__image=nd(e.image,t.__image,t,t.onload);if(!(!i||!vo(i))){var n=e.x||0,a=e.y||0,o=t.getWidth(),s=t.getHeight(),u=i.width/i.height;if(o==null&&s!=null?o=s*u:s==null&&o!=null?s=o/u:o==null&&s==null&&(o=i.width,s=i.height),e.sWidth&&e.sHeight){var l=e.sx||0,f=e.sy||0;r.drawImage(i,l,f,e.sWidth,e.sHeight,n,a,o,s)}else if(e.sx&&e.sy){var l=e.sx,f=e.sy,h=o-l,c=s-f;r.drawImage(i,l,f,h,c,n,a,o,s)}else r.drawImage(i,n,a,o,s)}}function HS(r,t,e){var i,n=e.text;if(n!=null&&(n+=""),n){r.font=e.font||kr,r.textAlign=e.textAlign,r.textBaseline=e.textBaseline;var a=void 0,o=void 0;r.setLineDash&&e.lineDash&&(i=Op(t),a=i[0],o=i[1]),a&&(r.setLineDash(a),r.lineDashOffset=o),e.strokeFirst?(to(e)&&r.strokeText(n,e.x,e.y),eo(e)&&r.fillText(n,e.x,e.y)):(eo(e)&&r.fillText(n,e.x,e.y),to(e)&&r.strokeText(n,e.x,e.y)),a&&r.setLineDash([])}}var _v=["shadowBlur","shadowOffsetX","shadowOffsetY"],yv=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function kp(r,t,e,i,n){var a=!1;if(!i&&(e=e||{},t===e))return!1;if(i||t.opacity!==e.opacity){kt(r,n),a=!0;var o=Math.max(Math.min(t.opacity,1),0);r.globalAlpha=isNaN(o)?Ir.opacity:o}(i||t.blend!==e.blend)&&(a||(kt(r,n),a=!0),r.globalCompositeOperation=t.blend||Ir.blend);for(var s=0;s<_v.length;s++){var u=_v[s];(i||t[u]!==e[u])&&(a||(kt(r,n),a=!0),r[u]=r.dpr*(t[u]||0))}return(i||t.shadowColor!==e.shadowColor)&&(a||(kt(r,n),a=!0),r.shadowColor=t.shadowColor||Ir.shadowColor),a}function mv(r,t,e,i,n){var a=Tn(t,n.inHover),o=i?null:e&&Tn(e,n.inHover)||{};if(a===o)return!1;var s=kp(r,a,o,i,n);if((i||a.fill!==o.fill)&&(s||(kt(r,n),s=!0),dv(a.fill)&&(r.fillStyle=a.fill)),(i||a.stroke!==o.stroke)&&(s||(kt(r,n),s=!0),dv(a.stroke)&&(r.strokeStyle=a.stroke)),(i||a.opacity!==o.opacity)&&(s||(kt(r,n),s=!0),r.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var u=a.lineWidth,l=u/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);r.lineWidth!==l&&(s||(kt(r,n),s=!0),r.lineWidth=l)}for(var f=0;f<yv.length;f++){var h=yv[f],c=h[0];(i||a[c]!==o[c])&&(s||(kt(r,n),s=!0),r[c]=a[c]||h[1])}return s}function GS(r,t,e,i,n){return kp(r,Tn(t,n.inHover),e&&Tn(e,n.inHover),i,n)}function Bp(r,t){var e=t.transform,i=r.dpr||1;e?r.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):r.setTransform(i,0,0,i,0,0)}function WS(r,t,e){for(var i=!1,n=0;n<r.length;n++){var a=r[n];i=i||a.isZeroArea(),Bp(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}e.allClipped=i}function VS(r,t){return r&&t?r[0]!==t[0]||r[1]!==t[1]||r[2]!==t[2]||r[3]!==t[3]||r[4]!==t[4]||r[5]!==t[5]:!(!r&&!t)}var wv=1,Sv=2,bv=3,Tv=4;function US(r){var t=eo(r),e=to(r);return!(r.lineDash||!(+t^+e)||t&&typeof r.fill!="string"||e&&typeof r.stroke!="string"||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1)}function kt(r,t){t.batchFill&&r.fill(),t.batchStroke&&r.stroke(),t.batchFill="",t.batchStroke=""}function Tn(r,t){return t&&r.__hoverStyle||r.style}function Fp(r,t){Pr(r,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Pr(r,t,e,i){var n=t.transform;if(!t.shouldBePainted(e.viewWidth,e.viewHeight,!1,!1)){t.__dirty&=~Gt,t.__isRendered=!1;return}var a=t.__clipPaths,o=e.prevElClipPaths,s=!1,u=!1;if((!o||kS(a,o))&&(o&&o.length&&(kt(r,e),r.restore(),u=s=!0,e.prevElClipPaths=null,e.allClipped=!1,e.prevEl=null),a&&a.length&&(kt(r,e),r.save(),WS(a,r,e),s=!0),e.prevElClipPaths=a),e.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var l=e.prevEl;l||(u=s=!0);var f=t instanceof at&&t.autoBatch&&US(t.style);s||VS(n,l.transform)?(kt(r,e),Bp(r,t)):f||kt(r,e);var h=Tn(t,e.inHover);t instanceof at?(e.lastDrawType!==wv&&(u=!0,e.lastDrawType=wv),mv(r,t,l,u,e),(!f||!e.batchFill&&!e.batchStroke)&&r.beginPath(),NS(r,t,h,f),f&&(e.batchFill=h.fill||"",e.batchStroke=h.stroke||"")):t instanceof Ya?(e.lastDrawType!==bv&&(u=!0,e.lastDrawType=bv),mv(r,t,l,u,e),HS(r,t,h)):t instanceof Fr?(e.lastDrawType!==Sv&&(u=!0,e.lastDrawType=Sv),GS(r,t,l,u,e),zS(r,t,h)):t.getTemporalDisplayables&&(e.lastDrawType!==Tv&&(u=!0,e.lastDrawType=Tv),YS(r,t,e)),f&&i&&kt(r,e),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),e.prevEl=t,t.__dirty=0,t.__isRendered=!0}function YS(r,t,e){var i=t.getDisplayables(),n=t.getTemporalDisplayables();r.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:e.viewWidth,viewHeight:e.viewHeight,inHover:e.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var u=i[o];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Pr(r,u,a,o===s-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}for(var l=0,f=n.length;l<f;l++){var u=n[l];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Pr(r,u,a,l===f-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}t.clearTemporalDisplayables(),t.notClear=!0,r.restore()}var As=new TS,xv=new Mn(100),Cv=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Bu(r,t){if(r==="none")return null;var e=t.getDevicePixelRatio(),i=t.getZr(),n=i.painter.type==="svg";r.dirty&&As.delete(r);var a=As.get(r);if(a)return a;var o=ot(r,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return u(s),s.rotation=o.rotation,s.scaleX=s.scaleY=n?1:1/e,As.set(r,s),r.dirty=!1,s;function u(l){for(var f=[e],h=!0,c=0;c<Cv.length;++c){var v=o[Cv[c]];if(v!=null&&!k(v)&&!B(v)&&!ht(v)&&typeof v!="boolean"){h=!1;break}f.push(v)}var d;if(h){d=f.join(",")+(n?"-svg":"");var _=xv.get(d);_&&(n?l.svgElement=_:l.image=_)}var p=zp(o.dashArrayX),g=XS(o.dashArrayY),y=Np(o.symbol),m=$S(p),w=Hp(g),b=!n&&_i.createCanvas(),S=n&&{tag:"g",attrs:{},key:"dcl",children:[]},x=M(),C;b&&(b.width=x.width*e,b.height=x.height*e,C=b.getContext("2d")),A(),h&&xv.put(d,b||S),l.image=b,l.svgElement=S,l.svgWidth=x.width,l.svgHeight=x.height;function M(){for(var T=1,P=0,L=m.length;P<L;++P)T=Ff(T,m[P]);for(var I=1,P=0,L=y.length;P<L;++P)I=Ff(I,y[P].length);T*=I;var R=w*m.length*y.length;return{width:Math.max(1,Math.min(T,o.maxTileWidth)),height:Math.max(1,Math.min(R,o.maxTileHeight))}}function A(){C&&(C.clearRect(0,0,b.width,b.height),o.backgroundColor&&(C.fillStyle=o.backgroundColor,C.fillRect(0,0,b.width,b.height)));for(var T=0,P=0;P<g.length;++P)T+=g[P];if(T<=0)return;for(var L=-w,I=0,R=0,E=0;L<x.height;){if(I%2===0){for(var U=R/2%y.length,N=0,z=0,G=0;N<x.width*2;){for(var rt=0,P=0;P<p[E].length;++P)rt+=p[E][P];if(rt<=0)break;if(z%2===0){var j=(1-o.symbolSize)*.5,ct=N+p[E][z]*j,xt=L+g[I]*j,te=p[E][z]*o.symbolSize,tr=g[I]*o.symbolSize,er=G/2%y[U].length;zr(ct,xt,te,tr,y[U][er])}N+=p[E][z],++G,++z,z===p[E].length&&(z=0)}++E,E===p.length&&(E=0)}L+=g[I],++R,++I,I===g.length&&(I=0)}function zr(Ht,dt,V,q,rr){var Ct=n?1:e,ef=Hl(rr,Ht*Ct,dt*Ct,V*Ct,q*Ct,o.color,o.symbolKeepAspect);if(n){var rf=i.painter.renderOneToVNode(ef);rf&&S.children.push(rf)}else Fp(C,ef)}}}}function Np(r){if(!r||r.length===0)return[["rect"]];if(B(r))return[[r]];for(var t=!0,e=0;e<r.length;++e)if(!B(r[e])){t=!1;break}if(t)return Np([r]);for(var i=[],e=0;e<r.length;++e)B(r[e])?i.push([r[e]]):i.push(r[e]);return i}function zp(r){if(!r||r.length===0)return[[0,0]];if(ht(r)){var t=Math.ceil(r);return[[t,t]]}for(var e=!0,i=0;i<r.length;++i)if(!ht(r[i])){e=!1;break}if(e)return zp([r]);for(var n=[],i=0;i<r.length;++i)if(ht(r[i])){var t=Math.ceil(r[i]);n.push([t,t])}else{var t=Y(r[i],function(s){return Math.ceil(s)});t.length%2===1?n.push(t.concat(t)):n.push(t)}return n}function XS(r){if(!r||typeof r=="object"&&r.length===0)return[0,0];if(ht(r)){var t=Math.ceil(r);return[t,t]}var e=Y(r,function(i){return Math.ceil(i)});return r.length%2?e.concat(e):e}function $S(r){return Y(r,function(t){return Hp(t)})}function Hp(r){for(var t=0,e=0;e<r.length;++e)t+=r[e];return r.length%2===1?t*2:t}function qS(r,t){r.eachRawSeries(function(e){if(!r.isSeriesFiltered(e)){var i=e.getData();i.hasItemVisual()&&i.each(function(o){var s=i.getItemVisual(o,"decal");if(s){var u=i.ensureUniqueItemVisual(o,"style");u.decal=Bu(s,t)}});var n=i.getVisual("decal");if(n){var a=i.getVisual("style");a.decal=Bu(n,t)}}})}var ae=new Ce,Gp={};function ZS(r,t){Gp[r]=t}function KS(r){return Gp[r]}var QS=1,JS=800,jS=900,tb=1e3,eb=2e3,rb=5e3,Wp=1e3,ib=1100,Gl=2e3,Vp=3e3,nb=4e3,Lo=4500,ab=4600,ob=5e3,sb=6e3,Up=7e3,ub={PROCESSOR:{FILTER:tb,SERIES_FILTER:JS,STATISTIC:rb},VISUAL:{LAYOUT:Wp,PROGRESSIVE_LAYOUT:ib,GLOBAL:Gl,CHART:Vp,POST_CHART_LAYOUT:ab,COMPONENT:nb,BRUSH:ob,CHART_ITEM:Lo,ARIA:sb,DECAL:Up}},wt="__flagInMainProcess",Rt="__pendingUpdate",Ps="__needsUpdateStatus",Dv=/^[a-zA-Z0-9_]+$/,Ls="__connectUpdateStatus",Mv=0,lb=1,fb=2;function Yp(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.isDisposed()){this.id;return}return $p(this,r,t)}}function Xp(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return $p(this,r,t)}}function $p(r,t,e){return e[0]=e[0]&&e[0].toLowerCase(),Ce.prototype[t].apply(r,e)}var qp=function(r){F(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(Ce),Zp=qp.prototype;Zp.on=Xp("on");Zp.off=Xp("off");var Jr,Is,ua,Ge,Rs,Es,Os,ki,Bi,Av,Pv,ks,Lv,la,Iv,Kp,Vt,Rv,Qp=function(r){F(t,r);function t(e,i,n){var a=r.call(this,new pS)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],n=n||{},B(i)&&(i=Jp[i]),a._dom=e;var o="canvas",s="auto",u=!1;n.ssr;var l=a._zr=kf(e,{renderer:n.renderer||o,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:X(n.useDirtyRect,u),useCoarsePointer:X(n.useCoarsePointer,s),pointerSize:n.pointerSize});a._ssr=n.ssr,a._throttledZrFlush=zl(st(l.flush,l),17),i=K(i),i&&op(i,!0),a._theme=i,a._locale=D1(n.locale||Nd),a._coordSysMgr=new np;var f=a._api=Iv(a);function h(c,v){return c.__prio-v.__prio}return Sa(io,h),Sa(Fu,h),a._scheduler=new Pp(a,f,Fu,io),a._messageCenter=new qp,a._initEvents(),a.resize=st(a.resize,a),l.animation.on("frame",a._onframe,a),Av(l,a),Pv(l,a),Ba(a),a}return t.prototype._onframe=function(){if(!this._disposed){Rv(this);var e=this._scheduler;if(this[Rt]){var i=this[Rt].silent;this[wt]=!0;try{Jr(this),Ge.update.call(this,null,this[Rt].updateParams)}catch(u){throw this[wt]=!1,this[Rt]=null,u}this._zr.flush(),this[wt]=!1,this[Rt]=null,ki.call(this,i),Bi.call(this,i)}else if(e.unfinished){var n=QS,a=this._model,o=this._api;e.unfinished=!1;do{var s=+new Date;e.performSeriesTasks(a),e.performDataProcessorTasks(a),Es(this,a),e.performVisualTasks(a),la(this,this._model,o,"remain",{}),n-=+new Date-s}while(n>0&&e.unfinished);e.unfinished||this._zr.flush()}}},t.prototype.getDom=function(){return this._dom},t.prototype.getId=function(){return this.id},t.prototype.getZr=function(){return this._zr},t.prototype.isSSR=function(){return this._ssr},t.prototype.setOption=function(e,i,n){if(!this[wt]){if(this._disposed){this.id;return}var a,o,s;if(H(i)&&(n=i.lazyUpdate,a=i.silent,o=i.replaceMerge,s=i.transition,i=i.notMerge),this[wt]=!0,!this._model||i){var u=new Q1(this._api),l=this._theme,f=this._model=new kl;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,l,this._locale,u)}this._model.setOption(e,{replaceMerge:o},Nu);var h={seriesTransition:s,optionChanged:!0};if(n)this[Rt]={silent:a,updateParams:h},this[wt]=!1,this.getZr().wakeUp();else{try{Jr(this),Ge.update.call(this,null,h)}catch(c){throw this[Rt]=null,this[wt]=!1,c}this._ssr||this._zr.flush(),this[Rt]=null,this[wt]=!1,ki.call(this,a),Bi.call(this,a)}}},t.prototype.setTheme=function(){},t.prototype.getModel=function(){return this._model},t.prototype.getOption=function(){return this._model&&this._model.getOption()},t.prototype.getWidth=function(){return this._zr.getWidth()},t.prototype.getHeight=function(){return this._zr.getHeight()},t.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||W.hasGlobalWindow&&window.devicePixelRatio||1},t.prototype.getRenderedCanvas=function(e){return this.renderToCanvas(e)},t.prototype.renderToCanvas=function(e){e=e||{};var i=this._zr.painter;return i.getRenderedCanvas({backgroundColor:e.backgroundColor||this._model.get("backgroundColor"),pixelRatio:e.pixelRatio||this.getDevicePixelRatio()})},t.prototype.renderToSVGString=function(e){e=e||{};var i=this._zr.painter;return i.renderToString({useViewBox:e.useViewBox})},t.prototype.getSvgDataURL=function(){if(W.svgSupported){var e=this._zr,i=e.storage.getDisplayList();return D(i,function(n){n.stopAnimation(null,!0)}),e.painter.toDataURL()}},t.prototype.getDataURL=function(e){if(this._disposed){this.id;return}e=e||{};var i=e.excludeComponents,n=this._model,a=[],o=this;D(i,function(u){n.eachComponent({mainType:u},function(l){var f=o._componentsMap[l.__viewId];f.group.ignore||(a.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(e).toDataURL("image/"+(e&&e.type||"png"));return D(a,function(u){u.group.ignore=!1}),s},t.prototype.getConnectedDataURL=function(e){if(this._disposed){this.id;return}var i=e.type==="svg",n=this.group,a=Math.min,o=Math.max,s=1/0;if(Ev[n]){var u=s,l=s,f=-s,h=-s,c=[],v=e&&e.pixelRatio||this.getDevicePixelRatio();D(ln,function(m,w){if(m.group===n){var b=i?m.getZr().painter.getSvgDom().innerHTML:m.renderToCanvas(K(e)),S=m.getDom().getBoundingClientRect();u=a(S.left,u),l=a(S.top,l),f=o(S.right,f),h=o(S.bottom,h),c.push({dom:b,left:S.left,top:S.top})}}),u*=v,l*=v,f*=v,h*=v;var d=f-u,_=h-l,p=_i.createCanvas(),g=kf(p,{renderer:i?"svg":"canvas"});if(g.resize({width:d,height:_}),i){var y="";return D(c,function(m){var w=m.left-u,b=m.top-l;y+='<g transform="translate('+w+","+b+')">'+m.dom+"</g>"}),g.painter.getSvgRoot().innerHTML=y,e.connectedBackgroundColor&&g.painter.setBackgroundColor(e.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()}else return e.connectedBackgroundColor&&g.add(new Pt({shape:{x:0,y:0,width:d,height:_},style:{fill:e.connectedBackgroundColor}})),D(c,function(m){var w=new Fr({style:{x:m.left*v-u,y:m.top*v-l,image:m.dom}});g.add(w)}),g.refreshImmediately(),p.toDataURL("image/"+(e&&e.type||"png"))}else return this.getDataURL(e)},t.prototype.convertToPixel=function(e,i){return Rs(this,"convertToPixel",e,i)},t.prototype.convertFromPixel=function(e,i){return Rs(this,"convertFromPixel",e,i)},t.prototype.containPixel=function(e,i){if(this._disposed){this.id;return}var n=this._model,a,o=es(n,e);return D(o,function(s,u){u.indexOf("Models")>=0&&D(s,function(l){var f=l.coordinateSystem;if(f&&f.containPoint)a=a||!!f.containPoint(i);else if(u==="seriesModels"){var h=this._chartsMap[l.__viewId];h&&h.containPoint&&(a=a||h.containPoint(i,l))}},this)},this),!!a},t.prototype.getVisual=function(e,i){var n=this._model,a=es(n,e,{defaultMainType:"series"}),o=a.seriesModel,s=o.getData(),u=a.hasOwnProperty("dataIndexInside")?a.dataIndexInside:a.hasOwnProperty("dataIndex")?s.indexOfRawIndex(a.dataIndex):null;return u!=null?yS(s,u,i):mS(s,i)},t.prototype.getViewOfComponentModel=function(e){return this._componentsMap[e.__viewId]},t.prototype.getViewOfSeriesModel=function(e){return this._chartsMap[e.__viewId]},t.prototype._initEvents=function(){var e=this;D(hb,function(i){var n=function(a){var o=e.getModel(),s=a.target,u,l=i==="globalout";if(l?u={}:s&&Qi(s,function(d){var _=it(d);if(_&&_.dataIndex!=null){var p=_.dataModel||o.getSeriesByIndex(_.seriesIndex);return u=p&&p.getDataParams(_.dataIndex,_.dataType,s)||{},!0}else if(_.eventData)return u=O({},_.eventData),!0},!0),u){var f=u.componentType,h=u.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",h=u.seriesIndex);var c=f&&h!=null&&o.getComponent(f,h),v=c&&e[c.mainType==="series"?"_chartsMap":"_componentsMap"][c.__viewId];u.event=a,u.type=i,e._$eventProcessor.eventInfo={targetEl:s,packedEvent:u,model:c,view:v},e.trigger(i,u)}};n.zrEventfulCallAtLast=!0,e._zr.on(i,n,e)}),D(un,function(i,n){e._messageCenter.on(n,function(a){this.trigger(n,a)},e)}),D(["selectchanged"],function(i){e._messageCenter.on(i,function(n){this.trigger(i,n)},e)}),wS(this._messageCenter,this,this._api)},t.prototype.isDisposed=function(){return this._disposed},t.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},t.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var e=this.getDom();e&&ed(this.getDom(),Vl,"");var i=this,n=i._api,a=i._model;D(i._componentsViews,function(o){o.dispose(a,n)}),D(i._chartsViews,function(o){o.dispose(a,n)}),i._zr.dispose(),i._dom=i._model=i._chartsMap=i._componentsMap=i._chartsViews=i._componentsViews=i._scheduler=i._api=i._zr=i._throttledZrFlush=i._theme=i._coordSysMgr=i._messageCenter=null,delete ln[i.id]},t.prototype.resize=function(e){if(!this[wt]){if(this._disposed){this.id;return}this._zr.resize(e);var i=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!i){var n=i.resetOption("media"),a=e&&e.silent;this[Rt]&&(a==null&&(a=this[Rt].silent),n=!0,this[Rt]=null),this[wt]=!0;try{n&&Jr(this),Ge.update.call(this,{type:"resize",animation:O({duration:0},e&&e.animation)})}catch(o){throw this[wt]=!1,o}this[wt]=!1,ki.call(this,a),Bi.call(this,a)}}},t.prototype.showLoading=function(e,i){if(this._disposed){this.id;return}if(H(e)&&(i=e,e=""),e=e||"default",this.hideLoading(),!!zu[e]){var n=zu[e](this._api,i),a=this._zr;this._loadingFX=n,a.add(n)}},t.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},t.prototype.makeActionFromEvent=function(e){var i=O({},e);return i.type=un[e.type],i},t.prototype.dispatchAction=function(e,i){if(this._disposed){this.id;return}if(H(i)||(i={silent:!!i}),!!ro[e.type]&&this._model){if(this[wt]){this._pendingActions.push(e);return}var n=i.silent;Os.call(this,e,n);var a=i.flush;a?this._zr.flush():a!==!1&&W.browser.weChat&&this._throttledZrFlush(),ki.call(this,n),Bi.call(this,n)}},t.prototype.updateLabelLayout=function(){ae.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},t.prototype.appendData=function(e){if(this._disposed){this.id;return}var i=e.seriesIndex,n=this.getModel(),a=n.getSeriesByIndex(i);a.appendData(e),this._scheduler.unfinished=!0,this.getZr().wakeUp()},t.internalField=function(){Jr=function(h){var c=h._scheduler;c.restorePipelines(h._model),c.prepareStageTasks(),Is(h,!0),Is(h,!1),c.plan()},Is=function(h,c){for(var v=h._model,d=h._scheduler,_=c?h._componentsViews:h._chartsViews,p=c?h._componentsMap:h._chartsMap,g=h._zr,y=h._api,m=0;m<_.length;m++)_[m].__alive=!1;c?v.eachComponent(function(S,x){S!=="series"&&w(x)}):v.eachSeries(w);function w(S){var x=S.__requireNewView;S.__requireNewView=!1;var C="_ec_"+S.id+"_"+S.type,M=!x&&p[C];if(!M){var A=Se(S.type),T=c?he.getClass(A.main,A.sub):Er.getClass(A.sub);M=new T,M.init(v,y),p[C]=M,_.push(M),g.add(M.group)}S.__viewId=M.__id=C,M.__alive=!0,M.__model=S,M.group.__ecComponentInfo={mainType:S.mainType,index:S.componentIndex},!c&&d.prepareView(M,S,v,y)}for(var m=0;m<_.length;){var b=_[m];b.__alive?m++:(!c&&b.renderTask.dispose(),g.remove(b.group),b.dispose(v,y),_.splice(m,1),p[b.__id]===b&&delete p[b.__id],b.__id=b.group.__ecComponentInfo=null)}},ua=function(h,c,v,d,_){var p=h._model;if(p.setUpdatePayload(v),!d){D([].concat(h._componentsViews).concat(h._chartsViews),b);return}var g={};g[d+"Id"]=v[d+"Id"],g[d+"Index"]=v[d+"Index"],g[d+"Name"]=v[d+"Name"];var y={mainType:d,query:g};_&&(y.subType=_);var m=v.excludeSeriesId,w;m!=null&&(w=$(),D(Tt(m),function(S){var x=le(S,null);x!=null&&w.set(x,!0)})),p&&p.eachComponent(y,function(S){var x=w&&w.get(S.id)!=null;if(!x)if(vh(v))if(S instanceof Br)v.type===Rr&&!v.notBlur&&!S.get(["emphasis","disabled"])&&rm(S,v,h._api);else{var C=vl(S.mainType,S.componentIndex,v.name,h._api),M=C.focusSelf,A=C.dispatchers;v.type===Rr&&M&&!v.notBlur&&_u(S.mainType,S.componentIndex,h._api),A&&D(A,function(T){v.type===Rr?du(T):pu(T)})}else wu(v)&&S instanceof Br&&(am(S,v,h._api),fh(S),Vt(h))},h),p&&p.eachComponent(y,function(S){var x=w&&w.get(S.id)!=null;x||b(h[d==="series"?"_chartsMap":"_componentsMap"][S.__viewId])},h);function b(S){S&&S.__alive&&S[c]&&S[c](S.__model,p,h._api,v)}},Ge={prepareAndUpdate:function(h){Jr(this),Ge.update.call(this,h,{optionChanged:h.newOption!=null})},update:function(h,c){var v=this._model,d=this._api,_=this._zr,p=this._coordSysMgr,g=this._scheduler;if(v){v.setUpdatePayload(h),g.restoreData(v,h),g.performSeriesTasks(v),p.create(v,d),g.performDataProcessorTasks(v,h),Es(this,v),p.update(v,d),e(v),g.performVisualTasks(v,h),ks(this,v,d,h,c);var y=v.get("backgroundColor")||"transparent",m=v.get("darkMode");_.setBackgroundColor(y),m!=null&&m!=="auto"&&_.setDarkMode(m),ae.trigger("afterupdate",v,d)}},updateTransform:function(h){var c=this,v=this._model,d=this._api;if(v){v.setUpdatePayload(h);var _=[];v.eachComponent(function(g,y){if(g!=="series"){var m=c.getViewOfComponentModel(y);if(m&&m.__alive)if(m.updateTransform){var w=m.updateTransform(y,v,d,h);w&&w.update&&_.push(m)}else _.push(m)}});var p=$();v.eachSeries(function(g){var y=c._chartsMap[g.__viewId];if(y.updateTransform){var m=y.updateTransform(g,v,d,h);m&&m.update&&p.set(g.uid,1)}else p.set(g.uid,1)}),e(v),this._scheduler.performVisualTasks(v,h,{setDirty:!0,dirtyMap:p}),la(this,v,d,h,{},p),ae.trigger("afterupdate",v,d)}},updateView:function(h){var c=this._model;c&&(c.setUpdatePayload(h),Er.markUpdateMethod(h,"updateView"),e(c),this._scheduler.performVisualTasks(c,h,{setDirty:!0}),ks(this,c,this._api,h,{}),ae.trigger("afterupdate",c,this._api))},updateVisual:function(h){var c=this,v=this._model;v&&(v.setUpdatePayload(h),v.eachSeries(function(d){d.getData().clearAllVisual()}),Er.markUpdateMethod(h,"updateVisual"),e(v),this._scheduler.performVisualTasks(v,h,{visualType:"visual",setDirty:!0}),v.eachComponent(function(d,_){if(d!=="series"){var p=c.getViewOfComponentModel(_);p&&p.__alive&&p.updateVisual(_,v,c._api,h)}}),v.eachSeries(function(d){var _=c._chartsMap[d.__viewId];_.updateVisual(d,v,c._api,h)}),ae.trigger("afterupdate",v,this._api))},updateLayout:function(h){Ge.update.call(this,h)}},Rs=function(h,c,v,d){if(h._disposed){h.id;return}for(var _=h._model,p=h._coordSysMgr.getCoordinateSystems(),g,y=es(_,v),m=0;m<p.length;m++){var w=p[m];if(w[c]&&(g=w[c](_,y,d))!=null)return g}},Es=function(h,c){var v=h._chartsMap,d=h._scheduler;c.eachSeries(function(_){d.updateStreamModes(_,v[_.__viewId])})},Os=function(h,c){var v=this,d=this.getModel(),_=h.type,p=h.escapeConnect,g=ro[_],y=g.actionInfo,m=(y.update||"update").split(":"),w=m.pop(),b=m[0]!=null&&Se(m[0]);this[wt]=!0;var S=[h],x=!1;h.batch&&(x=!0,S=Y(h.batch,function(I){return I=ot(O({},I),h),I.batch=null,I}));var C=[],M,A=wu(h),T=vh(h);if(T&&wd(this._api),D(S,function(I){if(M=g.action(I,v._model,v._api),M=M||O({},I),M.type=y.event||M.type,C.push(M),T){var R=sl(h),E=R.queryOptionMap,U=R.mainTypeSpecified,N=U?E.keys()[0]:"series";ua(v,w,I,N),Vt(v)}else A?(ua(v,w,I,"series"),Vt(v)):b&&ua(v,w,I,b.main,b.sub)}),w!=="none"&&!T&&!A&&!b)try{this[Rt]?(Jr(this),Ge.update.call(this,h),this[Rt]=null):Ge[w].call(this,h)}catch(I){throw this[wt]=!1,I}if(x?M={type:y.event||_,escapeConnect:p,batch:C}:M=C[0],this[wt]=!1,!c){var P=this._messageCenter;if(P.trigger(M.type,M),A){var L={type:"selectchanged",escapeConnect:p,selected:om(d),isFromClick:h.isFromClick||!1,fromAction:h.type,fromActionPayload:h};P.trigger(L.type,L)}}},ki=function(h){for(var c=this._pendingActions;c.length;){var v=c.shift();Os.call(this,v,h)}},Bi=function(h){!h&&this.trigger("updated")},Av=function(h,c){h.on("rendered",function(v){c.trigger("rendered",v),h.animation.isFinished()&&!c[Rt]&&!c._scheduler.unfinished&&!c._pendingActions.length&&c.trigger("finished")})},Pv=function(h,c){h.on("mouseover",function(v){var d=v.target,_=Qi(d,mu);_&&(im(_,v,c._api),Vt(c))}).on("mouseout",function(v){var d=v.target,_=Qi(d,mu);_&&(nm(_,v,c._api),Vt(c))}).on("click",function(v){var d=v.target,_=Qi(d,function(y){return it(y).dataIndex!=null},!0);if(_){var p=_.selected?"unselect":"select",g=it(_);c._api.dispatchAction({type:p,dataType:g.dataType,dataIndexInside:g.dataIndex,seriesIndex:g.seriesIndex,isFromClick:!0})}})};function e(h){h.clearColorPalette(),h.eachSeries(function(c){c.clearColorPalette()})}function i(h){var c=[],v=[],d=!1;if(h.eachComponent(function(y,m){var w=m.get("zlevel")||0,b=m.get("z")||0,S=m.getZLevelKey();d=d||!!S,(y==="series"?v:c).push({zlevel:w,z:b,idx:m.componentIndex,type:y,key:S})}),d){var _=c.concat(v),p,g;Sa(_,function(y,m){return y.zlevel===m.zlevel?y.z-m.z:y.zlevel-m.zlevel}),D(_,function(y){var m=h.getComponent(y.type,y.idx),w=y.zlevel,b=y.key;p!=null&&(w=Math.max(p,w)),b?(w===p&&b!==g&&w++,g=b):g&&(w===p&&w++,g=""),p=w,m.setZLevel(w)})}}ks=function(h,c,v,d,_){i(c),Lv(h,c,v,d,_),D(h._chartsViews,function(p){p.__alive=!1}),la(h,c,v,d,_),D(h._chartsViews,function(p){p.__alive||p.remove(c,v)})},Lv=function(h,c,v,d,_,p){D(p||h._componentsViews,function(g){var y=g.__model;l(y,g),g.render(y,c,v,d),s(y,g),f(y,g)})},la=function(h,c,v,d,_,p){var g=h._scheduler;_=O(_||{},{updatedSeries:c.getSeries()}),ae.trigger("series:beforeupdate",c,v,_);var y=!1;c.eachSeries(function(m){var w=h._chartsMap[m.__viewId];w.__alive=!0;var b=w.renderTask;g.updatePayload(b,d),l(m,w),p&&p.get(m.uid)&&b.dirty(),b.perform(g.getPerformArgs(b))&&(y=!0),w.group.silent=!!m.get("silent"),o(m,w),fh(m)}),g.unfinished=y||g.unfinished,ae.trigger("series:layoutlabels",c,v,_),ae.trigger("series:transition",c,v,_),c.eachSeries(function(m){var w=h._chartsMap[m.__viewId];s(m,w),f(m,w)}),a(h,c),ae.trigger("series:afterupdate",c,v,_)},Vt=function(h){h[Ps]=!0,h.getZr().wakeUp()},Rv=function(h){h[Ps]&&(h.getZr().storage.traverse(function(c){an(c)||n(c)}),h[Ps]=!1)};function n(h){for(var c=[],v=h.currentStates,d=0;d<v.length;d++){var _=v[d];_==="emphasis"||_==="blur"||_==="select"||c.push(_)}h.selected&&h.states.select&&c.push("select"),h.hoverState===go&&h.states.emphasis?c.push("emphasis"):h.hoverState===po&&h.states.blur&&c.push("blur"),h.useStates(c)}function a(h,c){var v=h._zr,d=v.storage,_=0;d.traverse(function(p){p.isGroup||_++}),_>c.get("hoverLayerThreshold")&&!W.node&&!W.worker&&c.eachSeries(function(p){if(!p.preventUsingHoverLayer){var g=h._chartsMap[p.__viewId];g.__alive&&g.eachRendered(function(y){y.states.emphasis&&(y.states.emphasis.hoverLayer=!0)})}})}function o(h,c){var v=h.get("blendMode")||null;c.eachRendered(function(d){d.isGroup||(d.style.blend=v)})}function s(h,c){if(!h.preventAutoZ){var v=h.get("z")||0,d=h.get("zlevel")||0;c.eachRendered(function(_){return u(_,v,d,-1/0),!0})}}function u(h,c,v,d){var _=h.getTextContent(),p=h.getTextGuideLine(),g=h.isGroup;if(g)for(var y=h.childrenRef(),m=0;m<y.length;m++)d=Math.max(u(y[m],c,v,d),d);else h.z=c,h.zlevel=v,d=Math.max(h.z2,d);if(_&&(_.z=c,_.zlevel=v,isFinite(d)&&(_.z2=d+2)),p){var w=h.textGuideLineConfig;p.z=c,p.zlevel=v,isFinite(d)&&(p.z2=d+(w&&w.showAbove?1:-1))}return d}function l(h,c){c.eachRendered(function(v){if(!an(v)){var d=v.getTextContent(),_=v.getTextGuideLine();v.stateTransition&&(v.stateTransition=null),d&&d.stateTransition&&(d.stateTransition=null),_&&_.stateTransition&&(_.stateTransition=null),v.hasState()?(v.prevStates=v.currentStates,v.clearStates()):v.prevStates&&(v.prevStates=null)}})}function f(h,c){var v=h.getModel("stateAnimation"),d=h.isAnimationEnabled(),_=v.get("duration"),p=_>0?{duration:_,delay:v.get("delay"),easing:v.get("easing")}:null;c.eachRendered(function(g){if(g.states&&g.states.emphasis){if(an(g))return;if(g instanceof at&&hm(g),g.__dirty){var y=g.prevStates;y&&g.useStates(y)}if(d){g.stateTransition=p;var m=g.getTextContent(),w=g.getTextGuideLine();m&&(m.stateTransition=p),w&&(w.stateTransition=p)}g.__dirty&&n(g)}})}Iv=function(h){return new(function(c){F(v,c);function v(){return c!==null&&c.apply(this,arguments)||this}return v.prototype.getCoordinateSystems=function(){return h._coordSysMgr.getCoordinateSystems()},v.prototype.getComponentByElement=function(d){for(;d;){var _=d.__ecComponentInfo;if(_!=null)return h._model.getComponent(_.mainType,_.index);d=d.parent}},v.prototype.enterEmphasis=function(d,_){du(d,_),Vt(h)},v.prototype.leaveEmphasis=function(d,_){pu(d,_),Vt(h)},v.prototype.enterBlur=function(d){em(d),Vt(h)},v.prototype.leaveBlur=function(d){gd(d),Vt(h)},v.prototype.enterSelect=function(d){_d(d),Vt(h)},v.prototype.leaveSelect=function(d){yd(d),Vt(h)},v.prototype.getModel=function(){return h.getModel()},v.prototype.getViewOfComponentModel=function(d){return h.getViewOfComponentModel(d)},v.prototype.getViewOfSeriesModel=function(d){return h.getViewOfSeriesModel(d)},v}(ip))(h)},Kp=function(h){function c(v,d){for(var _=0;_<v.length;_++){var p=v[_];p[Ls]=d}}D(un,function(v,d){h._messageCenter.on(d,function(_){if(Ev[h.group]&&h[Ls]!==Mv){if(_&&_.escapeConnect)return;var p=h.makeActionFromEvent(_),g=[];D(ln,function(y){y!==h&&y.group===h.group&&g.push(y)}),c(g,Mv),D(g,function(y){y[Ls]!==lb&&y.dispatchAction(p)}),c(g,fb)}})})}}(),t}(Ce),Wl=Qp.prototype;Wl.on=Yp("on");Wl.off=Yp("off");Wl.one=function(r,t,e){var i=this;function n(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];t&&t.apply&&t.apply(this,a),i.off(r,n)}this.on.call(this,r,n,e)};var hb=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var ro={},un={},Fu=[],Nu=[],io=[],Jp={},zu={},ln={},Ev={},vb=+new Date-0,Vl="_echarts_instance_";function cb(r,t,e){var i=!(e&&e.ssr);if(i){var n=db(r);if(n)return n}var a=new Qp(r,t,e);return a.id="ec_"+vb++,ln[a.id]=a,i&&ed(r,Vl,a.id),Kp(a),ae.trigger("afterinit",a),a}function db(r){return ln[Uy(r,Vl)]}function jp(r,t){Jp[r]=t}function tg(r){nt(Nu,r)<0&&Nu.push(r)}function eg(r,t){Yl(Fu,r,t,eb)}function pb(r){Ul("afterinit",r)}function gb(r){Ul("afterupdate",r)}function Ul(r,t){ae.on(r,t)}function bi(r,t,e){Z(t)&&(e=t,t="");var i=H(r)?r.type:[r,r={event:t}][0];r.event=(r.event||i).toLowerCase(),t=r.event,!un[t]&&(xe(Dv.test(i)&&Dv.test(t)),ro[i]||(ro[i]={action:e,actionInfo:r}),un[t]=i)}function _b(r,t){np.register(r,t)}function yb(r,t){Yl(io,r,t,Wp,"layout")}function Nr(r,t){Yl(io,r,t,Vp,"visual")}var Ov=[];function Yl(r,t,e,i,n){if((Z(t)||H(t))&&(e=t,t=i),!(nt(Ov,e)>=0)){Ov.push(e);var a=Pp.wrapStageHandler(e,n);a.__prio=t,a.__raw=e,r.push(a)}}function rg(r,t){zu[r]=t}function mb(r,t,e){var i=KS("registerMap");i&&i(r,t,e)}var wb=Mw;Nr(Gl,eS);Nr(Lo,rS);Nr(Lo,iS);Nr(Gl,gS);Nr(Lo,_S);Nr(Up,qS);tg(op);eg(jS,lw);rg("default",nS);bi({type:Rr,event:Rr,update:Rr},Bt);bi({type:Ma,event:Ma,update:Ma},Bt);bi({type:en,event:en,update:en},Bt);bi({type:Aa,event:Aa,update:Aa},Bt);bi({type:rn,event:rn,update:rn},Bt);jp("light",dS);jp("dark",Ep);function Fi(r){return r==null?0:r.length||1}function kv(r){return r}var Sb=function(){function r(t,e,i,n,a,o){this._old=t,this._new=e,this._oldKeyGetter=i||kv,this._newKeyGetter=n||kv,this.context=a,this._diffModeMultiple=o==="multiple"}return r.prototype.add=function(t){return this._add=t,this},r.prototype.update=function(t){return this._update=t,this},r.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},r.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},r.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},r.prototype.remove=function(t){return this._remove=t,this},r.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},r.prototype._executeOneToOne=function(){var t=this._old,e=this._new,i={},n=new Array(t.length),a=new Array(e.length);this._initIndexMap(t,null,n,"_oldKeyGetter"),this._initIndexMap(e,i,a,"_newKeyGetter");for(var o=0;o<t.length;o++){var s=n[o],u=i[s],l=Fi(u);if(l>1){var f=u.shift();u.length===1&&(i[s]=u[0]),this._update&&this._update(f,o)}else l===1?(i[s]=null,this._update&&this._update(u,o)):this._remove&&this._remove(o)}this._performRestAdd(a,i)},r.prototype._executeMultiple=function(){var t=this._old,e=this._new,i={},n={},a=[],o=[];this._initIndexMap(t,i,a,"_oldKeyGetter"),this._initIndexMap(e,n,o,"_newKeyGetter");for(var s=0;s<a.length;s++){var u=a[s],l=i[u],f=n[u],h=Fi(l),c=Fi(f);if(h>1&&c===1)this._updateManyToOne&&this._updateManyToOne(f,l),n[u]=null;else if(h===1&&c>1)this._updateOneToMany&&this._updateOneToMany(f,l),n[u]=null;else if(h===1&&c===1)this._update&&this._update(f,l),n[u]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(f,l),n[u]=null;else if(h>1)for(var v=0;v<h;v++)this._remove&&this._remove(l[v]);else this._remove&&this._remove(l)}this._performRestAdd(o,n)},r.prototype._performRestAdd=function(t,e){for(var i=0;i<t.length;i++){var n=t[i],a=e[n],o=Fi(a);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(a[s]);else o===1&&this._add&&this._add(a);e[n]=null}},r.prototype._initIndexMap=function(t,e,i,n){for(var a=this._diffModeMultiple,o=0;o<t.length;o++){var s="_ec_"+this[n](t[o],o);if(a||(i[o]=s),!!e){var u=e[s],l=Fi(u);l===0?(e[s]=o,a&&i.push(s)):l===1?e[s]=[u,o]:u.push(o)}}},r}(),bb=function(){function r(t,e){this._encode=t,this._schema=e}return r.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},r.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},r}();function Tb(r,t){var e={},i=e.encode={},n=$(),a=[],o=[],s={};D(r.dimensions,function(c){var v=r.getDimensionInfo(c),d=v.coordDim;if(d){var _=v.coordDimIndex;Bs(i,d)[_]=c,v.isExtraCoord||(n.set(d,1),xb(v.type)&&(a[0]=c),Bs(s,d)[_]=r.getDimensionIndex(v.name)),v.defaultTooltip&&o.push(c)}Jd.each(function(p,g){var y=Bs(i,g),m=v.otherDims[g];m!=null&&m!==!1&&(y[m]=v.name)})});var u=[],l={};n.each(function(c,v){var d=i[v];l[v]=d[0],u=u.concat(d)}),e.dataDimsOnCoord=u,e.dataDimIndicesOnCoord=Y(u,function(c){return r.getDimensionInfo(c).storeDimIndex}),e.encodeFirstDimNotExtra=l;var f=i.label;f&&f.length&&(a=f.slice());var h=i.tooltip;return h&&h.length?o=h.slice():o.length||(o=a.slice()),i.defaultedLabel=a,i.defaultedTooltip=o,e.userOutput=new bb(s,t),e}function Bs(r,t){return r.hasOwnProperty(t)||(r[t]=[]),r[t]}function OC(r){return r==="category"?"ordinal":r==="time"?"time":"float"}function xb(r){return!(r==="ordinal"||r==="time")}var Ra=function(){function r(t){this.otherDims={},t!=null&&O(this,t)}return r}(),Cb=yt(),Db={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},ig=function(){function r(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return r.prototype.isDimensionOmitted=function(){return this._dimOmitted},r.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=og(this.source)))},r.prototype.getSourceDimensionIndex=function(t){return X(this._dimNameMap.get(t),-1)},r.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},r.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=lp(this.source),i=!sg(t),n="",a=[],o=0,s=0;o<t;o++){var u=void 0,l=void 0,f=void 0,h=this.dimensions[s];if(h&&h.storeDimIndex===o)u=e?h.name:null,l=h.type,f=h.ordinalMeta,s++;else{var c=this.getSourceDimension(o);c&&(u=e?c.name:null,l=c.type)}a.push({property:u,type:l,ordinalMeta:f}),e&&u!=null&&(!h||!h.isCalculationCoord)&&(n+=i?u.replace(/\`/g,"`1").replace(/\$/g,"`2"):u),n+="$",n+=Db[l]||"f",f&&(n+=f.uid),n+="$"}var v=this.source,d=[v.seriesLayoutBy,v.startIndex,n].join("$$");return{dimensions:a,hash:d}},r.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,i=0;e<this._fullDimCount;e++){var n=void 0,a=this.dimensions[i];if(a&&a.storeDimIndex===e)a.isCalculationCoord||(n=a.name),i++;else{var o=this.getSourceDimension(e);o&&(n=o.name)}t.push(n)}return t},r.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},r}();function ng(r){return r instanceof ig}function ag(r){for(var t=$(),e=0;e<(r||[]).length;e++){var i=r[e],n=H(i)?i.name:i;n!=null&&t.get(n)==null&&t.set(n,e)}return t}function og(r){var t=Cb(r);return t.dimNameMap||(t.dimNameMap=ag(r.dimensionsDefine))}function sg(r){return r>30}var Ni=H,We=Y,Mb=typeof Int32Array>"u"?Array:Int32Array,Ab="e\0\0",Bv=-1,Pb=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],Lb=["_approximateExtent"],Fv,fa,zi,Hi,Fs,ha,Ns,kC=function(){function r(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"];var i,n=!1;ng(t)?(i=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(n=!0,i=t),i=i||["x","y"];for(var a={},o=[],s={},u=!1,l={},f=0;f<i.length;f++){var h=i[f],c=B(h)?new Ra({name:h}):h instanceof Ra?h:new Ra(h),v=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=v,c.coordDimIndex=0);var d=c.otherDims=c.otherDims||{};o.push(v),a[v]=c,l[v]!=null&&(u=!0),c.createInvertedIndices&&(s[v]=[]),d.itemName===0&&(this._nameDimIdx=f),d.itemId===0&&(this._idDimIdx=f),n&&(c.storeDimIndex=f)}if(this.dimensions=o,this._dimInfos=a,this._initGetDimensionInfo(u),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted){var _=this._dimIdxToName=$();D(o,function(p){_.set(a[p].storeDimIndex,p)})}}return r.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(e==null)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var i=this._dimIdxToName.get(e);if(i!=null)return i;var n=this._schema.getSourceDimension(e);if(n)return n.name},r.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(e!=null)return e;if(t==null)return-1;var i=this._getDimInfo(t);return i?i.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},r.prototype._recognizeDimIndex=function(t){if(ht(t)||t!=null&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},r.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},r.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},r.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(i){return e.hasOwnProperty(i)?e[i]:void 0}:function(i){return e[i]}},r.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},r.prototype.mapDimension=function(t,e){var i=this._dimSummary;if(e==null)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return n?n[e]:null},r.prototype.mapDimensionsAll=function(t){var e=this._dimSummary,i=e.encode[t];return(i||[]).slice()},r.prototype.getStore=function(){return this._store},r.prototype.initData=function(t,e,i){var n=this,a;if(t instanceof Mu&&(a=t),!a){var o=this.dimensions,s=Bl(t)||Ft(t)?new fp(t,o.length):t;a=new Mu;var u=We(o,function(l){return{type:n._dimInfos[l].type,property:l}});a.initData(s,u,i)}this._store=a,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,a.count()),this._dimSummary=Tb(this,this._schema),this.userOutput=this._dimSummary.userOutput},r.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},r.prototype.appendValues=function(t,e){var i=this._store.appendValues(t,e.length),n=i.start,a=i.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var s=n;s<a;s++){var u=s-n;this._nameList[s]=e[u],o&&Ns(this,s)}},r.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,i=0;i<e.length;i++){var n=this._dimInfos[e[i]];n.ordinalMeta&&t.collectOrdinalMeta(n.storeDimIndex,n.ordinalMeta)}},r.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return this._idDimIdx==null&&t.getSource().sourceFormat!==Ze&&!t.fillStorage},r.prototype._doInit=function(t,e){if(!(t>=e)){var i=this._store,n=i.getProvider();this._updateOrdinalMeta();var a=this._nameList,o=this._idList,s=n.getSource().sourceFormat,u=s===De;if(u&&!n.pure)for(var l=[],f=t;f<e;f++){var h=n.getItem(f,l);if(!this.hasItemOption&&Ey(h)&&(this.hasItemOption=!0),h){var c=h.name;a[f]==null&&c!=null&&(a[f]=le(c,null));var v=h.id;o[f]==null&&v!=null&&(o[f]=le(v,null))}}if(this._shouldMakeIdFromName())for(var f=t;f<e;f++)Ns(this,f);Fv(this)}},r.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},r.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},r.prototype.setCalculationInfo=function(t,e){Ni(t)?O(this._calculationInfo,t):this._calculationInfo[t]=e},r.prototype.getName=function(t){var e=this.getRawIndex(t),i=this._nameList[e];return i==null&&this._nameDimIdx!=null&&(i=zi(this,this._nameDimIdx,e)),i==null&&(i=""),i},r.prototype._getCategory=function(t,e){var i=this._store.get(t,e),n=this._store.getOrdinalMeta(t);return n?n.categories[i]:i},r.prototype.getId=function(t){return fa(this,this.getRawIndex(t))},r.prototype.count=function(){return this._store.count()},r.prototype.get=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.get(n.storeDimIndex,e)},r.prototype.getByRawIndex=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.getByRawIndex(n.storeDimIndex,e)},r.prototype.getIndices=function(){return this._store.getIndices()},r.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},r.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},r.prototype.getValues=function(t,e){var i=this,n=this._store;return k(t)?n.getValues(We(t,function(a){return i._getStoreDimIndex(a)}),e):n.getValues(t)},r.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,i=0,n=e.length;i<n;i++)if(isNaN(this._store.get(e[i],t)))return!1;return!0},r.prototype.indexOfName=function(t){for(var e=0,i=this._store.count();e<i;e++)if(this.getName(e)===t)return e;return-1},r.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},r.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},r.prototype.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i[e];return n==null||isNaN(n)?Bv:n},r.prototype.indicesOfNearest=function(t,e,i){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,i)},r.prototype.each=function(t,e,i){Z(t)&&(i=e,e=t,t=[]);var n=i||this,a=We(Hi(t),this._getStoreDimIndex,this);this._store.each(a,n?st(e,n):e)},r.prototype.filterSelf=function(t,e,i){Z(t)&&(i=e,e=t,t=[]);var n=i||this,a=We(Hi(t),this._getStoreDimIndex,this);return this._store=this._store.filter(a,n?st(e,n):e),this},r.prototype.selectRange=function(t){var e=this,i={},n=lt(t);return D(n,function(a){var o=e._getStoreDimIndex(a);i[o]=t[a]}),this._store=this._store.selectRange(i),this},r.prototype.mapArray=function(t,e,i){Z(t)&&(i=e,e=t,t=[]),i=i||this;var n=[];return this.each(t,function(){n.push(e&&e.apply(this,arguments))},i),n},r.prototype.map=function(t,e,i,n){var a=i||n||this,o=We(Hi(t),this._getStoreDimIndex,this),s=ha(this);return s._store=this._store.map(o,a?st(e,a):e),s},r.prototype.modify=function(t,e,i,n){var a=i||n||this,o=We(Hi(t),this._getStoreDimIndex,this);this._store.modify(o,a?st(e,a):e)},r.prototype.downSample=function(t,e,i,n){var a=ha(this);return a._store=this._store.downSample(this._getStoreDimIndex(t),e,i,n),a},r.prototype.lttbDownSample=function(t,e){var i=ha(this);return i._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),i},r.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},r.prototype.getItemModel=function(t){var e=this.hostModel,i=this.getRawDataItem(t);return new ft(i,e,e&&e.ecModel)},r.prototype.diff=function(t){var e=this;return new Sb(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(i){return fa(t,i)},function(i){return fa(e,i)})},r.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},r.prototype.setVisual=function(t,e){this._visual=this._visual||{},Ni(t)?O(this._visual,t):this._visual[t]=e},r.prototype.getItemVisual=function(t,e){var i=this._itemVisuals[t],n=i&&i[e];return n??this.getVisual(e)},r.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},r.prototype.ensureUniqueItemVisual=function(t,e){var i=this._itemVisuals,n=i[t];n||(n=i[t]={});var a=n[e];return a==null&&(a=this.getVisual(e),k(a)?a=a.slice():Ni(a)&&(a=O({},a)),n[e]=a),a},r.prototype.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};this._itemVisuals[t]=n,Ni(e)?O(n,e):n[e]=i},r.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},r.prototype.setLayout=function(t,e){Ni(t)?O(this._layout,t):this._layout[t]=e},r.prototype.getLayout=function(t){return this._layout[t]},r.prototype.getItemLayout=function(t){return this._itemLayouts[t]},r.prototype.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?O(this._itemLayouts[t]||{},e):e},r.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},r.prototype.setItemGraphicEl=function(t,e){var i=this.hostModel&&this.hostModel.seriesIndex;Y0(i,this.dataType,t,e),this._graphicEls[t]=e},r.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},r.prototype.eachItemGraphicEl=function(t,e){D(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},r.prototype.cloneShallow=function(t){return t||(t=new r(this._schema?this._schema:We(this.dimensions,this._getDimInfo,this),this.hostModel)),Fs(t,this),t._store=this._store,t},r.prototype.wrapMethod=function(t,e){var i=this[t];Z(i)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var n=i.apply(this,arguments);return e.apply(this,[n].concat(Ju(arguments)))})},r.internalField=function(){Fv=function(t){var e=t._invertedIndicesMap;D(e,function(i,n){var a=t._dimInfos[n],o=a.ordinalMeta,s=t._store;if(o){i=e[n]=new Mb(o.categories.length);for(var u=0;u<i.length;u++)i[u]=Bv;for(var u=0;u<s.count();u++)i[s.get(a.storeDimIndex,u)]=u}})},zi=function(t,e,i){return le(t._getCategory(e,i),null)},fa=function(t,e){var i=t._idList[e];return i==null&&t._idDimIdx!=null&&(i=zi(t,t._idDimIdx,e)),i==null&&(i=Ab+e),i},Hi=function(t){return k(t)||(t=t!=null?[t]:[]),t},ha=function(t){var e=new r(t._schema?t._schema:We(t.dimensions,t._getDimInfo,t),t.hostModel);return Fs(e,t),e},Fs=function(t,e){D(Pb.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,D(Lb,function(i){t[i]=K(e[i])}),t._calculationInfo=O({},e._calculationInfo)},Ns=function(t,e){var i=t._nameList,n=t._idList,a=t._nameDimIdx,o=t._idDimIdx,s=i[e],u=n[e];if(s==null&&a!=null&&(i[e]=s=zi(t,a,e)),u==null&&o!=null&&(n[e]=u=zi(t,o,e)),u==null&&s!=null){var l=t._nameRepeatCount,f=l[s]=(l[s]||0)+1;u=s,f>1&&(u+="__ec__"+f),n[e]=u}}}(),r}();function BC(r,t){Bl(r)||(r=sp(r)),t=t||{};var e=t.coordDimensions||[],i=t.dimensionsDefine||r.dimensionsDefine||[],n=$(),a=[],o=Rb(r,e,i,t.dimensionsCount),s=t.canOmitUnusedDimensions&&sg(o),u=i===r.dimensionsDefine,l=u?og(r):ag(i),f=t.encodeDefine;!f&&t.encodeDefaulter&&(f=t.encodeDefaulter(r,o));for(var h=$(f),c=new pp(o),v=0;v<c.length;v++)c[v]=-1;function d(M){var A=c[M];if(A<0){var T=i[M],P=H(T)?T:{name:T},L=new Ra,I=P.name;I!=null&&l.get(I)!=null&&(L.name=L.displayName=I),P.type!=null&&(L.type=P.type),P.displayName!=null&&(L.displayName=P.displayName);var R=a.length;return c[M]=R,L.storeDimIndex=M,a.push(L),L}return a[A]}if(!s)for(var v=0;v<o;v++)d(v);h.each(function(M,A){var T=Tt(M).slice();if(T.length===1&&!B(T[0])&&T[0]<0){h.set(A,!1);return}var P=h.set(A,[]);D(T,function(L,I){var R=B(L)?l.get(L):L;R!=null&&R<o&&(P[I]=R,p(d(R),A,I))})});var _=0;D(e,function(M){var A,T,P,L;if(B(M))A=M,L={};else{L=M,A=L.name;var I=L.ordinalMeta;L.ordinalMeta=null,L=O({},L),L.ordinalMeta=I,T=L.dimsDef,P=L.otherDims,L.name=L.coordDim=L.coordDimIndex=L.dimsDef=L.otherDims=null}var R=h.get(A);if(R!==!1){if(R=Tt(R),!R.length)for(var E=0;E<(T&&T.length||1);E++){for(;_<o&&d(_).coordDim!=null;)_++;_<o&&R.push(_++)}D(R,function(U,N){var z=d(U);if(u&&L.type!=null&&(z.type=L.type),p(ot(z,L),A,N),z.name==null&&T){var G=T[N];!H(G)&&(G={name:G}),z.name=z.displayName=G.name,z.defaultTooltip=G.defaultTooltip}P&&ot(z.otherDims,P)})}});function p(M,A,T){Jd.get(A)!=null?M.otherDims[A]=T:(M.coordDim=A,M.coordDimIndex=T,n.set(A,!0))}var g=t.generateCoord,y=t.generateCoordCount,m=y!=null;y=g?y||1:0;var w=g||"value";function b(M){M.name==null&&(M.name=M.coordDim)}if(s)D(a,function(M){b(M)}),a.sort(function(M,A){return M.storeDimIndex-A.storeDimIndex});else for(var S=0;S<o;S++){var x=d(S),C=x.coordDim;C==null&&(x.coordDim=Eb(w,n,m),x.coordDimIndex=0,(!g||y<=0)&&(x.isExtraCoord=!0),y--),b(x),x.type==null&&(ep(r,S)===pt.Must||x.isExtraCoord&&(x.otherDims.itemName!=null||x.otherDims.seriesName!=null))&&(x.type="ordinal")}return Ib(a),new ig({source:r,dimensions:a,fullDimensionCount:o,dimensionOmitted:s})}function Ib(r){for(var t=$(),e=0;e<r.length;e++){var i=r[e],n=i.name,a=t.get(n)||0;a>0&&(i.name=n+(a-1)),a++,t.set(n,a)}}function Rb(r,t,e,i){var n=Math.max(r.dimensionsDetectedCount||1,t.length,e.length,i||0);return D(t,function(a){var o;H(a)&&(o=a.dimsDef)&&(n=Math.max(n,o.length))}),n}function Eb(r,t,e){if(e||t.hasKey(r)){for(var i=0;t.hasKey(r+i);)i++;r+=i}return t.set(r,!0),r}function FC(r,t,e){e=e||{};var i=e.byIndex,n=e.stackedCoordDimension,a,o,s;Ob(t)?a=t:(o=t.schema,a=o.dimensions,s=t.store);var u=!!(r&&r.get("stack")),l,f,h,c;if(D(a,function(y,m){B(y)&&(a[m]=y={name:y}),u&&!y.isExtraCoord&&(!i&&!l&&y.ordinalMeta&&(l=y),!f&&y.type!=="ordinal"&&y.type!=="time"&&(!n||n===y.coordDim)&&(f=y))}),f&&!i&&!l&&(i=!0),f){h="__\0ecstackresult_"+r.id,c="__\0ecstackedover_"+r.id,l&&(l.createInvertedIndices=!0);var v=f.coordDim,d=f.type,_=0;D(a,function(y){y.coordDim===v&&_++});var p={name:h,coordDim:v,coordDimIndex:_,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length},g={name:c,coordDim:c,coordDimIndex:_+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length+1};o?(s&&(p.storeDimIndex=s.ensureCalculationDimension(c,d),g.storeDimIndex=s.ensureCalculationDimension(h,d)),o.appendCalculationDimension(p),o.appendCalculationDimension(g)):(a.push(p),a.push(g))}return{stackedDimension:f&&f.name,stackedByDimension:l&&l.name,isStackedByIndex:i,stackedOverDimension:c,stackResultDimension:h}}function Ob(r){return!ng(r.schema)}function ug(r,t){return!!t&&t===r.getCalculationInfo("stackedDimension")}function kb(r,t){return ug(r,t)?r.getCalculationInfo("stackResultDimension"):t}var Ae=function(){function r(t){this._setting=t||{},this._extent=[1/0,-1/0]}return r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},r.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r}();ho(Ae);var Bb=0,Nv=function(){function r(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++Bb}return r.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&Y(i,Fb);return new r({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})},r.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},r.prototype.parseAndCollect=function(t){var e,i=this._needCollect;if(!B(t)&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=this._getOrCreateMap();return e=n.get(t),e==null&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=NaN),e},r.prototype._getOrCreateMap=function(){return this._map||(this._map=$(this.categories))},r}();function Fb(r){return H(r)&&r.value!=null?r.value:r+""}function NC(r){return r.type==="interval"||r.type==="log"}function Nb(r,t,e,i){var n={},a=r[1]-r[0],o=n.interval=Zc(a/t);e!=null&&o<e&&(o=n.interval=e),i!=null&&o>i&&(o=n.interval=i);var s=n.intervalPrecision=lg(o),u=n.niceTickExtent=[ue(Math.ceil(r[0]/o)*o,s),ue(Math.floor(r[1]/o)*o,s)];return zb(u,r),n}function zC(r){var t=Math.pow(10,al(r)),e=r/t;return e?e===2?e=3:e===3?e=5:e*=2:e=1,ue(e*t)}function lg(r){return Re(r)+2}function zv(r,t,e){r[t]=Math.max(Math.min(r[t],e[1]),e[0])}function zb(r,t){!isFinite(r[0])&&(r[0]=t[0]),!isFinite(r[1])&&(r[1]=t[1]),zv(r,0,t),zv(r,1,t),r[0]>r[1]&&(r[0]=r[1])}function Io(r,t){return r>=t[0]&&r<=t[1]}function Ro(r,t){return t[1]===t[0]?.5:(r-t[0])/(t[1]-t[0])}function Eo(r,t){return r*(t[1]-t[0])+t[0]}var Xl=function(r){F(t,r);function t(e){var i=r.call(this,e)||this;i.type="ordinal";var n=i.getSetting("ordinalMeta");return n||(n=new Nv({})),k(n)&&(n=new Nv({categories:Y(n,function(a){return H(a)?a.value:a})})),i._ordinalMeta=n,i._extent=i.getSetting("extent")||[0,n.categories.length-1],i}return t.prototype.parse=function(e){return e==null?NaN:B(e)?this._ordinalMeta.getOrdinal(e):Math.round(e)},t.prototype.contain=function(e){return e=this.parse(e),Io(e,this._extent)&&this._ordinalMeta.categories[e]!=null},t.prototype.normalize=function(e){return e=this._getTickNumber(this.parse(e)),Ro(e,this._extent)},t.prototype.scale=function(e){return e=Math.round(Eo(e,this._extent)),this.getRawOrdinalNumber(e)},t.prototype.getTicks=function(){for(var e=[],i=this._extent,n=i[0];n<=i[1];)e.push({value:n}),n++;return e},t.prototype.getMinorTicks=function(e){},t.prototype.setSortInfo=function(e){if(e==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var i=e.ordinalNumbers,n=this._ordinalNumbersByTick=[],a=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,u=Math.min(s,i.length);o<u;++o){var l=i[o];n[o]=l,a[l]=o}for(var f=0;o<s;++o){for(;a[f]!=null;)f++;n.push(f),a[f]=o}},t.prototype._getTickNumber=function(e){var i=this._ticksByOrdinalNumber;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getRawOrdinalNumber=function(e){var i=this._ordinalNumbersByTick;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getLabel=function(e){if(!this.isBlank()){var i=this.getRawOrdinalNumber(e.value),n=this._ordinalMeta.categories[i];return n==null?"":n+""}},t.prototype.count=function(){return this._extent[1]-this._extent[0]+1},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.isInExtentRange=function(e){return e=this._getTickNumber(e),this._extent[0]<=e&&this._extent[1]>=e},t.prototype.getOrdinalMeta=function(){return this._ordinalMeta},t.prototype.calcNiceTicks=function(){},t.prototype.calcNiceExtent=function(){},t.type="ordinal",t}(Ae);Ae.registerClass(Xl);var Tr=ue,Rn=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return Io(e,this._extent)},t.prototype.normalize=function(e){return Ro(e,this._extent)},t.prototype.scale=function(e){return Eo(e,this._extent)},t.prototype.setExtent=function(e,i){var n=this._extent;isNaN(e)||(n[0]=parseFloat(e)),isNaN(i)||(n[1]=parseFloat(i))},t.prototype.unionExtent=function(e){var i=this._extent;e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1]),this.setExtent(i[0],i[1])},t.prototype.getInterval=function(){return this._interval},t.prototype.setInterval=function(e){this._interval=e,this._niceExtent=this._extent.slice(),this._intervalPrecision=lg(e)},t.prototype.getTicks=function(e){var i=this._interval,n=this._extent,a=this._niceExtent,o=this._intervalPrecision,s=[];if(!i)return s;var u=1e4;n[0]<a[0]&&(e?s.push({value:Tr(a[0]-i,o)}):s.push({value:n[0]}));for(var l=a[0];l<=a[1]&&(s.push({value:l}),l=Tr(l+i,o),l!==s[s.length-1].value);)if(s.length>u)return[];var f=s.length?s[s.length-1].value:a[1];return n[1]>f&&(e?s.push({value:Tr(f+i,o)}):s.push({value:n[1]})),s},t.prototype.getMinorTicks=function(e){for(var i=this.getTicks(!0),n=[],a=this.getExtent(),o=1;o<i.length;o++){for(var s=i[o],u=i[o-1],l=0,f=[],h=s.value-u.value,c=h/e;l<e-1;){var v=Tr(u.value+(l+1)*c);v>a[0]&&v<a[1]&&f.push(v),l++}n.push(f)}return n},t.prototype.getLabel=function(e,i){if(e==null)return"";var n=i&&i.precision;n==null?n=Re(e.value)||0:n==="auto"&&(n=this._intervalPrecision);var a=Tr(e.value,n,!0);return qd(a)},t.prototype.calcNiceTicks=function(e,i,n){e=e||5;var a=this._extent,o=a[1]-a[0];if(isFinite(o)){o<0&&(o=-o,a.reverse());var s=Nb(a,e,i,n);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1])if(i[0]!==0){var n=Math.abs(i[0]);e.fixMax||(i[1]+=n/2),i[0]-=n/2}else i[1]=1;var a=i[1]-i[0];isFinite(a)||(i[0]=0,i[1]=1),this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval);var o=this._interval;e.fixMin||(i[0]=Tr(Math.floor(i[0]/o)*o)),e.fixMax||(i[1]=Tr(Math.ceil(i[1]/o)*o))},t.prototype.setNiceExtent=function(e,i){this._niceExtent=[e,i]},t.type="interval",t}(Ae);Ae.registerClass(Rn);var fg=typeof Float32Array<"u",Hb=fg?Float32Array:Array;function zs(r){return k(r)?fg?new Float32Array(r):r:new Hb(r)}var Gb="__ec_stack_";function $l(r){return r.get("stack")||Gb+r.seriesIndex}function ql(r){return r.dim+r.index}function hg(r,t){var e=[];return t.eachSeriesByType(r,function(i){cg(i)&&e.push(i)}),e}function Wb(r){var t={};D(r,function(u){var l=u.coordinateSystem,f=l.getBaseAxis();if(!(f.type!=="time"&&f.type!=="value"))for(var h=u.getData(),c=f.dim+"_"+f.index,v=h.getDimensionIndex(h.mapDimension(f.dim)),d=h.getStore(),_=0,p=d.count();_<p;++_){var g=d.get(v,_);t[c]?t[c].push(g):t[c]=[g]}});var e={};for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];if(n){n.sort(function(u,l){return u-l});for(var a=null,o=1;o<n.length;++o){var s=n[o]-n[o-1];s>0&&(a=a===null?s:Math.min(a,s))}e[i]=a}}return e}function vg(r){var t=Wb(r),e=[];return D(r,function(i){var n=i.coordinateSystem,a=n.getBaseAxis(),o=a.getExtent(),s;if(a.type==="category")s=a.getBandWidth();else if(a.type==="value"||a.type==="time"){var u=a.dim+"_"+a.index,l=t[u],f=Math.abs(o[1]-o[0]),h=a.scale.getExtent(),c=Math.abs(h[1]-h[0]);s=l?f/c*l:f}else{var v=i.getData();s=Math.abs(o[1]-o[0])/v.count()}var d=At(i.get("barWidth"),s),_=At(i.get("barMaxWidth"),s),p=At(i.get("barMinWidth")||(dg(i)?.5:1),s),g=i.get("barGap"),y=i.get("barCategoryGap");e.push({bandWidth:s,barWidth:d,barMaxWidth:_,barMinWidth:p,barGap:g,barCategoryGap:y,axisKey:ql(a),stackId:$l(i)})}),Vb(e)}function Vb(r){var t={};D(r,function(i,n){var a=i.axisKey,o=i.bandWidth,s=t[a]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},u=s.stacks;t[a]=s;var l=i.stackId;u[l]||s.autoWidthCount++,u[l]=u[l]||{width:0,maxWidth:0};var f=i.barWidth;f&&!u[l].width&&(u[l].width=f,f=Math.min(s.remainedWidth,f),s.remainedWidth-=f);var h=i.barMaxWidth;h&&(u[l].maxWidth=h);var c=i.barMinWidth;c&&(u[l].minWidth=c);var v=i.barGap;v!=null&&(s.gap=v);var d=i.barCategoryGap;d!=null&&(s.categoryGap=d)});var e={};return D(t,function(i,n){e[n]={};var a=i.stacks,o=i.bandWidth,s=i.categoryGap;if(s==null){var u=lt(a).length;s=Math.max(35-u*4,15)+"%"}var l=At(s,o),f=At(i.gap,1),h=i.remainedWidth,c=i.autoWidthCount,v=(h-l)/(c+(c-1)*f);v=Math.max(v,0),D(a,function(g){var y=g.maxWidth,m=g.minWidth;if(g.width){var w=g.width;y&&(w=Math.min(w,y)),m&&(w=Math.max(w,m)),g.width=w,h-=w+f*w,c--}else{var w=v;y&&y<w&&(w=Math.min(y,h)),m&&m>w&&(w=m),w!==v&&(g.width=w,h-=w+f*w,c--)}}),v=(h-l)/(c+(c-1)*f),v=Math.max(v,0);var d=0,_;D(a,function(g,y){g.width||(g.width=v),_=g,d+=g.width*(1+f)}),_&&(d-=_.width*f);var p=-d/2;D(a,function(g,y){e[n][y]=e[n][y]||{bandWidth:o,offset:p,width:g.width},p+=g.width*(1+f)})}),e}function Ub(r,t,e){if(r&&t){var i=r[ql(t)];return i!=null&&e!=null?i[$l(e)]:i}}function HC(r,t){var e=hg(r,t),i=vg(e);D(e,function(n){var a=n.getData(),o=n.coordinateSystem,s=o.getBaseAxis(),u=$l(n),l=i[ql(s)][u],f=l.offset,h=l.width;a.setLayout({bandWidth:l.bandWidth,offset:f,size:h})})}function GC(r){return{seriesType:r,plan:xp(),reset:function(t){if(cg(t)){var e=t.getData(),i=t.coordinateSystem,n=i.getBaseAxis(),a=i.getOtherAxis(n),o=e.getDimensionIndex(e.mapDimension(a.dim)),s=e.getDimensionIndex(e.mapDimension(n.dim)),u=t.get("showBackground",!0),l=e.mapDimension(a.dim),f=e.getCalculationInfo("stackResultDimension"),h=ug(e,l)&&!!e.getCalculationInfo("stackedOnSeries"),c=a.isHorizontal(),v=Yb(n,a),d=dg(t),_=t.get("barMinHeight")||0,p=f&&e.getDimensionIndex(f),g=e.getLayout("size"),y=e.getLayout("offset");return{progress:function(m,w){for(var b=m.count,S=d&&zs(b*3),x=d&&u&&zs(b*3),C=d&&zs(b),M=i.master.getRect(),A=c?M.width:M.height,T,P=w.getStore(),L=0;(T=m.next())!=null;){var I=P.get(h?p:o,T),R=P.get(s,T),E=v,U=void 0;h&&(U=+I-P.get(o,T));var N=void 0,z=void 0,G=void 0,rt=void 0;if(c){var j=i.dataToPoint([I,R]);if(h){var ct=i.dataToPoint([U,R]);E=ct[0]}N=E,z=j[1]+y,G=j[0]-E,rt=g,Math.abs(G)<_&&(G=(G<0?-1:1)*_)}else{var j=i.dataToPoint([R,I]);if(h){var ct=i.dataToPoint([R,U]);E=ct[1]}N=j[0]+y,z=E,G=g,rt=j[1]-E,Math.abs(rt)<_&&(rt=(rt<=0?-1:1)*_)}d?(S[L]=N,S[L+1]=z,S[L+2]=c?G:rt,x&&(x[L]=c?M.x:N,x[L+1]=c?z:M.y,x[L+2]=A),C[T]=T):w.setItemLayout(T,{x:N,y:z,width:G,height:rt}),L+=3}d&&w.setLayout({largePoints:S,largeDataIndices:C,largeBackgroundPoints:x,valueAxisHorizontal:c})}}}}}}function cg(r){return r.coordinateSystem&&r.coordinateSystem.type==="cartesian2d"}function dg(r){return r.pipelineContext&&r.pipelineContext.large}function Yb(r,t){return t.toGlobalCoord(t.dataToCoord(t.type==="log"?1:0))}var Xb=function(r,t,e,i){for(;e<i;){var n=e+i>>>1;r[n][1]<t?e=n+1:i=n}return e},pg=function(r){F(t,r);function t(e){var i=r.call(this,e)||this;return i.type="time",i}return t.prototype.getLabel=function(e){var i=this.getSetting("useUTC");return bo(e.value,Ah[L1(hi(this._minLevelUnit))]||Ah.second,i,this.getSetting("locale"))},t.prototype.getFormattedLabel=function(e,i,n){var a=this.getSetting("useUTC"),o=this.getSetting("locale");return I1(e,i,n,o,a)},t.prototype.getTicks=function(){var e=this._interval,i=this._extent,n=[];if(!e)return n;n.push({value:i[0],level:0});var a=this.getSetting("useUTC"),o=jb(this._minLevelUnit,this._approxInterval,a,i);return n=n.concat(o),n.push({value:i[1],level:0}),n},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1]&&(i[0]-=jt,i[1]+=jt),i[1]===-1/0&&i[0]===1/0){var n=new Date;i[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),i[0]=i[1]-jt}this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval)},t.prototype.calcNiceTicks=function(e,i,n){e=e||10;var a=this._extent,o=a[1]-a[0];this._approxInterval=o/e,i!=null&&this._approxInterval<i&&(this._approxInterval=i),n!=null&&this._approxInterval>n&&(this._approxInterval=n);var s=va.length,u=Math.min(Xb(va,this._approxInterval,0,s),s-1);this._interval=va[u][1],this._minLevelUnit=va[Math.max(u-1,0)][0]},t.prototype.parse=function(e){return ht(e)?e:+Oe(e)},t.prototype.contain=function(e){return Io(this.parse(e),this._extent)},t.prototype.normalize=function(e){return Ro(this.parse(e),this._extent)},t.prototype.scale=function(e){return Eo(e,this._extent)},t.type="time",t}(Rn),va=[["second",Al],["minute",Pl],["hour",on],["quarter-day",on*6],["half-day",on*12],["day",jt*1.2],["half-week",jt*3.5],["week",jt*7],["month",jt*31],["quarter",jt*95],["half-year",Mh/2],["year",Mh]];function $b(r,t,e,i){var n=Oe(t),a=Oe(e),o=function(d){return Ph(n,d,i)===Ph(a,d,i)},s=function(){return o("year")},u=function(){return s()&&o("month")},l=function(){return u()&&o("day")},f=function(){return l()&&o("hour")},h=function(){return f()&&o("minute")},c=function(){return h()&&o("second")},v=function(){return c()&&o("millisecond")};switch(r){case"year":return s();case"month":return u();case"day":return l();case"hour":return f();case"minute":return h();case"second":return c();case"millisecond":return v()}}function qb(r,t){return r/=jt,r>16?16:r>7.5?7:r>3.5?4:r>1.5?2:1}function Zb(r){var t=30*jt;return r/=t,r>6?6:r>3?3:r>2?2:1}function Kb(r){return r/=on,r>12?12:r>6?6:r>3.5?4:r>2?2:1}function Hv(r,t){return r/=t?Pl:Al,r>30?30:r>20?20:r>15?15:r>10?10:r>5?5:r>2?2:1}function Qb(r){return Zc(r)}function Jb(r,t,e){var i=new Date(r);switch(hi(t)){case"year":case"month":i[Wd(e)](0);case"day":i[Vd(e)](1);case"hour":i[Ud(e)](0);case"minute":i[Yd(e)](0);case"second":i[Xd(e)](0),i[$d(e)](0)}return i.getTime()}function jb(r,t,e,i){var n=1e4,a=Hd,o=0;function s(A,T,P,L,I,R,E){for(var U=new Date(T),N=T,z=U[L]();N<P&&N<=i[1];)E.push({value:N}),z+=A,U[I](z),N=U.getTime();E.push({value:N,notAdd:!0})}function u(A,T,P){var L=[],I=!T.length;if(!$b(hi(A),i[0],i[1],e)){I&&(T=[{value:Jb(new Date(i[0]),A,e)},{value:i[1]}]);for(var R=0;R<T.length-1;R++){var E=T[R].value,U=T[R+1].value;if(E!==U){var N=void 0,z=void 0,G=void 0,rt=!1;switch(A){case"year":N=Math.max(1,Math.round(t/jt/365)),z=Ll(e),G=R1(e);break;case"half-year":case"quarter":case"month":N=Zb(t),z=vi(e),G=Wd(e);break;case"week":case"half-week":case"day":N=qb(t),z=To(e),G=Vd(e),rt=!0;break;case"half-day":case"quarter-day":case"hour":N=Kb(t),z=_n(e),G=Ud(e);break;case"minute":N=Hv(t,!0),z=xo(e),G=Yd(e);break;case"second":N=Hv(t,!1),z=Co(e),G=Xd(e);break;case"millisecond":N=Qb(t),z=Do(e),G=$d(e);break}s(N,E,U,z,G,rt,L),A==="year"&&P.length>1&&R===0&&P.unshift({value:P[0].value-N})}}for(var R=0;R<L.length;R++)P.push(L[R]);return L}}for(var l=[],f=[],h=0,c=0,v=0;v<a.length&&o++<n;++v){var d=hi(a[v]);if(P1(a[v])){u(a[v],l[l.length-1]||[],f);var _=a[v+1]?hi(a[v+1]):null;if(d!==_){if(f.length){c=h,f.sort(function(A,T){return A.value-T.value});for(var p=[],g=0;g<f.length;++g){var y=f[g].value;(g===0||f[g-1].value!==y)&&(p.push(f[g]),y>=i[0]&&y<=i[1]&&h++)}var m=(i[1]-i[0])/t;if(h>m*1.5&&c>m/1.5||(l.push(p),h>m||r===a[v]))break}f=[]}}}for(var w=Et(Y(l,function(A){return Et(A,function(T){return T.value>=i[0]&&T.value<=i[1]&&!T.notAdd})}),function(A){return A.length>0}),b=[],S=w.length-1,v=0;v<w.length;++v)for(var x=w[v],C=0;C<x.length;++C)b.push({value:x[C].value,level:S-v});b.sort(function(A,T){return A.value-T.value});for(var M=[],v=0;v<b.length;++v)(v===0||b[v].value!==b[v-1].value)&&M.push(b[v]);return M}Ae.registerClass(pg);var Gv=Ae.prototype,fn=Rn.prototype,tT=ue,eT=Math.floor,rT=Math.ceil,ca=Math.pow,re=Math.log,Zl=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new Rn,e._interval=0,e}return t.prototype.getTicks=function(e){var i=this._originalScale,n=this._extent,a=i.getExtent(),o=fn.getTicks.call(this,e);return Y(o,function(s){var u=s.value,l=ue(ca(this.base,u));return l=u===n[0]&&this._fixMin?da(l,a[0]):l,l=u===n[1]&&this._fixMax?da(l,a[1]):l,{value:l}},this)},t.prototype.setExtent=function(e,i){var n=re(this.base);e=re(Math.max(0,e))/n,i=re(Math.max(0,i))/n,fn.setExtent.call(this,e,i)},t.prototype.getExtent=function(){var e=this.base,i=Gv.getExtent.call(this);i[0]=ca(e,i[0]),i[1]=ca(e,i[1]);var n=this._originalScale,a=n.getExtent();return this._fixMin&&(i[0]=da(i[0],a[0])),this._fixMax&&(i[1]=da(i[1],a[1])),i},t.prototype.unionExtent=function(e){this._originalScale.unionExtent(e);var i=this.base;e[0]=re(e[0])/re(i),e[1]=re(e[1])/re(i),Gv.unionExtent.call(this,e)},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.calcNiceTicks=function(e){e=e||10;var i=this._extent,n=i[1]-i[0];if(!(n===1/0||n<=0)){var a=Ay(n),o=e/n*a;for(o<=.5&&(a*=10);!isNaN(a)&&Math.abs(a)<1&&Math.abs(a)>0;)a*=10;var s=[ue(rT(i[0]/a)*a),ue(eT(i[1]/a)*a)];this._interval=a,this._niceExtent=s}},t.prototype.calcNiceExtent=function(e){fn.calcNiceExtent.call(this,e),this._fixMin=e.fixMin,this._fixMax=e.fixMax},t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return e=re(e)/re(this.base),Io(e,this._extent)},t.prototype.normalize=function(e){return e=re(e)/re(this.base),Ro(e,this._extent)},t.prototype.scale=function(e){return e=Eo(e,this._extent),ca(this.base,e)},t.type="log",t}(Ae),gg=Zl.prototype;gg.getMinorTicks=fn.getMinorTicks;gg.getLabel=fn.getLabel;function da(r,t){return tT(r,Re(t))}Ae.registerClass(Zl);var iT=function(){function r(t,e,i){this._prepareParams(t,e,i)}return r.prototype._prepareParams=function(t,e,i){i[1]<i[0]&&(i=[NaN,NaN]),this._dataMin=i[0],this._dataMax=i[1];var n=this._isOrdinal=t.type==="ordinal";this._needCrossZero=t.type==="interval"&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=this._modelMinRaw=e.get("min",!0);Z(a)?this._modelMinNum=pa(t,a({min:i[0],max:i[1]})):a!=="dataMin"&&(this._modelMinNum=pa(t,a));var o=this._modelMaxRaw=e.get("max",!0);if(Z(o)?this._modelMaxNum=pa(t,o({min:i[0],max:i[1]})):o!=="dataMax"&&(this._modelMaxNum=pa(t,o)),n)this._axisDataLen=e.getCategories().length;else{var s=e.get("boundaryGap"),u=k(s)?s:[s||0,s||0];typeof u[0]=="boolean"||typeof u[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Qe(u[0],1),Qe(u[1],1)]}},r.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,i=this._dataMax,n=this._axisDataLen,a=this._boundaryGapInner,o=t?null:i-e||Math.abs(e),s=this._modelMinRaw==="dataMin"?e:this._modelMinNum,u=this._modelMaxRaw==="dataMax"?i:this._modelMaxNum,l=s!=null,f=u!=null;s==null&&(s=t?n?0:NaN:e-a[0]*o),u==null&&(u=t?n?n-1:NaN:i+a[1]*o),(s==null||!isFinite(s))&&(s=NaN),(u==null||!isFinite(u))&&(u=NaN);var h=ka(s)||ka(u)||t&&!n;this._needCrossZero&&(s>0&&u>0&&!l&&(s=0),s<0&&u<0&&!f&&(u=0));var c=this._determinedMin,v=this._determinedMax;return c!=null&&(s=c,l=!0),v!=null&&(u=v,f=!0),{min:s,max:u,minFixed:l,maxFixed:f,isBlank:h}},r.prototype.modifyDataMinMax=function(t,e){this[aT[t]]=e},r.prototype.setDeterminedMinMax=function(t,e){var i=nT[t];this[i]=e},r.prototype.freeze=function(){this.frozen=!0},r}(),nT={min:"_determinedMin",max:"_determinedMax"},aT={min:"_dataMin",max:"_dataMax"};function oT(r,t,e){var i=r.rawExtentInfo;return i||(i=new iT(r,t,e),r.rawExtentInfo=i,i)}function pa(r,t){return t==null?null:ka(t)?NaN:r.parse(t)}function sT(r,t){var e=r.type,i=oT(r,t,r.getExtent()).calculate();r.setBlank(i.isBlank);var n=i.min,a=i.max,o=t.ecModel;if(o&&e==="time"){var s=hg("bar",o),u=!1;if(D(s,function(h){u=u||h.getBaseAxis()===t.axis}),u){var l=vg(s),f=uT(n,a,t,l);n=f.min,a=f.max}}return{extent:[n,a],fixMin:i.minFixed,fixMax:i.maxFixed}}function uT(r,t,e,i){var n=e.axis.getExtent(),a=n[1]-n[0],o=Ub(i,e.axis);if(o===void 0)return{min:r,max:t};var s=1/0;D(o,function(v){s=Math.min(v.offset,s)});var u=-1/0;D(o,function(v){u=Math.max(v.offset+v.width,u)}),s=Math.abs(s),u=Math.abs(u);var l=s+u,f=t-r,h=1-(s+u)/a,c=f/h-f;return t+=c*(u/l),r-=c*(s/l),{min:r,max:t}}function WC(r,t){var e=t,i=sT(r,e),n=i.extent,a=e.get("splitNumber");r instanceof Zl&&(r.base=e.get("logBase"));var o=r.type,s=e.get("interval"),u=o==="interval"||o==="time";r.setExtent(n[0],n[1]),r.calcNiceExtent({splitNumber:a,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:u?e.get("minInterval"):null,maxInterval:u?e.get("maxInterval"):null}),s!=null&&r.setInterval&&r.setInterval(s)}function VC(r,t){if(t=t||r.get("type"),t)switch(t){case"category":return new Xl({ordinalMeta:r.getOrdinalMeta?r.getOrdinalMeta():r.getCategories(),extent:[1/0,-1/0]});case"time":return new pg({locale:r.ecModel.getLocaleModel(),useUTC:r.ecModel.get("useUTC")});default:return new(Ae.getClass(t)||Rn)}}function UC(r){var t=r.scale.getExtent(),e=t[0],i=t[1];return!(e>0&&i>0||e<0&&i<0)}function lT(r){var t=r.getLabelModel().get("formatter"),e=r.type==="category"?r.scale.getExtent()[0]:null;return r.scale.type==="time"?function(i){return function(n,a){return r.scale.getFormattedLabel(n,a,i)}}(t):B(t)?function(i){return function(n){var a=r.scale.getLabel(n),o=i.replace("{value}",a??"");return o}}(t):Z(t)?function(i){return function(n,a){return e!=null&&(a=n.value-e),i(Kl(r,n),a,n.level!=null?{level:n.level}:null)}}(t):function(i){return r.scale.getLabel(i)}}function Kl(r,t){return r.type==="category"?r.scale.getLabel(t):t.value}function YC(r){var t=r.model,e=r.scale;if(!(!t.get(["axisLabel","show"])||e.isBlank())){var i,n,a=e.getExtent();e instanceof Xl?n=e.count():(i=e.getTicks(),n=i.length);var o=r.getLabelModel(),s=lT(r),u,l=1;n>40&&(l=Math.ceil(n/40));for(var f=0;f<n;f+=l){var h=i?i[f]:{value:a[0]+f},c=s(h,f),v=o.getTextRect(c),d=fT(v,o.get("rotate")||0);u?u.union(d):u=d}return u}}function fT(r,t){var e=t*Math.PI/180,i=r.width,n=r.height,a=i*Math.abs(Math.cos(e))+Math.abs(n*Math.sin(e)),o=i*Math.abs(Math.sin(e))+Math.abs(n*Math.cos(e)),s=new J(r.x,r.y,a,o);return s}function hT(r){var t=r.get("interval");return t??"auto"}function vT(r){return r.type==="category"&&hT(r.getLabelModel())===0}function cT(r,t){var e={};return D(r.mapDimensionsAll(t),function(i){e[kb(r,i)]=!0}),lt(e)}function XC(r,t,e){t&&D(cT(t,e),function(i){var n=t.getApproximateExtent(i);n[0]<r[0]&&(r[0]=n[0]),n[1]>r[1]&&(r[1]=n[1])})}var Wv=[],dT={registerPreprocessor:tg,registerProcessor:eg,registerPostInit:pb,registerPostUpdate:gb,registerUpdateLifecycle:Ul,registerAction:bi,registerCoordinateSystem:_b,registerLayout:yb,registerVisual:Nr,registerTransform:wb,registerLoading:rg,registerMap:mb,registerImpl:ZS,PRIORITY:ub,ComponentModel:tt,ComponentView:he,SeriesModel:Br,ChartView:Er,registerComponentModel:function(r){tt.registerClass(r)},registerComponentView:function(r){he.registerClass(r)},registerSeriesModel:function(r){Br.registerClass(r)},registerChartView:function(r){Er.registerClass(r)},registerSubTypeDefaulter:function(r,t){tt.registerSubTypeDefaulter(r,t)},registerPainter:function(r,t){Ty(r,t)}};function xn(r){if(k(r)){D(r,function(t){xn(t)});return}nt(Wv,r)>=0||(Wv.push(r),Z(r)&&(r={install:r}),r.install(dT))}function pT(r){for(var t=[],e=0;e<r.length;e++){var i=r[e];if(!i.defaultAttr.ignore){var n=i.label,a=n.getComputedTransform(),o=n.getBoundingRect(),s=!a||a[1]<1e-5&&a[2]<1e-5,u=n.style.margin||0,l=o.clone();l.applyTransform(a),l.x-=u/2,l.y-=u/2,l.width+=u,l.height+=u;var f=s?new Xa(o,a):null;t.push({label:n,labelLine:i.labelLine,rect:l,localRect:o,obb:f,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:a})}}return t}function _g(r,t,e,i,n,a){var o=r.length;if(o<2)return;r.sort(function(C,M){return C.rect[t]-M.rect[t]});for(var s=0,u,l=!1,f=0,h=0;h<o;h++){var c=r[h],v=c.rect;u=v[t]-s,u<0&&(v[t]-=u,c.label[t]-=u,l=!0);var d=Math.max(-u,0);f+=d,s=v[t]+v[e]}f>0&&a&&b(-f/o,0,o);var _=r[0],p=r[o-1],g,y;m(),g<0&&S(-g,.8),y<0&&S(y,.8),m(),w(g,y,1),w(y,g,-1),m(),g<0&&x(-g),y<0&&x(y);function m(){g=_.rect[t]-i,y=n-p.rect[t]-p.rect[e]}function w(C,M,A){if(C<0){var T=Math.min(M,-C);if(T>0){b(T*A,0,o);var P=T+C;P<0&&S(-P*A,1)}else S(-C*A,1)}}function b(C,M,A){C!==0&&(l=!0);for(var T=M;T<A;T++){var P=r[T],L=P.rect;L[t]+=C,P.label[t]+=C}}function S(C,M){for(var A=[],T=0,P=1;P<o;P++){var L=r[P-1].rect,I=Math.max(r[P].rect[t]-L[t]-L[e],0);A.push(I),T+=I}if(T){var R=Math.min(Math.abs(C)/T,M);if(C>0)for(var P=0;P<o-1;P++){var E=A[P]*R;b(E,0,P+1)}else for(var P=o-1;P>0;P--){var E=A[P-1]*R;b(-E,P,o)}}}function x(C){var M=C<0?-1:1;C=Math.abs(C);for(var A=Math.ceil(C/(o-1)),T=0;T<o-1;T++)if(M>0?b(A,0,T+1):b(-A,o-T-1,o),C-=A,C<=0)return}return l}function $C(r,t,e,i){return _g(r,"x","width",t,e,i)}function qC(r,t,e,i){return _g(r,"y","height",t,e,i)}function gT(r){var t=[];r.sort(function(_,p){return p.priority-_.priority});var e=new J(0,0,0,0);function i(_){if(!_.ignore){var p=_.ensureState("emphasis");p.ignore==null&&(p.ignore=!1)}_.ignore=!0}for(var n=0;n<r.length;n++){var a=r[n],o=a.axisAligned,s=a.localRect,u=a.transform,l=a.label,f=a.labelLine;e.copy(a.rect),e.width-=.1,e.height-=.1,e.x+=.05,e.y+=.05;for(var h=a.obb,c=!1,v=0;v<t.length;v++){var d=t[v];if(e.intersect(d.rect)){if(o&&d.axisAligned){c=!0;break}if(d.obb||(d.obb=new Xa(d.localRect,d.transform)),h||(h=new Xa(s,u)),h.intersect(d.obb)){c=!0;break}}}c?(i(l),f&&i(f)):(l.attr("ignore",a.defaultAttr.ignore),f&&f.attr("ignore",a.defaultAttr.labelGuideIgnore),t.push(a))}}var Hs=null;function _T(r){return Hs||(Hs=(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){return setTimeout(t,16)}).bind(window)),Hs(r)}var Gs=null;function yT(r){Gs||(Gs=(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||function(t){clearTimeout(t)}).bind(window)),Gs(r)}function mT(r){var t=document.createElement("style");return t.styleSheet?t.styleSheet.cssText=r:t.appendChild(document.createTextNode(r)),(document.querySelector("head")||document.body).appendChild(t),t}function ga(r,t){t===void 0&&(t={});var e=document.createElement(r);return Object.keys(t).forEach(function(i){e[i]=t[i]}),e}function yg(r,t,e){var i=window.getComputedStyle(r,null)||{display:"none"};return i[t]}function Hu(r){if(!document.documentElement.contains(r))return{detached:!0,rendered:!1};for(var t=r;t!==document;){if(yg(t,"display")==="none")return{detached:!1,rendered:!1};t=t.parentNode}return{detached:!1,rendered:!0}}var wT='.resize-triggers{visibility:hidden;opacity:0;pointer-events:none}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:"";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}',Gu=0,Ea=null;function ST(r,t){r.__resize_mutation_handler__||(r.__resize_mutation_handler__=xT.bind(r));var e=r.__resize_listeners__;if(!e){if(r.__resize_listeners__=[],window.ResizeObserver){var i=r.offsetWidth,n=r.offsetHeight,a=new ResizeObserver(function(){!r.__resize_observer_triggered__&&(r.__resize_observer_triggered__=!0,r.offsetWidth===i&&r.offsetHeight===n)||no(r)}),o=Hu(r),s=o.detached,u=o.rendered;r.__resize_observer_triggered__=s===!1&&u===!1,r.__resize_observer__=a,a.observe(r)}else if(r.attachEvent&&r.addEventListener)r.__resize_legacy_resize_handler__=function(){no(r)},r.attachEvent("onresize",r.__resize_legacy_resize_handler__),document.addEventListener("DOMSubtreeModified",r.__resize_mutation_handler__);else if(Gu||(Ea=mT(wT)),CT(r),r.__resize_rendered__=Hu(r).rendered,window.MutationObserver){var l=new MutationObserver(r.__resize_mutation_handler__);l.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),r.__resize_mutation_observer__=l}}r.__resize_listeners__.push(t),Gu++}function bT(r,t){var e=r.__resize_listeners__;if(e){if(t&&e.splice(e.indexOf(t),1),!e.length||!t){if(r.detachEvent&&r.removeEventListener){r.detachEvent("onresize",r.__resize_legacy_resize_handler__),document.removeEventListener("DOMSubtreeModified",r.__resize_mutation_handler__);return}r.__resize_observer__?(r.__resize_observer__.unobserve(r),r.__resize_observer__.disconnect(),r.__resize_observer__=null):(r.__resize_mutation_observer__&&(r.__resize_mutation_observer__.disconnect(),r.__resize_mutation_observer__=null),r.removeEventListener("scroll",Ql),r.removeChild(r.__resize_triggers__.triggers),r.__resize_triggers__=null),r.__resize_listeners__=null}!--Gu&&Ea&&Ea.parentNode.removeChild(Ea)}}function TT(r){var t=r.__resize_last__,e=t.width,i=t.height,n=r.offsetWidth,a=r.offsetHeight;return n!==e||a!==i?{width:n,height:a}:null}function xT(){var r=Hu(this),t=r.rendered,e=r.detached;t!==this.__resize_rendered__&&(!e&&this.__resize_triggers__&&(Jl(this),this.addEventListener("scroll",Ql,!0)),this.__resize_rendered__=t,no(this))}function Ql(){var r=this;Jl(this),this.__resize_raf__&&yT(this.__resize_raf__),this.__resize_raf__=_T(function(){var t=TT(r);t&&(r.__resize_last__=t,no(r))})}function no(r){!r||!r.__resize_listeners__||r.__resize_listeners__.forEach(function(t){t.call(r,r)})}function CT(r){var t=yg(r,"position");(!t||t==="static")&&(r.style.position="relative"),r.__resize_old_position__=t,r.__resize_last__={};var e=ga("div",{className:"resize-triggers"}),i=ga("div",{className:"resize-expand-trigger"}),n=ga("div"),a=ga("div",{className:"resize-contract-trigger"});i.appendChild(n),e.appendChild(i),e.appendChild(a),r.appendChild(e),r.__resize_triggers__={triggers:e,expand:i,expandChild:n,contract:a},Jl(r),r.addEventListener("scroll",Ql,!0),r.__resize_last__={width:r.offsetWidth,height:r.offsetHeight}}function Jl(r){var t=r.__resize_triggers__,e=t.expand,i=t.expandChild,n=t.contract,a=n.scrollWidth,o=n.scrollHeight,s=e.offsetWidth,u=e.offsetHeight,l=e.scrollWidth,f=e.scrollHeight;n.scrollLeft=a,n.scrollTop=o,i.style.width=s+1+"px",i.style.height=u+1+"px",e.scrollLeft=l,e.scrollTop=f}var ye=function(){return ye=Object.assign||function(r){for(var t,e=1,i=arguments.length;e<i;e++)for(var n in t=arguments[e])Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n]);return r},ye.apply(this,arguments)};var DT=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function MT(r){return t=Object.create(null),DT.forEach(function(e){t[e]=function(i){return function(){for(var n=[],a=0;a<arguments.length;a++)n[a]=arguments[a];if(!r.value)throw new Error("ECharts is not initialized yet.");return r.value[i].apply(r.value,n)}}(e)}),t;var t}var AT={autoresize:[Boolean,Object]},PT=/^on[^a-z]/,Vv=function(r){return PT.test(r)};function _a(r,t){var e=zg(r)?Hg(r):r;return e&&typeof e=="object"&&"value"in e?e.value||t:e||t}var LT="ecLoadingOptions",IT={loading:Boolean,loadingOptions:Object},Gi=null,mg="x-vue-echarts",Uv=[],Wi=[];(function(r,t){if(r&&typeof document<"u"){var e,i=t.prepend===!0?"prepend":"append",n=t.singleTag===!0,a=typeof t.container=="string"?document.querySelector(t.container):document.getElementsByTagName("head")[0];if(n){var o=Uv.indexOf(a);o===-1&&(o=Uv.push(a)-1,Wi[o]={}),e=Wi[o]&&Wi[o][i]?Wi[o][i]:Wi[o][i]=s()}else e=s();r.charCodeAt(0)===65279&&(r=r.substring(1)),e.styleSheet?e.styleSheet.cssText+=r:e.appendChild(document.createTextNode(r))}function s(){var u=document.createElement("style");if(u.setAttribute("type","text/css"),t.attributes)for(var l=Object.keys(t.attributes),f=0;f<l.length;f++)u.setAttribute(l[f],t.attributes[l[f]]);var h=i==="prepend"?"afterbegin":"beforeend";return a.insertAdjacentElement(h,u),u}})(`x-vue-echarts{display:flex;flex-direction:column;width:100%;height:100%;min-width:0}
.vue-echarts-inner{flex-grow:1;min-width:0;width:auto!important;height:auto!important}
`,{});var RT=function(){if(Gi!=null)return Gi;if(typeof HTMLElement>"u"||typeof customElements>"u")return Gi=!1;try{new Function("tag",`class EChartsElement extends HTMLElement {
  __dispose = null;

  disconnectedCallback() {
    if (this.__dispose) {
      this.__dispose();
      this.__dispose = null;
    }
  }
}

if (customElements.get(tag) == null) {
  customElements.define(tag, EChartsElement);
}
`)(mg)}catch{return Gi=!1}return Gi=!0}(),ET="ecTheme",OT="ecInitOptions",kT="ecUpdateOptions",Yv=/(^&?~?!?)native:/,ZC=Eg({name:"echarts",props:ye(ye({option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean},AT),IT),emits:{},inheritAttrs:!1,setup:function(r,t){var e=t.attrs,i=En(),n=En(),a=En(),o=En(),s=On(ET,null),u=On(OT,null),l=On(kT,null),f=Og(r),h=f.autoresize,c=f.manualUpdate,v=f.loading,d=f.loadingOptions,_=Hr(function(){return o.value||r.option||null}),p=Hr(function(){return r.theme||_a(s,{})}),g=Hr(function(){return r.initOptions||_a(u,{})}),y=Hr(function(){return r.updateOptions||_a(l,{})}),m=Hr(function(){return function(T){var P={};for(var L in T)Vv(L)||(P[L]=T[L]);return P}(e)}),w={},b=kg().proxy.$listeners,S={};function x(T){if(n.value){var P=a.value=cb(n.value,p.value,g.value);r.group&&(P.group=r.group),Object.keys(S).forEach(function(I){var R=S[I];if(R){var E=I.toLowerCase();E.charAt(0)==="~"&&(E=E.substring(1),R.__once__=!0);var U=P;if(E.indexOf("zr:")===0&&(U=P.getZr(),E=E.substring(3)),R.__once__){delete R.__once__;var N=R;R=function(){for(var z=[],G=0;G<arguments.length;G++)z[G]=arguments[G];N.apply(void 0,z),U.off(E,R)}}U.on(E,R)}}),h.value?Ng(function(){P&&!P.isDisposed()&&P.resize(),L()}):L()}function L(){var I=T||_.value;I&&P.setOption(I,y.value)}}function C(){a.value&&(a.value.dispose(),a.value=void 0)}b?Object.keys(b).forEach(function(T){Yv.test(T)?w[T.replace(Yv,"$1")]=b[T]:S[T]=b[T]}):Object.keys(e).filter(function(T){return Vv(T)}).forEach(function(T){var P=T.charAt(2).toLowerCase()+T.slice(3);if(P.indexOf("native:")!==0)P.substring(P.length-4)==="Once"&&(P="~".concat(P.substring(0,P.length-4))),S[P]=e[T];else{var L="on".concat(P.charAt(7).toUpperCase()).concat(P.slice(8));w[L]=e[T]}});var M=null;kn(c,function(T){typeof M=="function"&&(M(),M=null),T||(M=kn(function(){return r.option},function(P,L){P&&(a.value?a.value.setOption(P,ye({notMerge:P!==L},y.value)):x())},{deep:!0}))},{immediate:!0}),kn([p,g],function(){C(),x()},{deep:!0}),nf(function(){r.group&&a.value&&(a.value.group=r.group)});var A=MT(a);return function(T,P,L){var I=On(LT,{}),R=Hr(function(){return ye(ye({},_a(I,{})),L==null?void 0:L.value)});nf(function(){var E=T.value;E&&(P.value?E.showLoading(R.value):E.hideLoading())})}(a,v,d),function(T,P,L){var I=null;kn([L,T,P],function(R,E,U){var N=R[0],z=R[1],G=R[2];if(N&&z&&G){var rt=G===!0?{}:G,j=rt.throttle,ct=j===void 0?100:j,xt=rt.onResize,te=function(){z.resize(),xt==null||xt()};I=ct?zl(te,ct):te,ST(N,I)}U(function(){N&&I&&bT(N,I)})})}(a,h,n),Bg(function(){x()}),Fg(function(){RT&&i.value?i.value.__dispose=C:C()}),ye({chart:a,root:i,inner:n,setOption:function(T,P){r.manualUpdate&&(o.value=T),a.value?a.value.setOption(T,P||{}):x(T)},nonEventAttrs:m,nativeListeners:w},A)},render:function(){var r=ye(ye({},this.nonEventAttrs),this.nativeListeners);return r.ref="root",r.class=r.class?["echarts"].concat(r.class):"echarts",af(mg,r,[af("div",{ref:"inner",class:"vue-echarts-inner"})])}});function KC(r,t,e){var i=r.get("borderRadius");if(i==null)return e?{cornerRadius:0}:null;k(i)||(i=[i,i,i,i]);var n=Math.abs(t.r||0-t.r0||0);return{cornerRadius:Y(i,function(a){return Qe(a,n)})}}function Xv(r,t,e){e=e||{};var i=r.coordinateSystem,n=t.axis,a={},o=n.getAxesOnZeroOf()[0],s=n.position,u=o?"onZero":s,l=n.dim,f=i.getRect(),h=[f.x,f.x+f.width,f.y,f.y+f.height],c={left:0,right:1,top:0,bottom:1,onZero:2},v=t.get("offset")||0,d=l==="x"?[h[2]-v,h[3]+v]:[h[0]-v,h[1]+v];if(o){var _=o.toGlobalCoord(o.dataToCoord(0));d[c.onZero]=Math.max(Math.min(_,d[1]),d[0])}a.position=[l==="y"?d[c[u]]:h[0],l==="x"?d[c[u]]:h[3]],a.rotation=Math.PI/2*(l==="x"?0:1);var p={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=p[s],a.labelOffset=o?d[c[s]]-d[c.onZero]:0,t.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),vn(e.labelInside,t.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var g=t.get(["axisLabel","rotate"]);return a.labelRotate=u==="top"?-g:g,a.z2=1,a}function QC(r){return r.get("coordinateSystem")==="cartesian2d"}function JC(r){var t={xAxisModel:null,yAxisModel:null};return D(t,function(e,i){var n=i.replace(/Model$/,""),a=r.getReferringComponents(n,fo).models[0];t[i]=a}),t}var $e=Math.PI,Or=function(){function r(t,e){this.group=new fe,this.opt=e,this.axisModel=t,ot(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var i=new fe({x:e.position[0],y:e.position[1],rotation:e.rotation});i.updateTransform(),this._transformGroup=i}return r.prototype.hasBuilder=function(t){return!!$v[t]},r.prototype.add=function(t){$v[t](this.opt,this.axisModel,this.group,this._transformGroup)},r.prototype.getGroup=function(){return this.group},r.innerTextLayout=function(t,e,i){var n=qc(e-t),a,o;return Va(n)?(o=i>0?"top":"bottom",a="center"):Va(n-$e)?(o=i>0?"bottom":"top",a="center"):(o="middle",n>0&&n<$e?a=i>0?"right":"left":a=i>0?"left":"right"),{rotation:n,textAlign:a,textVerticalAlign:o}},r.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},r.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},r}(),$v={axisLine:function(r,t,e,i){var n=t.get(["axisLine","show"]);if(n==="auto"&&r.handleAutoShown&&(n=r.handleAutoShown("axisLine")),!!n){var a=t.axis.getExtent(),o=i.transform,s=[a[0],0],u=[a[1],0],l=s[0]>u[0];o&&(be(s,s,o),be(u,u,o));var f=O({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),h=new wi({shape:{x1:s[0],y1:s[1],x2:u[0],y2:u[1]},style:f,strokeContainThreshold:r.strokeContainThreshold||5,silent:!0,z2:1});Sl(h.shape,h.style.lineWidth),h.anid="line",e.add(h);var c=t.get(["axisLine","symbol"]);if(c!=null){var v=t.get(["axisLine","symbolSize"]);B(c)&&(c=[c,c]),(B(v)||ht(v))&&(v=[v,v]);var d=RS(t.get(["axisLine","symbolOffset"])||0,v),_=v[0],p=v[1];D([{rotate:r.rotation+Math.PI/2,offset:d[0],r:0},{rotate:r.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-u[0])*(s[0]-u[0])+(s[1]-u[1])*(s[1]-u[1]))}],function(g,y){if(c[y]!=="none"&&c[y]!=null){var m=Hl(c[y],-_/2,-p/2,_,p,f.stroke,!0),w=g.r+g.offset,b=l?u:s;m.attr({rotation:g.rotate,x:b[0]+w*Math.cos(r.rotation),y:b[1]-w*Math.sin(r.rotation),silent:!0,z2:11}),e.add(m)}})}}},axisTickLabel:function(r,t,e,i){var n=NT(e,i,t,r),a=HT(e,i,t,r);if(FT(t,a,n),zT(e,i,t,r.tickDirection),t.get(["axisLabel","hideOverlap"])){var o=pT(Y(a,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));gT(o)}},axisName:function(r,t,e,i){var n=vn(r.axisName,t.get("name"));if(n){var a=t.get("nameLocation"),o=r.nameDirection,s=t.getModel("nameTextStyle"),u=t.get("nameGap")||0,l=t.axis.getExtent(),f=l[0]>l[1]?-1:1,h=[a==="start"?l[0]-f*u:a==="end"?l[1]+f*u:(l[0]+l[1])/2,Zv(a)?r.labelOffset+o*u:0],c,v=t.get("nameRotate");v!=null&&(v=v*$e/180);var d;Zv(a)?c=Or.innerTextLayout(r.rotation,v??r.rotation,o):(c=BT(r.rotation,a,v||0,l),d=r.axisNameAvailableWidth,d!=null&&(d=Math.abs(d/Math.sin(c.rotation)),!isFinite(d)&&(d=null)));var _=s.getFont(),p=t.get("nameTruncate",!0)||{},g=p.ellipsis,y=vn(r.nameTruncateMaxWidth,p.maxWidth,d),m=new Lt({x:h[0],y:h[1],rotation:c.rotation,silent:Or.isLabelSilent(t),style:je(s,{text:n,font:_,overflow:"truncate",width:y,ellipsis:g,fill:s.getTextColor()||t.get(["axisLine","lineStyle","color"]),align:s.get("align")||c.textAlign,verticalAlign:s.get("verticalAlign")||c.textVerticalAlign}),z2:1});if(xl({el:m,componentModel:t,itemName:n}),m.__fullText=n,m.anid="name",t.get("triggerEvent")){var w=Or.makeAxisEventDataBase(t);w.targetType="axisName",w.name=n,it(m).eventData=w}i.add(m),m.updateTransform(),e.add(m),m.decomposeTransform()}}};function BT(r,t,e,i){var n=qc(e-r),a,o,s=i[0]>i[1],u=t==="start"&&!s||t!=="start"&&s;return Va(n-$e/2)?(o=u?"bottom":"top",a="center"):Va(n-$e*1.5)?(o=u?"top":"bottom",a="center"):(o="middle",n<$e*1.5&&n>$e/2?a=u?"left":"right":a=u?"right":"left"),{rotation:n,textAlign:a,textVerticalAlign:o}}function FT(r,t,e){if(!vT(r.axis)){var i=r.get(["axisLabel","showMinLabel"]),n=r.get(["axisLabel","showMaxLabel"]);t=t||[],e=e||[];var a=t[0],o=t[1],s=t[t.length-1],u=t[t.length-2],l=e[0],f=e[1],h=e[e.length-1],c=e[e.length-2];i===!1?(Ut(a),Ut(l)):qv(a,o)&&(i?(Ut(o),Ut(f)):(Ut(a),Ut(l))),n===!1?(Ut(s),Ut(h)):qv(u,s)&&(n?(Ut(u),Ut(c)):(Ut(s),Ut(h)))}}function Ut(r){r&&(r.ignore=!0)}function qv(r,t){var e=r&&r.getBoundingRect().clone(),i=t&&t.getBoundingRect().clone();if(!(!e||!i)){var n=tl([]);return el(n,n,-r.rotation),e.applyTransform(ui([],n,r.getLocalTransform())),i.applyTransform(ui([],n,t.getLocalTransform())),e.intersect(i)}}function Zv(r){return r==="middle"||r==="center"}function wg(r,t,e,i,n){for(var a=[],o=[],s=[],u=0;u<r.length;u++){var l=r[u].coord;o[0]=l,o[1]=0,s[0]=l,s[1]=e,t&&(be(o,o,t),be(s,s,t));var f=new wi({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});Sl(f.shape,f.style.lineWidth),f.anid=n+"_"+r[u].tickValue,a.push(f)}return a}function NT(r,t,e,i){var n=e.axis,a=e.getModel("axisTick"),o=a.get("show");if(o==="auto"&&i.handleAutoShown&&(o=i.handleAutoShown("axisTick")),!(!o||n.scale.isBlank())){for(var s=a.getModel("lineStyle"),u=i.tickDirection*a.get("length"),l=n.getTicksCoords(),f=wg(l,t.transform,u,ot(s.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<f.length;h++)r.add(f[h]);return f}}function zT(r,t,e,i){var n=e.axis,a=e.getModel("minorTick");if(!(!a.get("show")||n.scale.isBlank())){var o=n.getMinorTicksCoords();if(o.length)for(var s=a.getModel("lineStyle"),u=i*a.get("length"),l=ot(s.getLineStyle(),ot(e.getModel("axisTick").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})),f=0;f<o.length;f++)for(var h=wg(o[f],t.transform,u,l,"minorticks_"+f),c=0;c<h.length;c++)r.add(h[c])}}function HT(r,t,e,i){var n=e.axis,a=vn(i.axisLabelShow,e.get(["axisLabel","show"]));if(!(!a||n.scale.isBlank())){var o=e.getModel("axisLabel"),s=o.get("margin"),u=n.getViewLabels(),l=(vn(i.labelRotate,o.get("rotate"))||0)*$e/180,f=Or.innerTextLayout(i.rotation,l,i.labelDirection),h=e.getCategories&&e.getCategories(!0),c=[],v=Or.isLabelSilent(e),d=e.get("triggerEvent");return D(u,function(_,p){var g=n.scale.type==="ordinal"?n.scale.getRawOrdinalNumber(_.tickValue):_.tickValue,y=_.formattedLabel,m=_.rawLabel,w=o;if(h&&h[g]){var b=h[g];H(b)&&b.textStyle&&(w=new ft(b.textStyle,o,e.ecModel))}var S=w.getTextColor()||e.get(["axisLine","lineStyle","color"]),x=n.dataToCoord(g),C=w.getShallow("align",!0)||f.textAlign,M=X(w.getShallow("alignMinLabel",!0),C),A=X(w.getShallow("alignMaxLabel",!0),C),T=w.getShallow("verticalAlign",!0)||w.getShallow("baseline",!0)||f.textVerticalAlign,P=X(w.getShallow("verticalAlignMinLabel",!0),T),L=X(w.getShallow("verticalAlignMaxLabel",!0),T),I=new Lt({x,y:i.labelOffset+i.labelDirection*s,rotation:f.rotation,silent:v,z2:10+(_.level||0),style:je(w,{text:y,align:p===0?M:p===u.length-1?A:C,verticalAlign:p===0?P:p===u.length-1?L:T,fill:Z(S)?S(n.type==="category"?m:n.type==="value"?g+"":g,p):S})});if(I.anid="label_"+g,d){var R=Or.makeAxisEventDataBase(e);R.targetType="axisLabel",R.value=m,R.tickIndex=p,n.type==="category"&&(R.dataIndex=g),it(I).eventData=R}t.add(I),I.updateTransform(),c.push(I),r.add(I),I.decomposeTransform()}),c}}function GT(r,t){var e={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return WT(e,r,t),e.seriesInvolved&&UT(e,r),e}function WT(r,t,e){var i=t.getComponent("tooltip"),n=t.getComponent("axisPointer"),a=n.get("link",!0)||[],o=[];D(e.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var u=Cn(s.model),l=r.coordSysAxesInfo[u]={};r.coordSysMap[u]=s;var f=s.model,h=f.getModel("tooltip",i);if(D(s.getAxes(),_t(_,!1,null)),s.getTooltipAxes&&i&&h.get("show")){var c=h.get("trigger")==="axis",v=h.get(["axisPointer","type"])==="cross",d=s.getTooltipAxes(h.get(["axisPointer","axis"]));(c||v)&&D(d.baseAxes,_t(_,v?"cross":!0,c)),v&&D(d.otherAxes,_t(_,"cross",!1))}function _(p,g,y){var m=y.model.getModel("axisPointer",n),w=m.get("show");if(!(!w||w==="auto"&&!p&&!Wu(m))){g==null&&(g=m.get("triggerTooltip")),m=p?VT(y,h,n,t,p,g):m;var b=m.get("snap"),S=m.get("triggerEmphasis"),x=Cn(y.model),C=g||b||y.type==="category",M=r.axesInfo[x]={key:x,axis:y,coordSys:s,axisPointerModel:m,triggerTooltip:g,triggerEmphasis:S,involveSeries:C,snap:b,useHandle:Wu(m),seriesModels:[],linkGroup:null};l[x]=M,r.seriesInvolved=r.seriesInvolved||C;var A=YT(a,y);if(A!=null){var T=o[A]||(o[A]={axesInfo:{}});T.axesInfo[x]=M,T.mapper=a[A].mapper,M.linkGroup=T}}}})}function VT(r,t,e,i,n,a){var o=t.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],u={};D(s,function(c){u[c]=K(o.get(c))}),u.snap=r.type!=="category"&&!!a,o.get("type")==="cross"&&(u.type="line");var l=u.label||(u.label={});if(l.show==null&&(l.show=!1),n==="cross"){var f=o.get(["label","show"]);if(l.show=f??!0,!a){var h=u.lineStyle=o.get("crossStyle");h&&ot(l,h.textStyle)}}return r.model.getModel("axisPointer",new ft(u,e,i))}function UT(r,t){t.eachSeries(function(e){var i=e.coordinateSystem,n=e.get(["tooltip","trigger"],!0),a=e.get(["tooltip","show"],!0);!i||n==="none"||n===!1||n==="item"||a===!1||e.get(["axisPointer","show"],!0)===!1||D(r.coordSysAxesInfo[Cn(i.model)],function(o){var s=o.axis;i.getAxis(s.dim)===s&&(o.seriesModels.push(e),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=e.getData().count())})})}function YT(r,t){for(var e=t.model,i=t.dim,n=0;n<r.length;n++){var a=r[n]||{};if(Ws(a[i+"AxisId"],e.id)||Ws(a[i+"AxisIndex"],e.componentIndex)||Ws(a[i+"AxisName"],e.name))return n}}function Ws(r,t){return r==="all"||k(r)&&nt(r,t)>=0||r===t}function XT(r){var t=jl(r);if(t){var e=t.axisPointerModel,i=t.axis.scale,n=e.option,a=e.get("status"),o=e.get("value");o!=null&&(o=i.parse(o));var s=Wu(e);a==null&&(n.status=s?"show":"hide");var u=i.getExtent().slice();u[0]>u[1]&&u.reverse(),(o==null||o>u[1])&&(o=u[1]),o<u[0]&&(o=u[0]),n.value=o,s&&(n.status=t.axis.scale.isBlank()?"hide":"show")}}function jl(r){var t=(r.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return t&&t.axesInfo[Cn(r)]}function $T(r){var t=jl(r);return t&&t.axisPointerModel}function Wu(r){return!!r.get(["handle","show"])}function Cn(r){return r.type+"||"+r.id}var Kv={},qT=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n,a){this.axisPointerClass&&XT(e),r.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,n,!0)},t.prototype.updateAxisPointer=function(e,i,n,a){this._doUpdateAxisPointerClass(e,n,!1)},t.prototype.remove=function(e,i){var n=this._axisPointer;n&&n.remove(i)},t.prototype.dispose=function(e,i){this._disposeAxisPointer(i),r.prototype.dispose.apply(this,arguments)},t.prototype._doUpdateAxisPointerClass=function(e,i,n){var a=t.getAxisPointerClass(this.axisPointerClass);if(a){var o=$T(e);o?(this._axisPointer||(this._axisPointer=new a)).render(e,o,i,n):this._disposeAxisPointer(i)}},t.prototype._disposeAxisPointer=function(e){this._axisPointer&&this._axisPointer.dispose(e),this._axisPointer=null},t.registerAxisPointerClass=function(e,i){Kv[e]=i},t.getAxisPointerClass=function(e){return e&&Kv[e]},t.type="axis",t}(he),Cr=yt(),Qv=K,Vs=st,ZT=function(){function r(){this._dragging=!1,this.animationThreshold=15}return r.prototype.render=function(t,e,i,n){var a=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,!(!n&&this._lastValue===a&&this._lastStatus===o)){this._lastValue=a,this._lastStatus=o;var s=this._group,u=this._handle;if(!o||o==="hide"){s&&s.hide(),u&&u.hide();return}s&&s.show(),u&&u.show();var l={};this.makeElOption(l,a,t,e,i);var f=l.graphicKey;f!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=f;var h=this._moveAnimation=this.determineAnimation(t,e);if(!s)s=this._group=new fe,this.createPointerEl(s,l,t,e),this.createLabelEl(s,l,t,e),i.getZr().add(s);else{var c=_t(Jv,e,h);this.updatePointerEl(s,l,c),this.updateLabelEl(s,l,c,e)}tc(s,e,!0),this._renderHandle(a)}},r.prototype.remove=function(t){this.clear(t)},r.prototype.dispose=function(t){this.clear(t)},r.prototype.determineAnimation=function(t,e){var i=e.get("animation"),n=t.axis,a=n.type==="category",o=e.get("snap");if(!o&&!a)return!1;if(i==="auto"||i==null){var s=this.animationThreshold;if(a&&n.getBandWidth()>s)return!0;if(o){var u=jl(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/u>s}return!1}return i===!0},r.prototype.makeElOption=function(t,e,i,n,a){},r.prototype.createPointerEl=function(t,e,i,n){var a=e.pointer;if(a){var o=Cr(t).pointerEl=new l1[a.type](Qv(e.pointer));t.add(o)}},r.prototype.createLabelEl=function(t,e,i,n){if(e.label){var a=Cr(t).labelEl=new Lt(Qv(e.label));t.add(a),jv(a,n)}},r.prototype.updatePointerEl=function(t,e,i){var n=Cr(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},r.prototype.updateLabelEl=function(t,e,i,n){var a=Cr(t).labelEl;a&&(a.setStyle(e.label.style),i(a,{x:e.label.x,y:e.label.y}),jv(a,n))},r.prototype._renderHandle=function(t){if(!(this._dragging||!this.updateHandleTransform)){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,a=e.getModel("handle"),o=e.get("status");if(!a.get("show")||!o||o==="hide"){n&&i.remove(n),this._handle=null;return}var s;this._handle||(s=!0,n=this._handle=Tl(a.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(l){Dc(l.event)},onmousedown:Vs(this._onHandleDragMove,this,0,0),drift:Vs(this._onHandleDragMove,this),ondragend:Vs(this._onHandleDragEnd,this)}),i.add(n)),tc(n,e,!1),n.setStyle(a.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var u=a.get("size");k(u)||(u=[u,u]),n.scaleX=u[0]/2,n.scaleY=u[1]/2,Dp(this,"_doDispatchAxisPointer",a.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,s)}},r.prototype._moveHandleToValue=function(t,e){Jv(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Us(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},r.prototype._onHandleDragMove=function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(Us(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(Us(n)),Cr(i).lastProp=null,this._doDispatchAxisPointer()}},r.prototype._doDispatchAxisPointer=function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},r.prototype._onHandleDragEnd=function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},r.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),Iu(this,"_doDispatchAxisPointer")},r.prototype.doClear=function(){},r.prototype.buildLabel=function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}},r}();function Jv(r,t,e,i){Sg(Cr(e).lastProp,i)||(Cr(e).lastProp=i,t?In(e,i,r):(e.stopAnimation(),e.attr(i)))}function Sg(r,t){if(H(r)&&H(t)){var e=!0;return D(t,function(i,n){e=e&&Sg(r[n],i)}),!!e}else return r===t}function jv(r,t){r[t.get(["label","show"])?"show":"hide"]()}function Us(r){return{x:r.x||0,y:r.y||0,rotation:r.rotation||0}}function tc(r,t,e){var i=t.get("z"),n=t.get("zlevel");r&&r.traverse(function(a){a.type!=="group"&&(i!=null&&(a.z=i),n!=null&&(a.zlevel=n),a.silent=e)})}function KT(r){var t=r.get("type"),e=r.getModel(t+"Style"),i;return t==="line"?(i=e.getLineStyle(),i.fill=null):t==="shadow"&&(i=e.getAreaStyle(),i.stroke=null),i}function QT(r,t,e,i,n){var a=e.get("value"),o=bg(a,t.axis,t.ecModel,e.get("seriesDataIndices"),{precision:e.get(["label","precision"]),formatter:e.get(["label","formatter"])}),s=e.getModel("label"),u=Mo(s.get("padding")||0),l=s.getFont(),f=Vc(o,l),h=n.position,c=f.width+u[1]+u[3],v=f.height+u[0]+u[2],d=n.align;d==="right"&&(h[0]-=c),d==="center"&&(h[0]-=c/2);var _=n.verticalAlign;_==="bottom"&&(h[1]-=v),_==="middle"&&(h[1]-=v/2),JT(h,c,v,i);var p=s.get("backgroundColor");(!p||p==="auto")&&(p=t.get(["axisLine","lineStyle","color"])),r.label={x:h[0],y:h[1],style:je(s,{text:o,font:l,fill:s.getTextColor(),padding:u,backgroundColor:p}),z2:10}}function JT(r,t,e,i){var n=i.getWidth(),a=i.getHeight();r[0]=Math.min(r[0]+t,n)-t,r[1]=Math.min(r[1]+e,a)-e,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0)}function bg(r,t,e,i,n){r=t.scale.parse(r);var a=t.scale.getLabel({value:r},{precision:n.precision}),o=n.formatter;if(o){var s={value:Kl(t,{value:r}),axisDimension:t.dim,axisIndex:t.index,seriesData:[]};D(i,function(u){var l=e.getSeriesByIndex(u.seriesIndex),f=u.dataIndexInside,h=l&&l.getDataParams(f);h&&s.seriesData.push(h)}),B(o)?a=o.replace("{value}",a):Z(o)&&(a=o(s))}return a}function Tg(r,t,e){var i=si();return el(i,i,e.rotation),tu(i,i,e.position),bl([r.dataToCoord(t),(e.labelOffset||0)+(e.labelDirection||1)*(e.labelMargin||0)],i)}function jT(r,t,e,i,n,a){var o=Or.innerTextLayout(e.rotation,0,e.labelDirection);e.labelMargin=n.get(["label","margin"]),QT(t,i,n,a,{position:Tg(i.axis,r,e),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function tx(r,t,e){return e=e||0,{x1:r[e],y1:r[1-e],x2:t[e],y2:t[1-e]}}function ex(r,t,e){return e=e||0,{x:r[e],y:r[1-e],width:t[e],height:t[1-e]}}var rx=function(r){F(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.makeElOption=function(e,i,n,a,o){var s=n.axis,u=s.grid,l=a.get("type"),f=ec(u,s).getOtherAxis(s).getGlobalExtent(),h=s.toGlobalCoord(s.dataToCoord(i,!0));if(l&&l!=="none"){var c=KT(a),v=ix[l](s,h,f);v.style=c,e.graphicKey=v.type,e.pointer=v}var d=Xv(u.model,n);jT(i,e,d,n,a,o)},t.prototype.getHandleTransform=function(e,i,n){var a=Xv(i.axis.grid.model,i,{labelInside:!1});a.labelMargin=n.get(["handle","margin"]);var o=Tg(i.axis,e,a);return{x:o[0],y:o[1],rotation:a.rotation+(a.labelDirection<0?Math.PI:0)}},t.prototype.updateHandleTransform=function(e,i,n,a){var o=n.axis,s=o.grid,u=o.getGlobalExtent(!0),l=ec(s,o).getOtherAxis(o).getGlobalExtent(),f=o.dim==="x"?0:1,h=[e.x,e.y];h[f]+=i[f],h[f]=Math.min(u[1],h[f]),h[f]=Math.max(u[0],h[f]);var c=(l[1]+l[0])/2,v=[c,c];v[f]=h[f];var d=[{verticalAlign:"middle"},{align:"center"}];return{x:h[0],y:h[1],rotation:e.rotation,cursorPoint:v,tooltipOption:d[f]}},t}(ZT);function ec(r,t){var e={};return e[t.dim+"AxisIndex"]=t.index,r.getCartesian(e)}var ix={line:function(r,t,e){var i=tx([t,e[0]],[t,e[1]],rc(r));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(r,t,e){var i=Math.max(1,r.getBandWidth()),n=e[1]-e[0];return{type:"Rect",shape:ex([t-i/2,e[0]],[i,n],rc(r))}}};function rc(r){return r.dim==="x"?0:1}var nx=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="axisPointer",t.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},t}(tt),Ee=yt(),ax=D;function xg(r,t,e){if(!W.node){var i=t.getZr();Ee(i).records||(Ee(i).records={}),ox(i,t);var n=Ee(i).records[r]||(Ee(i).records[r]={});n.handler=e}}function ox(r,t){if(Ee(r).initialized)return;Ee(r).initialized=!0,e("click",_t(ic,"click")),e("mousemove",_t(ic,"mousemove")),e("globalout",ux);function e(i,n){r.on(i,function(a){var o=lx(t);ax(Ee(r).records,function(s){s&&n(s,a,o.dispatchAction)}),sx(o.pendings,t)})}}function sx(r,t){var e=r.showTip.length,i=r.hideTip.length,n;e?n=r.showTip[e-1]:i&&(n=r.hideTip[i-1]),n&&(n.dispatchAction=null,t.dispatchAction(n))}function ux(r,t,e){r.handler("leave",null,e)}function ic(r,t,e,i){t.handler(r,e,i)}function lx(r){var t={showTip:[],hideTip:[]},e=function(i){var n=t[i.type];n?n.push(i):(i.dispatchAction=e,r.dispatchAction(i))};return{dispatchAction:e,pendings:t}}function Vu(r,t){if(!W.node){var e=t.getZr(),i=(Ee(e).records||{})[r];i&&(Ee(e).records[r]=null)}}var fx=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){var a=i.getComponent("tooltip"),o=e.get("triggerOn")||a&&a.get("triggerOn")||"mousemove|click";xg("axisPointer",n,function(s,u,l){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&l({type:"updateAxisPointer",currTrigger:s,x:u&&u.offsetX,y:u&&u.offsetY})})},t.prototype.remove=function(e,i){Vu("axisPointer",i)},t.prototype.dispose=function(e,i){Vu("axisPointer",i)},t.type="axisPointer",t}(he);function Cg(r,t){var e=[],i=r.seriesIndex,n;if(i==null||!(n=t.getSeriesByIndex(i)))return{point:[]};var a=n.getData(),o=An(a,r);if(o==null||o<0||k(o))return{point:[]};var s=a.getItemGraphicEl(o),u=n.coordinateSystem;if(n.getTooltipPosition)e=n.getTooltipPosition(o)||[];else if(u&&u.dataToPoint)if(r.isStacked){var l=u.getBaseAxis(),f=u.getOtherAxis(l),h=f.dim,c=l.dim,v=h==="x"||h==="radius"?1:0,d=a.mapDimension(c),_=[];_[v]=a.get(d,o),_[1-v]=a.get(a.getCalculationInfo("stackResultDimension"),o),e=u.dataToPoint(_)||[]}else e=u.dataToPoint(a.getValues(Y(u.dimensions,function(g){return a.mapDimension(g)}),o))||[];else if(s){var p=s.getBoundingRect().clone();p.applyTransform(s.transform),e=[p.x+p.width/2,p.y+p.height/2]}return{point:e,el:s}}var nc=yt();function hx(r,t,e){var i=r.currTrigger,n=[r.x,r.y],a=r,o=r.dispatchAction||st(e.dispatchAction,e),s=t.getComponent("axisPointer").coordSysAxesInfo;if(s){Oa(n)&&(n=Cg({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},t).point);var u=Oa(n),l=a.axesInfo,f=s.axesInfo,h=i==="leave"||Oa(n),c={},v={},d={list:[],map:{}},_={showPointer:_t(cx,v),showTooltip:_t(dx,d)};D(s.coordSysMap,function(g,y){var m=u||g.containPoint(n);D(s.coordSysAxesInfo[y],function(w,b){var S=w.axis,x=yx(l,w);if(!h&&m&&(!l||x)){var C=x&&x.value;C==null&&!u&&(C=S.pointToData(n)),C!=null&&ac(w,C,_,!1,c)}})});var p={};return D(f,function(g,y){var m=g.linkGroup;m&&!v[y]&&D(m.axesInfo,function(w,b){var S=v[b];if(w!==g&&S){var x=S.value;m.mapper&&(x=g.axis.scale.parse(m.mapper(x,oc(w),oc(g)))),p[g.key]=x}})}),D(p,function(g,y){ac(f[y],g,_,!0,c)}),px(v,f,c),gx(d,n,r,o),_x(f,o,e),c}}function ac(r,t,e,i,n){var a=r.axis;if(!(a.scale.isBlank()||!a.containData(t))){if(!r.involveSeries){e.showPointer(r,t);return}var o=vx(t,r),s=o.payloadBatch,u=o.snapToValue;s[0]&&n.seriesIndex==null&&O(n,s[0]),!i&&r.snap&&a.containData(u)&&u!=null&&(t=u),e.showPointer(r,t,s),e.showTooltip(r,o,u)}}function vx(r,t){var e=t.axis,i=e.dim,n=r,a=[],o=Number.MAX_VALUE,s=-1;return D(t.seriesModels,function(u,l){var f=u.getData().mapDimensionsAll(i),h,c;if(u.getAxisTooltipData){var v=u.getAxisTooltipData(f,r,e);c=v.dataIndices,h=v.nestestValue}else{if(c=u.getData().indicesOfNearest(f[0],r,e.type==="category"?.5:null),!c.length)return;h=u.getData().get(f[0],c[0])}if(!(h==null||!isFinite(h))){var d=r-h,_=Math.abs(d);_<=o&&((_<o||d>=0&&s<0)&&(o=_,s=d,n=h,a.length=0),D(c,function(p){a.push({seriesIndex:u.seriesIndex,dataIndexInside:p,dataIndex:u.getData().getRawIndex(p)})}))}}),{payloadBatch:a,snapToValue:n}}function cx(r,t,e,i){r[t.key]={value:e,payloadBatch:i}}function dx(r,t,e,i){var n=e.payloadBatch,a=t.axis,o=a.model,s=t.axisPointerModel;if(!(!t.triggerTooltip||!n.length)){var u=t.coordSys.model,l=Cn(u),f=r.map[l];f||(f=r.map[l]={coordSysId:u.id,coordSysIndex:u.componentIndex,coordSysType:u.type,coordSysMainType:u.mainType,dataByAxis:[]},r.list.push(f)),f.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:i,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:n.slice()})}}function px(r,t,e){var i=e.axesInfo=[];D(t,function(n,a){var o=n.axisPointerModel.option,s=r[a];s?(!n.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!n.useHandle&&(o.status="hide"),o.status==="show"&&i.push({axisDim:n.axis.dim,axisIndex:n.axis.model.componentIndex,value:o.value})})}function gx(r,t,e,i){if(Oa(t)||!r.list.length){i({type:"hideTip"});return}var n=((r.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:e.tooltipOption,position:e.position,dataIndexInside:n.dataIndexInside,dataIndex:n.dataIndex,seriesIndex:n.seriesIndex,dataByCoordSys:r.list})}function _x(r,t,e){var i=e.getZr(),n="axisPointerLastHighlights",a=nc(i)[n]||{},o=nc(i)[n]={};D(r,function(l,f){var h=l.axisPointerModel.option;h.status==="show"&&l.triggerEmphasis&&D(h.seriesDataIndices,function(c){var v=c.seriesIndex+" | "+c.dataIndex;o[v]=c})});var s=[],u=[];D(a,function(l,f){!o[f]&&u.push(l)}),D(o,function(l,f){!a[f]&&s.push(l)}),u.length&&e.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:u}),s.length&&e.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function yx(r,t){for(var e=0;e<(r||[]).length;e++){var i=r[e];if(t.axis.dim===i.axisDim&&t.axis.model.componentIndex===i.axisIndex)return i}}function oc(r){var t=r.axis.model,e={},i=e.axisDim=r.axis.dim;return e.axisIndex=e[i+"AxisIndex"]=t.componentIndex,e.axisName=e[i+"AxisName"]=t.name,e.axisId=e[i+"AxisId"]=t.id,e}function Oa(r){return!r||r[0]==null||isNaN(r[0])||r[1]==null||isNaN(r[1])}function mx(r){qT.registerAxisPointerClass("CartesianAxisPointer",rx),r.registerComponentModel(nx),r.registerComponentView(fx),r.registerPreprocessor(function(t){if(t){(!t.axisPointer||t.axisPointer.length===0)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!k(e)&&(t.axisPointer.link=[e])}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=GT(t,e)}),r.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},hx)}function jC(r,t,e){var i=t.getBoxLayoutParams(),n=t.get("padding"),a={width:e.getWidth(),height:e.getHeight()},o=gi(i,a,n);ci(t.get("orient"),r,t.get("itemGap"),o.width,o.height),k1(r,i,a,n)}function Sx(r,t){var e=Mo(t.get("padding")),i=t.getItemStyle(["color","opacity"]);return i.fill=t.get("backgroundColor"),r=new Pt({shape:{x:r.x-e[3],y:r.y-e[0],width:r.width+e[1]+e[3],height:r.height+e[0]+e[2],r:t.get("borderRadius")},style:i,silent:!0,z2:-1}),r}var bx=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="tooltip",t.dependencies=["axisPointer"],t.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},t}(tt);function Dg(r){var t=r.get("confine");return t!=null?!!t:r.get("renderMode")==="richText"}function Mg(r){if(W.domSupported){for(var t=document.documentElement.style,e=0,i=r.length;e<i;e++)if(r[e]in t)return r[e]}}var Ag=Mg(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Tx=Mg(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function Pg(r,t){if(!r)return t;t=Zd(t,!0);var e=r.indexOf(t);return r=e===-1?t:"-"+r.slice(0,e)+"-"+t,r.toLowerCase()}function xx(r,t){var e=r.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r);return e?e[t]:null}var Cx=Pg(Tx,"transition"),tf=Pg(Ag,"transform"),Dx="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(W.transform3dSupported?"will-change:transform;":"");function Mx(r){return r=r==="left"?"right":r==="right"?"left":r==="top"?"bottom":"top",r}function Ax(r,t,e){if(!B(e)||e==="inside")return"";var i=r.get("backgroundColor"),n=r.get("borderWidth");t=yn(t);var a=Mx(e),o=Math.max(Math.round(n)*1.5,6),s="",u=tf+":",l;nt(["left","right"],a)>-1?(s+="top:50%",u+="translateY(-50%) rotate("+(l=a==="left"?-225:-45)+"deg)"):(s+="left:50%",u+="translateX(-50%) rotate("+(l=a==="top"?225:45)+"deg)");var f=l*Math.PI/180,h=o+n,c=h*Math.abs(Math.cos(f))+h*Math.abs(Math.sin(f)),v=Math.round(((c-Math.SQRT2*n)/2+Math.SQRT2*n-(c-h)/2)*100)/100;s+=";"+a+":-"+v+"px";var d=t+" solid "+n+"px;",_=["position:absolute;width:"+o+"px;height:"+o+"px;z-index:-1;",s+";"+u+";","border-bottom:"+d,"border-right:"+d,"background-color:"+i+";"];return'<div style="'+_.join("")+'"></div>'}function Px(r,t){var e="cubic-bezier(0.23,1,0.32,1)",i=" "+r/2+"s "+e,n="opacity"+i+",visibility"+i;return t||(i=" "+r+"s "+e,n+=W.transformSupported?","+tf+i:",left"+i+",top"+i),Cx+":"+n}function sc(r,t,e){var i=r.toFixed(0)+"px",n=t.toFixed(0)+"px";if(!W.transformSupported)return e?"top:"+n+";left:"+i+";":[["top",n],["left",i]];var a=W.transform3dSupported,o="translate"+(a?"3d":"")+"("+i+","+n+(a?",0":"")+")";return e?"top:0;left:0;"+tf+":"+o+";":[["top",0],["left",0],[Ag,o]]}function Lx(r){var t=[],e=r.get("fontSize"),i=r.getTextColor();i&&t.push("color:"+i),t.push("font:"+r.getFont()),e&&t.push("line-height:"+Math.round(e*3/2)+"px");var n=r.get("textShadowColor"),a=r.get("textShadowBlur")||0,o=r.get("textShadowOffsetX")||0,s=r.get("textShadowOffsetY")||0;return n&&a&&t.push("text-shadow:"+o+"px "+s+"px "+a+"px "+n),D(["decoration","align"],function(u){var l=r.get(u);l&&t.push("text-"+u+":"+l)}),t.join(";")}function Ix(r,t,e){var i=[],n=r.get("transitionDuration"),a=r.get("backgroundColor"),o=r.get("shadowBlur"),s=r.get("shadowColor"),u=r.get("shadowOffsetX"),l=r.get("shadowOffsetY"),f=r.getModel("textStyle"),h=Tp(r,"html"),c=u+"px "+l+"px "+o+"px "+s;return i.push("box-shadow:"+c),t&&n&&i.push(Px(n,e)),a&&i.push("background-color:"+a),D(["width","color","radius"],function(v){var d="border-"+v,_=Zd(d),p=r.get(_);p!=null&&i.push(d+":"+p+(v==="color"?"":"px"))}),i.push(Lx(f)),h!=null&&i.push("padding:"+Mo(h).join("px ")+"px"),i.join(";")+";"}function uc(r,t,e,i,n){var a=t&&t.painter;if(e){var o=a&&a.getViewportRoot();o&&__(r,o,e,i,n)}else{r[0]=i,r[1]=n;var s=a&&a.getViewportRootOffset();s&&(r[0]+=s.offsetLeft,r[1]+=s.offsetTop)}r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var Rx=function(){function r(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,W.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var n=this._zr=t.getZr(),a=e.appendTo,o=a&&(B(a)?document.querySelector(a):hn(a)?a:Z(a)&&a(t.getDom()));uc(this._styleCoord,n,o,t.getWidth()/2,t.getHeight()/2),(o||t.getDom()).appendChild(i),this._api=t,this._container=o;var s=this;i.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},i.onmousemove=function(u){if(u=u||window.event,!s._enterable){var l=n.handler,f=n.painter.getViewportRoot();Xt(f,u,!0),l.dispatch("mousemove",u)}},i.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return r.prototype.update=function(t){if(!this._container){var e=this._api.getDom(),i=xx(e,"position"),n=e.style;n.position!=="absolute"&&i!=="absolute"&&(n.position="relative")}var a=t.get("alwaysShowContent");a&&this._moveIfResized(),this._alwaysShowContent=a,this.el.className=t.get("className")||""},r.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var i=this.el,n=i.style,a=this._styleCoord;i.innerHTML?n.cssText=Dx+Ix(t,!this._firstShow,this._longHide)+sc(a[0],a[1],!0)+("border-color:"+yn(e)+";")+(t.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):n.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},r.prototype.setContent=function(t,e,i,n,a){var o=this.el;if(t==null){o.innerHTML="";return}var s="";if(B(a)&&i.get("trigger")==="item"&&!Dg(i)&&(s=Ax(i,n,a)),B(t))o.innerHTML=t+s;else if(t){o.innerHTML="",k(t)||(t=[t]);for(var u=0;u<t.length;u++)hn(t[u])&&t[u].parentNode!==o&&o.appendChild(t[u]);if(s&&o.childNodes.length){var l=document.createElement("div");l.innerHTML=s,o.appendChild(l)}}},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el;return[t.offsetWidth,t.offsetHeight]},r.prototype.moveTo=function(t,e){var i=this._styleCoord;if(uc(i,this._zr,this._container,t,e),i[0]!=null&&i[1]!=null){var n=this.el.style,a=sc(i[0],i[1]);D(a,function(o){n[o[0]]=o[1]})}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",W.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(st(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},r}(),Ex=function(){function r(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),fc(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return r.prototype.update=function(t){var e=t.get("alwaysShowContent");e&&this._moveIfResized(),this._alwaysShowContent=e},r.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},r.prototype.setContent=function(t,e,i,n,a){var o=this;H(t)&&Ot(""),this.el&&this._zr.remove(this.el);var s=i.getModel("textStyle");this.el=new Lt({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:n,textShadowColor:s.get("textShadowColor"),fill:i.get(["textStyle","color"]),padding:Tp(i,"richText"),verticalAlign:"top",align:"left"},z:i.get("z")}),D(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(l){o.el.style[l]=i.get(l)}),D(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(l){o.el.style[l]=s.get(l)||0}),this._zr.add(this.el);var u=this;this.el.on("mouseover",function(){u._enterable&&(clearTimeout(u._hideTimeout),u._show=!0),u._inContent=!0}),this.el.on("mouseout",function(){u._enterable&&u._show&&u.hideLater(u._hideDelay),u._inContent=!1})},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),i=lc(t.style);return[e.width+i.left+i.right,e.height+i.top+i.bottom]},r.prototype.moveTo=function(t,e){var i=this.el;if(i){var n=this._styleCoord;fc(n,this._zr,t,e),t=n[0],e=n[1];var a=i.style,o=Ue(a.borderWidth||0),s=lc(a);i.x=t+o+s.left,i.y=e+o+s.top,i.markRedraw()}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(st(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){this._zr.remove(this.el)},r}();function Ue(r){return Math.max(0,r)}function lc(r){var t=Ue(r.shadowBlur||0),e=Ue(r.shadowOffsetX||0),i=Ue(r.shadowOffsetY||0);return{left:Ue(t-e),right:Ue(t+e),top:Ue(t-i),bottom:Ue(t+i)}}function fc(r,t,e,i){r[0]=e,r[1]=i,r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var Ox=new Pt({shape:{x:-1,y:-1,width:2,height:2}}),kx=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.init=function(e,i){if(!(W.node||!i.getDom())){var n=e.getComponent("tooltip"),a=this._renderMode=Yy(n.get("renderMode"));this._tooltipContent=a==="richText"?new Ex(i):new Rx(i,{appendTo:n.get("appendToBody",!0)?"body":n.get("appendTo",!0)})}},t.prototype.render=function(e,i,n){if(!(W.node||!n.getDom())){this.group.removeAll(),this._tooltipModel=e,this._ecModel=i,this._api=n;var a=this._tooltipContent;a.update(e),a.setEnterable(e.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&e.get("transitionDuration")?Dp(this,"_updatePosition",50,"fixRate"):Iu(this,"_updatePosition")}},t.prototype._initGlobalListener=function(){var e=this._tooltipModel,i=e.get("triggerOn");xg("itemTooltip",this._api,st(function(n,a,o){i!=="none"&&(i.indexOf(n)>=0?this._tryShow(a,o):n==="leave"&&this._hide(o))},this))},t.prototype._keepShow=function(){var e=this._tooltipModel,i=this._ecModel,n=this._api,a=e.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&a!=="none"&&a!=="click"){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&o.manuallyShowTip(e,i,n,{x:o._lastX,y:o._lastY,dataByCoordSys:o._lastDataByCoordSys})})}},t.prototype.manuallyShowTip=function(e,i,n,a){if(!(a.from===this.uid||W.node||!n.getDom())){var o=hc(a,n);this._ticket="";var s=a.dataByCoordSys,u=zx(a,i,n);if(u){var l=u.el.getBoundingRect().clone();l.applyTransform(u.el.transform),this._tryShow({offsetX:l.x+l.width/2,offsetY:l.y+l.height/2,target:u.el,position:a.position,positionDefault:"bottom"},o)}else if(a.tooltip&&a.x!=null&&a.y!=null){var f=Ox;f.x=a.x,f.y=a.y,f.update(),it(f).tooltipConfig={name:null,option:a.tooltip},this._tryShow({offsetX:a.x,offsetY:a.y,target:f},o)}else if(s)this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,dataByCoordSys:s,tooltipOption:a.tooltipOption},o);else if(a.seriesIndex!=null){if(this._manuallyAxisShowTip(e,i,n,a))return;var h=Cg(a,i),c=h.point[0],v=h.point[1];c!=null&&v!=null&&this._tryShow({offsetX:c,offsetY:v,target:h.el,position:a.position,positionDefault:"bottom"},o)}else a.x!=null&&a.y!=null&&(n.dispatchAction({type:"updateAxisPointer",x:a.x,y:a.y}),this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,target:n.getZr().findHover(a.x,a.y).target},o))}},t.prototype.manuallyHideTip=function(e,i,n,a){var o=this._tooltipContent;this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,a.from!==this.uid&&this._hide(hc(a,n))},t.prototype._manuallyAxisShowTip=function(e,i,n,a){var o=a.seriesIndex,s=a.dataIndex,u=i.getComponent("axisPointer").coordSysAxesInfo;if(!(o==null||s==null||u==null)){var l=i.getSeriesByIndex(o);if(l){var f=l.getData(),h=Vi([f.getItemModel(s),l,(l.coordinateSystem||{}).model],this._tooltipModel);if(h.get("trigger")==="axis")return n.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:s,position:a.position}),!0}}},t.prototype._tryShow=function(e,i){var n=e.target,a=this._tooltipModel;if(a){this._lastX=e.offsetX,this._lastY=e.offsetY;var o=e.dataByCoordSys;if(o&&o.length)this._showAxisTooltip(o,e);else if(n){var s=it(n);if(s.ssrType==="legend")return;this._lastDataByCoordSys=null;var u,l;Qi(n,function(f){if(it(f).dataIndex!=null)return u=f,!0;if(it(f).tooltipConfig!=null)return l=f,!0},!0),u?this._showSeriesItemTooltip(e,u,i):l?this._showComponentItemTooltip(e,l,i):this._hide(i)}else this._lastDataByCoordSys=null,this._hide(i)}},t.prototype._showOrMove=function(e,i){var n=e.get("showDelay");i=st(i,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(i,n):i()},t.prototype._showAxisTooltip=function(e,i){var n=this._ecModel,a=this._tooltipModel,o=[i.offsetX,i.offsetY],s=Vi([i.tooltipOption],a),u=this._renderMode,l=[],f=Sn("section",{blocks:[],noHeader:!0}),h=[],c=new Ms;D(e,function(y){D(y.dataByAxis,function(m){var w=n.getComponent(m.axisDim+"Axis",m.axisIndex),b=m.value;if(!(!w||b==null)){var S=bg(b,w.axis,n,m.seriesDataIndices,m.valueLabelOpt),x=Sn("section",{header:S,noHeader:!we(S),sortBlocks:!0,blocks:[]});f.blocks.push(x),D(m.seriesDataIndices,function(C){var M=n.getSeriesByIndex(C.seriesIndex),A=C.dataIndexInside,T=M.getDataParams(A);if(!(T.dataIndex<0)){T.axisDim=m.axisDim,T.axisIndex=m.axisIndex,T.axisType=m.axisType,T.axisId=m.axisId,T.axisValue=Kl(w.axis,{value:b}),T.axisValueLabel=S,T.marker=c.makeTooltipMarker("item",yn(T.color),u);var P=Zh(M.formatTooltip(A,!0,null)),L=P.frag;if(L){var I=Vi([M],a).get("valueFormatter");x.blocks.push(I?O({valueFormatter:I},L):L)}P.text&&h.push(P.text),l.push(T)}})}})}),f.blocks.reverse(),h.reverse();var v=i.position,d=s.get("order"),_=ev(f,c,u,d,n.get("useUTC"),s.get("textStyle"));_&&h.unshift(_);var p=u==="richText"?`

`:"<br/>",g=h.join(p);this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(e,l)?this._updatePosition(s,v,o[0],o[1],this._tooltipContent,l):this._showTooltipContent(s,g,l,Math.random()+"",o[0],o[1],v,null,c)})},t.prototype._showSeriesItemTooltip=function(e,i,n){var a=this._ecModel,o=it(i),s=o.seriesIndex,u=a.getSeriesByIndex(s),l=o.dataModel||u,f=o.dataIndex,h=o.dataType,c=l.getData(h),v=this._renderMode,d=e.positionDefault,_=Vi([c.getItemModel(f),l,u&&(u.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),p=_.get("trigger");if(!(p!=null&&p!=="item")){var g=l.getDataParams(f,h),y=new Ms;g.marker=y.makeTooltipMarker("item",yn(g.color),v);var m=Zh(l.formatTooltip(f,!1,h)),w=_.get("order"),b=_.get("valueFormatter"),S=m.frag,x=S?ev(b?O({valueFormatter:b},S):S,y,v,w,a.get("useUTC"),_.get("textStyle")):m.text,C="item_"+l.name+"_"+f;this._showOrMove(_,function(){this._showTooltipContent(_,x,g,C,e.offsetX,e.offsetY,e.position,e.target,y)}),n({type:"showTip",dataIndexInside:f,dataIndex:c.getRawIndex(f),seriesIndex:s,from:this.uid})}},t.prototype._showComponentItemTooltip=function(e,i,n){var a=it(i),o=a.tooltipConfig,s=o.option||{};if(B(s)){var u=s;s={content:u,formatter:u}}var l=[s],f=this._ecModel.getComponent(a.componentMainType,a.componentIndex);f&&l.push(f),l.push({formatter:s.content});var h=e.positionDefault,c=Vi(l,this._tooltipModel,h?{position:h}:null),v=c.get("content"),d=Math.random()+"",_=new Ms;this._showOrMove(c,function(){var p=K(c.get("formatterParams")||{});this._showTooltipContent(c,v,p,d,e.offsetX,e.offsetY,e.position,i,_)}),n({type:"showTip",from:this.uid})},t.prototype._showTooltipContent=function(e,i,n,a,o,s,u,l,f){if(this._ticket="",!(!e.get("showContent")||!e.get("show"))){var h=this._tooltipContent;h.setEnterable(e.get("enterable"));var c=e.get("formatter");u=u||e.get("position");var v=i,d=this._getNearestPoint([o,s],n,e.get("trigger"),e.get("borderColor")),_=d.color;if(c)if(B(c)){var p=e.ecModel.get("useUTC"),g=k(n)?n[0]:n,y=g&&g.axisType&&g.axisType.indexOf("time")>=0;v=c,y&&(v=bo(g.axisValue,v,p)),v=Kd(v,n,!0)}else if(Z(c)){var m=st(function(w,b){w===this._ticket&&(h.setContent(b,f,e,_,u),this._updatePosition(e,u,o,s,h,n,l))},this);this._ticket=a,v=c(n,a,m)}else v=c;h.setContent(v,f,e,_,u),h.show(e,_),this._updatePosition(e,u,o,s,h,n,l)}},t.prototype._getNearestPoint=function(e,i,n,a){if(n==="axis"||k(i))return{color:a||(this._renderMode==="html"?"#fff":"none")};if(!k(i))return{color:a||i.color||i.borderColor}},t.prototype._updatePosition=function(e,i,n,a,o,s,u){var l=this._api.getWidth(),f=this._api.getHeight();i=i||e.get("position");var h=o.getSize(),c=e.get("align"),v=e.get("verticalAlign"),d=u&&u.getBoundingRect().clone();if(u&&d.applyTransform(u.transform),Z(i)&&(i=i([n,a],s,o.el,d,{viewSize:[l,f],contentSize:h.slice()})),k(i))n=At(i[0],l),a=At(i[1],f);else if(H(i)){var _=i;_.width=h[0],_.height=h[1];var p=gi(_,{width:l,height:f});n=p.x,a=p.y,c=null,v=null}else if(B(i)&&u){var g=Nx(i,d,h,e.get("borderWidth"));n=g[0],a=g[1]}else{var g=Bx(n,a,o,l,f,c?null:20,v?null:20);n=g[0],a=g[1]}if(c&&(n-=vc(c)?h[0]/2:c==="right"?h[0]:0),v&&(a-=vc(v)?h[1]/2:v==="bottom"?h[1]:0),Dg(e)){var g=Fx(n,a,o,l,f);n=g[0],a=g[1]}o.moveTo(n,a)},t.prototype._updateContentNotChangedOnAxis=function(e,i){var n=this._lastDataByCoordSys,a=this._cbParamsList,o=!!n&&n.length===e.length;return o&&D(n,function(s,u){var l=s.dataByAxis||[],f=e[u]||{},h=f.dataByAxis||[];o=o&&l.length===h.length,o&&D(l,function(c,v){var d=h[v]||{},_=c.seriesDataIndices||[],p=d.seriesDataIndices||[];o=o&&c.value===d.value&&c.axisType===d.axisType&&c.axisId===d.axisId&&_.length===p.length,o&&D(_,function(g,y){var m=p[y];o=o&&g.seriesIndex===m.seriesIndex&&g.dataIndex===m.dataIndex}),a&&D(c.seriesDataIndices,function(g){var y=g.seriesIndex,m=i[y],w=a[y];m&&w&&w.data!==m.data&&(o=!1)})})}),this._lastDataByCoordSys=e,this._cbParamsList=i,!!o},t.prototype._hide=function(e){this._lastDataByCoordSys=null,e({type:"hideTip",from:this.uid})},t.prototype.dispose=function(e,i){W.node||!i.getDom()||(Iu(this,"_updatePosition"),this._tooltipContent.dispose(),Vu("itemTooltip",i))},t.type="tooltip",t}(he);function Vi(r,t,e){var i=t.ecModel,n;e?(n=new ft(e,i,i),n=new ft(t.option,n,i)):n=t;for(var a=r.length-1;a>=0;a--){var o=r[a];o&&(o instanceof ft&&(o=o.get("tooltip",!0)),B(o)&&(o={formatter:o}),o&&(n=new ft(o,n,i)))}return n}function hc(r,t){return r.dispatchAction||st(t.dispatchAction,t)}function Bx(r,t,e,i,n,a,o){var s=e.getSize(),u=s[0],l=s[1];return a!=null&&(r+u+a+2>i?r-=u+a:r+=a),o!=null&&(t+l+o>n?t-=l+o:t+=o),[r,t]}function Fx(r,t,e,i,n){var a=e.getSize(),o=a[0],s=a[1];return r=Math.min(r+o,i)-o,t=Math.min(t+s,n)-s,r=Math.max(r,0),t=Math.max(t,0),[r,t]}function Nx(r,t,e,i){var n=e[0],a=e[1],o=Math.ceil(Math.SQRT2*i)+8,s=0,u=0,l=t.width,f=t.height;switch(r){case"inside":s=t.x+l/2-n/2,u=t.y+f/2-a/2;break;case"top":s=t.x+l/2-n/2,u=t.y-a-o;break;case"bottom":s=t.x+l/2-n/2,u=t.y+f+o;break;case"left":s=t.x-n-o,u=t.y+f/2-a/2;break;case"right":s=t.x+l+o,u=t.y+f/2-a/2}return[s,u]}function vc(r){return r==="center"||r==="middle"}function zx(r,t,e){var i=sl(r).queryOptionMap,n=i.keys()[0];if(!(!n||n==="series")){var a=Pn(t,n,i.get(n),{useDefault:!1,enableAll:!1,enableNone:!1}),o=a.models[0];if(o){var s=e.getViewOfComponentModel(o),u;if(s.group.traverse(function(l){var f=it(l).tooltipConfig;if(f&&f.name===r.name)return u=l,!0}),u)return{componentMainType:n,componentIndex:o.componentIndex,el:u}}}}function tD(r){xn(mx),r.registerComponentModel(bx),r.registerComponentView(kx),r.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Bt),r.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Bt)}var Hx=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.type="title",t.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},t}(tt),Gx=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){if(this.group.removeAll(),!!e.get("show")){var a=this.group,o=e.getModel("textStyle"),s=e.getModel("subtextStyle"),u=e.get("textAlign"),l=X(e.get("textBaseline"),e.get("textVerticalAlign")),f=new Lt({style:je(o,{text:e.get("text"),fill:o.getTextColor()},{disableBox:!0}),z2:10}),h=f.getBoundingRect(),c=e.get("subtext"),v=new Lt({style:je(s,{text:c,fill:s.getTextColor(),y:h.height+e.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),d=e.get("link"),_=e.get("sublink"),p=e.get("triggerEvent",!0);f.silent=!d&&!p,v.silent=!_&&!p,d&&f.on("click",function(){Ih(d,"_"+e.get("target"))}),_&&v.on("click",function(){Ih(_,"_"+e.get("subtarget"))}),it(f).eventData=it(v).eventData=p?{componentType:"title",componentIndex:e.componentIndex}:null,a.add(f),c&&a.add(v);var g=a.getBoundingRect(),y=e.getBoxLayoutParams();y.width=g.width,y.height=g.height;var m=gi(y,{width:n.getWidth(),height:n.getHeight()},e.get("padding"));u||(u=e.get("left")||e.get("right"),u==="middle"&&(u="center"),u==="right"?m.x+=m.width:u==="center"&&(m.x+=m.width/2)),l||(l=e.get("top")||e.get("bottom"),l==="center"&&(l="middle"),l==="bottom"?m.y+=m.height:l==="middle"&&(m.y+=m.height/2),l=l||"top"),a.x=m.x,a.y=m.y,a.markRedraw();var w={align:u,verticalAlign:l};f.setStyle(w),v.setStyle(w),g=a.getBoundingRect();var b=m.margin,S=e.getItemStyle(["color","opacity"]);S.fill=e.get("backgroundColor");var x=new Pt({shape:{x:g.x-b[3],y:g.y-b[0],width:g.width+b[1]+b[3],height:g.height+b[0]+b[2],r:e.get("borderRadius")},style:S,subPixelOptimize:!0,silent:!0});a.add(x)}},t.type="title",t}(he);function eD(r){r.registerComponentModel(Hx),r.registerComponentView(Gx)}var Wx=function(r,t){if(t==="all")return{type:"all",title:r.getLocaleModel().get(["legend","selector","all"])};if(t==="inverse")return{type:"inverse",title:r.getLocaleModel().get(["legend","selector","inverse"])}},Uu=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n),e.selected=e.selected||{},this._updateSelector(e)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),this._updateSelector(e)},t.prototype._updateSelector=function(e){var i=e.selector,n=this.ecModel;i===!0&&(i=e.selector=["all","inverse"]),k(i)&&D(i,function(a,o){B(a)&&(a={type:a}),i[o]=ut(a,Wx(n,a.type))})},t.prototype.optionUpdated=function(){this._updateData(this.ecModel);var e=this._data;if(e[0]&&this.get("selectedMode")==="single"){for(var i=!1,n=0;n<e.length;n++){var a=e[n].get("name");if(this.isSelected(a)){this.select(a),i=!0;break}}!i&&this.select(e[0].get("name"))}},t.prototype._updateData=function(e){var i=[],n=[];e.eachRawSeries(function(u){var l=u.name;n.push(l);var f;if(u.legendVisualProvider){var h=u.legendVisualProvider,c=h.getAllNames();e.isSeriesFiltered(u)||(n=n.concat(c)),c.length?i=i.concat(c):f=!0}else f=!0;f&&ol(u)&&i.push(u.name)}),this._availableNames=n;var a=this.get("data")||i,o=$(),s=Y(a,function(u){return(B(u)||ht(u))&&(u={name:u}),o.get(u.name)?null:(o.set(u.name,!0),new ft(u,this,this.ecModel))},this);this._data=Et(s,function(u){return!!u})},t.prototype.getData=function(){return this._data},t.prototype.select=function(e){var i=this.option.selected,n=this.get("selectedMode");if(n==="single"){var a=this._data;D(a,function(o){i[o.get("name")]=!1})}i[e]=!0},t.prototype.unSelect=function(e){this.get("selectedMode")!=="single"&&(this.option.selected[e]=!1)},t.prototype.toggleSelected=function(e){var i=this.option.selected;i.hasOwnProperty(e)||(i[e]=!0),this[i[e]?"unSelect":"select"](e)},t.prototype.allSelect=function(){var e=this._data,i=this.option.selected;D(e,function(n){i[n.get("name",!0)]=!0})},t.prototype.inverseSelect=function(){var e=this._data,i=this.option.selected;D(e,function(n){var a=n.get("name",!0);i.hasOwnProperty(a)||(i[a]=!0),i[a]=!i[a]})},t.prototype.isSelected=function(e){var i=this.option.selected;return!(i.hasOwnProperty(e)&&!i[e])&&nt(this._availableNames,e)>=0},t.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},t.type="legend.plain",t.dependencies=["series"],t.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},t}(tt),jr=_t,Yu=D,ya=fe,Lg=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!1,e}return t.prototype.init=function(){this.group.add(this._contentGroup=new ya),this.group.add(this._selectorGroup=new ya),this._isFirstRender=!0},t.prototype.getContentGroup=function(){return this._contentGroup},t.prototype.getSelectorGroup=function(){return this._selectorGroup},t.prototype.render=function(e,i,n){var a=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!e.get("show",!0)){var o=e.get("align"),s=e.get("orient");(!o||o==="auto")&&(o=e.get("left")==="right"&&s==="vertical"?"right":"left");var u=e.get("selector",!0),l=e.get("selectorPosition",!0);u&&(!l||l==="auto")&&(l=s==="horizontal"?"end":"start"),this.renderInner(o,e,i,n,u,s,l);var f=e.getBoxLayoutParams(),h={width:n.getWidth(),height:n.getHeight()},c=e.get("padding"),v=gi(f,h,c),d=this.layoutInner(e,o,v,a,u,l),_=gi(ot({width:d.width,height:d.height},f),h,c);this.group.x=_.x-d.x,this.group.y=_.y-d.y,this.group.markRedraw(),this.group.add(this._backgroundEl=Sx(d,e))}},t.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},t.prototype.renderInner=function(e,i,n,a,o,s,u){var l=this.getContentGroup(),f=$(),h=i.get("selectedMode"),c=[];n.eachRawSeries(function(v){!v.get("legendHoverLink")&&c.push(v.id)}),Yu(i.getData(),function(v,d){var _=v.get("name");if(!this.newlineDisabled&&(_===""||_===`
`)){var p=new ya;p.newline=!0,l.add(p);return}var g=n.getSeriesByName(_)[0];if(!f.get(_))if(g){var y=g.getData(),m=y.getVisual("legendLineStyle")||{},w=y.getVisual("legendIcon"),b=y.getVisual("style"),S=this._createItem(g,_,d,v,i,e,m,b,w,h,a);S.on("click",jr(cc,_,null,a,c)).on("mouseover",jr(Xu,g.name,null,a,c)).on("mouseout",jr($u,g.name,null,a,c)),n.ssr&&S.eachChild(function(x){var C=it(x);C.seriesIndex=g.seriesIndex,C.dataIndex=d,C.ssrType="legend"}),f.set(_,!0)}else n.eachRawSeries(function(x){if(!f.get(_)&&x.legendVisualProvider){var C=x.legendVisualProvider;if(!C.containName(_))return;var M=C.indexOfName(_),A=C.getItemVisual(M,"style"),T=C.getItemVisual(M,"legendIcon"),P=se(A.fill);P&&P[3]===0&&(P[3]=.2,A=O(O({},A),{fill:mi(P,"rgba")}));var L=this._createItem(x,_,d,v,i,e,{},A,T,h,a);L.on("click",jr(cc,null,_,a,c)).on("mouseover",jr(Xu,null,_,a,c)).on("mouseout",jr($u,null,_,a,c)),n.ssr&&L.eachChild(function(I){var R=it(I);R.seriesIndex=x.seriesIndex,R.dataIndex=d,R.ssrType="legend"}),f.set(_,!0)}},this)},this),o&&this._createSelector(o,i,a,s,u)},t.prototype._createSelector=function(e,i,n,a,o){var s=this.getSelectorGroup();Yu(e,function(l){var f=l.type,h=new Lt({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:f==="all"?"legendAllSelect":"legendInverseSelect"})}});s.add(h);var c=i.getModel("selectorLabel"),v=i.getModel(["emphasis","selectorLabel"]);f1(h,{normal:c,emphasis:v},{defaultText:l.title}),yu(h)})},t.prototype._createItem=function(e,i,n,a,o,s,u,l,f,h,c){var v=e.visualDrawType,d=o.get("itemWidth"),_=o.get("itemHeight"),p=o.isSelected(i),g=a.get("symbolRotate"),y=a.get("symbolKeepAspect"),m=a.get("icon");f=m||f||"roundRect";var w=Vx(f,a,u,l,v,p,c),b=new ya,S=a.getModel("textStyle");if(Z(e.getLegendIcon)&&(!m||m==="inherit"))b.add(e.getLegendIcon({itemWidth:d,itemHeight:_,icon:f,iconRotate:g,itemStyle:w.itemStyle,lineStyle:w.lineStyle,symbolKeepAspect:y}));else{var x=m==="inherit"&&e.getData().getVisual("symbol")?g==="inherit"?e.getData().getVisual("symbolRotate"):g:0;b.add(Ux({itemWidth:d,itemHeight:_,icon:f,iconRotate:x,itemStyle:w.itemStyle,lineStyle:w.lineStyle,symbolKeepAspect:y}))}var C=s==="left"?d+5:-5,M=s,A=o.get("formatter"),T=i;B(A)&&A?T=A.replace("{name}",i??""):Z(A)&&(T=A(i));var P=p?S.getTextColor():a.get("inactiveColor");b.add(new Lt({style:je(S,{text:T,x:C,y:_/2,fill:P,align:M,verticalAlign:"middle"},{inheritColor:P})}));var L=new Pt({shape:b.getBoundingRect(),style:{fill:"transparent"}}),I=a.getModel("tooltip");return I.get("show")&&xl({el:L,componentModel:o,itemName:i,itemTooltipOption:I.option}),b.add(L),b.eachChild(function(R){R.silent=!0}),L.silent=!h,this.getContentGroup().add(b),yu(b),b.__legendDataIndex=n,b},t.prototype.layoutInner=function(e,i,n,a,o,s){var u=this.getContentGroup(),l=this.getSelectorGroup();ci(e.get("orient"),u,e.get("itemGap"),n.width,n.height);var f=u.getBoundingRect(),h=[-f.x,-f.y];if(l.markRedraw(),u.markRedraw(),o){ci("horizontal",l,e.get("selectorItemGap",!0));var c=l.getBoundingRect(),v=[-c.x,-c.y],d=e.get("selectorButtonGap",!0),_=e.getOrient().index,p=_===0?"width":"height",g=_===0?"height":"width",y=_===0?"y":"x";s==="end"?v[_]+=f[p]+d:h[_]+=c[p]+d,v[1-_]+=f[g]/2-c[g]/2,l.x=v[0],l.y=v[1],u.x=h[0],u.y=h[1];var m={x:0,y:0};return m[p]=f[p]+d+c[p],m[g]=Math.max(f[g],c[g]),m[y]=Math.min(0,c[y]+v[1-_]),m}else return u.x=h[0],u.y=h[1],this.group.getBoundingRect()},t.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},t.type="legend.plain",t}(he);function Vx(r,t,e,i,n,a,o){function s(p,g){p.lineWidth==="auto"&&(p.lineWidth=g.lineWidth>0?2:0),Yu(p,function(y,m){p[m]==="inherit"&&(p[m]=g[m])})}var u=t.getModel("itemStyle"),l=u.getItemStyle(),f=r.lastIndexOf("empty",0)===0?"fill":"stroke",h=u.getShallow("decal");l.decal=!h||h==="inherit"?i.decal:Bu(h,o),l.fill==="inherit"&&(l.fill=i[n]),l.stroke==="inherit"&&(l.stroke=i[f]),l.opacity==="inherit"&&(l.opacity=(n==="fill"?i:e).opacity),s(l,i);var c=t.getModel("lineStyle"),v=c.getLineStyle();if(s(v,e),l.fill==="auto"&&(l.fill=i.fill),l.stroke==="auto"&&(l.stroke=i.fill),v.stroke==="auto"&&(v.stroke=i.fill),!a){var d=t.get("inactiveBorderWidth"),_=l[f];l.lineWidth=d==="auto"?i.lineWidth>0&&_?2:0:l.lineWidth,l.fill=t.get("inactiveColor"),l.stroke=t.get("inactiveBorderColor"),v.stroke=c.get("inactiveColor"),v.lineWidth=c.get("inactiveWidth")}return{itemStyle:l,lineStyle:v}}function Ux(r){var t=r.icon||"roundRect",e=Hl(t,0,0,r.itemWidth,r.itemHeight,r.itemStyle.fill,r.symbolKeepAspect);return e.setStyle(r.itemStyle),e.rotation=(r.iconRotate||0)*Math.PI/180,e.setOrigin([r.itemWidth/2,r.itemHeight/2]),t.indexOf("empty")>-1&&(e.style.stroke=e.style.fill,e.style.fill="#fff",e.style.lineWidth=2),e}function cc(r,t,e,i){$u(r,t,e,i),e.dispatchAction({type:"legendToggleSelect",name:r??t}),Xu(r,t,e,i)}function Ig(r){for(var t=r.getZr().storage.getDisplayList(),e,i=0,n=t.length;i<n&&!(e=t[i].states.emphasis);)i++;return e&&e.hoverLayer}function Xu(r,t,e,i){Ig(e)||e.dispatchAction({type:"highlight",seriesName:r,name:t,excludeSeriesId:i})}function $u(r,t,e,i){Ig(e)||e.dispatchAction({type:"downplay",seriesName:r,name:t,excludeSeriesId:i})}function Yx(r){var t=r.findComponents({mainType:"legend"});t&&t.length&&r.filterSeries(function(e){for(var i=0;i<t.length;i++)if(!t[i].isSelected(e.name))return!1;return!0})}function Ui(r,t,e){var i={},n=r==="toggleSelected",a;return e.eachComponent("legend",function(o){n&&a!=null?o[a?"select":"unSelect"](t.name):r==="allSelect"||r==="inverseSelect"?o[r]():(o[r](t.name),a=o.isSelected(t.name));var s=o.getData();D(s,function(u){var l=u.get("name");if(!(l===`
`||l==="")){var f=o.isSelected(l);i.hasOwnProperty(l)?i[l]=i[l]&&f:i[l]=f}})}),r==="allSelect"||r==="inverseSelect"?{selected:i}:{name:t.name,selected:i}}function Xx(r){r.registerAction("legendToggleSelect","legendselectchanged",_t(Ui,"toggleSelected")),r.registerAction("legendAllSelect","legendselectall",_t(Ui,"allSelect")),r.registerAction("legendInverseSelect","legendinverseselect",_t(Ui,"inverseSelect")),r.registerAction("legendSelect","legendselected",_t(Ui,"select")),r.registerAction("legendUnSelect","legendunselected",_t(Ui,"unSelect"))}function Rg(r){r.registerComponentModel(Uu),r.registerComponentView(Lg),r.registerProcessor(r.PRIORITY.PROCESSOR.SERIES_FILTER,Yx),r.registerSubTypeDefaulter("legend",function(){return"plain"}),Xx(r)}var $x=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.setScrollDataIndex=function(e){this.option.scrollDataIndex=e},t.prototype.init=function(e,i,n){var a=Rl(e);r.prototype.init.call(this,e,i,n),dc(this,e,a)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),dc(this,this.option,e)},t.type="legend.scroll",t.defaultOption=T1(Uu.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),t}(Uu);function dc(r,t,e){var i=r.getOrient(),n=[1,1];n[i.index]=0,mn(t,e,{type:"box",ignoreSize:!!n})}var pc=fe,Ys=["width","height"],Xs=["x","y"],qx=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!0,e._currentIndex=0,e}return t.prototype.init=function(){r.prototype.init.call(this),this.group.add(this._containerGroup=new pc),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new pc)},t.prototype.resetInner=function(){r.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},t.prototype.renderInner=function(e,i,n,a,o,s,u){var l=this;r.prototype.renderInner.call(this,e,i,n,a,o,s,u);var f=this._controllerGroup,h=i.get("pageIconSize",!0),c=k(h)?h:[h,h];d("pagePrev",0);var v=i.getModel("pageTextStyle");f.add(new Lt({name:"pageText",style:{text:"xx/xx",fill:v.getTextColor(),font:v.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),d("pageNext",1);function d(_,p){var g=_+"DataIndex",y=Tl(i.get("pageIcons",!0)[i.getOrient().name][p],{onclick:st(l._pageGo,l,g,i,a)},{x:-c[0]/2,y:-c[1]/2,width:c[0],height:c[1]});y.name=_,f.add(y)}},t.prototype.layoutInner=function(e,i,n,a,o,s){var u=this.getSelectorGroup(),l=e.getOrient().index,f=Ys[l],h=Xs[l],c=Ys[1-l],v=Xs[1-l];o&&ci("horizontal",u,e.get("selectorItemGap",!0));var d=e.get("selectorButtonGap",!0),_=u.getBoundingRect(),p=[-_.x,-_.y],g=K(n);o&&(g[f]=n[f]-_[f]-d);var y=this._layoutContentAndController(e,a,g,l,f,c,v,h);if(o){if(s==="end")p[l]+=y[f]+d;else{var m=_[f]+d;p[l]-=m,y[h]-=m}y[f]+=_[f]+d,p[1-l]+=y[v]+y[c]/2-_[c]/2,y[c]=Math.max(y[c],_[c]),y[v]=Math.min(y[v],_[v]+p[1-l]),u.x=p[0],u.y=p[1],u.markRedraw()}return y},t.prototype._layoutContentAndController=function(e,i,n,a,o,s,u,l){var f=this.getContentGroup(),h=this._containerGroup,c=this._controllerGroup;ci(e.get("orient"),f,e.get("itemGap"),a?n.width:null,a?null:n.height),ci("horizontal",c,e.get("pageButtonItemGap",!0));var v=f.getBoundingRect(),d=c.getBoundingRect(),_=this._showController=v[o]>n[o],p=[-v.x,-v.y];i||(p[a]=f[l]);var g=[0,0],y=[-d.x,-d.y],m=X(e.get("pageButtonGap",!0),e.get("itemGap",!0));if(_){var w=e.get("pageButtonPosition",!0);w==="end"?y[a]+=n[o]-d[o]:g[a]+=d[o]+m}y[1-a]+=v[s]/2-d[s]/2,f.setPosition(p),h.setPosition(g),c.setPosition(y);var b={x:0,y:0};if(b[o]=_?n[o]:v[o],b[s]=Math.max(v[s],d[s]),b[u]=Math.min(0,d[u]+y[1-a]),h.__rectSize=n[o],_){var S={x:0,y:0};S[o]=Math.max(n[o]-d[o]-m,0),S[s]=b[s],h.setClipPath(new Pt({shape:S})),h.__rectSize=S[o]}else c.eachChild(function(C){C.attr({invisible:!0,silent:!0})});var x=this._getPageInfo(e);return x.pageIndex!=null&&In(f,{x:x.contentPosition[0],y:x.contentPosition[1]},_?e:null),this._updatePageInfoView(e,x),b},t.prototype._pageGo=function(e,i,n){var a=this._getPageInfo(i)[e];a!=null&&n.dispatchAction({type:"legendScroll",scrollDataIndex:a,legendId:i.id})},t.prototype._updatePageInfoView=function(e,i){var n=this._controllerGroup;D(["pagePrev","pageNext"],function(f){var h=f+"DataIndex",c=i[h]!=null,v=n.childOfName(f);v&&(v.setStyle("fill",c?e.get("pageIconColor",!0):e.get("pageIconInactiveColor",!0)),v.cursor=c?"pointer":"default")});var a=n.childOfName("pageText"),o=e.get("pageFormatter"),s=i.pageIndex,u=s!=null?s+1:0,l=i.pageCount;a&&o&&a.setStyle("text",B(o)?o.replace("{current}",u==null?"":u+"").replace("{total}",l==null?"":l+""):o({current:u,total:l}))},t.prototype._getPageInfo=function(e){var i=e.get("scrollDataIndex",!0),n=this.getContentGroup(),a=this._containerGroup.__rectSize,o=e.getOrient().index,s=Ys[o],u=Xs[o],l=this._findTargetItemIndex(i),f=n.children(),h=f[l],c=f.length,v=c?1:0,d={contentPosition:[n.x,n.y],pageCount:v,pageIndex:v-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!h)return d;var _=w(h);d.contentPosition[o]=-_.s;for(var p=l+1,g=_,y=_,m=null;p<=c;++p)m=w(f[p]),(!m&&y.e>g.s+a||m&&!b(m,g.s))&&(y.i>g.i?g=y:g=m,g&&(d.pageNextDataIndex==null&&(d.pageNextDataIndex=g.i),++d.pageCount)),y=m;for(var p=l-1,g=_,y=_,m=null;p>=-1;--p)m=w(f[p]),(!m||!b(y,m.s))&&g.i<y.i&&(y=g,d.pagePrevDataIndex==null&&(d.pagePrevDataIndex=g.i),++d.pageCount,++d.pageIndex),g=m;return d;function w(S){if(S){var x=S.getBoundingRect(),C=x[u]+S[u];return{s:C,e:C+x[s],i:S.__legendDataIndex}}}function b(S,x){return S.e>=x&&S.s<=x+a}},t.prototype._findTargetItemIndex=function(e){if(!this._showController)return 0;var i,n=this.getContentGroup(),a;return n.eachChild(function(o,s){var u=o.__legendDataIndex;a==null&&u!=null&&(a=s),u===e&&(i=s)}),i??a},t.type="legend.scroll",t}(Lg);function Zx(r){r.registerAction("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;i!=null&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(n){n.setScrollDataIndex(i)})})}function Kx(r){xn(Rg),r.registerComponentModel($x),r.registerComponentView(qx),Zx(r)}function rD(r){xn(Rg),xn(Kx)}var Qx=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="dataset",e}return t.prototype.init=function(e,i,n){r.prototype.init.call(this,e,i,n),this._sourceManager=new _p(this),jh(this)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),jh(this)},t.prototype.optionUpdated=function(){this._sourceManager.dirty()},t.prototype.getSourceManager=function(){return this._sourceManager},t.type="dataset",t.defaultOption={seriesLayoutBy:Te},t}(tt),Jx=function(r){F(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="dataset",e}return t.type="dataset",t}(he);function iD(r){r.registerComponentModel(Qx),r.registerComponentView(Jx)}function gc(r,t,e){var i=_i.createCanvas(),n=t.getWidth(),a=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=n+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",r)),i.width=n*e,i.height=a*e,i}var $s=function(r){F(t,r);function t(e,i,n){var a=r.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var o;n=n||Wa,typeof e=="string"?o=gc(e,i,n):H(e)&&(o=e,e=o.id),a.id=e,a.dom=o;var s=o.style;return s&&(xc(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),a.painter=i,a.dpr=n,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var e=this.dpr;this.domBack=gc("back-"+this.id,this.painter,e),this.ctxBack=this.domBack.getContext("2d"),e!==1&&this.ctxBack.scale(e,e)},t.prototype.createRepaintRects=function(e,i,n,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,u=!1,l=new J(0,0,0,0);function f(y){if(!(!y.isFinite()||y.isZero()))if(o.length===0){var m=new J(0,0,0,0);m.copy(y),o.push(m)}else{for(var w=!1,b=1/0,S=0,x=0;x<o.length;++x){var C=o[x];if(C.intersect(y)){var M=new J(0,0,0,0);M.copy(C),M.union(y),o[x]=M,w=!0;break}else if(u){l.copy(y),l.union(C);var A=y.width*y.height,T=C.width*C.height,P=l.width*l.height,L=P-A-T;L<b&&(b=L,S=x)}}if(u&&(o[S].union(y),w=!0),!w){var m=new J(0,0,0,0);m.copy(y),o.push(m)}u||(u=o.length>=s)}}for(var h=this.__startIndex;h<this.__endIndex;++h){var c=e[h];if(c){var v=c.shouldBePainted(n,a,!0,!0),d=c.__isRendered&&(c.__dirty&Gt||!v)?c.getPrevPaintRect():null;d&&f(d);var _=v&&(c.__dirty&Gt||!c.__isRendered)?c.getPaintRect():null;_&&f(_)}}for(var h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var c=i[h],v=c&&c.shouldBePainted(n,a,!0,!0);if(c&&(!v||!c.__zr)&&c.__isRendered){var d=c.getPrevPaintRect();d&&f(d)}}var p;do{p=!1;for(var h=0;h<o.length;){if(o[h].isZero()){o.splice(h,1);continue}for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(p=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(p);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(e,i){var n=this.dpr,a=this.dom,o=a.style,s=this.domBack;o&&(o.width=e+"px",o.height=i+"px"),a.width=e*n,a.height=i*n,s&&(s.width=e*n,s.height=i*n,n!==1&&this.ctxBack.scale(n,n))},t.prototype.clear=function(e,i,n){var a=this.dom,o=this.ctx,s=a.width,u=a.height;i=i||this.clearColor;var l=this.motionBlur&&!e,f=this.lastFrameAlpha,h=this.dpr,c=this;l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,s/h,u/h));var v=this.domBack;function d(_,p,g,y){if(o.clearRect(_,p,g,y),i&&i!=="transparent"){var m=void 0;if(oo(i)){var w=i.global||i.__width===g&&i.__height===y;m=w&&i.__canvasGradient||Ou(o,i,{x:0,y:0,width:g,height:y}),i.__canvasGradient=m,i.__width=g,i.__height=y}else r_(i)&&(i.scaleX=i.scaleX||h,i.scaleY=i.scaleY||h,m=ku(o,i,{dirty:function(){c.setUnpainted(),c.painter.refresh()}}));o.save(),o.fillStyle=m||i,o.fillRect(_,p,g,y),o.restore()}l&&(o.save(),o.globalAlpha=f,o.drawImage(v,_,p,g,y),o.restore())}!n||l?d(0,0,s,u):n.length&&D(n,function(_){d(_.x*h,_.y*h,_.width*h,_.height*h)})},t}(Ce),_c=1e5,xr=314159,ma=.01,jx=.001;function tC(r){return r?r.__builtin__?!0:!(typeof r.resize!="function"||typeof r.refresh!="function"):!1}function eC(r,t){var e=document.createElement("div");return e.style.cssText=["position:relative","width:"+r+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",e}var rC=function(){function r(t,e,i,n){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=O({},i||{}),this.dpr=i.devicePixelRatio||Wa,this._singleCanvas=a,this.root=t;var o=t.style;o&&(xc(t),t.innerHTML=""),this.storage=e;var s=this._zlevelList;this._prevDisplayList=[];var u=this._layers;if(a){var f=t,h=f.width,c=f.height;i.width!=null&&(h=i.width),i.height!=null&&(c=i.height),this.dpr=i.devicePixelRatio||1,f.width=h*this.dpr,f.height=c*this.dpr,this._width=h,this._height=c;var v=new $s(f,this,this.dpr);v.__builtin__=!0,v.initContext(),u[xr]=v,v.zlevel=xr,s.push(xr),this._domRoot=t}else{this._width=sa(t,0,i),this._height=sa(t,1,i);var l=this._domRoot=eC(this._width,this._height);t.appendChild(l)}}return r.prototype.getType=function(){return"canvas"},r.prototype.isSingleCanvas=function(){return this._singleCanvas},r.prototype.getViewportRoot=function(){return this._domRoot},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,i,t,this._redrawId);for(var a=0;a<n.length;a++){var o=n[a],s=this._layers[o];if(!s.__builtin__&&s.refresh){var u=a===0?this._backgroundColor:null;s.refresh(u)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},r.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},r.prototype._paintHoverList=function(t){var e=t.length,i=this._hoverlayer;if(i&&i.clear(),!!e){for(var n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,o=0;o<e;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(_c)),a||(a=i.ctx,a.save()),Pr(a,s,n,o===e-1))}a&&a.restore()}},r.prototype.getHoverLayer=function(){return this.getLayer(_c)},r.prototype.paintOne=function(t,e){Fp(t,e)},r.prototype._paintList=function(t,e,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,e,i),o=a.finished,s=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(l){l.afterBrush&&l.afterBrush()});else{var u=this;Fa(function(){u._paintList(t,e,i,n)})}}},r.prototype._compositeManually=function(){var t=this.getLayer(xr).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},r.prototype._doPaintList=function(t,e,i){for(var n=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var u=this._zlevelList[s],l=this._layers[u];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||i)&&a.push(l)}for(var f=!0,h=!1,c=function(_){var p=a[_],g=p.ctx,y=o&&p.createRepaintRects(t,e,v._width,v._height),m=i?p.__startIndex:p.__drawIndex,w=!i&&p.incremental&&Date.now,b=w&&Date.now(),S=p.zlevel===v._zlevelList[0]?v._backgroundColor:null;if(p.__startIndex===p.__endIndex)p.clear(!1,S,y);else if(m===p.__startIndex){var x=t[m];(!x.incremental||!x.notClear||i)&&p.clear(!1,S,y)}m===-1&&(console.error("For some unknown reason. drawIndex is -1"),m=p.__startIndex);var C,M=function(L){var I={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(C=m;C<p.__endIndex;C++){var R=t[C];if(R.__inHover&&(h=!0),n._doPaintEl(R,p,o,L,I,C===p.__endIndex-1),w){var E=Date.now()-b;if(E>15)break}}I.prevElClipPaths&&g.restore()};if(y)if(y.length===0)C=p.__endIndex;else for(var A=v.dpr,T=0;T<y.length;++T){var P=y[T];g.save(),g.beginPath(),g.rect(P.x*A,P.y*A,P.width*A,P.height*A),g.clip(),M(P),g.restore()}else g.save(),M(),g.restore();p.__drawIndex=C,p.__drawIndex<p.__endIndex&&(f=!1)},v=this,d=0;d<a.length;d++)c(d);return W.wxa&&D(this._layers,function(_){_&&_.ctx&&_.ctx.draw&&_.ctx.draw()}),{finished:f,needsRefreshHover:h}},r.prototype._doPaintEl=function(t,e,i,n,a,o){var s=e.ctx;if(i){var u=t.getPaintRect();(!n||u&&u.intersect(n))&&(Pr(s,t,a,o),t.setPrevPaintRect(u))}else Pr(s,t,a,o)},r.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=xr);var i=this._layers[t];return i||(i=new $s("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?ut(i,this._layerConfig[t],!0):this._layerConfig[t-ma]&&ut(i,this._layerConfig[t-ma],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},r.prototype.insertLayer=function(t,e){var i=this._layers,n=this._zlevelList,a=n.length,o=this._domRoot,s=null,u=-1;if(!i[t]&&tC(e)){if(a>0&&t>n[0]){for(u=0;u<a-1&&!(n[u]<t&&n[u+1]>t);u++);s=i[n[u]]}if(n.splice(u+1,0,t),i[t]=e,!e.virtual)if(s){var l=s.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},r.prototype.eachLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n];t.call(e,this._layers[a],a)}},r.prototype.eachBuiltinLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__&&t.call(e,o,a)}},r.prototype.eachOtherLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__||t.call(e,o,a)}},r.prototype.getLayers=function(){return this._layers},r.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(h,c){h.__dirty=h.__used=!1});function e(h){a&&(a.__endIndex!==h&&(a.__dirty=!0),a.__endIndex=h)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var a=null,o=0,s,u;for(u=0;u<t.length;u++){var n=t[u],l=n.zlevel,f=void 0;s!==l&&(s=l,o=0),n.incremental?(f=this.getLayer(l+jx,this._needsManuallyCompositing),f.incremental=!0,o=1):f=this.getLayer(l+(o>0?ma:0),this._needsManuallyCompositing),f.__builtin__||Qu("ZLevel "+l+" has been used by unkown layer "+f.id),f!==a&&(f.__used=!0,f.__startIndex!==u&&(f.__dirty=!0),f.__startIndex=u,f.incremental?f.__drawIndex=-1:f.__drawIndex=u,e(u),a=f),n.__dirty&Gt&&!n.__inHover&&(f.__dirty=!0,f.incremental&&f.__drawIndex<0&&(f.__drawIndex=u))}e(u),this.eachBuiltinLayer(function(h,c){!h.__used&&h.getElementCount()>0&&(h.__dirty=!0,h.__startIndex=h.__endIndex=h.__drawIndex=0),h.__dirty&&h.__drawIndex<0&&(h.__drawIndex=h.__startIndex)})},r.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},r.prototype._clearLayer=function(t){t.clear()},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t,D(this._layers,function(e){e.setUnpainted()})},r.prototype.configLayer=function(t,e){if(e){var i=this._layerConfig;i[t]?ut(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+ma){var o=this._layers[a];ut(o,i[t],!0)}}}},r.prototype.delLayer=function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(nt(i,t),1))},r.prototype.resize=function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,a=this.root;if(t!=null&&(n.width=t),e!=null&&(n.height=e),t=sa(a,0,n),e=sa(a,1,n),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(t==null||e==null)return;this._width=t,this._height=e,this.getLayer(xr).resize(t,e)}return this},r.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},r.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},r.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[xr].dom;var e=new $s("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var i=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,a=e.dom.height;this.eachLayer(function(h){h.__builtin__?i.drawImage(h.dom,0,0,n,a):h.renderToCanvas&&(i.save(),h.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),u=0,l=s.length;u<l;u++){var f=s[u];Pr(i,f,o,u===l-1)}return e.dom},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r}();function nD(r){r.registerPainter("canvas",rC)}export{di as $,_t as A,J as B,Er as C,Sb as D,Ce as E,ot as F,fe as G,r1 as H,e1 as I,a1 as J,O as K,wi as L,OC as M,AC as N,bC as O,at as P,SC as Q,Pt as R,Br as S,ke as T,H as U,xp as V,t1 as W,zs as X,At as Y,X as Z,F as _,yl as a,Hf as a$,lt as a0,Ke as a1,dn as a2,Ln as a3,nt as a4,ba as a5,Ft as a6,yt as a7,Xm as a8,Tt as a9,c1 as aA,jC as aB,Sx as aC,Vc as aD,W as aE,bi as aF,fo as aG,hn as aH,C_ as aI,es as aJ,LC as aK,gC as aL,lo as aM,kC as aN,T1 as aO,yw as aP,Sn as aQ,tu as aR,el as aS,yu as aT,Qe as aU,hC as aV,bl as aW,Rn as aX,pg as aY,Xl as aZ,si as a_,Et as aa,Rl as ab,tt as ac,MC as ad,mn as ae,B as af,DC as ag,Z as ah,Lt as ai,je as aj,M1 as ak,he as al,Oe as am,gi as an,Oy as ao,B1 as ap,$ as aq,le as ar,xl as as,k1 as at,Qm as au,O1 as av,Fr as aw,ft as ax,So as ay,Tl as az,In as b,Sl as b$,Ia as b0,ug as b1,Re as b2,em as b3,gd as b4,mS as b5,vn as b6,kb as b7,ht as b8,mx as b9,Nm as bA,sC as bB,Uc as bC,$m as bD,KC as bE,xC as bF,zl as bG,GC as bH,HC as bI,Ka as bJ,Nv as bK,Mc as bL,be as bM,sT as bN,zC as bO,YC as bP,VC as bQ,QC as bR,JC as bS,cT as bT,UC as bU,NC as bV,WC as bW,Xv as bX,Or as bY,n1 as bZ,qT as b_,iD as ba,rD as bb,eD as bc,tD as bd,nD as be,ZC as bf,De as bg,BC as bh,FC as bi,np as bj,hT as bk,lT as bl,vT as bm,vC as bn,fC as bo,Qa as bp,Pd as bq,Oc as br,pi as bs,gt as bt,dl as bu,yn as bv,An as bw,mC as bx,Xy as by,Cl as bz,Hl as c,Bt as c0,aC as c1,Dc as c2,xe as c3,oC as c4,mi as c5,se as c6,pC as c7,uC as c8,lC as c9,an as cA,CC as cB,m0 as cC,wa as cD,PC as cE,cC as cF,RC as cG,yC as ca,XC as cb,oT as cc,yS as cd,IC as ce,Dp as cf,Iu as cg,Eu as ch,et as ci,Mo as cj,Sd as ck,_C as cl,Qi as cm,dC as cn,ci as co,oh as cp,c_ as cq,nC as cr,V_ as cs,H_ as ct,qn as cu,il as cv,pT as cw,$C as cx,qC as cy,gT as cz,TC as d,D as e,Je as f,it as g,k as h,Ad as i,du as j,l_ as k,pu as l,EC as m,v_ as n,RS as o,Od as p,st as q,ue as r,f1 as s,wC as t,xn as u,ut as v,K as w,Y as x,_l as y,gl as z};
//# sourceMappingURL=installCanvasRenderer-DFpQ5KDo.js.map
