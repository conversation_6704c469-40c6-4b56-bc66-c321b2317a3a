{"version": 3, "file": "EditValue.vue_vue_type_script_setup_true_lang-CWpIX5x3.js", "sources": ["../../src/components/general/EditValue.vue"], "sourcesContent": ["<template>\n  <form class=\"d-flex flex-row\" @submit.prevent=\"saveNewName\">\n    <div class=\"flex-grow-1\">\n      <slot v-if=\"mode === EditState.None\"> </slot>\n      <b-form-input v-else v-model=\"localName\" size=\"sm\"> </b-form-input>\n    </div>\n    <div\n      class=\"flex-grow-2 mt-auto d-flex gap-1 ms-1\"\n      :class=\"alignVertical ? 'flex-column' : 'flex-row'\"\n    >\n      <template v-if=\"allowEdit && mode === EditState.None\">\n        <b-button\n          size=\"sm\"\n          variant=\"secondary\"\n          :title=\"`Edit this ${editableName}.`\"\n          @click=\"mode = EditState.Editing\"\n        >\n          <i-mdi-pencil />\n        </b-button>\n        <b-button\n          v-if=\"allowDuplicate\"\n          size=\"sm\"\n          variant=\"secondary\"\n          :title=\"`Duplicate ${editableName}.`\"\n          @click=\"duplicate\"\n        >\n          <i-mdi-content-copy />\n        </b-button>\n        <b-button\n          size=\"sm\"\n          variant=\"secondary\"\n          :title=\"`Delete this ${editableName}.`\"\n          @click=\"$emit('delete', modelValue)\"\n        >\n          <i-mdi-delete />\n        </b-button>\n      </template>\n      <b-button\n        v-if=\"allowAdd && mode === EditState.None\"\n        size=\"sm\"\n        :title=\"`Add new ${editableName}.`\"\n        variant=\"primary\"\n        @click=\"addNewClick\"\n        ><i-mdi-plus-box-outline />\n      </b-button>\n      <template v-if=\"mode !== EditState.None\">\n        <b-button\n          size=\"sm\"\n          :title=\"`Add new ${editableName}`\"\n          variant=\"primary\"\n          @click=\"saveNewName\"\n        >\n          <i-mdi-check />\n        </b-button>\n        <b-button size=\"sm\" title=\"Abort\" variant=\"secondary\" @click=\"abort\">\n          <i-mdi-close />\n        </b-button>\n      </template>\n    </div>\n  </form>\n</template>\n\n<script setup lang=\"ts\">\nconst props = defineProps({\n  modelValue: {\n    type: String,\n    required: true,\n  },\n  allowEdit: {\n    type: Boolean,\n    default: false,\n  },\n  allowAdd: {\n    type: Boolean,\n    default: false,\n  },\n  allowDuplicate: {\n    type: Boolean,\n    default: false,\n  },\n  editableName: {\n    type: String,\n    required: true,\n  },\n  alignVertical: {\n    type: Boolean,\n    default: false,\n  },\n});\n\nconst emit = defineEmits<{\n  delete: [value: string];\n  new: [value: string];\n  duplicate: [oldName: string, newName: string];\n  rename: [oldName: string, newName: string];\n}>();\n\nenum EditState {\n  None,\n  Editing,\n  Adding,\n  Duplicating,\n}\n\nconst localName = ref<string>('');\nconst mode = ref<EditState>(EditState.None);\nonMounted(() => {\n  localName.value = props.modelValue;\n});\n\nfunction abort() {\n  mode.value = EditState.None;\n  localName.value = props.modelValue;\n}\n\nfunction duplicate() {\n  localName.value = localName.value + ' (copy)';\n  mode.value = EditState.Duplicating;\n}\n\nfunction addNewClick() {\n  localName.value = '';\n  mode.value = EditState.Adding;\n}\n\nwatch(\n  () => props.modelValue,\n  () => {\n    localName.value = props.modelValue;\n  },\n);\n\nfunction saveNewName() {\n  if (mode.value === EditState.Adding) {\n    emit('new', localName.value);\n  } else if (mode.value === EditState.Duplicating) {\n    emit('duplicate', props.modelValue, localName.value);\n  } else {\n    // Editing\n    emit('rename', props.modelValue, localName.value);\n  }\n  mode.value = EditState.None;\n}\n</script>\n"], "names": ["props", "__props", "emit", "__emit", "localName", "ref", "mode", "onMounted", "abort", "duplicate", "addNewClick", "watch", "saveNewName"], "mappings": "q4BA+DA,MAAMA,EAAQC,EA2BRC,EAAOC,EAcPC,EAAYC,EAAY,EAAE,EAC1BC,EAAOD,EAAe,GAC5BE,EAAU,IAAM,CACdH,EAAU,MAAQJ,EAAM,UAAA,CACzB,EAED,SAASQ,GAAQ,CACfF,EAAK,MAAQ,EACbF,EAAU,MAAQJ,EAAM,UAC1B,CAEA,SAASS,GAAY,CACTL,EAAA,MAAQA,EAAU,MAAQ,UACpCE,EAAK,MAAQ,CACf,CAEA,SAASI,GAAc,CACrBN,EAAU,MAAQ,GAClBE,EAAK,MAAQ,CACf,CAEAK,EACE,IAAMX,EAAM,WACZ,IAAM,CACJI,EAAU,MAAQJ,EAAM,UAC1B,CAAA,EAGF,SAASY,GAAc,CACjBN,EAAK,QAAU,EACZJ,EAAA,MAAOE,EAAU,KAAK,EAClBE,EAAK,QAAU,EACxBJ,EAAK,YAAaF,EAAM,WAAYI,EAAU,KAAK,EAGnDF,EAAK,SAAUF,EAAM,WAAYI,EAAU,KAAK,EAElDE,EAAK,MAAQ,CACf"}