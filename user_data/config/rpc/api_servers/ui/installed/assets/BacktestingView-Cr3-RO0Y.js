import{o as n,c as k,a as r,g as T,u as H,r as M,k as A,M as J,b as s,w as i,h as t,e as x,x as B,F as I,K as O,y as h,A as p,N as ne,v as N,O as $e,P as ie,_ as K,Q as X,W as G,p as Q,d as Y,n as w,m as ae,R as we,S as ee,t as W,U as Re,l as Z,z as U,$ as Ve,q as se,V as Se,i as ce,X as Ce,Y as re,Z as de,a0 as Te,a1 as qe,a2 as le,L as me,a3 as j,a4 as Ae,a5 as Le,a6 as Me,a7 as Ie,j as _e,a8 as Ue,a9 as Pe,aa as ze,ab as De,ac as Ne,ad as He,ae as Fe,af as Ee,ag as je}from"./index-B2p78N-x.js";import{c as ue}from"./TradeList-BP6RUI29.js";import{_ as Oe}from"./chevron-down-BTtjNJA7.js";import{_ as We,a as Ke,b as Ge,c as fe}from"./InfoBox.vue_vue_type_script_setup_true_lang-Dr_hBaFv.js";import{_ as Qe}from"./CandleChartContainer-DXl2ryLC.js";import{a as Ye,_ as Ze}from"./MessageBox.vue_vue_type_script_setup_true_lang-t_g1ZU8z.js";import{_ as Je,a as Xe,b as et}from"./TradesLogChart-DDJ_fK8s.js";import{_ as tt,a as at,b as st}from"./StrategySelect.vue_vue_type_script_setup_true_lang-DCnRMwU3.js";import"./EditValue.vue_vue_type_script_setup_true_lang-CWpIX5x3.js";import"./plus-box-outline-DIIEsppr.js";import"./installCanvasRenderer-DFpQ5KDo.js";import"./install-DosLD5hS.js";import"./createSeriesDataSimply-Bq9ycDaP.js";const ot={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},lt=r("path",{fill:"currentColor",d:"M8.59 16.58L13.17 12L8.59 7.41L10 6l6 6l-6 6z"},null,-1),nt=[lt];function it(e,a){return n(),k("svg",ot,[...nt])}const oe={name:"mdi-chevron-right",render:it},ct={class:"d-flex"},rt={class:"d-flex flex-column"},dt={key:0},mt={class:"px-3 m-0"},_t=T({__name:"TradeListNav",props:{trades:{required:!0,type:Array},backtestMode:{required:!1,default:!1,type:Boolean}},emits:["trade-select"],setup(e,{emit:a}){const o=e,u=a,m=H(),l=M({}),d=M(!0),c=y=>{l.value=y,u("trade-select",y)},_=A(()=>o.trades.slice().sort((y,b)=>d.value?b.open_timestamp-y.open_timestamp:y.open_timestamp-b.open_timestamp)),g=M(_.value.map(()=>!1));return J(()=>m.activeBot.selectedPair,()=>{g.value=_.value.map(()=>!1)}),(y,b)=>{const R=ne,V=We,S=Ke,$=Ge,f=oe,L=Oe,F=N,E=$e,v=ie;return n(),k("div",null,[s(v,null,{default:i(()=>[s(R,{button:"",class:"d-flex flex-wrap justify-content-center align-items-center",title:"Trade Navigation",onClick:b[0]||(b[0]=C=>d.value=!t(d))},{default:i(()=>[x("Trade Navigation "+B(t(d)?"↓":"↑"),1)]),_:1}),(n(!0),k(I,null,O(t(_),(C,P)=>(n(),h(R,{key:C.open_timestamp,button:"",class:"d-flex flex-column py-1 pe-1 align-items-stretch",title:`${C.pair}`,active:C.open_timestamp===t(l).open_timestamp,onClick:z=>c(C)},{default:i(()=>[r("div",ct,[r("div",rt,[r("div",null,[t(m).activeBot.botState.trading_mode!=="spot"?(n(),k("span",dt,B(C.is_short?"S-":"L-"),1)):p("",!0),s(V,{date:C.open_timestamp},null,8,["date"])]),s(S,{trade:C,class:"my-1"},null,8,["trade"]),e.backtestMode?(n(),h($,{key:0,"profit-ratio":C.profit_ratio,"stake-currency":t(m).activeBot.stakeCurrency},null,8,["profit-ratio","stake-currency"])):p("",!0)]),s(F,{size:"sm",class:"ms-auto mt-auto",variant:"outline-secondary",onClick:z=>t(g)[P]=!t(g)[P]},{default:i(()=>[t(g)[P]?p("",!0):(n(),h(f,{key:0,width:"24",height:"24"})),t(g)[P]?(n(),h(L,{key:1,width:"24",height:"24"})):p("",!0)]),_:2},1032,["onClick"])]),s(E,{modelValue:t(g)[P],"onUpdate:modelValue":z=>t(g)[P]=z},{default:i(()=>{var z;return[r("ul",mt,[(n(!0),k(I,null,O((z=C.orders)==null?void 0:z.filter(D=>D.order_filled_timestamp!==null),D=>(n(),k("li",{key:D.order_timestamp},B(D.ft_order_side)+" "+B(D.amount)+" at "+B(D.safe_price),1))),128))])]}),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["title","active","onClick"]))),128)),e.trades.length===0?(n(),h(R,{key:0},{default:i(()=>[x("No trades to show...")]),_:1})):p("",!0)]),_:1})])}}}),ut=K(_t,[["__scopeId","data-v-a94a2947"]]),ft={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},pt=r("path",{fill:"currentColor",d:"M15.41 16.58L10.83 12l4.58-4.59L14 6l-6 6l6 6z"},null,-1),bt=[pt];function kt(e,a){return n(),k("svg",ft,[...bt])}const pe={name:"mdi-chevron-left",render:kt},gt=e=>(Q("data-v-b07a5f59"),e=e(),Y(),e),ht={class:"d-flex flex-row mb-1 align-items-center"},yt={class:"me-2"},vt={class:"flex-fill"},xt=gt(()=>r("br",null,null,-1)),Bt={class:"col-md-1 text-end"},$t={class:"text-center d-flex flex-row h-100 align-items-stretch"},wt=T({__name:"BacktestResultChart",props:{timeframe:{required:!0,type:String},strategy:{required:!0,type:String},freqaiModel:{required:!1,default:void 0,type:String},timerange:{required:!0,type:String},pairlist:{required:!0,type:Array},trades:{required:!0,type:Array}},setup(e){const a=H(),o=M({right:!0,left:!0}),u=M(),m=l=>{u.value={startValue:l.open_timestamp,endValue:l.close_timestamp}};return(l,d)=>{const c=oe,_=pe,g=N,y=Ye,b=Qe,R=ut,V=ue,S=G;return n(),k("div",null,[r("div",ht,[r("div",yt,[s(g,{"aria-label":"Close",title:"Pair Navigation",variant:"outline-secondary",size:"sm",onClick:d[0]||(d[0]=$=>t(o).left=!t(o).left)},{default:i(()=>[t(o).left?p("",!0):(n(),h(c,{key:0,width:"24",height:"24"})),t(o).left?(n(),h(_,{key:1,width:"24",height:"24"})):p("",!0)]),_:1})]),r("span",vt,[x(" Graph will always show the latest values for the selected strategy. "),xt,x(" Timerange: "+B(e.timerange)+" - "+B(e.strategy),1)]),r("div",Bt,[s(g,{"aria-label":"Close",variant:"outline-secondary",title:"Trade Navigation",size:"sm",onClick:d[1]||(d[1]=$=>t(o).right=!t(o).right)},{default:i(()=>[t(o).right?(n(),h(c,{key:0,width:"24",height:"24"})):p("",!0),t(o).right?p("",!0):(n(),h(_,{key:1,width:"24",height:"24"}))]),_:1})])]),r("div",$t,[s(X,{name:"fadeleft"},{default:i(()=>[t(o).left?(n(),h(y,{key:0,class:"col-md-2 overflow-y-auto overflow-x-hidden",style:{"max-height":"calc(100vh - 200px)"},pairlist:e.pairlist,trades:e.trades,"sort-method":"profit","backtest-mode":!0},null,8,["pairlist","trades"])):p("",!0)]),_:1}),s(b,{"available-pairs":e.pairlist,"historic-view":!0,timeframe:e.timeframe,timerange:e.timerange,strategy:e.strategy,trades:e.trades,class:"flex-shrink-1 candle-chart-container w-100 px-0 h-100 align-self-stretch","slider-position":t(u),"freqai-model":e.freqaiModel},null,8,["available-pairs","timeframe","timerange","strategy","trades","slider-position","freqai-model"]),s(X,{name:"fade"},{default:i(()=>[t(o).right?(n(),h(R,{key:0,class:"overflow-y-auto col-md-2 overflow-x-visible",style:{"max-height":"calc(100vh - 200px)"},trades:e.trades.filter($=>$.pair===t(a).activeBot.selectedPair),onTradeSelect:m},null,8,["trades"])):p("",!0)]),_:1})]),s(S,{header:"Single trades",class:"row mt-2 w-100"},{default:i(()=>[s(V,{class:"row trade-history mt-2 w-100",trades:e.trades,"show-filter":!0},null,8,["trades"])]),_:1})])}}}),Rt=K(wt,[["__scopeId","data-v-b07a5f59"]]),Vt=e=>(Q("data-v-bb4fb14c"),e=e(),Y(),e),St={class:"text-center flex-fill mt-2 d-flex flex-column"},Ct=Vt(()=>r("hr",null,null,-1)),Tt=T({__name:"BacktestGraphs",props:{trades:{required:!0,type:Array}},setup(e){return(a,o)=>{const u=Je,m=Xe,l=et;return n(),k("div",St,[s(u,{trades:e.trades,class:"trades-log"},null,8,["trades"]),s(m,{trades:e.trades,class:"cum-profit","show-title":!0},null,8,["trades"]),Ct,s(l,{class:"mt-3",trades:e.trades,"show-title":!0},null,8,["trades"])])}}}),qt=K(Tt,[["__scopeId","data-v-bb4fb14c"]]),At={class:"d-flex flex-column me-2 text-start"},Lt={class:"fw-bold"},Mt={class:"text-small"},It={key:0,class:"text-small",style:{"white-space":"pre-wrap"}},be=T({__name:"BacktestResultSelectEntry",props:{backtestResult:{required:!0,type:Object},selectedBacktestResultKey:{required:!1,default:"",type:String},canUseModify:{required:!1,default:!1,type:Boolean}},setup(e){return(a,o)=>(n(),k("div",At,[r("div",Lt,B(e.backtestResult.metadata.strategyName)+" - "+B(e.backtestResult.strategy.timeframe),1),r("div",Mt," TradeCount: "+B(e.backtestResult.strategy.total_trades)+" - Profit: "+B(t(w)(e.backtestResult.strategy.profit_total)),1),e.canUseModify?(n(),k("div",It,B(e.backtestResult.metadata.notes),1)):p("",!0)]))}});function te(e,a){return Object.entries(e).reduce((u,[m,l])=>(l.forEach(d=>{const[c,_]=Object.entries(d)[0],g=u.find(y=>y[a]===c);g?g[m]=_:u.push({[a]:c,[m]:_})}),u),[])}function Ut(e){return e.slice().sort((o,u)=>o.profit_ratio-u.profit_ratio)}function Pt(e){if(e.length===0)return"N/A";const a=e[e.length-1];return`${a.pair} ${w(a.profit_ratio,2)}`}function zt(e){if(e.length===0)return"N/A";const a=e[0];return`${a.pair} ${w(a.profit_ratio,2)}`}function ke(e,a){return u=>`${ae(u,e)} ${a}`}function ge(e){const a=Ut(e.trades),o=Pt(a),u=zt(a),m=e.results_per_pair[e.results_per_pair.length-1],l=ke(e.stake_currency_decimals,e.stake_currency),d=e.trade_count_short&&e.trade_count_short>0?[{"___ ":"___"},{"Long / Short":`${e.trade_count_long} / ${e.trade_count_short}`},{"Total profit Long":`${w(e.profit_total_long||0)} | ${l(e.profit_total_long_abs)}`},{"Total profit Short":`${w(e.profit_total_short||0)} | ${l(e.profit_total_short_abs)}`}]:[];return[{"Total Profit":`${w(e.profit_total)} | ${l(e.profit_total_abs)}`},{CAGR:`${e.cagr?w(e.cagr):"N/A"}`},{Sortino:`${e.sortino?e.sortino.toFixed(2):"N/A"}`},{Sharpe:`${e.sharpe?e.sharpe.toFixed(2):"N/A"}`},{Calmar:`${e.calmar?e.calmar.toFixed(2):"N/A"}`},{[`Expectancy ${e.expectancy_ratio?"(ratio)":""}`]:`${e.expectancy?e.expectancy_ratio?e.expectancy.toFixed(2)+" ("+e.expectancy_ratio.toFixed(2)+")":e.expectancy.toFixed(2):"N/A"}`},{"Profit factor":`${e.profit_factor?ae(e.profit_factor,3):"N/A"}`},{"Total trades / Daily Avg Trades":`${e.total_trades} / ${e.trades_per_day}`},{"Best day":`${w(e.backtest_best_day,2)} | ${l(e.backtest_best_day_abs)}`},{"Worst day":`${w(e.backtest_worst_day,2)} | ${l(e.backtest_worst_day_abs)}`},{"Win/Draw/Loss":`${m.wins} / ${m.draws} / ${m.losses} ${we(m.winrate)?"(WR: "+w(e.results_per_pair[e.results_per_pair.length-1].winrate??0,2)+")":""}`},{"Days win/draw/loss":`${e.winning_days} / ${e.draw_days} / ${e.losing_days}`},{"Avg. Duration winners":ee(e.winner_holding_avg_s)},{"Avg. Duration Losers":ee(e.loser_holding_avg_s)},{"Max Consecutive Wins / Loss":e.max_consecutive_wins===void 0?"N/A":`${e.max_consecutive_wins} / ${e.max_consecutive_losses}`},{"Rejected entry signals":e.rejected_signals},{"Entry/Exit timeouts":`${e.timedout_entry_orders} / ${e.timedout_exit_orders}`},{"Canceled Trade Entries":e.canceled_trade_entries??"N/A"},{"Canceled Entry Orders":e.canceled_entry_orders??"N/A"},{"Replaced Entry Orders":e.replaced_entry_orders??"N/A"},...d,{___:"___"},{"Min balance":l(e.csum_min)},{"Max balance":l(e.csum_max)},{"Market change":w(e.market_change)},{"___  ":"___"},{"Max Drawdown (Account)":w(e.max_drawdown_account)},{"Max Drawdown ABS":l(e.max_drawdown_abs)},{"Drawdown high | low":`${l(e.max_drawdown_high)} | ${l(e.max_drawdown_low)}`},{"Drawdown start":W(e.drawdown_start_ts)},{"Drawdown end":W(e.drawdown_end_ts)},{"___   ":"___"},{"Best Pair":`${e.best_pair.key} ${w(e.best_pair.profit_total)}`},{"Worst Pair":`${e.worst_pair.key} ${w(e.worst_pair.profit_total)}`},{"Best single Trade":o},{"Worst single Trade":u}]}function Dt(e){const a=ke(e.stake_currency_decimals,e.stake_currency);return[{"Backtesting from":W(e.backtest_start_ts)},{"Backtesting to":W(e.backtest_end_ts)},{"BT execution time":ee(e.backtest_run_end_ts-e.backtest_run_start_ts)},{"Max open trades":e.max_open_trades},{Timeframe:e.timeframe},{"Timeframe Detail":e.timeframe_detail||"N/A"},{Timerange:e.timerange},{Stoploss:w(e.stoploss,2)},{"Trailing Stoploss":e.trailing_stop},{"Trail only when offset is reached":e.trailing_only_offset_is_reached},{"Trailing Stop positive":e.trailing_stop_positive},{"Trailing stop positive offset":e.trailing_stop_positive_offset},{"Custom Stoploss":e.use_custom_stoploss},{ROI:JSON.stringify(e.minimal_roi)},{"Use Exit Signal":e.use_exit_signal!==void 0?e.use_exit_signal:e.use_sell_signal},{"Exit profit only":e.exit_profit_only!==void 0?e.exit_profit_only:e.sell_profit_only},{"Exit profit offset":e.exit_profit_offset!==void 0?e.exit_profit_offset:e.sell_profit_offset},{"Enable protections":e.enable_protections},{"Starting balance":a(e.starting_balance)},{"Final balance":a(e.final_balance)},{"Avg. stake amount":a(e.avg_stake_amount)},{"Total trade volume":a(e.total_volume)}]}const Nt={class:"px-0 mw-100"},Ht=r("div",{class:"d-flex justify-content-center"},[r("h3",null,"Backtest-result comparison")],-1),Ft={class:"d-flex flex-column text-start ms-0 me-2 gap-2"},Et={class:"d-flex flex-column flex-xl-row"},jt={class:"px-0 px-xl-0 pt-2 pt-xl-0 ps-xl-1 flex-fill"},Ot=T({__name:"BacktestResultComparison",props:{backtestResults:{required:!0,type:Object}},setup(e){const a=e,o=A(()=>{const m={};return Object.entries(a.backtestResults).forEach(([l,d])=>{const c=ge(d.strategy);m[l]=c}),console.log(m),te(m,"metric")}),u=A(()=>{const m=[{key:"metric",label:"Metric"}];return Object.entries(a.backtestResults).forEach(([l,d])=>{m.push({key:l,label:d.metadata.strategyName})}),m});return(m,l)=>{const d=be,c=Z;return n(),k("div",Nt,[Ht,r("div",Ft,[r("div",Et,[r("div",jt,[s(c,{bordered:"",items:t(o),fields:t(u)},Re({_:2},[O(Object.entries(e.backtestResults),([_,g])=>({name:`head(${_})`,fn:i(()=>[s(d,{"backtest-result":g},null,8,["backtest-result"])])}))]),1032,["items","fields"])])])])])}}}),Wt=T({__name:"BacktestResultPeriodBreakdown",props:{periodicBreakdown:{type:Object,required:!0}},setup(e){const a=[{value:"day",text:"Days"},{value:"week",text:"Weeks"},{value:"month",text:"Months"}],o=M("month"),u=A(()=>[{key:"date",label:"Date"},{key:"wins",label:"Wins"},{key:"draws",label:"Draws"},{key:"loses",label:"Losses"}]);return(m,l)=>{const d=Ve,c=Z;return n(),k(I,null,[s(d,{id:"order-direction",modelValue:t(o),"onUpdate:modelValue":l[0]||(l[0]=_=>U(o)?o.value=_:null),options:a,name:"radios-btn-default",size:"sm",buttons:"",style:{"min-width":"10em"},"button-variant":"outline-primary"},null,8,["modelValue"]),s(c,{small:"",hover:"",stacked:"sm",items:e.periodicBreakdown[t(o)],fields:t(u)},null,8,["items","fields"])],64)}}}),Kt=T({__name:"BacktestResultTablePer",props:{title:{type:String,required:!0},results:{type:Array,required:!0},stakeCurrency:{type:String,required:!0},stakeCurrencyDecimals:{type:Number,required:!0},keyHeader:{type:String,default:""},keyHeaders:{type:Array,default:()=>[]}},setup(e){const a=e,o=A(()=>a.results),u=A(()=>{const m=[];if(a.keyHeaders.length>0)for(let l=0;l<a.keyHeaders.length;l+=1)m.push({key:"key",label:a.keyHeaders[l],formatter:(d,c,_)=>Array.isArray(d)?d[l]:d||_.exit_reason||"OTHER"});else m.push({key:"key",label:a.keyHeader,formatter:(l,d,c)=>l||c.exit_reason||"OTHER"});return[...m,{key:"trades",label:"Trades"},{key:"profit_mean",label:"Avg Profit %",formatter:l=>w(l,2)},{key:"profit_total_abs",label:`Tot Profit ${a.stakeCurrency}`,formatter:l=>ae(l,a.stakeCurrencyDecimals)},{key:"profit_total",label:"Tot Profit %",formatter:l=>w(l,2)},{key:"wins",label:"Wins"},{key:"draws",label:"Draws"},{key:"losses",label:"Losses"}]});return(m,l)=>{const d=Z,c=G;return n(),h(c,{header:e.title},{default:i(()=>[s(d,{small:"",hover:"",stacked:"sm",items:t(o),fields:t(u)},null,8,["items","fields"])]),_:1},8,["header"])}}}),Gt={class:"px-0 mw-100"},Qt={class:"d-flex justify-content-center"},Yt={class:"d-flex flex-column text-start ms-0 me-2 gap-2"},Zt={class:"d-flex flex-column flex-xl-row"},Jt={class:"px-0 px-xl-0 pe-xl-1 flex-fill"},Xt={class:"px-0 px-xl-0 pt-2 pt-xl-0 ps-xl-1 flex-fill"},ea=T({__name:"BacktestResultAnalysis",props:{backtestResult:{required:!0,type:Object}},setup(e){const a=e,o=A(()=>{const d=ge(a.backtestResult);return te({value:d},"metric")}),u=A(()=>{const d=Dt(a.backtestResult);return te({value:d},"setting")}),m=[{key:"metric",label:"Metric"},{key:"value",label:"Value"}],l=[{key:"setting",label:"Setting"},{key:"value",label:"Value"}];return(d,c)=>{const _=Z,g=G,y=Kt,b=Wt,R=ue;return n(),k("div",Gt,[r("div",Qt,[r("h3",null,"Backtest-result for "+B(e.backtestResult.strategy_name),1)]),r("div",Yt,[r("div",Zt,[r("div",Jt,[s(g,{header:"Strategy settings"},{default:i(()=>[s(_,{small:"",borderless:"",items:t(u),fields:l},null,8,["items"])]),_:1})]),r("div",Xt,[s(g,{header:"Metrics"},{default:i(()=>[s(_,{small:"",borderless:"",items:t(o),fields:m},null,8,["items"])]),_:1})])]),s(y,{title:"Results per Enter tag",results:e.backtestResult.results_per_enter_tag,"stake-currency":e.backtestResult.stake_currency,"key-header":"Enter Tag","stake-currency-decimals":e.backtestResult.stake_currency_decimals},null,8,["results","stake-currency","stake-currency-decimals"]),s(y,{title:"Results per Exit reason",results:e.backtestResult.exit_reason_summary??[],"stake-currency":e.backtestResult.stake_currency,"key-header":"Exit Reason","stake-currency-decimals":e.backtestResult.stake_currency_decimals},null,8,["results","stake-currency","stake-currency-decimals"]),e.backtestResult.mix_tag_stats?(n(),h(y,{key:0,title:"Results Mixed Tag",results:e.backtestResult.mix_tag_stats??[],"stake-currency":e.backtestResult.stake_currency,"key-headers":["Enter Tag","Exit Tag"],"stake-currency-decimals":e.backtestResult.stake_currency_decimals},null,8,["results","stake-currency","stake-currency-decimals"])):p("",!0),s(y,{title:"Results per pair",results:e.backtestResult.results_per_pair,"stake-currency":e.backtestResult.stake_currency,"key-header":"Pair","stake-currency-decimals":e.backtestResult.stake_currency_decimals},null,8,["results","stake-currency","stake-currency-decimals"]),e.backtestResult.periodic_breakdown?(n(),h(g,{key:1,header:"Periodic breakdown"},{default:i(()=>[s(b,{"periodic-breakdown":e.backtestResult.periodic_breakdown},null,8,["periodic-breakdown"])]),_:1})):p("",!0),s(g,{header:"Single trades"},{default:i(()=>[s(R,{trades:e.backtestResult.trades,"show-filter":!0,"stake-currency":e.backtestResult.stake_currency},null,8,["trades","stake-currency"])]),_:1})])])}}}),ta={class:"w-100 d-flex"},aa={class:"ms-2"},sa=T({__name:"FreqaiModelSelect",props:{modelValue:{type:String,required:!0}},emits:["update:modelValue"],setup(e,{emit:a}){const o=e,u=a,m=H(),l=A({get(){return o.modelValue},set(d){u("update:modelValue",d)}});return se(()=>{m.activeBot.freqaiModelList.length===0&&m.activeBot.getFreqAIModelList()}),(d,c)=>{const _=Se,g=ce,y=N;return n(),k("div",null,[r("div",ta,[s(_,{id:"freqaiModel-select",modelValue:t(l),"onUpdate:modelValue":c[0]||(c[0]=b=>U(l)?l.value=b:null),options:t(m).activeBot.freqaiModelList},null,8,["modelValue","options"]),r("div",aa,[s(y,{onClick:t(m).activeBot.getFreqAIModelList},{default:i(()=>[s(g)]),_:1},8,["onClick"])])])])}}}),he=Ce("btStore",{state:()=>({strategy:"",selectedTimeframe:"",selectedDetailTimeframe:"",timerange:"",maxOpenTrades:"",stakeAmount:"",startingCapital:"",allowCache:!0,enableProtections:!1,stakeAmountUnlimited:!1,freqAI:{enabled:!1,model:"",identifier:""}}),getters:{canRunBacktest:e=>e.strategy!==""},actions:{}}),oa={class:"mb-2"},la=r("span",null,"Strategy",-1),na={class:"d-flex align-items-center"},ia={style:{"flex-basis":"100%"},class:"d-flex"},ca={class:"d-flex justify-content-center"},ra=r("span",{class:"me-2"},"Enable FreqAI:",-1),da=r("hr",null,null,-1),ma=r("h3",{class:"mt-3"},"Backtesting summary",-1),_a={class:"d-flex flex-wrap flex-md-nowrap justify-content-between justify-content-md-center"},ua=T({__name:"BacktestRun",setup(e){const a=H(),o=he();function u(){const m={strategy:o.strategy,timerange:o.timerange,enable_protections:o.enableProtections},l=parseInt(o.maxOpenTrades,10);if(l&&(m.max_open_trades=l),o.stakeAmountUnlimited)m.stake_amount="unlimited";else{const c=Number(o.stakeAmount);c&&(m.stake_amount=c.toString())}const d=Number(o.startingCapital);d&&(m.dry_run_wallet=d),o.selectedTimeframe&&(m.timeframe=o.selectedTimeframe),o.selectedDetailTimeframe&&(m.timeframe_detail=o.selectedDetailTimeframe),o.allowCache||(m.backtest_cache="none"),o.freqAI.enabled&&(m.freqaimodel=o.freqAI.model,o.freqAI.identifier!==""&&(m.freqai={identifier:o.freqAI.identifier})),a.activeBot.startBacktest(m)}return(m,l)=>{const d=tt,c=at,_=re,g=de,y=Te,b=fe,R=sa,V=st,S=G,$=N;return n(),k(I,null,[r("div",oa,[la,s(d,{modelValue:t(o).strategy,"onUpdate:modelValue":l[0]||(l[0]=f=>t(o).strategy=f)},null,8,["modelValue"])]),s(S,{disabled:t(a).activeBot.backtestRunning},{default:i(()=>[s(_,{"label-cols-lg":"2",label:"Backtest params","label-size":"sm","label-class":"fw-bold pt-0",class:"mb-0"},{default:i(()=>[s(_,{"label-cols-sm":"5",label:"Timeframe:","label-align-sm":"right","label-for":"timeframe-select"},{default:i(()=>[s(c,{id:"timeframe-select",modelValue:t(o).selectedTimeframe,"onUpdate:modelValue":l[1]||(l[1]=f=>t(o).selectedTimeframe=f)},null,8,["modelValue"])]),_:1}),s(_,{"label-cols-sm":"5",label:"Detail Timeframe:","label-align-sm":"right","label-for":"timeframe-detail-select",title:"Detail timeframe, to simulate intra-candle results. Not setting this will not use this functionality."},{default:i(()=>[s(c,{id:"timeframe-detail-select",modelValue:t(o).selectedDetailTimeframe,"onUpdate:modelValue":l[2]||(l[2]=f=>t(o).selectedDetailTimeframe=f),"below-timeframe":t(o).selectedTimeframe},null,8,["modelValue","below-timeframe"])]),_:1}),s(_,{"label-cols-sm":"5",label:"Max open trades:","label-align-sm":"right","label-for":"max-open-trades"},{default:i(()=>[s(g,{id:"max-open-trades",modelValue:t(o).maxOpenTrades,"onUpdate:modelValue":l[3]||(l[3]=f=>t(o).maxOpenTrades=f),placeholder:"Use strategy default",type:"number"},null,8,["modelValue"])]),_:1}),s(_,{"label-cols-sm":"5",label:"Starting capital:","label-align-sm":"right","label-for":"starting-capital"},{default:i(()=>[s(g,{id:"starting-capital",modelValue:t(o).startingCapital,"onUpdate:modelValue":l[4]||(l[4]=f=>t(o).startingCapital=f),placeholder:"Use config default",type:"number",step:"0.001"},null,8,["modelValue"])]),_:1}),s(_,{"label-cols-sm":"5",label:"Stake amount:","label-align-sm":"right","label-for":"stake-amount"},{default:i(()=>[r("div",na,[r("div",ia,[s(y,{id:"stake-amount-bool",modelValue:t(o).stakeAmountUnlimited,"onUpdate:modelValue":l[5]||(l[5]=f=>t(o).stakeAmountUnlimited=f)},{default:i(()=>[x("Unlimited stake")]),_:1},8,["modelValue"])]),s(g,{id:"stake-amount",modelValue:t(o).stakeAmount,"onUpdate:modelValue":l[6]||(l[6]=f=>t(o).stakeAmount=f),type:"number",placeholder:"Use strategy default",step:"0.01",style:{"flex-basis":"100%"},disabled:t(o).stakeAmountUnlimited},null,8,["modelValue","disabled"])])]),_:1}),s(_,{"label-cols-sm":"5",label:"Enable Protections:","label-align-sm":"right","label-for":"enable-protections",class:"align-items-center"},{default:i(()=>[s(y,{id:"enable-protections",modelValue:t(o).enableProtections,"onUpdate:modelValue":l[7]||(l[7]=f=>t(o).enableProtections=f)},null,8,["modelValue"])]),_:1}),t(a).activeBot.botApiVersion>=2.22?(n(),h(_,{key:0,"label-cols-sm":"5",label:"Cache Backtest results:","label-align-sm":"right","label-for":"enable-cache",class:"align-items-center"},{default:i(()=>[s(y,{id:"enable-cache",modelValue:t(o).allowCache,"onUpdate:modelValue":l[8]||(l[8]=f=>t(o).allowCache=f)},null,8,["modelValue"])]),_:1})):p("",!0),t(a).activeBot.botApiVersion>=2.22?(n(),k(I,{key:1},[s(_,{"label-cols-sm":"5",label:"Enable FreqAI:","label-align-sm":"right","label-for":"enable-freqai",class:"align-items-center"},{label:i(()=>[r("div",ca,[ra,s(b,{hint:"Assumes freqAI configuration is setup in the configuration, and the strategy is a freqAI strategy. Will fail if that's not the case."})])]),default:i(()=>[s(y,{id:"enable-freqai",modelValue:t(o).freqAI.enabled,"onUpdate:modelValue":l[9]||(l[9]=f=>t(o).freqAI.enabled=f)},null,8,["modelValue"])]),_:1}),t(o).freqAI.enabled?(n(),h(_,{key:0,"label-cols-sm":"5",label:"FreqAI identifier:","label-align-sm":"right","label-for":"freqai-identifier"},{default:i(()=>[s(g,{id:"freqai-identifier",modelValue:t(o).freqAI.identifier,"onUpdate:modelValue":l[10]||(l[10]=f=>t(o).freqAI.identifier=f),placeholder:"Use config default"},null,8,["modelValue"])]),_:1})):p("",!0),t(o).freqAI.enabled?(n(),h(_,{key:1,"label-cols-sm":"5",label:"FreqAI Model","label-align-sm":"right","label-for":"freqai-model"},{default:i(()=>[s(R,{id:"freqai-model",modelValue:t(o).freqAI.model,"onUpdate:modelValue":l[11]||(l[11]=f=>t(o).freqAI.model=f)},null,8,["modelValue"])]),_:1})):p("",!0)],64)):p("",!0),da,s(V,{modelValue:t(o).timerange,"onUpdate:modelValue":l[12]||(l[12]=f=>t(o).timerange=f),class:"mt-2"},null,8,["modelValue"])]),_:1})]),_:1},8,["disabled"]),ma,r("div",_a,[s($,{id:"start-backtest",variant:"primary",disabled:!t(o).canRunBacktest||t(a).activeBot.backtestRunning||!t(a).activeBot.canRunBacktest,class:"mx-1",onClick:u},{default:i(()=>[x(" Start backtest ")]),_:1},8,["disabled"]),s($,{variant:"secondary",disabled:t(a).activeBot.backtestRunning||!t(a).activeBot.canRunBacktest,class:"mx-1",onClick:t(a).activeBot.pollBacktest},{default:i(()=>[x(" Load backtest result ")]),_:1},8,["disabled","onClick"]),s($,{variant:"secondary",class:"mx-1",disabled:!t(a).activeBot.backtestRunning,onClick:t(a).activeBot.stopBacktest},{default:i(()=>[x("Stop Backtest")]),_:1},8,["disabled","onClick"]),s($,{variant:"secondary",class:"mx-1",disabled:t(a).activeBot.backtestRunning||!t(a).activeBot.canRunBacktest,onClick:t(a).activeBot.removeBacktest},{default:i(()=>[x("Reset Backtest")]),_:1},8,["disabled","onClick"])])],64)}}}),fa={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},pa=r("path",{fill:"currentColor",d:"M4 11v2h12l-5.5 5.5l1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5L16 11z"},null,-1),ba=[pa];function ka(e,a){return n(),k("svg",fa,[...ba])}const ga={name:"mdi-arrow-right",render:ka},ha=e=>(Q("data-v-d459ee7e"),e=e(),Y(),e),ya=ha(()=>r("p",null," Load Historic results from disk. You can click on multiple results to load all of them into freqUI. ",-1)),va={class:"d-flex align-items-center"},xa={key:0,class:"ms-1"},Ba={class:"d-flex align-items-center"},$a=T({__name:"BacktestHistoryLoad",setup(e){const a=H(),o=M(),u=M(""),m=qe(u,350,{maxWait:1e3});se(()=>{a.activeBot.getBacktestHistory()});function l(d){var _;const c={title:"Delete result",message:`Delete result ${d.filename} from disk?`,accept:()=>{a.activeBot.deleteBacktestHistoryResult(d)}};(_=o.value)==null||_.show(c)}return(d,c)=>{const _=ce,g=de,y=re,b=Ae,R=Le,V=Me,S=Ie,$=ga,f=N,L=_e,F=Ue,E=Pe;return n(),k(I,null,[r("div",null,[r("button",{class:"btn btn-secondary float-end",title:"Refresh","aria-label":"Refresh",onClick:c[0]||(c[0]=(...v)=>t(a).activeBot.getBacktestHistory&&t(a).activeBot.getBacktestHistory(...v))},[s(_)]),ya,r("div",va,[t(a).activeBot.backtestHistoryList.length>0?(n(),h(y,{key:0,class:"my-2","label-for":"trade-filter"},{default:i(()=>[s(g,{id:"trade-filter",modelValue:t(u),"onUpdate:modelValue":c[1]||(c[1]=v=>U(u)?u.value=v:null),type:"text",placeholder:"Filter Strategies",tilte:"Filter Strategies"},null,8,["modelValue"])]),_:1})):p("",!0)]),t(a).activeBot.backtestHistoryList.length>0?(n(),h(E,{key:0,responsive:"",small:"",class:"rounded-1 table-rounded-corners"},{default:i(()=>[s(V,null,{default:i(()=>[s(R,null,{default:i(()=>[s(b,null,{default:i(()=>[x("Strategy")]),_:1}),s(b,null,{default:i(()=>[x("Details")]),_:1}),s(b,null,{default:i(()=>[x("Backtest Time")]),_:1}),s(b,null,{default:i(()=>[x("Filename")]),_:1}),s(b,null,{default:i(()=>[x("Actions")]),_:1})]),_:1})]),_:1}),s(F,null,{default:i(()=>[(n(!0),k(I,null,O(t(a).activeBot.backtestHistoryList.filter(v=>v.filename.toLowerCase().includes(t(m).toLowerCase())||v.strategy.toLowerCase().includes(t(m).toLowerCase())),v=>(n(),h(R,{key:v.filename+v.strategy,role:"button",onClick:C=>t(a).activeBot.getBacktestHistoryResult(v)},{default:i(()=>[s(S,null,{default:i(()=>[x(B(v.strategy),1)]),_:2},1024),s(S,null,{default:i(()=>[r("strong",null,B(v.timeframe),1),v.backtest_start_ts&&v.backtest_end_ts?(n(),k("span",xa,B(t(le)(v.backtest_start_ts*1e3))+"-"+B(t(le)(v.backtest_end_ts*1e3)),1)):p("",!0)]),_:2},1024),s(S,null,{default:i(()=>[r("small",null,B(t(W)(v.backtest_start_time*1e3)),1)]),_:2},1024),s(S,null,{default:i(()=>[r("small",null,B(v.filename),1)]),_:2},1024),s(S,null,{default:i(()=>[r("div",Ba,[t(a).activeBot.botApiVersion>=2.32?(n(),h(fe,{key:0,class:me(v.notes?"opacity-100":"opacity-0"),hint:v.notes??""},null,8,["class","hint"])):p("",!0),t(a).activeBot.botApiVersion>=2.31?(n(),h(f,{key:1,class:"ms-1",size:"sm",title:"Load this Result.",disabled:v.run_id in t(a).activeBot.backtestHistory,onClick:j(C=>t(a).activeBot.getBacktestHistoryResult(v),["stop"])},{default:i(()=>[s($)]),_:2},1032,["disabled","onClick"])):p("",!0),t(a).activeBot.botApiVersion>=2.31?(n(),h(f,{key:2,class:"ms-1",size:"sm",title:"Delete this Result.",disabled:v.run_id in t(a).activeBot.backtestHistory,onClick:j(C=>l(v),["stop"])},{default:i(()=>[s(L)]),_:2},1032,["disabled","onClick"])):p("",!0)])]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1})):p("",!0)]),s(Ze,{ref_key:"msgBox",ref:o},null,512)],64)}}}),wa=K($a,[["__scopeId","data-v-d459ee7e"]]),Ra={class:"container d-flex flex-column align-items-stretch"},Va=r("h3",null,"Available results:",-1),Sa={class:"d-flex"},Ca=T({__name:"BacktestResultSelect",props:{backtestHistory:{required:!0,type:Object},selectedBacktestResultKey:{required:!1,default:"",type:String},canUseModify:{required:!1,default:!1,type:Boolean}},emits:["selectionChange","removeResult","updateResult"],setup(e,{emit:a}){const o=a,u=l=>{o("selectionChange",l)};function m(l,d){d.metadata.editing=!d.metadata.editing,d.metadata.filename&&o("updateResult",{run_id:l,notes:d.metadata.notes??"",filename:d.metadata.filename,strategy:d.metadata.strategyName})}return(l,d)=>{const c=be,_=ze,g=N,y=_e,b=De,R=Ne,V=ne,S=ie;return n(),k("div",Ra,[Va,s(S,{class:"ms-2"},{default:i(()=>[(n(!0),k(I,null,O(Object.entries(e.backtestHistory),([$,f])=>(n(),h(V,{key:$,button:"",active:$===e.selectedBacktestResultKey,class:"d-flex justify-content-between align-items-center py-1 pe-1",onClick:L=>u($)},{default:i(()=>[f.metadata.editing?p("",!0):(n(),k(I,{key:0},[s(c,{"backtest-result":f},null,8,["backtest-result"]),r("div",Sa,[e.canUseModify?(n(),h(g,{key:0,class:"flex-nowrap",size:"sm",title:"Modify result notes.",onClick:j(L=>f.metadata.editing=!f.metadata.editing,["stop"])},{default:i(()=>[s(_)]),_:2},1032,["onClick"])):p("",!0),s(g,{size:"sm",class:"flex-nowrap",title:"Delete this Result from UI.",onClick:j(L=>o("removeResult",$),["stop"])},{default:i(()=>[s(y)]),_:2},1032,["onClick"])])],64)),f.metadata.editing?(n(),k(I,{key:1},[s(b,{modelValue:f.metadata.notes,"onUpdate:modelValue":L=>f.metadata.notes=L,placeholder:"notes",size:"sm"},null,8,["modelValue","onUpdate:modelValue"]),s(g,{size:"sm",title:"Confirm",onClick:j(L=>m($,f),["stop"])},{default:i(()=>[s(R)]),_:2},1032,["onClick"])],64)):p("",!0)]),_:2},1032,["active","onClick"]))),128))]),_:1})])}}}),Ta={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},qa=r("path",{fill:"currentColor",d:"M21 8c-1.5 0-2.3 1.4-1.9 2.5l-3.6 3.6c-.3-.1-.7-.1-1 0l-2.6-2.6c.4-1.1-.4-2.5-1.9-2.5c-1.4 0-2.3 1.4-1.9 2.5L3.5 16c-1.1-.3-2.5.5-2.5 2c0 1.1.9 2 2 2c1.4 0 2.3-1.4 1.9-2.5l4.5-4.6c.3.1.7.1 1 0l2.6 2.6c-.3 1 .5 2.5 2 2.5s2.3-1.4 1.9-2.5l3.6-3.6c1.1.3 2.5-.5 2.5-1.9c0-1.1-.9-2-2-2m-6 1l.9-2.1L18 6l-2.1-.9L15 3l-.9 2.1L12 6l2.1.9zM3.5 11L4 9l2-.5L4 8l-.5-2L3 8l-2 .5L3 9z"},null,-1),Aa=[qa];function La(e,a){return n(),k("svg",Ta,[...Aa])}const Ma={name:"mdi-chart-timeline-variant-shimmer",render:La},Ia={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Ua=r("path",{fill:"currentColor",d:"M4 19v1h18v2H2V2h2v15c3 0 6-2 8.1-5.6c3-5 6.3-7.4 9.9-7.4v2c-2.8 0-5.5 2.1-8.1 6.5C11.3 16.6 7.7 19 4 19"},null,-1),Pa=[Ua];function za(e,a){return n(),k("svg",Ia,[...Pa])}const Da={name:"mdi-chart-bell-curve-cumulative",render:za},Na={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Ha=r("path",{fill:"currentColor",d:"M9 14H2v2h7v3l4-4l-4-4zm6-1v-3h7V8h-7V5l-4 4z"},null,-1),Fa=[Ha];function Ea(e,a){return n(),k("svg",Na,[...Fa])}const ja={name:"mdi-compare-horizontal",render:Ea},Oa={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Wa=r("path",{fill:"currentColor",d:"M17 16.88c.56 0 1 .44 1 1s-.44 1-1 1s-1-.45-1-1s.44-1 1-1m0-3c2.73 0 5.06 1.66 6 4c-.94 2.34-3.27 4-6 4s-5.06-1.66-6-4c.94-2.34 3.27-4 6-4m0 1.5a2.5 2.5 0 0 0 0 5a2.5 2.5 0 0 0 0-5M18 3H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h5.42c-.16-.32-.3-.66-.42-1c.12-.34.26-.68.42-1H4v-4h6v2.97c.55-.86 1.23-1.6 2-2.21V13h1.15c1.16-.64 2.47-1 3.85-1c1.06 0 2.07.21 3 .59V5c0-1.1-.9-2-2-2m-8 8H4V7h6zm8 0h-6V7h6z"},null,-1),Ka=[Wa];function Ga(e,a){return n(),k("svg",Oa,[...Ka])}const Qa={name:"mdi-table-eye",render:Ga},Ya={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Za=r("path",{fill:"currentColor",d:"M6.5 20q-2.28 0-3.89-1.57Q1 16.85 1 14.58q0-1.95 1.17-3.48q1.18-1.53 3.08-1.95q.58-2.02 2.14-3.4Q8.95 4.38 11 4.08v8.07L9.4 10.6L8 12l4 4l4-4l-1.4-1.4l-1.6 1.55V4.08q2.58.35 4.29 2.31T19 11q1.73.2 2.86 1.5q1.14 1.28 1.14 3q0 1.88-1.31 3.19T18.5 20Z"},null,-1),Ja=[Za];function Xa(e,a){return n(),k("svg",Ya,[...Ja])}const es={name:"mdi-cloud-download",render:Xa},ts=e=>(Q("data-v-f0dfb2b9"),e=e(),Y(),e),as={class:"d-flex flex-column pt-1 me-1",style:{height:"calc(100vh - 60px)"}},ss={class:"d-flex flex-row"},os=ts(()=>r("h2",{class:"ms-5"},"Backtesting",-1)),ls={key:0},ns={class:"w-100"},is={class:"mx-md-5 d-flex flex-wrap justify-content-md-center justify-content-between mb-4 gap-2"},cs={class:"d-flex flex-md-row"},rs={class:"d-flex flex-column flex-fill mw-100"},ds={class:"d-md-flex"},ms={key:0,class:"flex-fill d-flex flex-column bt-config"},_s={key:1,class:"flex-fill d-flex flex-column bt-config"},us={key:0,class:"text-center w-100 mt-2"},fs=T({__name:"BacktestingView",setup(e){const a=H(),o=he(),u=A(()=>a.activeBot.backtestHistory?Object.keys(a.activeBot.backtestHistory).length!==0:!1),m=A(()=>a.activeBot.backtestHistory?Object.keys(a.activeBot.backtestHistory).length>1:!1),l=A(()=>{try{return a.activeBot.selectedBacktestResult.timeframe}catch{return""}}),d=M(!1),c=M("run"),_=M(null),g=()=>{o.strategy=a.activeBot.selectedBacktestResult.strategy_name,a.activeBot.getStrategy(o.strategy),o.selectedTimeframe=a.activeBot.selectedBacktestResult.timeframe,o.selectedDetailTimeframe=a.activeBot.selectedBacktestResult.timeframe_detail||"",o.timerange=a.activeBot.selectedBacktestResult.timerange};return J(()=>a.activeBot.selectedBacktestResultKey,()=>{g()}),se(()=>a.activeBot.getState()),J(()=>a.activeBot.backtestRunning,()=>{a.activeBot.backtestRunning===!0?_.value=window.setInterval(a.activeBot.pollBacktest,1e3):_.value&&(clearInterval(_.value),_.value=null)}),(y,b)=>{const R=es,V=Ee,S=je,$=Qa,f=ja,L=Da,F=Ma,E=oe,v=pe,C=N,P=Ca,z=wa,D=ua,ye=ea,ve=Ot,xe=qt,Be=Rt;return n(),k("div",as,[r("div",null,[r("div",ss,[os,t(a).activeBot.canRunBacktest?p("",!0):(n(),k("p",ls," Bot must be in webserver mode to enable Backtesting. ")),r("div",ns,[r("div",is,[t(a).activeBot.botApiVersion>=2.15?(n(),h(V,{key:0,modelValue:t(c),"onUpdate:modelValue":b[0]||(b[0]=q=>U(c)?c.value=q:null),name:"bt-form-radios",button:"",class:"mx-1 flex-samesize-items",value:"historicResults",disabled:!t(a).activeBot.canRunBacktest},{default:i(()=>[s(R,{class:"me-2"}),x("Load Results")]),_:1},8,["modelValue","disabled"])):p("",!0),s(V,{modelValue:t(c),"onUpdate:modelValue":b[1]||(b[1]=q=>U(c)?c.value=q:null),name:"bt-form-radios",button:"",class:"mx-1 flex-samesize-items",value:"run",disabled:!t(a).activeBot.canRunBacktest},{default:i(()=>[s(S,{class:"me-2"}),x("Run backtest")]),_:1},8,["modelValue","disabled"]),s(V,{id:"bt-analyze-btn",modelValue:t(c),"onUpdate:modelValue":b[2]||(b[2]=q=>U(c)?c.value=q:null),name:"bt-form-radios",button:"",class:"mx-1 flex-samesize-items",value:"results",disabled:!t(u)},{default:i(()=>[s($,{class:"me-2"}),x("Analyze result")]),_:1},8,["modelValue","disabled"]),t(m)?(n(),h(V,{key:1,modelValue:t(c),"onUpdate:modelValue":b[3]||(b[3]=q=>U(c)?c.value=q:null),name:"bt-form-radios",button:"",class:"mx-1 flex-samesize-items",value:"compare-results",disabled:!t(m)},{default:i(()=>[s(f,{class:"me-2"}),x("Compare results")]),_:1},8,["modelValue","disabled"])):p("",!0),s(V,{modelValue:t(c),"onUpdate:modelValue":b[4]||(b[4]=q=>U(c)?c.value=q:null),name:"bt-form-radios",button:"",class:"mx-1 flex-samesize-items",value:"visualize-summary",disabled:!t(u)},{default:i(()=>[s(L,{class:"me-2"}),x("Visualize summary")]),_:1},8,["modelValue","disabled"]),s(V,{modelValue:t(c),"onUpdate:modelValue":b[5]||(b[5]=q=>U(c)?c.value=q:null),name:"bt-form-radios",button:"",class:"mx-1 flex-samesize-items",value:"visualize",disabled:!t(u)},{default:i(()=>[s(F,{class:"me-2"}),x("Visualize result")]),_:1},8,["modelValue","disabled"])]),He(r("small",{class:"text-end bt-running-label"},"Backtest running: "+B(t(a).activeBot.backtestStep)+" "+B(t(w)(t(a).activeBot.backtestProgress,2)),513),[[Fe,t(a).activeBot.backtestRunning]])])])]),r("div",cs,[t(c)!=="visualize"?(n(),k("div",{key:0,class:me([`${t(d)?"col-md-3":""}`,"sticky-top sticky-offset me-3 d-flex flex-column absolute"]),style:{"max-height":"calc(100vh - 60px)"}},[s(C,{class:"align-self-start","aria-label":"Close",size:"sm",variant:"outline-secondary",onClick:b[6]||(b[6]=q=>d.value=!t(d))},{default:i(()=>[t(d)?p("",!0):(n(),h(E,{key:0,width:"24",height:"24"})),t(d)?(n(),h(v,{key:1,width:"24",height:"24"})):p("",!0)]),_:1}),s(X,{name:"fade"},{default:i(()=>[t(d)?(n(),h(P,{key:0,"backtest-history":t(a).activeBot.backtestHistory,"selected-backtest-result-key":t(a).activeBot.selectedBacktestResultKey,"can-use-modify":t(a).activeBot.botApiVersion>=2.32,onSelectionChange:t(a).activeBot.setBacktestResultKey,onRemoveResult:t(a).activeBot.removeBacktestResultFromMemory,onUpdateResult:t(a).activeBot.saveBacktestResultMetadata},null,8,["backtest-history","selected-backtest-result-key","can-use-modify","onSelectionChange","onRemoveResult","onUpdateResult"])):p("",!0)]),_:1})],2)):p("",!0),r("div",rs,[r("div",ds,[t(c)==="historicResults"?(n(),k("div",ms,[s(z)])):p("",!0),t(c)==="run"?(n(),k("div",_s,[s(D)])):p("",!0),t(u)&&t(c)==="results"?(n(),h(ye,{key:2,"backtest-result":t(a).activeBot.selectedBacktestResult,class:"flex-fill"},null,8,["backtest-result"])):p("",!0),t(u)&&t(c)==="compare-results"?(n(),h(ve,{key:3,"backtest-results":t(a).activeBot.backtestHistory,class:"flex-fill"},null,8,["backtest-results"])):p("",!0),t(u)&&t(c)==="visualize-summary"?(n(),h(xe,{key:4,trades:t(a).activeBot.selectedBacktestResult.trades,class:"flex-fill"},null,8,["trades"])):p("",!0)]),t(u)&&t(c)==="visualize"?(n(),k("div",us,[s(Be,{timeframe:t(l),strategy:t(o).strategy,timerange:t(o).timerange,pairlist:t(a).activeBot.selectedBacktestResult.pairlist,trades:t(a).activeBot.selectedBacktestResult.trades,"freqai-model":t(o).freqAI.enabled?t(o).freqAI.model:void 0},null,8,["timeframe","strategy","timerange","pairlist","trades","freqai-model"])])):p("",!0)])])])}}}),Ss=K(fs,[["__scopeId","data-v-f0dfb2b9"]]);export{Ss as default};
//# sourceMappingURL=BacktestingView-Cr3-RO0Y.js.map
