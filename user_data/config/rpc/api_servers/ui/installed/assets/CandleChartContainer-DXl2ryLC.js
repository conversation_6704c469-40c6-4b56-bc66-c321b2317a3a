import{o as O,c as $,aS as ao,at as ne,au as re,av as de,a as N,F as Fa,K as Wa,e as pe,x as he,y as ee,aw as Lt,A as K,H as Ua,aT as no,ad as ht,ae as Xt,b as M,w as z,a3 as Ga,L as Za,Q as Qn,g as Ue,r as U,q as va,M as X,h as C,z as se,Y as ga,aU as ro,v as xt,aV as be,k as Z,aW as oo,V as ma,Z as io,aX as so,aY as ya,u as er,aZ as Ha,s as ja,ab as lo,p as tr,d as ar,_ as _a,n as nr,B as rr,a_ as uo,a$ as co,aq as Ya,t as Ka,aN as fo,b0 as po,b1 as Ve,i as ho,b2 as vo,a0 as go,b3 as mo,ai as yo}from"./index-B2p78N-x.js";import{_ as or}from"./EditValue.vue_vue_type_script_setup_true_lang-CWpIX5x3.js";import{_ as V,S as ir,B as ba,P as it,G as we,c as xa,g as Re,C as sr,u as Sa,L as Jt,a as _o,n as lr,i as ur,e as D,b as Ca,d as cr,f as qa,s as bo,r as xo,h as W,t as So,j as Qt,l as ea,k as Co,m as wa,o as Aa,p as dr,q as oe,v as le,w as ve,x as Y,D as fr,E as wo,y as pr,z as Ao,A as q,R as ta,F as Ge,H as To,I as hr,J as Do,K as J,M as Xa,N as Po,O as Lo,Q as ko,T as Ta,U as at,V as vr,W as kt,X as Io,Y as Se,Z as xe,$ as B,a0 as Ze,a1 as Oo,a2 as nt,a3 as gr,a4 as ue,a5 as Mo,a6 as Bo,a7 as Ae,a8 as mr,a9 as Da,aa as St,ab as Vo,ac as st,ad as Eo,ae as yr,af as Ce,ag as No,ah as ze,ai as _e,aj as Ne,ak as Ja,al as lt,am as Ro,an as _r,ao as zo,ap as $o,aq as mt,ar as et,as as br,at as Fo,au as Wo,av as Uo,aw as Go,ax as Zo,ay as Ho,az as xr,aA as jo,aB as Yo,aC as Ko,aD as qo,aE as Sr,aF as Pa,aG as Xo,aH as Jo,aI as Qa,aJ as Cr,aK as Qo,aL as ei,aM as ti,aN as vt,aO as ai,aP as wr,aQ as aa,aR as en,aS as ni,aT as It,aU as ri,aV as oi,aW as ii,aX as si,aY as li,aZ as ui,a_ as ci,a$ as di,b0 as tn,b1 as fi,b2 as pi,b3 as hi,b4 as vi,b5 as gi,b6 as mi,b7 as yi,b8 as _i,b9 as bi,ba as xi,bb as Si,bc as Ci,bd as wi,be as Ai,bf as Ti}from"./installCanvasRenderer-DFpQ5KDo.js";import{c as Di,p as Ar,S as Pi,i as Li,t as ki,r as Ii,a as Oi,D as Mi,b as Bi,d as Vi,s as Ei,A as Ni,e as Ri,f as zi,g as $i,h as Fi,j as Wi,k as Ui,l as Gi,m as Zi}from"./install-DosLD5hS.js";import{c as Hi}from"./createSeriesDataSimply-Bq9ycDaP.js";function ji(t){if(!t)return{baseCurrency:"",quoteCurrency:""};const[e,a]=t.split("/");if(a!==void 0){const n=a.split(":");return{baseCurrency:e,quoteCurrency:n[0]||a}}return{baseCurrency:a??"",quoteCurrency:e??""}}var Yi=Object.defineProperty,Ki=Object.defineProperties,qi=Object.getOwnPropertyDescriptors,an=Object.getOwnPropertySymbols,Xi=Object.prototype.hasOwnProperty,Ji=Object.prototype.propertyIsEnumerable,nn=(t,e,a)=>e in t?Yi(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,Ee=(t,e)=>{for(var a in e||(e={}))Xi.call(e,a)&&nn(t,a,e[a]);if(an)for(var a of an(e))Ji.call(e,a)&&nn(t,a,e[a]);return t},rn=(t,e)=>Ki(t,qi(e));const Qi={props:{autoscroll:{type:Boolean,default:!0}},watch:{typeAheadPointer(){this.autoscroll&&this.maybeAdjustScroll()},open(t){this.autoscroll&&t&&this.$nextTick(()=>this.maybeAdjustScroll())}},methods:{maybeAdjustScroll(){var t;const e=((t=this.$refs.dropdownMenu)==null?void 0:t.children[this.typeAheadPointer])||!1;if(e){const a=this.getDropdownViewport(),{top:n,bottom:r,height:o}=e.getBoundingClientRect();if(n<a.top)return this.$refs.dropdownMenu.scrollTop=e.offsetTop;if(r>a.bottom)return this.$refs.dropdownMenu.scrollTop=e.offsetTop-(a.height-o)}},getDropdownViewport(){return this.$refs.dropdownMenu?this.$refs.dropdownMenu.getBoundingClientRect():{height:0,top:0,bottom:0}}}},es={data(){return{typeAheadPointer:-1}},watch:{filteredOptions(){for(let t=0;t<this.filteredOptions.length;t++)if(this.selectable(this.filteredOptions[t])){this.typeAheadPointer=t;break}},open(t){t&&this.typeAheadToLastSelected()},selectedValue(){this.open&&this.typeAheadToLastSelected()}},methods:{typeAheadUp(){for(let t=this.typeAheadPointer-1;t>=0;t--)if(this.selectable(this.filteredOptions[t])){this.typeAheadPointer=t;break}},typeAheadDown(){for(let t=this.typeAheadPointer+1;t<this.filteredOptions.length;t++)if(this.selectable(this.filteredOptions[t])){this.typeAheadPointer=t;break}},typeAheadSelect(){const t=this.filteredOptions[this.typeAheadPointer];t&&this.selectable(t)&&this.select(t)},typeAheadToLastSelected(){this.typeAheadPointer=this.selectedValue.length!==0?this.filteredOptions.indexOf(this.selectedValue[this.selectedValue.length-1]):-1}}},ts={props:{loading:{type:Boolean,default:!1}},data(){return{mutableLoading:!1}},watch:{search(){this.$emit("search",this.search,this.toggleLoading)},loading(t){this.mutableLoading=t}},methods:{toggleLoading(t=null){return t==null?this.mutableLoading=!this.mutableLoading:this.mutableLoading=t}}},La=(t,e)=>{const a=t.__vccOpts||t;for(const[n,r]of e)a[n]=r;return a},as={},ns={xmlns:"http://www.w3.org/2000/svg",width:"10",height:"10"},rs=N("path",{d:"M6.895455 5l2.842897-2.842898c.348864-.348863.348864-.914488 0-1.263636L9.106534.261648c-.348864-.348864-.914489-.348864-1.263636 0L5 3.104545 2.157102.261648c-.348863-.348864-.914488-.348864-1.263636 0L.261648.893466c-.348864.348864-.348864.914489 0 1.263636L3.104545 5 .261648 7.842898c-.348864.348863-.348864.914488 0 1.263636l.631818.631818c.348864.348864.914773.348864 1.263636 0L5 6.895455l2.842898 2.842897c.348863.348864.914772.348864 1.263636 0l.631818-.631818c.348864-.348864.348864-.914489 0-1.263636L6.895455 5z"},null,-1),os=[rs];function is(t,e){return O(),$("svg",ns,os)}const ss=La(as,[["render",is]]),ls={},us={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"10"},cs=N("path",{d:"M9.211364 7.59931l4.48338-4.867229c.407008-.441854.407008-1.158247 0-1.60046l-.73712-.80023c-.407008-.441854-1.066904-.441854-1.474243 0L7 5.198617 2.51662.33139c-.407008-.441853-1.066904-.441853-1.474243 0l-.737121.80023c-.407008.441854-.407008 1.158248 0 1.600461l4.48338 4.867228L7 10l2.211364-2.40069z"},null,-1),ds=[cs];function fs(t,e){return O(),$("svg",us,ds)}const ps=La(ls,[["render",fs]]),on={Deselect:ss,OpenIndicator:ps},hs={mounted(t,{instance:e}){if(e.appendToBody){const{height:a,top:n,left:r,width:o}=e.$refs.toggle.getBoundingClientRect();let i=window.scrollX||window.pageXOffset,s=window.scrollY||window.pageYOffset;t.unbindPosition=e.calculatePosition(t,e,{width:o+"px",left:i+r+"px",top:s+n+a+"px"}),document.body.appendChild(t)}},unmounted(t,{instance:e}){e.appendToBody&&(t.unbindPosition&&typeof t.unbindPosition=="function"&&t.unbindPosition(),t.parentNode&&t.parentNode.removeChild(t))}};function vs(t){const e={};return Object.keys(t).sort().forEach(a=>{e[a]=t[a]}),JSON.stringify(e)}let gs=0;function ms(){return++gs}const ys={components:Ee({},on),directives:{appendToBody:hs},mixins:[Qi,es,ts],compatConfig:{MODE:3},emits:["open","close","update:modelValue","search","search:compositionstart","search:compositionend","search:keydown","search:blur","search:focus","search:input","option:created","option:selecting","option:selected","option:deselecting","option:deselected"],props:{modelValue:{},components:{type:Object,default:()=>({})},options:{type:Array,default(){return[]}},disabled:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},deselectFromDropdown:{type:Boolean,default:!1},searchable:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},placeholder:{type:String,default:""},transition:{type:String,default:"vs__fade"},clearSearchOnSelect:{type:Boolean,default:!0},closeOnSelect:{type:Boolean,default:!0},label:{type:String,default:"label"},autocomplete:{type:String,default:"off"},reduce:{type:Function,default:t=>t},selectable:{type:Function,default:t=>!0},getOptionLabel:{type:Function,default(t){return typeof t=="object"?t.hasOwnProperty(this.label)?t[this.label]:console.warn(`[vue-select warn]: Label key "option.${this.label}" does not exist in options object ${JSON.stringify(t)}.
https://vue-select.org/api/props.html#getoptionlabel`):t}},getOptionKey:{type:Function,default(t){if(typeof t!="object")return t;try{return t.hasOwnProperty("id")?t.id:vs(t)}catch(e){return console.warn(`[vue-select warn]: Could not stringify this option to generate unique key. Please provide'getOptionKey' prop to return a unique key for each option.
https://vue-select.org/api/props.html#getoptionkey`,t,e)}}},onTab:{type:Function,default:function(){this.selectOnTab&&!this.isComposing&&this.typeAheadSelect()}},taggable:{type:Boolean,default:!1},tabindex:{type:Number,default:null},pushTags:{type:Boolean,default:!1},filterable:{type:Boolean,default:!0},filterBy:{type:Function,default(t,e,a){return(e||"").toLocaleLowerCase().indexOf(a.toLocaleLowerCase())>-1}},filter:{type:Function,default(t,e){return t.filter(a=>{let n=this.getOptionLabel(a);return typeof n=="number"&&(n=n.toString()),this.filterBy(a,n,e)})}},createOption:{type:Function,default(t){return typeof this.optionList[0]=="object"?{[this.label]:t}:t}},resetOnOptionsChange:{default:!1,validator:t=>["function","boolean"].includes(typeof t)},clearSearchOnBlur:{type:Function,default:function({clearSearchOnSelect:t,multiple:e}){return t&&!e}},noDrop:{type:Boolean,default:!1},inputId:{type:String},dir:{type:String,default:"auto"},selectOnTab:{type:Boolean,default:!1},selectOnKeyCodes:{type:Array,default:()=>[13]},searchInputQuerySelector:{type:String,default:"[type=search]"},mapKeydown:{type:Function,default:(t,e)=>t},appendToBody:{type:Boolean,default:!1},calculatePosition:{type:Function,default(t,e,{width:a,top:n,left:r}){t.style.top=n,t.style.left=r,t.style.width=a}},dropdownShouldOpen:{type:Function,default({noDrop:t,open:e,mutableLoading:a}){return t?!1:e&&!a}},uid:{type:[String,Number],default:()=>ms()}},data(){return{search:"",open:!1,isComposing:!1,pushedTags:[],_value:[],deselectButtons:[]}},computed:{isReducingValues(){return this.$props.reduce!==this.$options.props.reduce.default},isTrackingValues(){return typeof this.modelValue>"u"||this.isReducingValues},selectedValue(){let t=this.modelValue;return this.isTrackingValues&&(t=this.$data._value),t!=null&&t!==""?[].concat(t):[]},optionList(){return this.options.concat(this.pushTags?this.pushedTags:[])},searchEl(){return this.$slots.search?this.$refs.selectedOptions.querySelector(this.searchInputQuerySelector):this.$refs.search},scope(){const t={search:this.search,loading:this.loading,searching:this.searching,filteredOptions:this.filteredOptions};return{search:{attributes:Ee({disabled:this.disabled,placeholder:this.searchPlaceholder,tabindex:this.tabindex,readonly:!this.searchable,id:this.inputId,"aria-autocomplete":"list","aria-labelledby":`vs${this.uid}__combobox`,"aria-controls":`vs${this.uid}__listbox`,ref:"search",type:"search",autocomplete:this.autocomplete,value:this.search},this.dropdownOpen&&this.filteredOptions[this.typeAheadPointer]?{"aria-activedescendant":`vs${this.uid}__option-${this.typeAheadPointer}`}:{}),events:{compositionstart:()=>this.isComposing=!0,compositionend:()=>this.isComposing=!1,keydown:this.onSearchKeyDown,blur:this.onSearchBlur,focus:this.onSearchFocus,input:e=>this.search=e.target.value}},spinner:{loading:this.mutableLoading},noOptions:{search:this.search,loading:this.mutableLoading,searching:this.searching},openIndicator:{attributes:{ref:"openIndicator",role:"presentation",class:"vs__open-indicator"}},listHeader:t,listFooter:t,header:rn(Ee({},t),{deselect:this.deselect}),footer:rn(Ee({},t),{deselect:this.deselect})}},childComponents(){return Ee(Ee({},on),this.components)},stateClasses(){return{"vs--open":this.dropdownOpen,"vs--single":!this.multiple,"vs--multiple":this.multiple,"vs--searching":this.searching&&!this.noDrop,"vs--searchable":this.searchable&&!this.noDrop,"vs--unsearchable":!this.searchable,"vs--loading":this.mutableLoading,"vs--disabled":this.disabled}},searching(){return!!this.search},dropdownOpen(){return this.dropdownShouldOpen(this)},searchPlaceholder(){return this.isValueEmpty&&this.placeholder?this.placeholder:void 0},filteredOptions(){const t=[].concat(this.optionList);if(!this.filterable&&!this.taggable)return t;const e=this.search.length?this.filter(t,this.search,this):t;if(this.taggable&&this.search.length){const a=this.createOption(this.search);this.optionExists(a)||e.unshift(a)}return e},isValueEmpty(){return this.selectedValue.length===0},showClearButton(){return!this.multiple&&this.clearable&&!this.open&&!this.isValueEmpty}},watch:{options(t,e){const a=()=>typeof this.resetOnOptionsChange=="function"?this.resetOnOptionsChange(t,e,this.selectedValue):this.resetOnOptionsChange;!this.taggable&&a()&&this.clearSelection(),this.modelValue&&this.isTrackingValues&&this.setInternalValueFromOptions(this.modelValue)},modelValue:{immediate:!0,handler(t){this.isTrackingValues&&this.setInternalValueFromOptions(t)}},multiple(){this.clearSelection()},open(t){this.$emit(t?"open":"close")}},created(){this.mutableLoading=this.loading},methods:{setInternalValueFromOptions(t){Array.isArray(t)?this.$data._value=t.map(e=>this.findOptionFromReducedValue(e)):this.$data._value=this.findOptionFromReducedValue(t)},select(t){this.$emit("option:selecting",t),this.isOptionSelected(t)?this.deselectFromDropdown&&(this.clearable||this.multiple&&this.selectedValue.length>1)&&this.deselect(t):(this.taggable&&!this.optionExists(t)&&(this.$emit("option:created",t),this.pushTag(t)),this.multiple&&(t=this.selectedValue.concat(t)),this.updateValue(t),this.$emit("option:selected",t)),this.onAfterSelect(t)},deselect(t){this.$emit("option:deselecting",t),this.updateValue(this.selectedValue.filter(e=>!this.optionComparator(e,t))),this.$emit("option:deselected",t)},clearSelection(){this.updateValue(this.multiple?[]:null)},onAfterSelect(t){this.closeOnSelect&&(this.open=!this.open,this.searchEl.blur()),this.clearSearchOnSelect&&(this.search="")},updateValue(t){typeof this.modelValue>"u"&&(this.$data._value=t),t!==null&&(Array.isArray(t)?t=t.map(e=>this.reduce(e)):t=this.reduce(t)),this.$emit("update:modelValue",t)},toggleDropdown(t){const e=t.target!==this.searchEl;e&&t.preventDefault();const a=[...this.deselectButtons||[],this.$refs.clearButton];if(this.searchEl===void 0||a.filter(Boolean).some(n=>n.contains(t.target)||n===t.target)){t.preventDefault();return}this.open&&e?this.searchEl.blur():this.disabled||(this.open=!0,this.searchEl.focus())},isOptionSelected(t){return this.selectedValue.some(e=>this.optionComparator(e,t))},isOptionDeselectable(t){return this.isOptionSelected(t)&&this.deselectFromDropdown},optionComparator(t,e){return this.getOptionKey(t)===this.getOptionKey(e)},findOptionFromReducedValue(t){const e=n=>JSON.stringify(this.reduce(n))===JSON.stringify(t),a=[...this.options,...this.pushedTags].filter(e);return a.length===1?a[0]:a.find(n=>this.optionComparator(n,this.$data._value))||t},closeSearchOptions(){this.open=!1,this.$emit("search:blur")},maybeDeleteValue(){if(!this.searchEl.value.length&&this.selectedValue&&this.selectedValue.length&&this.clearable){let t=null;this.multiple&&(t=[...this.selectedValue.slice(0,this.selectedValue.length-1)]),this.updateValue(t)}},optionExists(t){return this.optionList.some(e=>this.optionComparator(e,t))},normalizeOptionForSlot(t){return typeof t=="object"?t:{[this.label]:t}},pushTag(t){this.pushedTags.push(t)},onEscape(){this.search.length?this.search="":this.searchEl.blur()},onSearchBlur(){if(this.mousedown&&!this.searching)this.mousedown=!1;else{const{clearSearchOnSelect:t,multiple:e}=this;this.clearSearchOnBlur({clearSearchOnSelect:t,multiple:e})&&(this.search=""),this.closeSearchOptions();return}if(this.search.length===0&&this.options.length===0){this.closeSearchOptions();return}},onSearchFocus(){this.open=!0,this.$emit("search:focus")},onMousedown(){this.mousedown=!0},onMouseUp(){this.mousedown=!1},onSearchKeyDown(t){const e=r=>(r.preventDefault(),!this.isComposing&&this.typeAheadSelect()),a={8:r=>this.maybeDeleteValue(),9:r=>this.onTab(),27:r=>this.onEscape(),38:r=>(r.preventDefault(),this.typeAheadUp()),40:r=>(r.preventDefault(),this.typeAheadDown())};this.selectOnKeyCodes.forEach(r=>a[r]=e);const n=this.mapKeydown(a,this);if(typeof n[t.keyCode]=="function")return n[t.keyCode](t)}}},_s=["dir"],bs=["id","aria-expanded","aria-owns"],xs={ref:"selectedOptions",class:"vs__selected-options"},Ss=["disabled","title","aria-label","onClick"],Cs={ref:"actions",class:"vs__actions"},ws=["disabled"],As={class:"vs__spinner"},Ts=["id"],Ds=["id","aria-selected","onMouseover","onClick"],Ps={key:0,class:"vs__no-options"},Ls=pe(" Sorry, no matching options. "),ks=["id"];function Is(t,e,a,n,r,o){const i=ao("append-to-body");return O(),$("div",{dir:a.dir,class:Za(["v-select",o.stateClasses])},[ne(t.$slots,"header",re(de(o.scope.header))),N("div",{id:`vs${a.uid}__combobox`,ref:"toggle",class:"vs__dropdown-toggle",role:"combobox","aria-expanded":o.dropdownOpen.toString(),"aria-owns":`vs${a.uid}__listbox`,"aria-label":"Search for option",onMousedown:e[1]||(e[1]=s=>o.toggleDropdown(s))},[N("div",xs,[(O(!0),$(Fa,null,Wa(o.selectedValue,(s,l)=>ne(t.$slots,"selected-option-container",{option:o.normalizeOptionForSlot(s),deselect:o.deselect,multiple:a.multiple,disabled:a.disabled},()=>[(O(),$("span",{key:a.getOptionKey(s),class:"vs__selected"},[ne(t.$slots,"selected-option",re(de(o.normalizeOptionForSlot(s))),()=>[pe(he(a.getOptionLabel(s)),1)]),a.multiple?(O(),$("button",{key:0,ref_for:!0,ref:u=>r.deselectButtons[l]=u,disabled:a.disabled,type:"button",class:"vs__deselect",title:`Deselect ${a.getOptionLabel(s)}`,"aria-label":`Deselect ${a.getOptionLabel(s)}`,onClick:u=>o.deselect(s)},[(O(),ee(Lt(o.childComponents.Deselect)))],8,Ss)):K("",!0)]))])),256)),ne(t.$slots,"search",re(de(o.scope.search)),()=>[N("input",Ua({class:"vs__search"},o.scope.search.attributes,no(o.scope.search.events)),null,16)])],512),N("div",Cs,[ht(N("button",{ref:"clearButton",disabled:a.disabled,type:"button",class:"vs__clear",title:"Clear Selected","aria-label":"Clear Selected",onClick:e[0]||(e[0]=(...s)=>o.clearSelection&&o.clearSelection(...s))},[(O(),ee(Lt(o.childComponents.Deselect)))],8,ws),[[Xt,o.showClearButton]]),ne(t.$slots,"open-indicator",re(de(o.scope.openIndicator)),()=>[a.noDrop?K("",!0):(O(),ee(Lt(o.childComponents.OpenIndicator),re(Ua({key:0},o.scope.openIndicator.attributes)),null,16))]),ne(t.$slots,"spinner",re(de(o.scope.spinner)),()=>[ht(N("div",As,"Loading...",512),[[Xt,t.mutableLoading]])])],512)],40,bs),M(Qn,{name:a.transition},{default:z(()=>[o.dropdownOpen?ht((O(),$("ul",{id:`vs${a.uid}__listbox`,ref:"dropdownMenu",key:`vs${a.uid}__listbox`,class:"vs__dropdown-menu",role:"listbox",tabindex:"-1",onMousedown:e[2]||(e[2]=Ga((...s)=>o.onMousedown&&o.onMousedown(...s),["prevent"])),onMouseup:e[3]||(e[3]=(...s)=>o.onMouseUp&&o.onMouseUp(...s))},[ne(t.$slots,"list-header",re(de(o.scope.listHeader))),(O(!0),$(Fa,null,Wa(o.filteredOptions,(s,l)=>(O(),$("li",{id:`vs${a.uid}__option-${l}`,key:a.getOptionKey(s),role:"option",class:Za(["vs__dropdown-option",{"vs__dropdown-option--deselect":o.isOptionDeselectable(s)&&l===t.typeAheadPointer,"vs__dropdown-option--selected":o.isOptionSelected(s),"vs__dropdown-option--highlight":l===t.typeAheadPointer,"vs__dropdown-option--disabled":!a.selectable(s)}]),"aria-selected":l===t.typeAheadPointer?!0:null,onMouseover:u=>a.selectable(s)?t.typeAheadPointer=l:null,onClick:Ga(u=>a.selectable(s)?o.select(s):null,["prevent","stop"])},[ne(t.$slots,"option",re(de(o.normalizeOptionForSlot(s))),()=>[pe(he(a.getOptionLabel(s)),1)])],42,Ds))),128)),o.filteredOptions.length===0?(O(),$("li",Ps,[ne(t.$slots,"no-options",re(de(o.scope.noOptions)),()=>[Ls])])):K("",!0),ne(t.$slots,"list-footer",re(de(o.scope.listFooter)))],40,Ts)),[[i]]):(O(),$("ul",{key:1,id:`vs${a.uid}__listbox`,role:"listbox",style:{display:"none",visibility:"hidden"}},null,8,ks))]),_:3},8,["name"]),ne(t.$slots,"footer",re(de(o.scope.footer)))],10,_s)}const Tr=La(ys,[["render",Is]]),Os={class:"d-flex flex-row"},Dr=Ue({__name:"PlotIndicatorSelect",props:{modelValue:{required:!1,default:"",type:String},columns:{required:!0,type:Array},label:{required:!0,type:String}},emits:["update:modelValue","indicatorSelected"],setup(t,{emit:e}){const a=t,n=e,r=U("");function o(){n("indicatorSelected",r.value),n("update:modelValue",r.value)}function i(){r.value="",o()}return va(()=>{r.value=a.modelValue}),X(()=>a.modelValue,s=>{r.value=s}),(s,l)=>{const u=ga,c=ro,d=xt;return O(),$("div",Os,[M(u,{class:"flex-grow-1",label:t.label,"label-for":"indicatorSelector"},{default:z(()=>[M(C(Tr),{modelValue:C(r),"onUpdate:modelValue":l[0]||(l[0]=f=>se(r)?r.value=f:null),options:t.columns,size:"sm",clearable:!1,"onOption:selected":o},null,8,["modelValue","options"])]),_:1},8,["label"]),M(d,{size:"sm",title:"Abort",class:"ms-1 mt-auto",variant:"secondary",onClick:i},{default:z(()=>[M(c)]),_:1})])}}}),Ms={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},Bs=N("path",{fill:"currentColor",d:"M19.78 3h-8.56C10.55 3 10 3.55 10 4.22V8h6v6h3.78c.67 0 1.22-.55 1.22-1.22V4.22C21 3.55 20.45 3 19.78 3m-7.34 3.67a1.23 1.23 0 1 1 0-2.46a1.23 1.23 0 0 1 1.23 1.23c0 .68-.55 1.23-1.23 1.23m6.12 6.11a1.23 1.23 0 1 1-.02-2.46c.68-.01 1.23.54 1.24 1.24c-.01.67-.55 1.21-1.22 1.22m0-6.11a1.23 1.23 0 1 1-.02-2.46a1.231 1.231 0 0 1 .02 2.46M4.22 10h8.56A1.22 1.22 0 0 1 14 11.22v8.56c0 .67-.55 1.22-1.22 1.22H4.22C3.55 21 3 20.45 3 19.78v-8.56c0-.67.55-1.22 1.22-1.22m4.28 4.28c-.67 0-1.22.55-1.22 1.22s.55 1.22 1.22 1.22s1.22-.55 1.22-1.22a1.22 1.22 0 0 0-1.22-1.22m-3.06-3.06c-.67 0-1.22.55-1.22 1.22a1.22 1.22 0 0 0 1.22 1.22c.67 0 1.22-.55 1.22-1.22s-.55-1.22-1.22-1.22m6.11 6.11c-.67 0-1.22.55-1.22 1.22s.55 1.22 1.22 1.22a1.22 1.22 0 0 0 1.22-1.22c0-.67-.54-1.21-1.21-1.22z"},null,-1),Vs=[Bs];function Es(t,e){return O(),$("svg",Ms,[...Vs])}const Ns={name:"mdi-dice-multiple",render:Es};function tt(){return`#${(Math.random()*16777215<<0).toString(16)}`}const Rs={class:"d-flex flex-col flex-xl-row justify-content-between mt-1"},zs=Ue({__name:"PlotIndicator",props:{modelValue:{required:!0,type:Object},columns:{required:!0,type:Array}},emits:["update:modelValue"],setup(t,{emit:e}){const a=t,n=e,r=U(tt()),o=U(be.line),i=U(Object.keys(be)),s=U(""),l=U(!1),u=U("");function c(){r.value=tt()}const d=Z(()=>{if(l.value||!s.value)return{};const p={color:r.value,type:o.value};return u.value&&o.value===be.line&&(p.fill_to=u.value),{[s.value]:p}});function f(){n("update:modelValue",d.value)}return X(()=>a.modelValue,()=>{if([s.value]=Object.keys(a.modelValue),l.value=!1,s.value&&a.modelValue){const p=a.modelValue[s.value];r.value=p.color||tt(),o.value=p.type||be.line,u.value=p.fill_to||""}},{immediate:!0}),oo([r,o,u],()=>{f()},{debounce:200}),(p,h)=>{const g=ma,m=ga,_=io,b=Ns,y=xt,v=so,S=Dr;return O(),$("div",null,[N("div",Rs,[M(m,{class:"col flex-grow-1",label:"Type","label-for":"plotTypeSelector"},{default:z(()=>[M(g,{id:"plotTypeSelector",modelValue:C(o),"onUpdate:modelValue":h[0]||(h[0]=x=>se(o)?o.value=x:null),size:"sm",options:C(i)},null,8,["modelValue","options"])]),_:1}),M(m,{label:"Color","label-for":"colsel",size:"sm",class:"ms-xl-1 col"},{default:z(()=>[M(v,null,{prepend:z(()=>[M(_,{modelValue:C(r),"onUpdate:modelValue":h[1]||(h[1]=x=>se(r)?r.value=x:null),type:"color",size:"sm",class:"p-0",style:{"max-width":"29px"}},null,8,["modelValue"])]),append:z(()=>[M(y,{variant:"primary",size:"sm",onClick:c},{default:z(()=>[M(b)]),_:1})]),default:z(()=>[M(_,{id:"colsel",modelValue:C(r),"onUpdate:modelValue":h[2]||(h[2]=x=>se(r)?r.value=x:null),size:"sm",class:"flex-grow-1"},null,8,["modelValue"])]),_:1})]),_:1})]),C(o)===C(be).line?(O(),ee(S,{key:0,modelValue:C(u),"onUpdate:modelValue":h[3]||(h[3]=x=>se(u)?u.value=x:null),columns:t.columns,class:"mt-1",label:"Area chart - Fill to (leave empty for line chart)"},null,8,["modelValue","columns"])):K("",!0)])}}}),Pr=Ue({__name:"PlotConfigSelect",props:{allowEdit:{type:Boolean,default:!1},editableName:{type:String,default:"plot configuration"}},setup(t){const e=ya();return(a,n)=>{const r=ma,o=or;return O(),ee(o,{modelValue:C(e).plotConfigName,"onUpdate:modelValue":n[1]||(n[1]=i=>C(e).plotConfigName=i),"allow-edit":t.allowEdit,"allow-add":t.allowEdit,"allow-duplicate":t.allowEdit,"editable-name":"plot configuration",onRename:C(e).renamePlotConfig,onDelete:C(e).deletePlotConfig,onNew:C(e).newPlotConfig,onDuplicate:C(e).duplicatePlotConfig},{default:z(()=>[M(r,{id:"plotConfigSelect",modelValue:C(e).plotConfigName,"onUpdate:modelValue":[n[0]||(n[0]=i=>C(e).plotConfigName=i),C(e).plotConfigChanged],options:C(e).availablePlotConfigNames,size:"sm"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1},8,["modelValue","allow-edit","allow-add","allow-duplicate","onRename","onDelete","onNew","onDuplicate"])}}}),ka=t=>(tr("data-v-79de00fa"),t=t(),ar(),t),$s={key:0},Fs={class:"col-mb-3"},Ws=ka(()=>N("hr",null,null,-1)),Us=ka(()=>N("hr",null,null,-1)),Gs={class:"d-flex flex-row mt-1"},Zs=ka(()=>N("hr",null,null,-1)),Hs={class:"d-flex flex-row"},js={key:3,class:"col-mb-5 ms-1 mt-2"},Ys=Ue({__name:"PlotConfigurator",props:{columns:{required:!0,type:Array},isVisible:{required:!0,default:!1,type:Boolean}},setup(t){const e=t,a=ya(),n=er(),r=U("default"),o=U(""),i=U(!1),s=U(!1),l=U("main_plot"),u=U(),c=U(!0),d=Z(()=>l.value==="main_plot"),f=Z(()=>d.value?a.editablePlotConfig.main_plot:a.editablePlotConfig.subplots[l.value]),p=Z(()=>["main_plot",...Object.keys(a.editablePlotConfig.subplots)]),h=Z(()=>{let T=[];return d.value&&(T=Object.keys(a.editablePlotConfig.main_plot)),l.value in a.editablePlotConfig.subplots&&(T=Object.keys(a.editablePlotConfig.subplots[l.value])),T.map(L=>({value:L,html:e.columns.includes(L)?L:`<span title="Column not available">${L} <-- not available in this chart</span>`}))});function g(T){const L=Object.keys(T)[0],E=T[L];d.value?a.editablePlotConfig.main_plot[L]={...E}:a.editablePlotConfig.subplots[l.value][L]={...E},a.editablePlotConfig={...a.editablePlotConfig},i.value=!1}const m=Z({get(){return i.value?{}:o.value?{[o.value]:f.value[o.value]}:{}},set(T){Object.keys(T)[0]&&T?g(T):i.value=!1}}),_=Z({get(){return JSON.stringify(a.editablePlotConfig,null,2)},set(T){try{u.value=JSON.parse(T),c.value=!0}catch{c.value=!1}}});function b(){d.value?(console.log(`Removing ${o.value} from MainPlot`),delete a.editablePlotConfig.main_plot[o.value]):(console.log(`Removing ${o.value} from ${l.value}`),delete a.editablePlotConfig.subplots[l.value][o.value]),a.editablePlotConfig={...a.editablePlotConfig},o.value=""}function y(T){a.editablePlotConfig.subplots={...a.editablePlotConfig.subplots,[T]:{}},l.value=T}function v(T){delete a.editablePlotConfig.subplots[T],l.value=p.value[p.value.length-1]}function S(T,L){a.editablePlotConfig.subplots[L]=a.editablePlotConfig.subplots[T],delete a.editablePlotConfig.subplots[T],l.value=L}function x(){a.editablePlotConfig=Ha(a.customPlotConfigs[a.plotConfigName])}function w(){u.value!==void 0&&c.value&&(a.editablePlotConfig=u.value)}async function P(){if(n.activeBot.isWebserverMode&&!n.activeBot.strategy.strategy){ja("No strategy selected, can't load plot config.");return}try{await n.activeBot.getStrategyPlotConfig(),n.activeBot.strategyPlotConfig&&(a.editablePlotConfig=n.activeBot.strategyPlotConfig)}catch{ja("Failed to load Plot configuration from Strategy.")}}function A(){a.saveCustomPlotConfig(r.value,a.editablePlotConfig)}function I(T){i.value=!1,T&&(g({[T]:{color:tt()}}),o.value=T)}return X(l,()=>{o.value=""}),X(()=>a.plotConfigName,()=>{o.value="",r.value=a.plotConfigName}),X(()=>e.isVisible,()=>{e.isVisible?(a.editablePlotConfig=Ha(a.plotConfig),a.isEditing=!0,r.value=a.plotConfigName):a.isEditing=!1}),(T,L)=>{const E=Pr,G=ga,j=ma,ge=or,F=xt,Te=Dr,He=zs,me=lo;return t.columns?(O(),$("div",$s,[M(G,{label:"Plot config name","label-for":"idPlotConfigName"},{default:z(()=>[M(E,{"allow-edit":""})]),_:1}),N("div",Fs,[Ws,M(G,{label:"Target Plot","label-for":"FieldSel"},{default:z(()=>[M(ge,{modelValue:C(l),"onUpdate:modelValue":L[1]||(L[1]=R=>se(l)?l.value=R:null),"allow-edit":!C(d),"allow-add":"","editable-name":"plot configuration","align-vertical":"",onNew:y,onDelete:v,onRename:S},{default:z(()=>[M(j,{id:"FieldSel",modelValue:C(l),"onUpdate:modelValue":L[0]||(L[0]=R=>se(l)?l.value=R:null),options:C(p),"select-size":5},null,8,["modelValue","options"])]),_:1},8,["modelValue","allow-edit"])]),_:1})]),Us,N("div",null,[M(G,{label:"Indicators in this plot","label-for":"selectedIndicators"},{default:z(()=>[M(j,{id:"selectedIndicators",modelValue:C(o),"onUpdate:modelValue":L[2]||(L[2]=R=>se(o)?o.value=R:null),disabled:C(i),options:C(h),"select-size":4},null,8,["modelValue","disabled","options"])]),_:1})]),N("div",Gs,[M(F,{variant:"secondary",title:"Remove indicator to plot",size:"sm",disabled:!C(o),class:"col",onClick:b},{default:z(()=>[pe(" Remove indicator ")]),_:1},8,["disabled"]),M(F,{variant:"primary",title:"Add indicator to plot",size:"sm",class:"ms-1 col",disabled:C(i),onClick:L[3]||(L[3]=R=>{i.value=!C(i),o.value=""})},{default:z(()=>[pe(" Add new indicator ")]),_:1},8,["disabled"])]),C(i)?(O(),ee(Te,{key:0,columns:t.columns,class:"mt-1",label:"Select indicator to add",onIndicatorSelected:I},null,8,["columns"])):K("",!0),C(o)?(O(),ee(He,{key:1,modelValue:C(m),"onUpdate:modelValue":L[4]||(L[4]=R=>se(m)?m.value=R:null),class:"mt-1",columns:t.columns},null,8,["modelValue","columns"])):K("",!0),Zs,N("div",Hs,[M(F,{class:"ms-1 col",variant:"secondary",size:"sm",disabled:C(i),title:"Reset to last saved configuration",onClick:x},{default:z(()=>[pe("Reset")]),_:1},8,["disabled"]),M(F,{disabled:C(n).activeBot.isWebserverMode&&C(n).activeBot.botApiVersion<2.23||!C(n).activeBot.isBotOnline||C(i),class:"ms-1 col",variant:"secondary",size:"sm",onClick:P},{default:z(()=>[pe(" From strategy ")]),_:1},8,["disabled"]),M(F,{id:"showButton",class:"ms-1 col",variant:"secondary",size:"sm",disabled:C(i),title:"Show configuration for easy transfer to a strategy",onClick:L[5]||(L[5]=R=>s.value=!C(s))},{default:z(()=>[pe(he(C(s)?"Hide":"Show"),1)]),_:1},8,["disabled"]),M(F,{class:"ms-1 col",variant:"primary",size:"sm","data-toggle":"tooltip",disabled:C(i),title:"Save configuration",onClick:A},{default:z(()=>[pe("Save")]),_:1},8,["disabled"])]),C(s)?(O(),ee(F,{key:2,class:"ms-1 mt-1",variant:"secondary",size:"sm",title:"Load configuration from text box below",onClick:w},{default:z(()=>[pe("Load from String")]),_:1})):K("",!0),C(s)?(O(),$("div",js,[M(me,{id:"TextArea",modelValue:C(_),"onUpdate:modelValue":L[6]||(L[6]=R=>se(_)?_.value=R:null),class:"textArea",size:"sm",state:C(c)},null,8,["modelValue","state"])])):K("",!0)])):K("",!0)}}}),Ks=_a(Ys,[["__scopeId","data-v-79de00fa"]]);function na(t,e,a,n,r=0){return{name:a,type:n.type||"line",xAxisIndex:r,yAxisIndex:r,itemStyle:{color:n.color||tt()},encode:{x:t,y:e},showSymbol:!1}}function sn(t,e,a,n,r=0){const o={type:be.line},i=na(t,e,a,o,r),s={stack:a,stackStrategy:"all",lineStyle:{opacity:0},showSymbol:!1,areaStyle:{color:n.color,opacity:.1},tooltip:{show:!1}};return Object.assign(i,s),i}function qs(t,e){const a=t.indexOf("open"),n=t.indexOf("close"),r=t.indexOf("high"),o=t.indexOf("low");let i;return e.map((s,l)=>{const u=s.slice(),c=l===0?(u[a]+u[n])/2:(i[a]+i[n])/2,d=(u[a]+u[r]+u[o]+u[n])/4,f=Math.max(u[r],u[a],u[n]),p=Math.min(u[o],u[a],u[n]);return u[a]=c,u[n]=d,u[r]=f,u[o]=p,i=u.slice(),u})}const Xs=2,Js=3,Lr=4;function Le(t,e,a=Js){const n=e%t;let r=a===Xs;return a===Lr&&(r=n>t/2),e-n+(r?t:0)}function kr(t,e){return`${t.ft_order_side==="buy"?"+":"-"}${rr("cost"in t?t.cost:t.amount*t.safe_price,e)}`}function ln(t,e,a,n){let r=`${t.is_short?"Short":"Long"} ${a}
  ${nr(t.profit_ratio)} ${t.profit_abs?"("+rr(t.profit_abs,n)+")":""}
  ${kr(e,n)}
  Enter-tag: ${t.enter_tag??""}`;return r+=`${"ft_order_tag"in e&&e.ft_order_tag&&t.enter_tag!=e.ft_order_tag?`
Order-Tag: `+e.ft_order_tag:""}`,r+=`${t.exit_reason?`
Exit-Tag: `+t.exit_reason:""}`,r}function Qs(t,e,a){let n=`${t.is_short?"Short":"Long"} adjustment
  ${kr(e,a)}
  Enter-tag: ${t.enter_tag??""}`;return n+=`${"ft_order_tag"in e&&e.ft_order_tag?`
Order-Tag: `+e.ft_order_tag:""}`,n}const el="path://m 52.444161,104.1909 8.386653,25.34314 8.386651,25.34313 -16.731501,0.0422 -16.731501,0.0422 8.344848,-25.38539 z m 0.08656,-48.368126 8.386652,25.343139 8.386652,25.343137 -16.731501,0.0422 -16.731502,0.0422 8.344848,-25.385389 z",un="path://m 102.20764,19.885384 h 24.1454 v 41.928829 h -24.1454 z m 12.17344,36.423813 8.38665,25.343139 8.38666,25.343134 -16.7315,0.0422 -16.731507,0.0422 8.344847,-25.385386 z",Ot="#AD00FF",Mt="#0066FF";function tl(t,e){const a=[],n=t.data_stop_ts+t.timeframe_ms;for(let r=0,o=e.length;r<o;r+=1){const i=e[r],s=i.open_fill_timestamp??i.open_timestamp;if((Le(t.timeframe_ms??0,i.open_timestamp)<=n||!i.close_timestamp||i.close_timestamp&&i.close_timestamp>=t.data_start_ts)&&i.orders)for(let l=0;l<i.orders.length;l++){const u=i.orders[l],c=u.order_filled_timestamp??("order_timestamp"in u?u.order_timestamp:i.open_timestamp),{quoteCurrency:d}=ji(i.quote_currency??i.pair??"");c&&Le(t.timeframe_ms??0,c)<=n&&c>t.data_start_ts&&(l===0?a.push([Le(t.timeframe_ms??0,s),u.safe_price,un,u.ft_order_side=="sell"?180:0,i.is_short?Ot:Mt,(i.is_short?"Short":"Long")+(u.order_filled_timestamp?"":" (open)"),ln(i,u,"entry",d)]):l===i.orders.length-1&&i.close_timestamp?Le(t.timeframe_ms??0,i.close_timestamp)<=n&&i.close_timestamp>t.data_start_ts&&i.is_open===!1&&a.push([Le(t.timeframe_ms??0,i.close_timestamp),u.safe_price,un,i.is_short?0:180,i.is_short?Ot:Mt,nr(i.profit_ratio,2),ln(i,u,"exit",d)]):(u.ft_order_side!=="stoploss"||"filled"in u&&(u.filled??0)>0)&&a.push([Le(t.timeframe_ms??0,c),u.safe_price,el,u.ft_order_side=="sell"?180:0,i.is_short?Ot:Mt,"",Qs(i,u,d)]))}}return{tradeData:a}}function al(t,e,a,n){const{tradeData:r}=tl(a,n),o=n.filter(s=>s.is_open),i={name:t,type:"scatter",xAxisIndex:0,yAxisIndex:0,encode:{x:0,y:1,label:5,tooltip:6},label:{show:!0,fontSize:12,backgroundColor:e!=="dark"?"#fff":"#000",padding:2,color:e==="dark"?"#fff":"#000",rotate:75,offset:[10,0],align:"left"},itemStyle:{color:s=>s.data?s.data[4]:"#000",opacity:.9},symbol:s=>s[2],symbolRotate:s=>s[3],symbolSize:13,data:r};if(o.length>0){const s=a.timeframe_ms*10;i.markLine={symbol:"none",itemStyle:{color:"#ff0000AA"},label:{show:!0,position:"middle"},lineStyle:{type:"solid"},data:o.map(l=>[{name:"Stoploss",yAxis:l.stop_loss_abs,xAxis:a.data_stop_ts-s>l.open_timestamp?l.open_timestamp:a.data_stop_ts-s},{yAxis:l.stop_loss_abs,xAxis:l.close_timestamp??a.data_stop_ts+a.timeframe_ms}])}}return i}function nl(t,e,a,n){const r=t.indexOf(a),o=t.indexOf(n),i=r>0&&o>0;return i&&t.push(`${a}-${n}`),e.map(s=>{const l=s.slice();if(i){const u=l===null||l[o]===null||l[r]===null?null:l[o]-l[r];l.push(u)}return l})}function rl(t){const e=[];return"main_plot"in t&&Object.entries(t.main_plot).forEach(([a,n])=>{n.fill_to&&e.push([a,n.fill_to])}),"subplots"in t&&Object.values(t.subplots).forEach(a=>{Object.entries(a).forEach(([n,r])=>{r.fill_to&&e.push([n,r.fill_to])})}),e}var ol=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a.hasSymbolVisual=!0,a}return e.prototype.getInitialData=function(a,n){return Di(null,this,{useEncodeDefaulter:!0})},e.prototype.getProgressive=function(){var a=this.option.progressive;return a??(this.option.large?5e3:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var a=this.option.progressiveThreshold;return a??(this.option.large?1e4:this.get("progressiveThreshold"))},e.prototype.brushSelector=function(a,n,r){return r.point(n.getItemLayout(a))},e.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},e.type="series.scatter",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},e}(ir),Ir=4,il=function(){function t(){}return t}(),sl=function(t){V(e,t);function e(a){var n=t.call(this,a)||this;return n._off=0,n.hoverDataIdx=-1,n}return e.prototype.getDefaultShape=function(){return new il},e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.buildPath=function(a,n){var r=n.points,o=n.size,i=this.symbolProxy,s=i.shape,l=a.getContext?a.getContext():a,u=l&&o[0]<Ir,c=this.softClipShape,d;if(u){this._ctx=l;return}for(this._ctx=null,d=this._off;d<r.length;){var f=r[d++],p=r[d++];isNaN(f)||isNaN(p)||c&&!c.contain(f,p)||(s.x=f-o[0]/2,s.y=p-o[1]/2,s.width=o[0],s.height=o[1],i.buildPath(a,s,!0))}this.incremental&&(this._off=d,this.notClear=!0)},e.prototype.afterBrush=function(){var a=this.shape,n=a.points,r=a.size,o=this._ctx,i=this.softClipShape,s;if(o){for(s=this._off;s<n.length;){var l=n[s++],u=n[s++];isNaN(l)||isNaN(u)||i&&!i.contain(l,u)||o.fillRect(l-r[0]/2,u-r[1]/2,r[0],r[1])}this.incremental&&(this._off=s,this.notClear=!0)}},e.prototype.findDataIndex=function(a,n){for(var r=this.shape,o=r.points,i=r.size,s=Math.max(i[0],4),l=Math.max(i[1],4),u=o.length/2-1;u>=0;u--){var c=u*2,d=o[c]-s/2,f=o[c+1]-l/2;if(a>=d&&n>=f&&a<=d+s&&n<=f+l)return u}return-1},e.prototype.contain=function(a,n){var r=this.transformCoordToLocal(a,n),o=this.getBoundingRect();if(a=r[0],n=r[1],o.contain(a,n)){var i=this.hoverDataIdx=this.findDataIndex(a,n);return i>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var a=this._rect;if(!a){for(var n=this.shape,r=n.points,o=n.size,i=o[0],s=o[1],l=1/0,u=1/0,c=-1/0,d=-1/0,f=0;f<r.length;){var p=r[f++],h=r[f++];l=Math.min(p,l),c=Math.max(p,c),u=Math.min(h,u),d=Math.max(h,d)}a=this._rect=new ba(l-i/2,u-s/2,c-l+i,d-u+s)}return a},e}(it),ll=function(){function t(){this.group=new we}return t.prototype.updateData=function(e,a){this._clear();var n=this._create();n.setShape({points:e.getLayout("points")}),this._setCommon(n,e,a)},t.prototype.updateLayout=function(e){var a=e.getLayout("points");this.group.eachChild(function(n){if(n.startIndex!=null){var r=(n.endIndex-n.startIndex)*2,o=n.startIndex*4*2;a=new Float32Array(a.buffer,o,r)}n.setShape("points",a),n.reset()})},t.prototype.incrementalPrepareUpdate=function(e){this._clear()},t.prototype.incrementalUpdate=function(e,a,n){var r=this._newAdded[0],o=a.getLayout("points"),i=r&&r.shape.points;if(i&&i.length<2e4){var s=i.length,l=new Float32Array(s+o.length);l.set(i),l.set(o,s),r.endIndex=e.end,r.setShape({points:l})}else{this._newAdded=[];var u=this._create();u.startIndex=e.start,u.endIndex=e.end,u.incremental=!0,u.setShape({points:o}),this._setCommon(u,a,n)}},t.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},t.prototype._create=function(){var e=new sl({cursor:"default"});return e.ignoreCoarsePointer=!0,this.group.add(e),this._newAdded.push(e),e},t.prototype._setCommon=function(e,a,n){var r=a.hostModel;n=n||{};var o=a.getVisual("symbolSize");e.setShape("size",o instanceof Array?o:[o,o]),e.softClipShape=n.clipShape||null,e.symbolProxy=xa(a.getVisual("symbol"),0,0,0,0),e.setColor=e.symbolProxy.setColor;var i=e.shape.size[0]<Ir;e.useStyle(r.getModel("itemStyle").getItemStyle(i?["color","shadowBlur","shadowColor"]:["color"]));var s=a.getVisual("style"),l=s&&s.fill;l&&e.setColor(l);var u=Re(e);u.seriesIndex=r.seriesIndex,e.on("mousemove",function(c){u.dataIndex=null;var d=e.hoverDataIdx;d>=0&&(u.dataIndex=d+(e.startIndex||0))})},t.prototype.remove=function(){this._clear()},t.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},t}(),ul=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.render=function(a,n,r){var o=a.getData(),i=this._updateSymbolDraw(o,a);i.updateData(o,{clipShape:this._getClipShape(a)}),this._finished=!0},e.prototype.incrementalPrepareRender=function(a,n,r){var o=a.getData(),i=this._updateSymbolDraw(o,a);i.incrementalPrepareUpdate(o),this._finished=!1},e.prototype.incrementalRender=function(a,n,r){this._symbolDraw.incrementalUpdate(a,n.getData(),{clipShape:this._getClipShape(n)}),this._finished=a.end===n.getData().count()},e.prototype.updateTransform=function(a,n,r){var o=a.getData();if(this.group.dirty(),!this._finished||o.count()>1e4)return{update:!0};var i=Ar("").reset(a,n,r);i.progress&&i.progress({start:0,end:o.count(),count:o.count()},o),this._symbolDraw.updateLayout(o)},e.prototype.eachRendered=function(a){this._symbolDraw&&this._symbolDraw.eachRendered(a)},e.prototype._getClipShape=function(a){if(a.get("clip",!0)){var n=a.coordinateSystem;return n&&n.getArea&&n.getArea(.1)}},e.prototype._updateSymbolDraw=function(a,n){var r=this._symbolDraw,o=n.pipelineContext,i=o.large;return(!r||i!==this._isLargeDraw)&&(r&&r.remove(),r=this._symbolDraw=i?new ll:new Pi,this._isLargeDraw=i,this.group.removeAll()),this.group.add(r.group),r},e.prototype.remove=function(a,n){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},e.prototype.dispose=function(){},e.type="scatter",e}(sr);function cl(t){Sa(Li),t.registerSeriesModel(ol),t.registerChartView(ul),t.registerLayout(Ar("scatter"))}var dl={axisPointer:1,tooltip:1,brush:1};function fl(t,e,a){var n=e.getComponentByElement(t.topTarget),r=n&&n.coordinateSystem;return n&&n!==a&&!dl.hasOwnProperty(n.mainType)&&r&&r.model!==a}var cn=Jt.prototype,Bt=_o.prototype,Or=function(){function t(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return t}();(function(t){V(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}return e})(Or);function Vt(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}var pl=function(t){V(e,t);function e(a){var n=t.call(this,a)||this;return n.type="ec-line",n}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Or},e.prototype.buildPath=function(a,n){Vt(n)?cn.buildPath.call(this,a,n):Bt.buildPath.call(this,a,n)},e.prototype.pointAt=function(a){return Vt(this.shape)?cn.pointAt.call(this,a):Bt.pointAt.call(this,a)},e.prototype.tangentAt=function(a){var n=this.shape,r=Vt(n)?[n.x2-n.x1,n.y2-n.y1]:Bt.tangentAt.call(this,a);return lr(r,r)},e}(it),Et=["fromSymbol","toSymbol"];function dn(t){return"_"+t+"Type"}function fn(t,e,a){var n=e.getItemVisual(a,t);if(!n||n==="none")return n;var r=e.getItemVisual(a,t+"Size"),o=e.getItemVisual(a,t+"Rotate"),i=e.getItemVisual(a,t+"Offset"),s=e.getItemVisual(a,t+"KeepAspect"),l=wa(r),u=Aa(i||0,l);return n+l+u+(o||"")+(s||"")}function pn(t,e,a){var n=e.getItemVisual(a,t);if(!(!n||n==="none")){var r=e.getItemVisual(a,t+"Size"),o=e.getItemVisual(a,t+"Rotate"),i=e.getItemVisual(a,t+"Offset"),s=e.getItemVisual(a,t+"KeepAspect"),l=wa(r),u=Aa(i||0,l),c=xa(n,-l[0]/2+u[0],-l[1]/2+u[1],l[0],l[1],null,s);return c.__specifiedRotation=o==null||isNaN(o)?void 0:+o*Math.PI/180||0,c.name=t,c}}function hl(t){var e=new pl({name:"line",subPixelOptimize:!0});return ra(e.shape,t),e}function ra(t,e){t.x1=e[0][0],t.y1=e[0][1],t.x2=e[1][0],t.y2=e[1][1],t.percent=1;var a=e[2];a?(t.cpx1=a[0],t.cpy1=a[1]):(t.cpx1=NaN,t.cpy1=NaN)}var vl=function(t){V(e,t);function e(a,n,r){var o=t.call(this)||this;return o._createLine(a,n,r),o}return e.prototype._createLine=function(a,n,r){var o=a.hostModel,i=a.getItemLayout(n),s=hl(i);s.shape.percent=0,ur(s,{shape:{percent:1}},o,n),this.add(s),D(Et,function(l){var u=pn(l,a,n);this.add(u),this[dn(l)]=fn(l,a,n)},this),this._updateCommonStl(a,n,r)},e.prototype.updateData=function(a,n,r){var o=a.hostModel,i=this.childOfName("line"),s=a.getItemLayout(n),l={shape:{}};ra(l.shape,s),Ca(i,l,o,n),D(Et,function(u){var c=fn(u,a,n),d=dn(u);if(this[d]!==c){this.remove(this.childOfName(u));var f=pn(u,a,n);this.add(f)}this[d]=c},this),this._updateCommonStl(a,n,r)},e.prototype.getLinePath=function(){return this.childAt(0)},e.prototype._updateCommonStl=function(a,n,r){var o=a.hostModel,i=this.childOfName("line"),s=r&&r.emphasisLineStyle,l=r&&r.blurLineStyle,u=r&&r.selectLineStyle,c=r&&r.labelStatesModels,d=r&&r.emphasisDisabled,f=r&&r.focus,p=r&&r.blurScope;if(!r||a.hasItemOption){var h=a.getItemModel(n),g=h.getModel("emphasis");s=g.getModel("lineStyle").getLineStyle(),l=h.getModel(["blur","lineStyle"]).getLineStyle(),u=h.getModel(["select","lineStyle"]).getLineStyle(),d=g.get("disabled"),f=g.get("focus"),p=g.get("blurScope"),c=cr(h)}var m=a.getItemVisual(n,"style"),_=m.stroke;i.useStyle(m),i.style.fill=null,i.style.strokeNoScale=!0,i.ensureState("emphasis").style=s,i.ensureState("blur").style=l,i.ensureState("select").style=u,D(Et,function(x){var w=this.childOfName(x);if(w){w.setColor(_),w.style.opacity=m.opacity;for(var P=0;P<qa.length;P++){var A=qa[P],I=i.getState(A);if(I){var T=I.style||{},L=w.ensureState(A),E=L.style||(L.style={});T.stroke!=null&&(E[w.__isEmptyBrush?"stroke":"fill"]=T.stroke),T.opacity!=null&&(E.opacity=T.opacity)}}w.markRedraw()}},this);var b=o.getRawValue(n);bo(this,c,{labelDataIndex:n,labelFetcher:{getFormattedLabel:function(x,w){return o.getFormattedLabel(x,w,a.dataType)}},inheritColor:_||"#000",defaultOpacity:m.opacity,defaultText:(b==null?a.getName(n):isFinite(b)?xo(b):b)+""});var y=this.getTextContent();if(y){var v=c.normal;y.__align=y.style.align,y.__verticalAlign=y.style.verticalAlign,y.__position=v.get("position")||"middle";var S=v.get("distance");W(S)||(S=[S,S]),y.__labelDistance=S}this.setTextConfig({position:null,local:!0,inside:!1}),So(this,f,p,d)},e.prototype.highlight=function(){Qt(this)},e.prototype.downplay=function(){ea(this)},e.prototype.updateLayout=function(a,n){this.setLinePoints(a.getItemLayout(n))},e.prototype.setLinePoints=function(a){var n=this.childOfName("line");ra(n.shape,a),n.dirty()},e.prototype.beforeUpdate=function(){var a=this,n=a.childOfName("fromSymbol"),r=a.childOfName("toSymbol"),o=a.getTextContent();if(!n&&!r&&(!o||o.ignore))return;for(var i=1,s=this.parent;s;)s.scaleX&&(i/=s.scaleX),s=s.parent;var l=a.childOfName("line");if(!this.__dirty&&!l.__dirty)return;var u=l.shape.percent,c=l.pointAt(0),d=l.pointAt(u),f=Co([],d,c);lr(f,f);function p(I,T){var L=I.__specifiedRotation;if(L==null){var E=l.tangentAt(T);I.attr("rotation",(T===1?-1:1)*Math.PI/2-Math.atan2(E[1],E[0]))}else I.attr("rotation",L)}if(n&&(n.setPosition(c),p(n,0),n.scaleX=n.scaleY=i*u,n.markRedraw()),r&&(r.setPosition(d),p(r,1),r.scaleX=r.scaleY=i*u,r.markRedraw()),o&&!o.ignore){o.x=o.y=0,o.originX=o.originY=0;var h=void 0,g=void 0,m=o.__labelDistance,_=m[0]*i,b=m[1]*i,y=u/2,v=l.tangentAt(y),S=[v[1],-v[0]],x=l.pointAt(y);S[1]>0&&(S[0]=-S[0],S[1]=-S[1]);var w=v[0]<0?-1:1;if(o.__position!=="start"&&o.__position!=="end"){var P=-Math.atan2(v[1],v[0]);d[0]<c[0]&&(P=Math.PI+P),o.rotation=P}var A=void 0;switch(o.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":A=-b,g="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":A=b,g="top";break;default:A=0,g="middle"}switch(o.__position){case"end":o.x=f[0]*_+d[0],o.y=f[1]*b+d[1],h=f[0]>.8?"left":f[0]<-.8?"right":"center",g=f[1]>.8?"top":f[1]<-.8?"bottom":"middle";break;case"start":o.x=-f[0]*_+c[0],o.y=-f[1]*b+c[1],h=f[0]>.8?"right":f[0]<-.8?"left":"center",g=f[1]>.8?"bottom":f[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":o.x=_*w+c[0],o.y=c[1]+A,h=v[0]<0?"right":"left",o.originX=-_*w,o.originY=-A;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":o.x=x[0],o.y=x[1]+A,h="center",o.originY=-A;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":o.x=-_*w+d[0],o.y=d[1]+A,h=v[0]>=0?"right":"left",o.originX=_*w,o.originY=-A;break}o.scaleX=o.scaleY=i,o.setStyle({verticalAlign:o.__verticalAlign||g,align:o.__align||h})}},e}(we),gl=function(){function t(e){this.group=new we,this._LineCtor=e||vl}return t.prototype.updateData=function(e){var a=this;this._progressiveEls=null;var n=this,r=n.group,o=n._lineData;n._lineData=e,o||r.removeAll();var i=hn(e);e.diff(o).add(function(s){a._doAdd(e,s,i)}).update(function(s,l){a._doUpdate(o,e,l,s,i)}).remove(function(s){r.remove(o.getItemGraphicEl(s))}).execute()},t.prototype.updateLayout=function(){var e=this._lineData;e&&e.eachItemGraphicEl(function(a,n){a.updateLayout(e,n)},this)},t.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=hn(e),this._lineData=null,this.group.removeAll()},t.prototype.incrementalUpdate=function(e,a){this._progressiveEls=[];function n(s){!s.isGroup&&!ml(s)&&(s.incremental=!0,s.ensureState("emphasis").hoverLayer=!0)}for(var r=e.start;r<e.end;r++){var o=a.getItemLayout(r);if(Nt(o)){var i=new this._LineCtor(a,r,this._seriesScope);i.traverse(n),this.group.add(i),a.setItemGraphicEl(r,i),this._progressiveEls.push(i)}}},t.prototype.remove=function(){this.group.removeAll()},t.prototype.eachRendered=function(e){dr(this._progressiveEls||this.group,e)},t.prototype._doAdd=function(e,a,n){var r=e.getItemLayout(a);if(Nt(r)){var o=new this._LineCtor(e,a,n);e.setItemGraphicEl(a,o),this.group.add(o)}},t.prototype._doUpdate=function(e,a,n,r,o){var i=e.getItemGraphicEl(n);if(!Nt(a.getItemLayout(r))){this.group.remove(i);return}i?i.updateData(a,r,o):i=new this._LineCtor(a,r,o),a.setItemGraphicEl(r,i),this.group.add(i)},t}();function ml(t){return t.animators&&t.animators.length>0}function hn(t){var e=t.hostModel,a=e.getModel("emphasis");return{lineStyle:e.getModel("lineStyle").getLineStyle(),emphasisLineStyle:a.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:e.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:e.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:a.get("disabled"),blurScope:a.get("blurScope"),focus:a.get("focus"),labelStatesModels:cr(e)}}function vn(t){return isNaN(t[0])||isNaN(t[1])}function Nt(t){return t&&!vn(t[0])&&!vn(t[1])}var Oe=!0,rt=Math.min,$e=Math.max,yl=Math.pow,_l=1e4,bl=6,xl=6,gn="globalPan",Sl={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},Cl={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},mn={brushStyle:{lineWidth:2,stroke:"rgba(210,219,238,0.3)",fill:"#D2DBEE"},transformable:!0,brushMode:"single",removeOnClick:!1},wl=0,Al=function(t){V(e,t);function e(a){var n=t.call(this)||this;return n._track=[],n._covers=[],n._handlers={},n._zr=a,n.group=new we,n._uid="brushController_"+wl++,D(Ol,function(r,o){this._handlers[o]=oe(r,this)},n),n}return e.prototype.enableBrush=function(a){return this._brushType&&this._doDisableBrush(),a.brushType&&this._doEnableBrush(a),this},e.prototype._doEnableBrush=function(a){var n=this._zr;this._enableGlobalPan||ki(n,gn,this._uid),D(this._handlers,function(r,o){n.on(o,r)}),this._brushType=a.brushType,this._brushOption=le(ve(mn),a,!0)},e.prototype._doDisableBrush=function(){var a=this._zr;Ii(a,gn,this._uid),D(this._handlers,function(n,r){a.off(r,n)}),this._brushType=this._brushOption=null},e.prototype.setPanels=function(a){if(a&&a.length){var n=this._panels={};D(a,function(r){n[r.panelId]=ve(r)})}else this._panels=null;return this},e.prototype.mount=function(a){a=a||{},this._enableGlobalPan=a.enableGlobalPan;var n=this.group;return this._zr.add(n),n.attr({x:a.x||0,y:a.y||0,rotation:a.rotation||0,scaleX:a.scaleX||1,scaleY:a.scaleY||1}),this._transform=n.getLocalTransform(),this},e.prototype.updateCovers=function(a){a=Y(a,function(f){return le(ve(mn),f,!0)});var n="\0-brush-index-",r=this._covers,o=this._covers=[],i=this,s=this._creatingCover;return new fr(r,a,u,l).add(c).update(c).remove(d).execute(),this;function l(f,p){return(f.id!=null?f.id:n+p)+"-"+f.brushType}function u(f,p){return l(f.__brushOption,p)}function c(f,p){var h=a[f];if(p!=null&&r[p]===s)o[f]=r[p];else{var g=o[f]=p!=null?(r[p].__brushOption=h,r[p]):Br(i,Mr(i,h));Ia(i,g)}}function d(f){r[f]!==s&&i.group.remove(r[f])}},e.prototype.unmount=function(){return this.enableBrush(!1),oa(this),this._zr.remove(this.group),this},e.prototype.dispose=function(){this.unmount(),this.off()},e}(wo);function Mr(t,e){var a=Ct[e.brushType].createCover(t,e);return a.__brushOption=e,Er(a,e),t.group.add(a),a}function Br(t,e){var a=Oa(e);return a.endCreating&&(a.endCreating(t,e),Er(e,e.__brushOption)),e}function Vr(t,e){var a=e.__brushOption;Oa(e).updateCoverShape(t,e,a.range,a)}function Er(t,e){var a=e.z;a==null&&(a=_l),t.traverse(function(n){n.z=a,n.z2=a})}function Ia(t,e){Oa(e).updateCommon(t,e),Vr(t,e)}function Oa(t){return Ct[t.__brushOption.brushType]}function Ma(t,e,a){var n=t._panels;if(!n)return Oe;var r,o=t._transform;return D(n,function(i){i.isTargetByCursor(e,a,o)&&(r=i)}),r}function Nr(t,e){var a=t._panels;if(!a)return Oe;var n=e.__brushOption.panelId;return n!=null?a[n]:Oe}function oa(t){var e=t._covers,a=e.length;return D(e,function(n){t.group.remove(n)},t),e.length=0,!!a}function Me(t,e){var a=Y(t._covers,function(n){var r=n.__brushOption,o=ve(r.range);return{brushType:r.brushType,panelId:r.panelId,range:o}});t.trigger("brush",{areas:a,isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function Tl(t){var e=t._track;if(!e.length)return!1;var a=e[e.length-1],n=e[0],r=a[0]-n[0],o=a[1]-n[1],i=yl(r*r+o*o,.5);return i>bl}function Rr(t){var e=t.length-1;return e<0&&(e=0),[t[0],t[e]]}function zr(t,e,a,n){var r=new we;return r.add(new ta({name:"main",style:Ba(a),silent:!0,draggable:!0,cursor:"move",drift:q(yn,t,e,r,["n","s","w","e"]),ondragend:q(Me,e,{isEnd:!0})})),D(n,function(o){r.add(new ta({name:o.join(""),style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:q(yn,t,e,r,o),ondragend:q(Me,e,{isEnd:!0})}))}),r}function $r(t,e,a,n){var r=n.brushStyle.lineWidth||0,o=$e(r,xl),i=a[0][0],s=a[1][0],l=i-r/2,u=s-r/2,c=a[0][1],d=a[1][1],f=c-o+r/2,p=d-o+r/2,h=c-i,g=d-s,m=h+r,_=g+r;ye(t,e,"main",i,s,h,g),n.transformable&&(ye(t,e,"w",l,u,o,_),ye(t,e,"e",f,u,o,_),ye(t,e,"n",l,u,m,o),ye(t,e,"s",l,p,m,o),ye(t,e,"nw",l,u,o,o),ye(t,e,"ne",f,u,o,o),ye(t,e,"sw",l,p,o,o),ye(t,e,"se",f,p,o,o))}function ia(t,e){var a=e.__brushOption,n=a.transformable,r=e.childAt(0);r.useStyle(Ba(a)),r.attr({silent:!n,cursor:n?"move":"default"}),D([["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]],function(o){var i=e.childOfName(o.join("")),s=o.length===1?sa(t,o[0]):Pl(t,o);i&&i.attr({silent:!n,invisible:!n,cursor:n?Cl[s]+"-resize":null})})}function ye(t,e,a,n,r,o,i){var s=e.childOfName(a);s&&s.setShape(kl(Va(t,e,[[n,r],[n+o,r+i]])))}function Ba(t){return Ge({strokeNoScale:!0},t.brushStyle)}function Fr(t,e,a,n){var r=[rt(t,a),rt(e,n)],o=[$e(t,a),$e(e,n)];return[[r[0],o[0]],[r[1],o[1]]]}function Dl(t){return hr(t.group)}function sa(t,e){var a={w:"left",e:"right",n:"top",s:"bottom"},n={left:"w",right:"e",top:"n",bottom:"s"},r=To(a[e],Dl(t));return n[r]}function Pl(t,e){var a=[sa(t,e[0]),sa(t,e[1])];return(a[0]==="e"||a[0]==="w")&&a.reverse(),a.join("")}function yn(t,e,a,n,r,o){var i=a.__brushOption,s=t.toRectRange(i.range),l=Wr(e,r,o);D(n,function(u){var c=Sl[u];s[c[0]][c[1]]+=l[c[0]]}),i.range=t.fromRectRange(Fr(s[0][0],s[1][0],s[0][1],s[1][1])),Ia(e,a),Me(e,{isEnd:!1})}function Ll(t,e,a,n){var r=e.__brushOption.range,o=Wr(t,a,n);D(r,function(i){i[0]+=o[0],i[1]+=o[1]}),Ia(t,e),Me(t,{isEnd:!1})}function Wr(t,e,a){var n=t.group,r=n.transformCoordToLocal(e,a),o=n.transformCoordToLocal(0,0);return[r[0]-o[0],r[1]-o[1]]}function Va(t,e,a){var n=Nr(t,e);return n&&n!==Oe?n.clipPath(a,t._transform):ve(a)}function kl(t){var e=rt(t[0][0],t[1][0]),a=rt(t[0][1],t[1][1]),n=$e(t[0][0],t[1][0]),r=$e(t[0][1],t[1][1]);return{x:e,y:a,width:n-e,height:r-a}}function Il(t,e,a){if(!(!t._brushType||Ml(t,e.offsetX,e.offsetY))){var n=t._zr,r=t._covers,o=Ma(t,e,a);if(!t._dragging)for(var i=0;i<r.length;i++){var s=r[i].__brushOption;if(o&&(o===Oe||s.panelId===o.panelId)&&Ct[s.brushType].contain(r[i],a[0],a[1]))return}o&&n.setCursorStyle("crosshair")}}function la(t){var e=t.event;e.preventDefault&&e.preventDefault()}function ua(t,e,a){return t.childOfName("main").contain(e,a)}function Ur(t,e,a,n){var r=t._creatingCover,o=t._creatingPanel,i=t._brushOption,s;if(t._track.push(a.slice()),Tl(t)||r){if(o&&!r){i.brushMode==="single"&&oa(t);var l=ve(i);l.brushType=_n(l.brushType,o),l.panelId=o===Oe?null:o.panelId,r=t._creatingCover=Mr(t,l),t._covers.push(r)}if(r){var u=Ct[_n(t._brushType,o)],c=r.__brushOption;c.range=u.getCreatingRange(Va(t,r,t._track)),n&&(Br(t,r),u.updateCommon(t,r)),Vr(t,r),s={isEnd:n}}}else n&&i.brushMode==="single"&&i.removeOnClick&&Ma(t,e,a)&&oa(t)&&(s={isEnd:n,removeOnClick:!0});return s}function _n(t,e){return t==="auto"?e.defaultBrushType:t}var Ol={mousedown:function(t){if(this._dragging)bn(this,t);else if(!t.target||!t.target.draggable){la(t);var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY);this._creatingCover=null;var a=this._creatingPanel=Ma(this,t,e);a&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(t){var e=t.offsetX,a=t.offsetY,n=this.group.transformCoordToLocal(e,a);if(Il(this,t,n),this._dragging){la(t);var r=Ur(this,t,n,!1);r&&Me(this,r)}},mouseup:function(t){bn(this,t)}};function bn(t,e){if(t._dragging){la(e);var a=e.offsetX,n=e.offsetY,r=t.group.transformCoordToLocal(a,n),o=Ur(t,e,r,!0);t._dragging=!1,t._track=[],t._creatingCover=null,o&&Me(t,o)}}function Ml(t,e,a){var n=t._zr;return e<0||e>n.getWidth()||a<0||a>n.getHeight()}var Ct={lineX:xn(0),lineY:xn(1),rect:{createCover:function(t,e){function a(n){return n}return zr({toRectRange:a,fromRectRange:a},t,e,[["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]])},getCreatingRange:function(t){var e=Rr(t);return Fr(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(t,e,a,n){$r(t,e,a,n)},updateCommon:ia,contain:ua},polygon:{createCover:function(t,e){var a=new we;return a.add(new pr({name:"main",style:Ba(e),silent:!0})),a},getCreatingRange:function(t){return t},endCreating:function(t,e){e.remove(e.childAt(0)),e.add(new Ao({name:"main",draggable:!0,drift:q(Ll,t,e),ondragend:q(Me,t,{isEnd:!0})}))},updateCoverShape:function(t,e,a,n){e.childAt(0).setShape({points:Va(t,e,a)})},updateCommon:ia,contain:ua}};function xn(t){return{createCover:function(e,a){return zr({toRectRange:function(n){var r=[n,[0,100]];return t&&r.reverse(),r},fromRectRange:function(n){return n[t]}},e,a,[[["w"],["e"]],[["n"],["s"]]][t])},getCreatingRange:function(e){var a=Rr(e),n=rt(a[0][t],a[1][t]),r=$e(a[0][t],a[1][t]);return[n,r]},updateCoverShape:function(e,a,n,r){var o,i=Nr(e,a);if(i!==Oe&&i.getLinearBrushOtherExtent)o=i.getLinearBrushOtherExtent(t);else{var s=e._zr;o=[0,[s.getWidth(),s.getHeight()][1-t]]}var l=[n,o];t&&l.reverse(),$r(e,a,l,r)},updateCommon:ia,contain:ua}}function Bl(t){return t=Ea(t),function(e){return Do(e,t)}}function Vl(t,e){return t=Ea(t),function(a){var n=e??a,r=n?t.width:t.height,o=n?t.x:t.y;return[o,o+(r||0)]}}function El(t,e,a){var n=Ea(t);return function(r,o){return n.contain(o[0],o[1])&&!fl(r,e,a)}}function Ea(t){return ba.create(t)}var Nl=function(){function t(){}return t.prototype.getInitialData=function(e,a){var n,r=a.getComponent("xAxis",this.get("xAxisIndex")),o=a.getComponent("yAxis",this.get("yAxisIndex")),i=r.get("type"),s=o.get("type"),l;i==="category"?(e.layout="horizontal",n=r.getOrdinalMeta(),l=!0):s==="category"?(e.layout="vertical",n=o.getOrdinalMeta(),l=!0):e.layout=e.layout||"horizontal";var u=["x","y"],c=e.layout==="horizontal"?0:1,d=this._baseAxisDim=u[c],f=u[1-c],p=[r,o],h=p[c].get("type"),g=p[1-c].get("type"),m=e.data;if(m&&l){var _=[];D(m,function(v,S){var x;W(v)?(x=v.slice(),v.unshift(S)):W(v.value)?(x=J({},v),x.value=x.value.slice(),v.value.unshift(S)):x=v,_.push(x)}),e.data=_}var b=this.defaultValueDimensions,y=[{name:d,type:Xa(h),ordinalMeta:n,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:f,type:Xa(g),dimsDef:b.slice()}];return Hi(this,{coordDimensions:y,dimensionsCount:b.length+1,encodeDefaulter:q(Po,y,this)})},t.prototype.getBaseAxis=function(){var e=this._baseAxisDim;return this.ecModel.getComponent(e+"Axis",this.get(e+"AxisIndex")).axis},t}(),Rl=["color","borderColor"],zl=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.render=function(a,n,r){this.group.removeClipPath(),this._progressiveEls=null,this._updateDrawMode(a),this._isLargeDraw?this._renderLarge(a):this._renderNormal(a)},e.prototype.incrementalPrepareRender=function(a,n,r){this._clear(),this._updateDrawMode(a)},e.prototype.incrementalRender=function(a,n,r,o){this._progressiveEls=[],this._isLargeDraw?this._incrementalRenderLarge(a,n):this._incrementalRenderNormal(a,n)},e.prototype.eachRendered=function(a){dr(this._progressiveEls||this.group,a)},e.prototype._updateDrawMode=function(a){var n=a.pipelineContext.large;(this._isLargeDraw==null||n!==this._isLargeDraw)&&(this._isLargeDraw=n,this._clear())},e.prototype._renderNormal=function(a){var n=a.getData(),r=this._data,o=this.group,i=n.getLayout("isSimpleBox"),s=a.get("clip",!0),l=a.coordinateSystem,u=l.getArea&&l.getArea();this._data||o.removeAll(),n.diff(r).add(function(c){if(n.hasValue(c)){var d=n.getItemLayout(c);if(s&&Sn(u,d))return;var f=Rt(d,c,!0);ur(f,{shape:{points:d.ends}},a,c),zt(f,n,c,i),o.add(f),n.setItemGraphicEl(c,f)}}).update(function(c,d){var f=r.getItemGraphicEl(d);if(!n.hasValue(c)){o.remove(f);return}var p=n.getItemLayout(c);if(s&&Sn(u,p)){o.remove(f);return}f?(Ca(f,{shape:{points:p.ends}},a,c),Lo(f)):f=Rt(p),zt(f,n,c,i),o.add(f),n.setItemGraphicEl(c,f)}).remove(function(c){var d=r.getItemGraphicEl(c);d&&o.remove(d)}).execute(),this._data=n},e.prototype._renderLarge=function(a){this._clear(),Cn(a,this.group);var n=a.get("clip",!0)?Oi(a.coordinateSystem,!1,a):null;n?this.group.setClipPath(n):this.group.removeClipPath()},e.prototype._incrementalRenderNormal=function(a,n){for(var r=n.getData(),o=r.getLayout("isSimpleBox"),i;(i=a.next())!=null;){var s=r.getItemLayout(i),l=Rt(s);zt(l,r,i,o),l.incremental=!0,this.group.add(l),this._progressiveEls.push(l)}},e.prototype._incrementalRenderLarge=function(a,n){Cn(n,this.group,this._progressiveEls,!0)},e.prototype.remove=function(a){this._clear()},e.prototype._clear=function(){this.group.removeAll(),this._data=null},e.type="candlestick",e}(sr),$l=function(){function t(){}return t}(),Fl=function(t){V(e,t);function e(a){var n=t.call(this,a)||this;return n.type="normalCandlestickBox",n}return e.prototype.getDefaultShape=function(){return new $l},e.prototype.buildPath=function(a,n){var r=n.points;this.__simpleBox?(a.moveTo(r[4][0],r[4][1]),a.lineTo(r[6][0],r[6][1])):(a.moveTo(r[0][0],r[0][1]),a.lineTo(r[1][0],r[1][1]),a.lineTo(r[2][0],r[2][1]),a.lineTo(r[3][0],r[3][1]),a.closePath(),a.moveTo(r[4][0],r[4][1]),a.lineTo(r[5][0],r[5][1]),a.moveTo(r[6][0],r[6][1]),a.lineTo(r[7][0],r[7][1]))},e}(it);function Rt(t,e,a){var n=t.ends;return new Fl({shape:{points:a?Wl(n,t):n},z2:100})}function Sn(t,e){for(var a=!0,n=0;n<e.ends.length;n++)if(t.contain(e.ends[n][0],e.ends[n][1])){a=!1;break}return a}function zt(t,e,a,n){var r=e.getItemModel(a);t.useStyle(e.getItemVisual(a,"style")),t.style.strokeNoScale=!0,t.__simpleBox=n,ko(t,r)}function Wl(t,e){return Y(t,function(a){return a=a.slice(),a[1]=e.initBaseline,a})}var Ul=function(){function t(){}return t}(),$t=function(t){V(e,t);function e(a){var n=t.call(this,a)||this;return n.type="largeCandlestickBox",n}return e.prototype.getDefaultShape=function(){return new Ul},e.prototype.buildPath=function(a,n){for(var r=n.points,o=0;o<r.length;)if(this.__sign===r[o++]){var i=r[o++];a.moveTo(i,r[o++]),a.lineTo(i,r[o++])}else o+=3},e}(it);function Cn(t,e,a,n){var r=t.getData(),o=r.getLayout("largePoints"),i=new $t({shape:{points:o},__sign:1,ignoreCoarsePointer:!0});e.add(i);var s=new $t({shape:{points:o},__sign:-1,ignoreCoarsePointer:!0});e.add(s);var l=new $t({shape:{points:o},__sign:0,ignoreCoarsePointer:!0});e.add(l),Ft(1,i,t),Ft(-1,s,t),Ft(0,l,t),n&&(i.incremental=!0,s.incremental=!0),a&&a.push(i,s)}function Ft(t,e,a,n){var r=a.get(["itemStyle",t>0?"borderColor":"borderColor0"])||a.get(["itemStyle",t>0?"color":"color0"]);t===0&&(r=a.get(["itemStyle","borderColorDoji"]));var o=a.getModel("itemStyle").getItemStyle(Rl);e.useStyle(o),e.style.fill=null,e.style.stroke=r}var Gr=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a.defaultValueDimensions=[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],a}return e.prototype.getShadowDim=function(){return"open"},e.prototype.brushSelector=function(a,n,r){var o=n.getItemLayout(a);return o&&r.rect(o.brushRect)},e.type="series.candlestick",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,clip:!0,itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderColorDoji:null,borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationEasing:"linear",animationDuration:300},e}(ir);Ta(Gr,Nl,!0);function Gl(t){!t||!W(t.series)||D(t.series,function(e){at(e)&&e.type==="k"&&(e.type="candlestick")})}var Zl=["itemStyle","borderColor"],Hl=["itemStyle","borderColor0"],jl=["itemStyle","borderColorDoji"],Yl=["itemStyle","color"],Kl=["itemStyle","color0"],ql={seriesType:"candlestick",plan:vr(),performRawSeries:!0,reset:function(t,e){function a(o,i){return i.get(o>0?Yl:Kl)}function n(o,i){return i.get(o===0?jl:o>0?Zl:Hl)}if(!e.isSeriesFiltered(t)){var r=t.pipelineContext.large;return!r&&{progress:function(o,i){for(var s;(s=o.next())!=null;){var l=i.getItemModel(s),u=i.getItemLayout(s).sign,c=l.getItemStyle();c.fill=a(u,l),c.stroke=n(u,l)||c.fill;var d=i.ensureUniqueItemVisual(s,"style");J(d,c)}}}}}},Xl={seriesType:"candlestick",plan:vr(),reset:function(t){var e=t.coordinateSystem,a=t.getData(),n=Jl(t,a),r=0,o=1,i=["x","y"],s=a.getDimensionIndex(a.mapDimension(i[r])),l=Y(a.mapDimensionsAll(i[o]),a.getDimensionIndex,a),u=l[0],c=l[1],d=l[2],f=l[3];if(a.setLayout({candleWidth:n,isSimpleBox:n<=1.3}),s<0||l.length<4)return;return{progress:t.pipelineContext.large?h:p};function p(g,m){for(var _,b=m.getStore();(_=g.next())!=null;){var y=b.get(s,_),v=b.get(u,_),S=b.get(c,_),x=b.get(d,_),w=b.get(f,_),P=Math.min(v,S),A=Math.max(v,S),I=F(P,y),T=F(A,y),L=F(x,y),E=F(w,y),G=[];Te(G,T,0),Te(G,I,1),G.push(me(E),me(T),me(L),me(I));var j=m.getItemModel(_),ge=!!j.get(["itemStyle","borderColorDoji"]);m.setItemLayout(_,{sign:wn(b,_,v,S,c,ge),initBaseline:v>S?T[o]:I[o],ends:G,brushRect:He(x,w,y)})}function F(R,ce){var ae=[];return ae[r]=ce,ae[o]=R,isNaN(ce)||isNaN(R)?[NaN,NaN]:e.dataToPoint(ae)}function Te(R,ce,ae){var k=ce.slice(),H=ce.slice();k[r]=kt(k[r]+n/2,1,!1),H[r]=kt(H[r]-n/2,1,!0),ae?R.push(k,H):R.push(H,k)}function He(R,ce,ae){var k=F(R,ae),H=F(ce,ae);return k[r]-=n/2,H[r]-=n/2,{x:k[0],y:k[1],width:n,height:H[1]-k[1]}}function me(R){return R[r]=kt(R[r],1),R}}function h(g,m){for(var _=Io(g.count*4),b=0,y,v=[],S=[],x,w=m.getStore(),P=!!t.get(["itemStyle","borderColorDoji"]);(x=g.next())!=null;){var A=w.get(s,x),I=w.get(u,x),T=w.get(c,x),L=w.get(d,x),E=w.get(f,x);if(isNaN(A)||isNaN(L)||isNaN(E)){_[b++]=NaN,b+=3;continue}_[b++]=wn(w,x,I,T,c,P),v[r]=A,v[o]=L,y=e.dataToPoint(v,null,S),_[b++]=y?y[0]:NaN,_[b++]=y?y[1]:NaN,v[o]=E,y=e.dataToPoint(v,null,S),_[b++]=y?y[1]:NaN}m.setLayout("largePoints",_)}}};function wn(t,e,a,n,r,o){var i;return a>n?i=-1:a<n?i=1:i=o?0:e>0?t.get(r,e-1)<=n?1:-1:1,i}function Jl(t,e){var a=t.getBaseAxis(),n,r=a.type==="category"?a.getBandWidth():(n=a.getExtent(),Math.abs(n[1]-n[0])/e.count()),o=Se(xe(t.get("barMaxWidth"),r),r),i=Se(xe(t.get("barMinWidth"),1),r),s=t.get("barWidth");return s!=null?Se(s,r):Math.max(Math.min(r/2,o),i)}function Ql(t){t.registerChartView(zl),t.registerSeriesModel(Gr),t.registerPreprocessor(Gl),t.registerVisual(ql),t.registerLayout(Xl)}function eu(t,e,a,n){return t&&(t.legacy||t.legacy!==!1&&!a&&!n&&e!=="tspan"&&(e==="text"||B(t,"text")))}function tu(t,e,a){var n=t,r,o,i;if(e==="text")i=n;else{i={},B(n,"text")&&(i.text=n.text),B(n,"rich")&&(i.rich=n.rich),B(n,"textFill")&&(i.fill=n.textFill),B(n,"textStroke")&&(i.stroke=n.textStroke),B(n,"fontFamily")&&(i.fontFamily=n.fontFamily),B(n,"fontSize")&&(i.fontSize=n.fontSize),B(n,"fontStyle")&&(i.fontStyle=n.fontStyle),B(n,"fontWeight")&&(i.fontWeight=n.fontWeight),o={type:"text",style:i,silent:!0},r={};var s=B(n,"textPosition");r.position=s?n.textPosition:"inside",B(n,"textPosition")&&(r.position=n.textPosition),B(n,"textOffset")&&(r.offset=n.textOffset),B(n,"textRotation")&&(r.rotation=n.textRotation),B(n,"textDistance")&&(r.distance=n.textDistance)}return An(i,t),D(i.rich,function(l){An(l,l)}),{textConfig:r,textContent:o}}function An(t,e){e&&(e.font=e.textFont||e.font,B(e,"textStrokeWidth")&&(t.lineWidth=e.textStrokeWidth),B(e,"textAlign")&&(t.align=e.textAlign),B(e,"textVerticalAlign")&&(t.verticalAlign=e.textVerticalAlign),B(e,"textLineHeight")&&(t.lineHeight=e.textLineHeight),B(e,"textWidth")&&(t.width=e.textWidth),B(e,"textHeight")&&(t.height=e.textHeight),B(e,"textBackgroundColor")&&(t.backgroundColor=e.textBackgroundColor),B(e,"textPadding")&&(t.padding=e.textPadding),B(e,"textBorderColor")&&(t.borderColor=e.textBorderColor),B(e,"textBorderWidth")&&(t.borderWidth=e.textBorderWidth),B(e,"textBorderRadius")&&(t.borderRadius=e.textBorderRadius),B(e,"textBoxShadowColor")&&(t.shadowColor=e.textBoxShadowColor),B(e,"textBoxShadowBlur")&&(t.shadowBlur=e.textBoxShadowBlur),B(e,"textBoxShadowOffsetX")&&(t.shadowOffsetX=e.textBoxShadowOffsetX),B(e,"textBoxShadowOffsetY")&&(t.shadowOffsetY=e.textBoxShadowOffsetY))}var Zr={position:["x","y"],scale:["scaleX","scaleY"],origin:["originX","originY"]},Tn=Ze(Zr);Oo(nt,function(t,e){return t[e]=1,t},{});nt.join(", ");var yt=["","style","shape","extra"],Fe=Ae();function Na(t,e,a,n,r){var o=t+"Animation",i=mr(t,n,r)||{},s=Fe(e).userDuring;return i.duration>0&&(i.during=s?oe(su,{el:e,userDuring:s}):null,i.setToFinal=!0,i.scope=t),J(i,a[o]),i}function Wt(t,e,a,n){n=n||{};var r=n.dataIndex,o=n.isInit,i=n.clearStyle,s=a.isAnimationEnabled(),l=Fe(t),u=e.style;l.userDuring=e.during;var c={},d={};if(uu(t,e,d),Pn("shape",e,d),Pn("extra",e,d),!o&&s&&(lu(t,e,c),Dn("shape",t,e,c),Dn("extra",t,e,c),cu(t,e,u,c)),d.style=u,nu(t,d,i),ou(t,e),s)if(o){var f={};D(yt,function(h){var g=h?e[h]:e;g&&g.enterFrom&&(h&&(f[h]=f[h]||{}),J(h?f[h]:f,g.enterFrom))});var p=Na("enter",t,e,a,r);p.duration>0&&t.animateFrom(f,p)}else ru(t,e,r||0,a,c);Hr(t,e),u?t.dirty():t.markRedraw()}function Hr(t,e){for(var a=Fe(t).leaveToProps,n=0;n<yt.length;n++){var r=yt[n],o=r?e[r]:e;o&&o.leaveTo&&(a||(a=Fe(t).leaveToProps={}),r&&(a[r]=a[r]||{}),J(r?a[r]:a,o.leaveTo))}}function au(t,e,a,n){if(t){var r=t.parent,o=Fe(t).leaveToProps;if(o){var i=Na("update",t,e,a,0);i.done=function(){r.remove(t)},t.animateTo(o,i)}else r.remove(t)}}function Ie(t){return t==="all"}function nu(t,e,a){var n=e.style;if(!t.isGroup&&n){if(a){t.useStyle({});for(var r=t.animators,o=0;o<r.length;o++){var i=r[o];i.targetName==="style"&&i.changeTarget(t.style)}}t.setStyle(n)}e&&(e.style=null,e&&t.attr(e),e.style=n)}function ru(t,e,a,n,r){if(r){var o=Na("update",t,e,n,a);o.duration>0&&t.animateFrom(r,o)}}function ou(t,e){B(e,"silent")&&(t.silent=e.silent),B(e,"ignore")&&(t.ignore=e.ignore),t instanceof gr&&B(e,"invisible")&&(t.invisible=e.invisible),t instanceof it&&B(e,"autoBatch")&&(t.autoBatch=e.autoBatch)}var fe={},iu={setTransform:function(t,e){return fe.el[t]=e,this},getTransform:function(t){return fe.el[t]},setShape:function(t,e){var a=fe.el,n=a.shape||(a.shape={});return n[t]=e,a.dirtyShape&&a.dirtyShape(),this},getShape:function(t){var e=fe.el.shape;if(e)return e[t]},setStyle:function(t,e){var a=fe.el,n=a.style;return n&&(n[t]=e,a.dirtyStyle&&a.dirtyStyle()),this},getStyle:function(t){var e=fe.el.style;if(e)return e[t]},setExtra:function(t,e){var a=fe.el.extra||(fe.el.extra={});return a[t]=e,this},getExtra:function(t){var e=fe.el.extra;if(e)return e[t]}};function su(){var t=this,e=t.el;if(e){var a=Fe(e).userDuring,n=t.userDuring;if(a!==n){t.el=t.userDuring=null;return}fe.el=e,n(iu)}}function Dn(t,e,a,n){var r=a[t];if(r){var o=e[t],i;if(o){var s=a.transition,l=r.transition;if(l)if(!i&&(i=n[t]={}),Ie(l))J(i,o);else for(var u=Da(l),c=0;c<u.length;c++){var d=u[c],f=o[d];i[d]=f}else if(Ie(s)||ue(s,t)>=0){!i&&(i=n[t]={});for(var p=Ze(o),c=0;c<p.length;c++){var d=p[c],f=o[d];du(r[d],f)&&(i[d]=f)}}}}}function Pn(t,e,a){var n=e[t];if(n)for(var r=a[t]={},o=Ze(n),i=0;i<o.length;i++){var s=o[i];r[s]=Mo(n[s])}}function lu(t,e,a){for(var n=e.transition,r=Ie(n)?nt:Da(n||[]),o=0;o<r.length;o++){var i=r[o];if(!(i==="style"||i==="shape"||i==="extra")){var s=t[i];a[i]=s}}}function uu(t,e,a){for(var n=0;n<Tn.length;n++){var r=Tn[n],o=Zr[r],i=e[r];i&&(a[o[0]]=i[0],a[o[1]]=i[1])}for(var n=0;n<nt.length;n++){var s=nt[n];e[s]!=null&&(a[s]=e[s])}}function cu(t,e,a,n){if(a){var r=t.style,o;if(r){var i=a.transition,s=e.transition;if(i&&!Ie(i)){var l=Da(i);!o&&(o=n.style={});for(var u=0;u<l.length;u++){var c=l[u],d=r[c];o[c]=d}}else if(t.getAnimationStyleProps&&(Ie(s)||Ie(i)||ue(s,"style")>=0)){var f=t.getAnimationStyleProps(),p=f?f.style:null;if(p){!o&&(o=n.style={});for(var h=Ze(a),u=0;u<h.length;u++){var c=h[u];if(p[c]){var d=r[c];o[c]=d}}}}}}}function du(t,e){return Bo(t)?t!==e:t!=null&&isFinite(t)}var jr=Ae(),fu=["percent","easing","shape","style","extra"];function pu(t){t.stopAnimation("keyframe"),t.attr(jr(t))}function ca(t,e,a){if(!(!a.isAnimationEnabled()||!e)){if(W(e)){D(e,function(s){ca(t,s,a)});return}var n=e.keyframes,r=e.duration;if(a&&r==null){var o=mr("enter",a,0);r=o&&o.duration}if(!(!n||!r)){var i=jr(t);D(yt,function(s){if(!(s&&!t[s])){var l;n.sort(function(u,c){return u.percent-c.percent}),D(n,function(u){var c=t.animators,d=s?u[s]:u;if(d){var f=Ze(d);if(s||(f=St(f,function(g){return ue(fu,g)<0})),!!f.length){l||(l=t.animate(s,e.loop,!0),l.scope="keyframe");for(var p=0;p<c.length;p++)c[p]!==l&&c[p].targetName===l.targetName&&c[p].stopTracks(f);s&&(i[s]=i[s]||{});var h=s?i[s]:i;D(f,function(g){h[g]=((s?t[s]:t)||{})[g]}),l.whenWithKeys(r*u.percent,d,f,u.easing)}}}),l&&l.delay(e.delay||0).duration(r).start(e.easing)}})}}}var hu=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.init=function(a,n,r){var o=Vo(a);t.prototype.init.apply(this,arguments),Ln(a,o)},e.prototype.mergeOption=function(a){t.prototype.mergeOption.apply(this,arguments),Ln(this.option,a)},e.prototype.getCellSize=function(){return this.option.cellSize},e.type="calendar",e.defaultOption={z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},e}(st);function Ln(t,e){var a=t.cellSize,n;W(a)?n=a:n=t.cellSize=[a,a],n.length===1&&(n[1]=n[0]);var r=Y([0,1],function(o){return Eo(e,o)&&(n[o]="auto"),n[o]!=null&&n[o]!=="auto"});yr(t,e,{type:"box",ignoreSize:r})}var vu=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.render=function(a,n,r){var o=this.group;o.removeAll();var i=a.coordinateSystem,s=i.getRangeInfo(),l=i.getOrient(),u=n.getLocaleModel();this._renderDayRect(a,s,o),this._renderLines(a,s,l,o),this._renderYearText(a,s,l,o),this._renderMonthText(a,u,l,o),this._renderWeekText(a,u,s,l,o)},e.prototype._renderDayRect=function(a,n,r){for(var o=a.coordinateSystem,i=a.getModel("itemStyle").getItemStyle(),s=o.getCellWidth(),l=o.getCellHeight(),u=n.start.time;u<=n.end.time;u=o.getNextNDay(u,1).time){var c=o.dataToRect([u],!1).tl,d=new ta({shape:{x:c[0],y:c[1],width:s,height:l},cursor:"default",style:i});r.add(d)}},e.prototype._renderLines=function(a,n,r,o){var i=this,s=a.coordinateSystem,l=a.getModel(["splitLine","lineStyle"]).getLineStyle(),u=a.get(["splitLine","show"]),c=l.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var d=n.start,f=0;d.time<=n.end.time;f++){h(d.formatedDate),f===0&&(d=s.getDateInfo(n.start.y+"-"+n.start.m));var p=d.date;p.setMonth(p.getMonth()+1),d=s.getDateInfo(p)}h(s.getNextNDay(n.end.time,1).formatedDate);function h(g){i._firstDayOfMonth.push(s.getDateInfo(g)),i._firstDayPoints.push(s.dataToRect([g],!1).tl);var m=i._getLinePointsOfOneWeek(a,g,r);i._tlpoints.push(m[0]),i._blpoints.push(m[m.length-1]),u&&i._drawSplitline(m,l,o)}u&&this._drawSplitline(i._getEdgesPoints(i._tlpoints,c,r),l,o),u&&this._drawSplitline(i._getEdgesPoints(i._blpoints,c,r),l,o)},e.prototype._getEdgesPoints=function(a,n,r){var o=[a[0].slice(),a[a.length-1].slice()],i=r==="horizontal"?0:1;return o[0][i]=o[0][i]-n/2,o[1][i]=o[1][i]+n/2,o},e.prototype._drawSplitline=function(a,n,r){var o=new pr({z2:20,shape:{points:a},style:n});r.add(o)},e.prototype._getLinePointsOfOneWeek=function(a,n,r){for(var o=a.coordinateSystem,i=o.getDateInfo(n),s=[],l=0;l<7;l++){var u=o.getNextNDay(i.time,l),c=o.dataToRect([u.time],!1);s[2*u.day]=c.tl,s[2*u.day+1]=c[r==="horizontal"?"bl":"tr"]}return s},e.prototype._formatterLabel=function(a,n){return Ce(a)&&a?No(a,n):ze(a)?a(n):n.nameMap},e.prototype._yearTextPositionControl=function(a,n,r,o,i){var s=n[0],l=n[1],u=["center","bottom"];o==="bottom"?(l+=i,u=["center","top"]):o==="left"?s-=i:o==="right"?(s+=i,u=["center","top"]):l-=i;var c=0;return(o==="left"||o==="right")&&(c=Math.PI/2),{rotation:c,x:s,y:l,style:{align:u[0],verticalAlign:u[1]}}},e.prototype._renderYearText=function(a,n,r,o){var i=a.getModel("yearLabel");if(i.get("show")){var s=i.get("margin"),l=i.get("position");l||(l=r!=="horizontal"?"top":"left");var u=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],c=(u[0][0]+u[1][0])/2,d=(u[0][1]+u[1][1])/2,f=r==="horizontal"?0:1,p={top:[c,u[f][1]],bottom:[c,u[1-f][1]],left:[u[1-f][0],d],right:[u[f][0],d]},h=n.start.y;+n.end.y>+n.start.y&&(h=h+"-"+n.end.y);var g=i.get("formatter"),m={start:n.start.y,end:n.end.y,nameMap:h},_=this._formatterLabel(g,m),b=new _e({z2:30,style:Ne(i,{text:_})});b.attr(this._yearTextPositionControl(b,p[l],r,l,s)),o.add(b)}},e.prototype._monthTextPositionControl=function(a,n,r,o,i){var s="left",l="top",u=a[0],c=a[1];return r==="horizontal"?(c=c+i,n&&(s="center"),o==="start"&&(l="bottom")):(u=u+i,n&&(l="middle"),o==="start"&&(s="right")),{x:u,y:c,align:s,verticalAlign:l}},e.prototype._renderMonthText=function(a,n,r,o){var i=a.getModel("monthLabel");if(i.get("show")){var s=i.get("nameMap"),l=i.get("margin"),u=i.get("position"),c=i.get("align"),d=[this._tlpoints,this._blpoints];(!s||Ce(s))&&(s&&(n=Ja(s)||n),s=n.get(["time","monthAbbr"])||[]);var f=u==="start"?0:1,p=r==="horizontal"?0:1;l=u==="start"?-l:l;for(var h=c==="center",g=0;g<d[f].length-1;g++){var m=d[f][g].slice(),_=this._firstDayOfMonth[g];if(h){var b=this._firstDayPoints[g];m[p]=(b[p]+d[0][g+1][p])/2}var y=i.get("formatter"),v=s[+_.m-1],S={yyyy:_.y,yy:(_.y+"").slice(2),MM:_.m,M:+_.m,nameMap:v},x=this._formatterLabel(y,S),w=new _e({z2:30,style:J(Ne(i,{text:x}),this._monthTextPositionControl(m,h,r,u,l))});o.add(w)}}},e.prototype._weekTextPositionControl=function(a,n,r,o,i){var s="center",l="middle",u=a[0],c=a[1],d=r==="start";return n==="horizontal"?(u=u+o+(d?1:-1)*i[0]/2,s=d?"right":"left"):(c=c+o+(d?1:-1)*i[1]/2,l=d?"bottom":"top"),{x:u,y:c,align:s,verticalAlign:l}},e.prototype._renderWeekText=function(a,n,r,o,i){var s=a.getModel("dayLabel");if(s.get("show")){var l=a.coordinateSystem,u=s.get("position"),c=s.get("nameMap"),d=s.get("margin"),f=l.getFirstDayOfWeek();if(!c||Ce(c)){c&&(n=Ja(c)||n);var p=n.get(["time","dayOfWeekShort"]);c=p||Y(n.get(["time","dayOfWeekAbbr"]),function(S){return S[0]})}var h=l.getNextNDay(r.end.time,7-r.lweek).time,g=[l.getCellWidth(),l.getCellHeight()];d=Se(d,Math.min(g[1],g[0])),u==="start"&&(h=l.getNextNDay(r.start.time,-(7+r.fweek)).time,d=-d);for(var m=0;m<7;m++){var _=l.getNextNDay(h,m),b=l.dataToRect([_.time],!1).center,y=m;y=Math.abs((m+f)%7);var v=new _e({z2:30,style:J(Ne(s,{text:c[y]}),this._weekTextPositionControl(b,o,u,d,g))});i.add(v)}}},e.type="calendar",e}(lt),Ut=864e5,gu=function(){function t(e,a,n){this.type="calendar",this.dimensions=t.dimensions,this.getDimensionsInfo=t.getDimensionsInfo,this._model=e}return t.getDimensionsInfo=function(){return[{name:"time",type:"time"},"value"]},t.prototype.getRangeInfo=function(){return this._rangeInfo},t.prototype.getModel=function(){return this._model},t.prototype.getRect=function(){return this._rect},t.prototype.getCellWidth=function(){return this._sw},t.prototype.getCellHeight=function(){return this._sh},t.prototype.getOrient=function(){return this._orient},t.prototype.getFirstDayOfWeek=function(){return this._firstDayOfWeek},t.prototype.getDateInfo=function(e){e=Ro(e);var a=e.getFullYear(),n=e.getMonth()+1,r=n<10?"0"+n:""+n,o=e.getDate(),i=o<10?"0"+o:""+o,s=e.getDay();return s=Math.abs((s+7-this.getFirstDayOfWeek())%7),{y:a+"",m:r,d:i,day:s,time:e.getTime(),formatedDate:a+"-"+r+"-"+i,date:e}},t.prototype.getNextNDay=function(e,a){return a=a||0,a===0?this.getDateInfo(e):(e=new Date(this.getDateInfo(e).time),e.setDate(e.getDate()+a),this.getDateInfo(e))},t.prototype.update=function(e,a){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var n=this._rangeInfo.weeks||1,r=["width","height"],o=this._model.getCellSize().slice(),i=this._model.getBoxLayoutParams(),s=this._orient==="horizontal"?[n,7]:[7,n];D([0,1],function(d){c(o,d)&&(i[r[d]]=o[d]*s[d])});var l={width:a.getWidth(),height:a.getHeight()},u=this._rect=_r(i,l);D([0,1],function(d){c(o,d)||(o[d]=u[r[d]]/s[d])});function c(d,f){return d[f]!=null&&d[f]!=="auto"}this._sw=o[0],this._sh=o[1]},t.prototype.dataToPoint=function(e,a){W(e)&&(e=e[0]),a==null&&(a=!0);var n=this.getDateInfo(e),r=this._rangeInfo,o=n.formatedDate;if(a&&!(n.time>=r.start.time&&n.time<r.end.time+Ut))return[NaN,NaN];var i=n.day,s=this._getRangeInfo([r.start.time,o]).nthWeek;return this._orient==="vertical"?[this._rect.x+i*this._sw+this._sw/2,this._rect.y+s*this._sh+this._sh/2]:[this._rect.x+s*this._sw+this._sw/2,this._rect.y+i*this._sh+this._sh/2]},t.prototype.pointToData=function(e){var a=this.pointToDate(e);return a&&a.time},t.prototype.dataToRect=function(e,a){var n=this.dataToPoint(e,a);return{contentShape:{x:n[0]-(this._sw-this._lineWidth)/2,y:n[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:n,tl:[n[0]-this._sw/2,n[1]-this._sh/2],tr:[n[0]+this._sw/2,n[1]-this._sh/2],br:[n[0]+this._sw/2,n[1]+this._sh/2],bl:[n[0]-this._sw/2,n[1]+this._sh/2]}},t.prototype.pointToDate=function(e){var a=Math.floor((e[0]-this._rect.x)/this._sw)+1,n=Math.floor((e[1]-this._rect.y)/this._sh)+1,r=this._rangeInfo.range;return this._orient==="vertical"?this._getDateByWeeksAndDay(n,a-1,r):this._getDateByWeeksAndDay(a,n-1,r)},t.prototype.convertToPixel=function(e,a,n){var r=kn(a);return r===this?r.dataToPoint(n):null},t.prototype.convertFromPixel=function(e,a,n){var r=kn(a);return r===this?r.pointToData(n):null},t.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},t.prototype._initRangeOption=function(){var e=this._model.get("range"),a;if(W(e)&&e.length===1&&(e=e[0]),W(e))a=e;else{var n=e.toString();if(/^\d{4}$/.test(n)&&(a=[n+"-01-01",n+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(n)){var r=this.getDateInfo(n),o=r.date;o.setMonth(o.getMonth()+1);var i=this.getNextNDay(o,-1);a=[r.formatedDate,i.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(n)&&(a=[n,n])}if(!a)return e;var s=this._getRangeInfo(a);return s.start.time>s.end.time&&a.reverse(),a},t.prototype._getRangeInfo=function(e){var a=[this.getDateInfo(e[0]),this.getDateInfo(e[1])],n;a[0].time>a[1].time&&(n=!0,a.reverse());var r=Math.floor(a[1].time/Ut)-Math.floor(a[0].time/Ut)+1,o=new Date(a[0].time),i=o.getDate(),s=a[1].date.getDate();o.setDate(i+r-1);var l=o.getDate();if(l!==s)for(var u=o.getTime()-a[1].time>0?1:-1;(l=o.getDate())!==s&&(o.getTime()-a[1].time)*u>0;)r-=u,o.setDate(l-u);var c=Math.floor((r+a[0].day+6)/7),d=n?-c+1:c-1;return n&&a.reverse(),{range:[a[0].formatedDate,a[1].formatedDate],start:a[0],end:a[1],allDay:r,weeks:c,nthWeek:d,fweek:a[0].day,lweek:a[1].day}},t.prototype._getDateByWeeksAndDay=function(e,a,n){var r=this._getRangeInfo(n);if(e>r.weeks||e===0&&a<r.fweek||e===r.weeks&&a>r.lweek)return null;var o=(e-1)*7-r.fweek+a,i=new Date(r.start.time);return i.setDate(+r.start.d+o),this.getDateInfo(i)},t.create=function(e,a){var n=[];return e.eachComponent("calendar",function(r){var o=new t(r);n.push(o),r.coordinateSystem=o}),e.eachSeries(function(r){r.get("coordinateSystem")==="calendar"&&(r.coordinateSystem=n[r.get("calendarIndex")||0])}),n},t.dimensions=["time","value"],t}();function kn(t){var e=t.calendarModel,a=t.seriesModel,n=e?e.coordinateSystem:a?a.coordinateSystem:null;return n}function mu(t){t.registerComponentModel(hu),t.registerComponentView(vu),t.registerCoordinateSystem("calendar",gu)}function yu(t,e){var a=t.existing;if(e.id=t.keyInfo.id,!e.type&&a&&(e.type=a.type),e.parentId==null){var n=e.parentOption;n?e.parentId=n.id:a&&(e.parentId=a.parentId)}e.parentOption=null}function In(t,e){var a;return D(e,function(n){t[n]!=null&&t[n]!=="auto"&&(a=!0)}),a}function _u(t,e,a){var n=J({},a),r=t[e],o=a.$action||"merge";o==="merge"?r?(le(r,n,!0),yr(r,n,{ignoreSize:!0}),$o(a,r),ut(a,r),ut(a,r,"shape"),ut(a,r,"style"),ut(a,r,"extra"),a.clipPath=r.clipPath):t[e]=n:o==="replace"?t[e]=n:o==="remove"&&r&&(t[e]=null)}var Yr=["transition","enterFrom","leaveTo"],bu=Yr.concat(["enterAnimation","updateAnimation","leaveAnimation"]);function ut(t,e,a){if(a&&(!t[a]&&e[a]&&(t[a]={}),t=t[a],e=e[a]),!(!t||!e))for(var n=a?Yr:bu,r=0;r<n.length;r++){var o=n[r];t[o]==null&&e[o]!=null&&(t[o]=e[o])}}function xu(t,e){if(t&&(t.hv=e.hv=[In(e,["left","right"]),In(e,["top","bottom"])],t.type==="group")){var a=t,n=e;a.width==null&&(a.width=n.width=0),a.height==null&&(a.height=n.height=0)}}var Su=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a.preventAutoZ=!0,a}return e.prototype.mergeOption=function(a,n){var r=this.option.elements;this.option.elements=null,t.prototype.mergeOption.call(this,a,n),this.option.elements=r},e.prototype.optionUpdated=function(a,n){var r=this.option,o=(n?r:a).elements,i=r.elements=n?[]:r.elements,s=[];this._flatten(o,s,null);var l=zo(i,s,"normalMerge"),u=this._elOptionsToUpdate=[];D(l,function(c,d){var f=c.newOption;f&&(u.push(f),yu(c,f),_u(i,d,f),xu(i[d],f))},this),r.elements=St(i,function(c){return c&&delete c.$action,c!=null})},e.prototype._flatten=function(a,n,r){D(a,function(o){if(o){r&&(o.parentOption=r),n.push(o);var i=o.children;i&&i.length&&this._flatten(i,n,o),delete o.children}},this)},e.prototype.useElOptionsToUpdate=function(){var a=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,a},e.type="graphic",e.defaultOption={elements:[]},e}(st),On={path:null,compoundPath:null,group:we,image:Go,text:_e},te=Ae(),Cu=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.init=function(){this._elMap=mt()},e.prototype.render=function(a,n,r){a!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=a,this._updateElements(a),this._relocate(a,r)},e.prototype._updateElements=function(a){var n=a.useElOptionsToUpdate();if(n){var r=this._elMap,o=this.group,i=a.get("z"),s=a.get("zlevel");D(n,function(l){var u=et(l.id,null),c=u!=null?r.get(u):null,d=et(l.parentId,null),f=d!=null?r.get(d):o,p=l.type,h=l.style;p==="text"&&h&&l.hv&&l.hv[1]&&(h.textVerticalAlign=h.textBaseline=h.verticalAlign=h.align=null);var g=l.textContent,m=l.textConfig;if(h&&eu(h,p,!!m,!!g)){var _=tu(h,p);!m&&_.textConfig&&(m=l.textConfig=_.textConfig),!g&&_.textContent&&(g=_.textContent)}var b=wu(l),y=l.$action||"merge",v=y==="merge",S=y==="replace";if(v){var x=!c,w=c;x?w=Mn(u,f,l.type,r):(w&&(te(w).isNew=!1),pu(w)),w&&(Wt(w,b,a,{isInit:x}),Bn(w,l,i,s))}else if(S){gt(c,l,r,a);var P=Mn(u,f,l.type,r);P&&(Wt(P,b,a,{isInit:!0}),Bn(P,l,i,s))}else y==="remove"&&(Hr(c,l),gt(c,l,r,a));var A=r.get(u);if(A&&g)if(v){var I=A.getTextContent();I?I.attr(g):A.setTextContent(new _e(g))}else S&&A.setTextContent(new _e(g));if(A){var T=l.clipPath;if(T){var L=T.type,E=void 0,x=!1;if(v){var G=A.getClipPath();x=!G||te(G).type!==L,E=x?da(L):G}else S&&(x=!0,E=da(L));A.setClipPath(E),Wt(E,T,a,{isInit:x}),ca(E,T.keyframeAnimation,a)}var j=te(A);A.setTextConfig(m),j.option=l,Au(A,a,l),br({el:A,componentModel:a,itemName:A.name,itemTooltipOption:l.tooltip}),ca(A,l.keyframeAnimation,a)}})}},e.prototype._relocate=function(a,n){for(var r=a.option.elements,o=this.group,i=this._elMap,s=n.getWidth(),l=n.getHeight(),u=["x","y"],c=0;c<r.length;c++){var d=r[c],f=et(d.id,null),p=f!=null?i.get(f):null;if(!(!p||!p.isGroup)){var h=p.parent,g=h===o,m=te(p),_=te(h);m.width=Se(m.option.width,g?s:_.width)||0,m.height=Se(m.option.height,g?l:_.height)||0}}for(var c=r.length-1;c>=0;c--){var d=r[c],f=et(d.id,null),p=f!=null?i.get(f):null;if(p){var h=p.parent,_=te(h),b=h===o?{width:s,height:l}:{width:_.width,height:_.height},y={},v=Fo(p,d,b,null,{hv:d.hv,boundingMode:d.bounding},y);if(!te(p).isNew&&v){for(var S=d.transition,x={},w=0;w<u.length;w++){var P=u[w],A=y[P];S&&(Ie(S)||ue(S,P)>=0)?x[P]=A:p[P]=A}Ca(p,x,a,0)}else p.attr(y)}}},e.prototype._clear=function(){var a=this,n=this._elMap;n.each(function(r){gt(r,te(r).option,n,a._lastGraphicModel)}),this._elMap=mt()},e.prototype.dispose=function(){this._clear()},e.type="graphic",e}(lt);function da(t){var e=B(On,t)?On[t]:Wo(t),a=new e({});return te(a).type=t,a}function Mn(t,e,a,n){var r=da(a);return e.add(r),n.set(t,r),te(r).id=t,te(r).isNew=!0,r}function gt(t,e,a,n){var r=t&&t.parent;r&&(t.type==="group"&&t.traverse(function(o){gt(o,e,a,n)}),au(t,e,n),a.removeKey(te(t).id))}function Bn(t,e,a,n){t.isGroup||D([["cursor",gr.prototype.cursor],["zlevel",n||0],["z",a||0],["z2",0]],function(r){var o=r[0];B(e,o)?t[o]=xe(e[o],r[1]):t[o]==null&&(t[o]=r[1])}),D(Ze(e),function(r){if(r.indexOf("on")===0){var o=e[r];t[r]=ze(o)?o:null}}),B(e,"draggable")&&(t.draggable=e.draggable),e.name!=null&&(t.name=e.name),e.id!=null&&(t.id=e.id)}function wu(t){return t=J({},t),D(["id","parentId","$action","hv","bounding","textContent","clipPath"].concat(Uo),function(e){delete t[e]}),t}function Au(t,e,a){var n=Re(t).eventData;!t.silent&&!t.ignore&&!n&&(n=Re(t).eventData={componentType:"graphic",componentIndex:e.componentIndex,name:t.name}),n&&(n.info=a.info)}function Tu(t){t.registerComponentModel(Su),t.registerComponentView(Cu),t.registerPreprocessor(function(e){var a=e.graphic;W(a)?!a[0]||!a[0].elements?e.graphic=[{elements:a}]:e.graphic=[e.graphic[0]]:a&&!a.elements&&(e.graphic=[{elements:[a]}])})}var Du=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.type="dataZoom.select",e}(Mi),Pu=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.type="dataZoom.select",e}(Bi);function Lu(t){t.registerComponentModel(Du),t.registerComponentView(Pu),Vi(t)}var ie=function(){function t(){}return t}(),Kr={};function Je(t,e){Kr[t]=e}function qr(t){return Kr[t]}var ku=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.optionUpdated=function(){t.prototype.optionUpdated.apply(this,arguments);var a=this.ecModel;D(this.option.feature,function(n,r){var o=qr(r);o&&(o.getDefaultOption&&(o.defaultOption=o.getDefaultOption(a)),le(n,o.defaultOption))})},e.type="toolbox",e.layoutMode={type:"box",ignoreSize:!0},e.defaultOption={show:!0,z:6,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1,position:"bottom"}},e}(st),Iu=function(t){V(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}return e.prototype.render=function(a,n,r,o){var i=this.group;if(i.removeAll(),!a.get("show"))return;var s=+a.get("itemSize"),l=a.get("orient")==="vertical",u=a.get("feature")||{},c=this._features||(this._features={}),d=[];D(u,function(h,g){d.push(g)}),new fr(this._featureNames||[],d).add(f).update(f).remove(q(f,null)).execute(),this._featureNames=d;function f(h,g){var m=d[h],_=d[g],b=u[m],y=new Zo(b,a,a.ecModel),v;if(o&&o.newTitle!=null&&o.featureName===m&&(b.title=o.newTitle),m&&!_){if(Ou(m))v={onclick:y.option.onclick,featureName:m};else{var S=qr(m);if(!S)return;v=new S}c[m]=v}else if(v=c[_],!v)return;v.uid=Ho("toolbox-feature"),v.model=y,v.ecModel=n,v.api=r;var x=v instanceof ie;if(!m&&_){x&&v.dispose&&v.dispose(n,r);return}if(!y.get("show")||x&&v.unusable){x&&v.remove&&v.remove(n,r);return}p(y,v,m),y.setIconStatus=function(w,P){var A=this.option,I=this.iconPaths;A.iconStatus=A.iconStatus||{},A.iconStatus[w]=P,I[w]&&(P==="emphasis"?Qt:ea)(I[w])},v instanceof ie&&v.render&&v.render(y,n,r,o)}function p(h,g,m){var _=h.getModel("iconStyle"),b=h.getModel(["emphasis","iconStyle"]),y=g instanceof ie&&g.getIcons?g.getIcons():h.get("icon"),v=h.get("title")||{},S,x;Ce(y)?(S={},S[m]=y):S=y,Ce(v)?(x={},x[m]=v):x=v;var w=h.iconPaths={};D(S,function(P,A){var I=xr(P,{},{x:-s/2,y:-s/2,width:s,height:s});I.setStyle(_.getItemStyle());var T=I.ensureState("emphasis");T.style=b.getItemStyle();var L=new _e({style:{text:x[A],align:b.get("textAlign"),borderRadius:b.get("textBorderRadius"),padding:b.get("textPadding"),fill:null,font:jo({fontStyle:b.get("textFontStyle"),fontFamily:b.get("textFontFamily"),fontSize:b.get("textFontSize"),fontWeight:b.get("textFontWeight")},n)},ignore:!0});I.setTextContent(L),br({el:I,componentModel:a,itemName:A,formatterParamsExtra:{title:x[A]}}),I.__title=x[A],I.on("mouseover",function(){var E=b.getItemStyle(),G=l?a.get("right")==null&&a.get("left")!=="right"?"right":"left":a.get("bottom")==null&&a.get("top")!=="bottom"?"bottom":"top";L.setStyle({fill:b.get("textFill")||E.fill||E.stroke||"#000",backgroundColor:b.get("textBackgroundColor")}),I.setTextConfig({position:b.get("textPosition")||G}),L.ignore=!a.get("showTitle"),r.enterEmphasis(this)}).on("mouseout",function(){h.get(["iconStatus",A])!=="emphasis"&&r.leaveEmphasis(this),L.hide()}),(h.get(["iconStatus",A])==="emphasis"?Qt:ea)(I),i.add(I),I.on("click",oe(g.onclick,g,n,r,A)),w[A]=I})}Yo(i,a,r),i.add(Ko(i.getBoundingRect(),a)),l||i.eachChild(function(h){var g=h.__title,m=h.ensureState("emphasis"),_=m.textConfig||(m.textConfig={}),b=h.getTextContent(),y=b&&b.ensureState("emphasis");if(y&&!ze(y)&&g){var v=y.style||(y.style={}),S=qo(g,_e.makeFont(v)),x=h.x+i.x,w=h.y+i.y+s,P=!1;w+S.height>r.getHeight()&&(_.position="top",P=!0);var A=P?-5-S.height:s+10;x+S.width/2>r.getWidth()?(_.position=["100%",A],v.align="right"):x-S.width/2<0&&(_.position=[0,A],v.align="left")}})},e.prototype.updateView=function(a,n,r,o){D(this._features,function(i){i instanceof ie&&i.updateView&&i.updateView(i.model,n,r,o)})},e.prototype.remove=function(a,n){D(this._features,function(r){r instanceof ie&&r.remove&&r.remove(a,n)}),this.group.removeAll()},e.prototype.dispose=function(a,n){D(this._features,function(r){r instanceof ie&&r.dispose&&r.dispose(a,n)})},e.type="toolbox",e}(lt);function Ou(t){return t.indexOf("my")===0}var Mu=function(t){V(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}return e.prototype.onclick=function(a,n){var r=this.model,o=r.get("name")||a.get("title.0.text")||"echarts",i=n.getZr().painter.getType()==="svg",s=i?"svg":r.get("type",!0)||"png",l=n.getConnectedDataURL({type:s,backgroundColor:r.get("backgroundColor",!0)||a.get("backgroundColor")||"#fff",connectedBackgroundColor:r.get("connectedBackgroundColor"),excludeComponents:r.get("excludeComponents"),pixelRatio:r.get("pixelRatio")}),u=Sr.browser;if(ze(MouseEvent)&&(u.newEdge||!u.ie&&!u.edge)){var c=document.createElement("a");c.download=o+"."+s,c.target="_blank",c.href=l;var d=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});c.dispatchEvent(d)}else if(window.navigator.msSaveOrOpenBlob||i){var f=l.split(","),p=f[0].indexOf("base64")>-1,h=i?decodeURIComponent(f[1]):f[1];p&&(h=window.atob(h));var g=o+"."+s;if(window.navigator.msSaveOrOpenBlob){for(var m=h.length,_=new Uint8Array(m);m--;)_[m]=h.charCodeAt(m);var b=new Blob([_]);window.navigator.msSaveOrOpenBlob(b,g)}else{var y=document.createElement("iframe");document.body.appendChild(y);var v=y.contentWindow,S=v.document;S.open("image/svg+xml","replace"),S.write(h),S.close(),v.focus(),S.execCommand("SaveAs",!0,g),document.body.removeChild(y)}}else{var x=r.get("lang"),w='<body style="margin:0;"><img src="'+l+'" style="max-width:100%;" title="'+(x&&x[0]||"")+'" /></body>',P=window.open();P.document.write(w),P.document.title=o}},e.getDefaultOption=function(a){var n={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:a.getLocaleModel().get(["toolbox","saveAsImage","title"]),type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],lang:a.getLocaleModel().get(["toolbox","saveAsImage","lang"])};return n},e}(ie),Vn="__ec_magicType_stack__",Bu=[["line","bar"],["stack"]],Vu=function(t){V(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}return e.prototype.getIcons=function(){var a=this.model,n=a.get("icon"),r={};return D(a.get("type"),function(o){n[o]&&(r[o]=n[o])}),r},e.getDefaultOption=function(a){var n={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:a.getLocaleModel().get(["toolbox","magicType","title"]),option:{},seriesIndex:{}};return n},e.prototype.onclick=function(a,n,r){var o=this.model,i=o.get(["seriesIndex",r]);if(En[r]){var s={series:[]},l=function(d){var f=d.subType,p=d.id,h=En[r](f,p,d,o);h&&(Ge(h,d.option),s.series.push(h));var g=d.coordinateSystem;if(g&&g.type==="cartesian2d"&&(r==="line"||r==="bar")){var m=g.getAxesByScale("ordinal")[0];if(m){var _=m.dim,b=_+"Axis",y=d.getReferringComponents(b,Xo).models[0],v=y.componentIndex;s[b]=s[b]||[];for(var S=0;S<=v;S++)s[b][v]=s[b][v]||{};s[b][v].boundaryGap=r==="bar"}}};D(Bu,function(d){ue(d,r)>=0&&D(d,function(f){o.setIconStatus(f,"normal")})}),o.setIconStatus(r,"emphasis"),a.eachComponent({mainType:"series",query:i==null?null:{seriesIndex:i}},l);var u,c=r;r==="stack"&&(u=le({stack:o.option.title.tiled,tiled:o.option.title.stack},o.option.title),o.get(["iconStatus",r])!=="emphasis"&&(c="tiled")),n.dispatchAction({type:"changeMagicType",currentType:c,newOption:s,newTitle:u,featureName:"magicType"})}},e}(ie),En={line:function(t,e,a,n){if(t==="bar")return le({id:e,type:"line",data:a.get("data"),stack:a.get("stack"),markPoint:a.get("markPoint"),markLine:a.get("markLine")},n.get(["option","line"])||{},!0)},bar:function(t,e,a,n){if(t==="line")return le({id:e,type:"bar",data:a.get("data"),stack:a.get("stack"),markPoint:a.get("markPoint"),markLine:a.get("markLine")},n.get(["option","bar"])||{},!0)},stack:function(t,e,a,n){var r=a.get("stack")===Vn;if(t==="line"||t==="bar")return n.setIconStatus("stack",r?"normal":"emphasis"),le({id:e,stack:r?"":Vn},n.get(["option","stack"])||{},!0)}};Pa({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(t,e){e.mergeOption(t.newOption)});var wt=new Array(60).join("-"),We="	";function Eu(t){var e={},a=[],n=[];return t.eachRawSeries(function(r){var o=r.coordinateSystem;if(o&&(o.type==="cartesian2d"||o.type==="polar")){var i=o.getBaseAxis();if(i.type==="category"){var s=i.dim+"_"+i.index;e[s]||(e[s]={categoryAxis:i,valueAxis:o.getOtherAxis(i),series:[]},n.push({axisDim:i.dim,axisIndex:i.index})),e[s].series.push(r)}else a.push(r)}else a.push(r)}),{seriesGroupByCategoryAxis:e,other:a,meta:n}}function Nu(t){var e=[];return D(t,function(a,n){var r=a.categoryAxis,o=a.valueAxis,i=o.dim,s=[" "].concat(Y(a.series,function(p){return p.name})),l=[r.model.getCategories()];D(a.series,function(p){var h=p.getRawData();l.push(p.getRawData().mapArray(h.mapDimension(i),function(g){return g}))});for(var u=[s.join(We)],c=0;c<l[0].length;c++){for(var d=[],f=0;f<l.length;f++)d.push(l[f][c]);u.push(d.join(We))}e.push(u.join(`
`))}),e.join(`

`+wt+`

`)}function Ru(t){return Y(t,function(e){var a=e.getRawData(),n=[e.name],r=[];return a.each(a.dimensions,function(){for(var o=arguments.length,i=arguments[o-1],s=a.getName(i),l=0;l<o-1;l++)r[l]=arguments[l];n.push((s?s+We:"")+r.join(We))}),n.join(`
`)}).join(`

`+wt+`

`)}function zu(t){var e=Eu(t);return{value:St([Nu(e.seriesGroupByCategoryAxis),Ru(e.other)],function(a){return!!a.replace(/[\n\t\s]/g,"")}).join(`

`+wt+`

`),meta:e.meta}}function _t(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function $u(t){var e=t.slice(0,t.indexOf(`
`));if(e.indexOf(We)>=0)return!0}var fa=new RegExp("["+We+"]+","g");function Fu(t){for(var e=t.split(/\n+/g),a=_t(e.shift()).split(fa),n=[],r=Y(a,function(l){return{name:l,data:[]}}),o=0;o<e.length;o++){var i=_t(e[o]).split(fa);n.push(i.shift());for(var s=0;s<i.length;s++)r[s]&&(r[s].data[o]=i[s])}return{series:r,categories:n}}function Wu(t){for(var e=t.split(/\n+/g),a=_t(e.shift()),n=[],r=0;r<e.length;r++){var o=_t(e[r]);if(o){var i=o.split(fa),s="",l=void 0,u=!1;isNaN(i[0])?(u=!0,s=i[0],i=i.slice(1),n[r]={name:s,value:[]},l=n[r].value):l=n[r]=[];for(var c=0;c<i.length;c++)l.push(+i[c]);l.length===1&&(u?n[r].value=l[0]:n[r]=l[0])}}return{name:a,data:n}}function Uu(t,e){var a=t.split(new RegExp(`
*`+wt+`
*`,"g")),n={series:[]};return D(a,function(r,o){if($u(r)){var i=Fu(r),s=e[o],l=s.axisDim+"Axis";s&&(n[l]=n[l]||[],n[l][s.axisIndex]={data:i.categories},n.series=n.series.concat(i.series))}else{var i=Wu(r);n.series.push(i)}}),n}var Gu=function(t){V(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}return e.prototype.onclick=function(a,n){setTimeout(function(){n.dispatchAction({type:"hideTip"})});var r=n.getDom(),o=this.model;this._dom&&r.removeChild(this._dom);var i=document.createElement("div");i.style.cssText="position:absolute;top:0;bottom:0;left:0;right:0;padding:5px",i.style.backgroundColor=o.get("backgroundColor")||"#fff";var s=document.createElement("h4"),l=o.get("lang")||[];s.innerHTML=l[0]||o.get("title"),s.style.cssText="margin:10px 20px",s.style.color=o.get("textColor");var u=document.createElement("div"),c=document.createElement("textarea");u.style.cssText="overflow:auto";var d=o.get("optionToContent"),f=o.get("contentToOption"),p=zu(a);if(ze(d)){var h=d(n.getOption());Ce(h)?u.innerHTML=h:Jo(h)&&u.appendChild(h)}else{c.readOnly=o.get("readOnly");var g=c.style;g.cssText="display:block;width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;resize:none;box-sizing:border-box;outline:none",g.color=o.get("textColor"),g.borderColor=o.get("textareaBorderColor"),g.backgroundColor=o.get("textareaColor"),c.value=p.value,u.appendChild(c)}var m=p.meta,_=document.createElement("div");_.style.cssText="position:absolute;bottom:5px;left:0;right:0";var b="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",y=document.createElement("div"),v=document.createElement("div");b+=";background-color:"+o.get("buttonColor"),b+=";color:"+o.get("buttonTextColor");var S=this;function x(){r.removeChild(i),S._dom=null}Qa(y,"click",x),Qa(v,"click",function(){if(f==null&&d!=null||f!=null&&d==null){x();return}var w;try{ze(f)?w=f(u,n.getOption()):w=Uu(c.value,m)}catch(P){throw x(),new Error("Data view format error "+P)}w&&n.dispatchAction({type:"changeDataView",newOption:w}),x()}),y.innerHTML=l[1],v.innerHTML=l[2],v.style.cssText=y.style.cssText=b,!o.get("readOnly")&&_.appendChild(v),_.appendChild(y),i.appendChild(s),i.appendChild(u),i.appendChild(_),u.style.height=r.clientHeight-80+"px",r.appendChild(i),this._dom=i},e.prototype.remove=function(a,n){this._dom&&n.getDom().removeChild(this._dom)},e.prototype.dispose=function(a,n){this.remove(a,n)},e.getDefaultOption=function(a){var n={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:a.getLocaleModel().get(["toolbox","dataView","title"]),lang:a.getLocaleModel().get(["toolbox","dataView","lang"]),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"};return n},e}(ie);function Zu(t,e){return Y(t,function(a,n){var r=e&&e[n];if(at(r)&&!W(r)){var o=at(a)&&!W(a);o||(a={value:a});var i=r.name!=null&&a.name==null;return a=Ge(a,r),i&&delete a.name,a}else return a})}Pa({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(t,e){var a=[];D(t.newOption.series,function(n){var r=e.getSeriesByName(n.name)[0];if(!r)a.push(J({type:"scatter"},n));else{var o=r.get("data");a.push({name:n.name,data:Zu(n.data,o)})}}),e.mergeOption(Ge({series:a},t.newOption))});var Xr=D,Jr=Ae();function Hu(t,e){var a=Ra(t);Xr(e,function(n,r){for(var o=a.length-1;o>=0;o--){var i=a[o];if(i[r])break}if(o<0){var s=t.queryComponents({mainType:"dataZoom",subType:"select",id:r})[0];if(s){var l=s.getPercentRange();a[0][r]={dataZoomId:r,start:l[0],end:l[1]}}}}),a.push(e)}function ju(t){var e=Ra(t),a=e[e.length-1];e.length>1&&e.pop();var n={};return Xr(a,function(r,o){for(var i=e.length-1;i>=0;i--)if(r=e[i][o],r){n[o]=r;break}}),n}function Yu(t){Jr(t).snapshots=null}function Ku(t){return Ra(t).length}function Ra(t){var e=Jr(t);return e.snapshots||(e.snapshots=[{}]),e.snapshots}var qu=function(t){V(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}return e.prototype.onclick=function(a,n){Yu(a),n.dispatchAction({type:"restore",from:this.uid})},e.getDefaultOption=function(a){var n={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:a.getLocaleModel().get(["toolbox","restore","title"])};return n},e}(ie);Pa({type:"restore",event:"restore",update:"prepareAndUpdate"},function(t,e){e.resetOption("recreate")});var Xu=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],Qr=function(){function t(e,a,n){var r=this;this._targetInfoList=[];var o=Nn(a,e);D(Ju,function(i,s){(!n||!n.include||ue(n.include,s)>=0)&&i(o,r._targetInfoList)})}return t.prototype.setOutputRanges=function(e,a){return this.matchOutputRanges(e,a,function(n,r,o){if((n.coordRanges||(n.coordRanges=[])).push(r),!n.coordRange){n.coordRange=r;var i=Gt[n.brushType](0,o,r);n.__rangeOffset={offset:Fn[n.brushType](i.values,n.range,[1,1]),xyMinMax:i.xyMinMax}}}),e},t.prototype.matchOutputRanges=function(e,a,n){D(e,function(r){var o=this.findTargetInfo(r,a);o&&o!==!0&&D(o.coordSyses,function(i){var s=Gt[r.brushType](1,i,r.range,!0);n(r,s.values,i,a)})},this)},t.prototype.setInputRanges=function(e,a){D(e,function(n){var r=this.findTargetInfo(n,a);if(n.range=n.range||[],r&&r!==!0){n.panelId=r.panelId;var o=Gt[n.brushType](0,r.coordSys,n.coordRange),i=n.__rangeOffset;n.range=i?Fn[n.brushType](o.values,i.offset,Qu(o.xyMinMax,i.xyMinMax)):o.values}},this)},t.prototype.makePanelOpts=function(e,a){return Y(this._targetInfoList,function(n){var r=n.getPanelRect();return{panelId:n.panelId,defaultBrushType:a?a(n):null,clipPath:Bl(r),isTargetByCursor:El(r,e,n.coordSysModel),getLinearBrushOtherExtent:Vl(r)}})},t.prototype.controlSeries=function(e,a,n){var r=this.findTargetInfo(e,n);return r===!0||r&&ue(r.coordSyses,a.coordinateSystem)>=0},t.prototype.findTargetInfo=function(e,a){for(var n=this._targetInfoList,r=Nn(a,e),o=0;o<n.length;o++){var i=n[o],s=e.panelId;if(s){if(i.panelId===s)return i}else for(var l=0;l<Rn.length;l++)if(Rn[l](r,i))return i}return!0},t}();function pa(t){return t[0]>t[1]&&t.reverse(),t}function Nn(t,e){return Cr(t,e,{includeMainTypes:Xu})}var Ju={grid:function(t,e){var a=t.xAxisModels,n=t.yAxisModels,r=t.gridModels,o=mt(),i={},s={};!a&&!n&&!r||(D(a,function(l){var u=l.axis.grid.model;o.set(u.id,u),i[u.id]=!0}),D(n,function(l){var u=l.axis.grid.model;o.set(u.id,u),s[u.id]=!0}),D(r,function(l){o.set(l.id,l),i[l.id]=!0,s[l.id]=!0}),o.each(function(l){var u=l.coordinateSystem,c=[];D(u.getCartesians(),function(d,f){(ue(a,d.getAxis("x").model)>=0||ue(n,d.getAxis("y").model)>=0)&&c.push(d)}),e.push({panelId:"grid--"+l.id,gridModel:l,coordSysModel:l,coordSys:c[0],coordSyses:c,getPanelRect:zn.grid,xAxisDeclared:i[l.id],yAxisDeclared:s[l.id]})}))},geo:function(t,e){D(t.geoModels,function(a){var n=a.coordinateSystem;e.push({panelId:"geo--"+a.id,geoModel:a,coordSysModel:a,coordSys:n,coordSyses:[n],getPanelRect:zn.geo})})}},Rn=[function(t,e){var a=t.xAxisModel,n=t.yAxisModel,r=t.gridModel;return!r&&a&&(r=a.axis.grid.model),!r&&n&&(r=n.axis.grid.model),r&&r===e.gridModel},function(t,e){var a=t.geoModel;return a&&a===e.geoModel}],zn={grid:function(){return this.coordSys.master.getRect().clone()},geo:function(){var t=this.coordSys,e=t.getBoundingRect().clone();return e.applyTransform(hr(t)),e}},Gt={lineX:q($n,0),lineY:q($n,1),rect:function(t,e,a,n){var r=t?e.pointToData([a[0][0],a[1][0]],n):e.dataToPoint([a[0][0],a[1][0]],n),o=t?e.pointToData([a[0][1],a[1][1]],n):e.dataToPoint([a[0][1],a[1][1]],n),i=[pa([r[0],o[0]]),pa([r[1],o[1]])];return{values:i,xyMinMax:i}},polygon:function(t,e,a,n){var r=[[1/0,-1/0],[1/0,-1/0]],o=Y(a,function(i){var s=t?e.pointToData(i,n):e.dataToPoint(i,n);return r[0][0]=Math.min(r[0][0],s[0]),r[1][0]=Math.min(r[1][0],s[1]),r[0][1]=Math.max(r[0][1],s[0]),r[1][1]=Math.max(r[1][1],s[1]),s});return{values:o,xyMinMax:r}}};function $n(t,e,a,n){var r=a.getAxis(["x","y"][t]),o=pa(Y([0,1],function(s){return e?r.coordToData(r.toLocalCoord(n[s]),!0):r.toGlobalCoord(r.dataToCoord(n[s]))})),i=[];return i[t]=o,i[1-t]=[NaN,NaN],{values:o,xyMinMax:i}}var Fn={lineX:q(Wn,0),lineY:q(Wn,1),rect:function(t,e,a){return[[t[0][0]-a[0]*e[0][0],t[0][1]-a[0]*e[0][1]],[t[1][0]-a[1]*e[1][0],t[1][1]-a[1]*e[1][1]]]},polygon:function(t,e,a){return Y(t,function(n,r){return[n[0]-a[0]*e[r][0],n[1]-a[1]*e[r][1]]})}};function Wn(t,e,a,n){return[e[0]-n[t]*a[0],e[1]-n[t]*a[1]]}function Qu(t,e){var a=Un(t),n=Un(e),r=[a[0]/n[0],a[1]/n[1]];return isNaN(r[0])&&(r[0]=1),isNaN(r[1])&&(r[1]=1),r}function Un(t){return t?[t[0][1]-t[0][0],t[1][1]-t[1][0]]:[NaN,NaN]}var ha=D,ec=ei("toolbox-dataZoom_"),tc=function(t){V(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}return e.prototype.render=function(a,n,r,o){this._brushController||(this._brushController=new Al(r.getZr()),this._brushController.on("brush",oe(this._onBrush,this)).mount()),rc(a,n,this,o,r),nc(a,n)},e.prototype.onclick=function(a,n,r){ac[r].call(this)},e.prototype.remove=function(a,n){this._brushController&&this._brushController.unmount()},e.prototype.dispose=function(a,n){this._brushController&&this._brushController.dispose()},e.prototype._onBrush=function(a){var n=a.areas;if(!a.isEnd||!n.length)return;var r={},o=this.ecModel;this._brushController.updateCovers([]);var i=new Qr(za(this.model),o,{include:["grid"]});i.matchOutputRanges(n,o,function(u,c,d){if(d.type==="cartesian2d"){var f=u.brushType;f==="rect"?(s("x",d,c[0]),s("y",d,c[1])):s({lineX:"x",lineY:"y"}[f],d,c)}}),Hu(o,r),this._dispatchZoomAction(r);function s(u,c,d){var f=c.getAxis(u),p=f.model,h=l(u,p,o),g=h.findRepresentativeAxisProxy(p).getMinMaxSpan();(g.minValueSpan!=null||g.maxValueSpan!=null)&&(d=Ei(0,d.slice(),f.scale.getExtent(),0,g.minValueSpan,g.maxValueSpan)),h&&(r[h.id]={dataZoomId:h.id,startValue:d[0],endValue:d[1]})}function l(u,c,d){var f;return d.eachComponent({mainType:"dataZoom",subType:"select"},function(p){var h=p.getAxisModel(u,c.componentIndex);h&&(f=p)}),f}},e.prototype._dispatchZoomAction=function(a){var n=[];ha(a,function(r,o){n.push(ve(r))}),n.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:n})},e.getDefaultOption=function(a){var n={show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:a.getLocaleModel().get(["toolbox","dataZoom","title"]),brushStyle:{borderWidth:0,color:"rgba(210,219,238,0.2)"}};return n},e}(ie),ac={zoom:function(){var t=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:t})},back:function(){this._dispatchZoomAction(ju(this.ecModel))}};function za(t){var e={xAxisIndex:t.get("xAxisIndex",!0),yAxisIndex:t.get("yAxisIndex",!0),xAxisId:t.get("xAxisId",!0),yAxisId:t.get("yAxisId",!0)};return e.xAxisIndex==null&&e.xAxisId==null&&(e.xAxisIndex="all"),e.yAxisIndex==null&&e.yAxisId==null&&(e.yAxisIndex="all"),e}function nc(t,e){t.setIconStatus("back",Ku(e)>1?"emphasis":"normal")}function rc(t,e,a,n,r){var o=a._isZoomActive;n&&n.type==="takeGlobalCursor"&&(o=n.key==="dataZoomSelect"?n.dataZoomSelectActive:!1),a._isZoomActive=o,t.setIconStatus("zoom",o?"emphasis":"normal");var i=new Qr(za(t),e,{include:["grid"]}),s=i.makePanelOpts(r,function(l){return l.xAxisDeclared&&!l.yAxisDeclared?"lineX":!l.xAxisDeclared&&l.yAxisDeclared?"lineY":"rect"});a._brushController.setPanels(s).enableBrush(o&&s.length?{brushType:"auto",brushStyle:t.getModel("brushStyle").getItemStyle()}:!1)}Qo("dataZoom",function(t){var e=t.getComponent("toolbox",0),a=["feature","dataZoom"];if(!e||e.get(a)==null)return;var n=e.getModel(a),r=[],o=za(n),i=Cr(t,o);ha(i.xAxisModels,function(l){return s(l,"xAxis","xAxisIndex")}),ha(i.yAxisModels,function(l){return s(l,"yAxis","yAxisIndex")});function s(l,u,c){var d=l.componentIndex,f={type:"select",$fromToolbox:!0,filterMode:n.get("filterMode",!0)||"filter",id:ec+u+d};f[c]=d,r.push(f)}return r});function oc(t){t.registerComponentModel(ku),t.registerComponentView(Iu),Je("saveAsImage",Mu),Je("magicType",Vu),Je("dataView",Gu),Je("dataZoom",tc),Je("restore",qu),Sa(Lu)}var Gn=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a.layoutMode="box",a}return e.prototype.init=function(a,n,r){this.mergeDefaultAndTheme(a,r),this._initData()},e.prototype.mergeOption=function(a){t.prototype.mergeOption.apply(this,arguments),this._initData()},e.prototype.setCurrentIndex=function(a){a==null&&(a=this.option.currentIndex);var n=this._data.count();this.option.loop?a=(a%n+n)%n:(a>=n&&(a=n-1),a<0&&(a=0)),this.option.currentIndex=a},e.prototype.getCurrentIndex=function(){return this.option.currentIndex},e.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},e.prototype.setPlayState=function(a){this.option.autoPlay=!!a},e.prototype.getPlayState=function(){return!!this.option.autoPlay},e.prototype._initData=function(){var a=this.option,n=a.data||[],r=a.axisType,o=this._names=[],i;r==="category"?(i=[],D(n,function(u,c){var d=et(ti(u),""),f;at(u)?(f=ve(u),f.value=c):f=c,i.push(f),o.push(d)})):i=n;var s={category:"ordinal",time:"time",value:"number"}[r]||"number",l=this._data=new vt([{name:"value",type:s}],this);l.initData(i,o)},e.prototype.getData=function(){return this._data},e.prototype.getCategories=function(){if(this.get("axisType")==="category")return this._names.slice()},e.type="timeline",e.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},e}(st),eo=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.type="timeline.slider",e.defaultOption=ai(Gn.defaultOption,{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]}),e}(Gn);Ta(eo,wr.prototype);var ic=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.type="timeline",e}(lt),sc=function(t){V(e,t);function e(a,n,r,o){var i=t.call(this,a,n,r)||this;return i.type=o||"value",i}return e.prototype.getLabelModel=function(){return this.model.getModel("label")},e.prototype.isHorizontal=function(){return this.model.get("orient")==="horizontal"},e}(Ni),Zt=Math.PI,Zn=Ae(),lc=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.init=function(a,n){this.api=n},e.prototype.render=function(a,n,r){if(this.model=a,this.api=r,this.ecModel=n,this.group.removeAll(),a.get("show",!0)){var o=this._layout(a,r),i=this._createGroup("_mainGroup"),s=this._createGroup("_labelGroup"),l=this._axis=this._createAxis(o,a);a.formatTooltip=function(u){var c=l.scale.getLabel({value:u});return aa("nameValue",{noName:!0,value:c})},D(["AxisLine","AxisTick","Control","CurrentPointer"],function(u){this["_render"+u](o,i,l,a)},this),this._renderAxisLabel(o,s,l,a),this._position(o,a)}this._doPlayStop(),this._updateTicksStatus()},e.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},e.prototype.dispose=function(){this._clearTimer()},e.prototype._layout=function(a,n){var r=a.get(["label","position"]),o=a.get("orient"),i=cc(a,n),s;r==null||r==="auto"?s=o==="horizontal"?i.y+i.height/2<n.getHeight()/2?"-":"+":i.x+i.width/2<n.getWidth()/2?"+":"-":Ce(r)?s={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[o][r]:s=r;var l={horizontal:"center",vertical:s>=0||s==="+"?"left":"right"},u={horizontal:s>=0||s==="+"?"top":"bottom",vertical:"middle"},c={horizontal:0,vertical:Zt/2},d=o==="vertical"?i.height:i.width,f=a.getModel("controlStyle"),p=f.get("show",!0),h=p?f.get("itemSize"):0,g=p?f.get("itemGap"):0,m=h+g,_=a.get(["label","rotate"])||0;_=_*Zt/180;var b,y,v,S=f.get("position",!0),x=p&&f.get("showPlayBtn",!0),w=p&&f.get("showPrevBtn",!0),P=p&&f.get("showNextBtn",!0),A=0,I=d;S==="left"||S==="bottom"?(x&&(b=[0,0],A+=m),w&&(y=[A,0],A+=m),P&&(v=[I-h,0],I-=m)):(x&&(b=[I-h,0],I-=m),w&&(y=[0,0],A+=m),P&&(v=[I-h,0],I-=m));var T=[A,I];return a.get("inverse")&&T.reverse(),{viewRect:i,mainLength:d,orient:o,rotation:c[o],labelRotation:_,labelPosOpt:s,labelAlign:a.get(["label","align"])||l[o],labelBaseline:a.get(["label","verticalAlign"])||a.get(["label","baseline"])||u[o],playPosition:b,prevBtnPosition:y,nextBtnPosition:v,axisExtent:T,controlSize:h,controlGap:g}},e.prototype._position=function(a,n){var r=this._mainGroup,o=this._labelGroup,i=a.viewRect;if(a.orient==="vertical"){var s=ci(),l=i.x,u=i.y+i.height;en(s,s,[-l,-u]),ni(s,s,-Zt/2),en(s,s,[l,u]),i=i.clone(),i.applyTransform(s)}var c=b(i),d=b(r.getBoundingRect()),f=b(o.getBoundingRect()),p=[r.x,r.y],h=[o.x,o.y];h[0]=p[0]=c[0][0];var g=a.labelPosOpt;if(g==null||Ce(g)){var m=g==="+"?0:1;y(p,d,c,1,m),y(h,f,c,1,1-m)}else{var m=g>=0?0:1;y(p,d,c,1,m),h[1]=p[1]+g}r.setPosition(p),o.setPosition(h),r.rotation=o.rotation=a.rotation,_(r),_(o);function _(v){v.originX=c[0][0]-v.x,v.originY=c[1][0]-v.y}function b(v){return[[v.x,v.x+v.width],[v.y,v.y+v.height]]}function y(v,S,x,w,P){v[w]+=x[w][P]-S[w][P]}},e.prototype._createAxis=function(a,n){var r=n.getData(),o=n.get("axisType"),i=uc(n,o);i.getTicks=function(){return r.mapArray(["value"],function(u){return{value:u}})};var s=r.getDataExtent("value");i.setExtent(s[0],s[1]),i.calcNiceTicks();var l=new sc("value",i,a.axisExtent,o);return l.model=n,l},e.prototype._createGroup=function(a){var n=this[a]=new we;return this.group.add(n),n},e.prototype._renderAxisLine=function(a,n,r,o){var i=r.getExtent();if(o.get(["lineStyle","show"])){var s=new Jt({shape:{x1:i[0],y1:0,x2:i[1],y2:0},style:J({lineCap:"round"},o.getModel("lineStyle").getLineStyle()),silent:!0,z2:1});n.add(s);var l=this._progressLine=new Jt({shape:{x1:i[0],x2:this._currentPointer?this._currentPointer.x:i[0],y1:0,y2:0},style:Ge({lineCap:"round",lineWidth:s.style.lineWidth},o.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1});n.add(l)}},e.prototype._renderAxisTick=function(a,n,r,o){var i=this,s=o.getData(),l=r.scale.getTicks();this._tickSymbols=[],D(l,function(u){var c=r.dataToCoord(u.value),d=s.getItemModel(u.value),f=d.getModel("itemStyle"),p=d.getModel(["emphasis","itemStyle"]),h=d.getModel(["progress","itemStyle"]),g={x:c,y:0,onclick:oe(i._changeTimeline,i,u.value)},m=Hn(d,f,n,g);m.ensureState("emphasis").style=p.getItemStyle(),m.ensureState("progress").style=h.getItemStyle(),It(m);var _=Re(m);d.get("tooltip")?(_.dataIndex=u.value,_.dataModel=o):_.dataIndex=_.dataModel=null,i._tickSymbols.push(m)})},e.prototype._renderAxisLabel=function(a,n,r,o){var i=this,s=r.getLabelModel();if(s.get("show")){var l=o.getData(),u=r.getViewLabels();this._tickLabels=[],D(u,function(c){var d=c.tickValue,f=l.getItemModel(d),p=f.getModel("label"),h=f.getModel(["emphasis","label"]),g=f.getModel(["progress","label"]),m=r.dataToCoord(c.tickValue),_=new _e({x:m,y:0,rotation:a.labelRotation-a.rotation,onclick:oe(i._changeTimeline,i,d),silent:!1,style:Ne(p,{text:c.formattedLabel,align:a.labelAlign,verticalAlign:a.labelBaseline})});_.ensureState("emphasis").style=Ne(h),_.ensureState("progress").style=Ne(g),n.add(_),It(_),Zn(_).dataIndex=d,i._tickLabels.push(_)})}},e.prototype._renderControl=function(a,n,r,o){var i=a.controlSize,s=a.rotation,l=o.getModel("controlStyle").getItemStyle(),u=o.getModel(["emphasis","controlStyle"]).getItemStyle(),c=o.getPlayState(),d=o.get("inverse",!0);f(a.nextBtnPosition,"next",oe(this._changeTimeline,this,d?"-":"+")),f(a.prevBtnPosition,"prev",oe(this._changeTimeline,this,d?"+":"-")),f(a.playPosition,c?"stop":"play",oe(this._handlePlayClick,this,!c),!0);function f(p,h,g,m){if(p){var _=ri(xe(o.get(["controlStyle",h+"BtnSize"]),i),i),b=[0,-_/2,_,_],y=dc(o,h+"Icon",b,{x:p[0],y:p[1],originX:i/2,originY:0,rotation:m?-s:0,rectHover:!0,style:l,onclick:g});y.ensureState("emphasis").style=u,n.add(y),It(y)}}},e.prototype._renderCurrentPointer=function(a,n,r,o){var i=o.getData(),s=o.getCurrentIndex(),l=i.getItemModel(s).getModel("checkpointStyle"),u=this,c={onCreate:function(d){d.draggable=!0,d.drift=oe(u._handlePointerDrag,u),d.ondragend=oe(u._handlePointerDragend,u),jn(d,u._progressLine,s,r,o,!0)},onUpdate:function(d){jn(d,u._progressLine,s,r,o)}};this._currentPointer=Hn(l,l,this._mainGroup,{},this._currentPointer,c)},e.prototype._handlePlayClick=function(a){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:a,from:this.uid})},e.prototype._handlePointerDrag=function(a,n,r){this._clearTimer(),this._pointerChangeTimeline([r.offsetX,r.offsetY])},e.prototype._handlePointerDragend=function(a){this._pointerChangeTimeline([a.offsetX,a.offsetY],!0)},e.prototype._pointerChangeTimeline=function(a,n){var r=this._toAxisCoord(a)[0],o=this._axis,i=oi(o.getExtent().slice());r>i[1]&&(r=i[1]),r<i[0]&&(r=i[0]),this._currentPointer.x=r,this._currentPointer.markRedraw();var s=this._progressLine;s&&(s.shape.x2=r,s.dirty());var l=this._findNearestTick(r),u=this.model;(n||l!==u.getCurrentIndex()&&u.get("realtime"))&&this._changeTimeline(l)},e.prototype._doPlayStop=function(){var a=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(function(){var n=a.model;a._changeTimeline(n.getCurrentIndex()+(n.get("rewind",!0)?-1:1))},this.model.get("playInterval")))},e.prototype._toAxisCoord=function(a){var n=this._mainGroup.getLocalTransform();return ii(a,n,!0)},e.prototype._findNearestTick=function(a){var n=this.model.getData(),r=1/0,o,i=this._axis;return n.each(["value"],function(s,l){var u=i.dataToCoord(s),c=Math.abs(u-a);c<r&&(r=c,o=l)}),o},e.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},e.prototype._changeTimeline=function(a){var n=this.model.getCurrentIndex();a==="+"?a=n+1:a==="-"&&(a=n-1),this.api.dispatchAction({type:"timelineChange",currentIndex:a,from:this.uid})},e.prototype._updateTicksStatus=function(){var a=this.model.getCurrentIndex(),n=this._tickSymbols,r=this._tickLabels;if(n)for(var o=0;o<n.length;o++)n&&n[o]&&n[o].toggleState("progress",o<a);if(r)for(var o=0;o<r.length;o++)r&&r[o]&&r[o].toggleState("progress",Zn(r[o]).dataIndex<=a)},e.type="timeline.slider",e}(ic);function uc(t,e){if(e=e||t.get("type"),e)switch(e){case"category":return new ui({ordinalMeta:t.getCategories(),extent:[1/0,-1/0]});case"time":return new li({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new si}}function cc(t,e){return _r(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},t.get("padding"))}function dc(t,e,a,n){var r=n.style,o=xr(t.get(["controlStyle",e]),n||{},new ba(a[0],a[1],a[2],a[3]));return r&&o.setStyle(r),o}function Hn(t,e,a,n,r,o){var i=e.get("color");if(r)r.setColor(i),a.add(r),o&&o.onUpdate(r);else{var s=t.get("symbol");r=xa(s,-1,-1,2,2,i),r.setStyle("strokeNoScale",!0),a.add(r),o&&o.onCreate(r)}var l=e.getItemStyle(["color"]);r.setStyle(l),n=le({rectHover:!0,z2:100},n,!0);var u=wa(t.get("symbolSize"));n.scaleX=u[0]/2,n.scaleY=u[1]/2;var c=Aa(t.get("symbolOffset"),u);c&&(n.x=(n.x||0)+c[0],n.y=(n.y||0)+c[1]);var d=t.get("symbolRotate");return n.rotation=(d||0)*Math.PI/180||0,r.attr(n),r.updateTransform(),r}function jn(t,e,a,n,r,o){if(!t.dragging){var i=r.getModel("checkpointStyle"),s=n.dataToCoord(r.getData().get("value",a));if(o||!i.get("animation",!0))t.attr({x:s,y:0}),e&&e.attr({shape:{x2:s}});else{var l={duration:i.get("animationDuration",!0),easing:i.get("animationEasing",!0)};t.stopAnimation(null,!0),t.animateTo({x:s,y:0},l),e&&e.animateTo({shape:{x2:s}},l)}}}function fc(t){t.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(e,a,n){var r=a.getComponent("timeline");return r&&e.currentIndex!=null&&(r.setCurrentIndex(e.currentIndex),!r.get("loop",!0)&&r.isIndexMax()&&r.getPlayState()&&(r.setPlayState(!1),n.dispatchAction({type:"timelinePlayChange",playState:!1,from:e.from}))),a.resetOption("timeline",{replaceMerge:r.get("replaceMerge",!0)}),Ge({currentIndex:r.option.currentIndex},e)}),t.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(e,a){var n=a.getComponent("timeline");n&&e.playState!=null&&n.setPlayState(e.playState)})}function pc(t){var e=t&&t.timeline;W(e)||(e=e?[e]:[]),D(e,function(a){a&&hc(a)})}function hc(t){var e=t.type,a={number:"value",time:"time"};if(a[e]&&(t.axisType=a[e],delete t.type),Yn(t),ke(t,"controlPosition")){var n=t.controlStyle||(t.controlStyle={});ke(n,"position")||(n.position=t.controlPosition),n.position==="none"&&!ke(n,"show")&&(n.show=!1,delete n.position),delete t.controlPosition}D(t.data||[],function(r){at(r)&&!W(r)&&(!ke(r,"value")&&ke(r,"name")&&(r.value=r.name),Yn(r))})}function Yn(t){var e=t.itemStyle||(t.itemStyle={}),a=e.emphasis||(e.emphasis={}),n=t.label||t.label||{},r=n.normal||(n.normal={}),o={normal:1,emphasis:1};D(n,function(i,s){!o[s]&&!ke(r,s)&&(r[s]=i)}),a.label&&!ke(n,"emphasis")&&(n.emphasis=a.label,delete a.label)}function ke(t,e){return t.hasOwnProperty(e)}function vc(t){t.registerComponentModel(eo),t.registerComponentView(lc),t.registerSubTypeDefaulter("timeline",function(){return"slider"}),fc(t),t.registerPreprocessor(pc)}function gc(t,e){if(!t)return!1;for(var a=W(t)?t:[t],n=0;n<a.length;n++)if(a[n]&&a[n][e])return!0;return!1}function ct(t){di(t,"label",["show"])}var dt=Ae(),ot=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a.createdBySelf=!1,a}return e.prototype.init=function(a,n,r){this.mergeDefaultAndTheme(a,r),this._mergeOption(a,r,!1,!0)},e.prototype.isAnimationEnabled=function(){if(Sr.node)return!1;var a=this.__hostSeries;return this.getShallow("animation")&&a&&a.isAnimationEnabled()},e.prototype.mergeOption=function(a,n){this._mergeOption(a,n,!1,!1)},e.prototype._mergeOption=function(a,n,r,o){var i=this.mainType;r||n.eachSeries(function(s){var l=s.get(this.mainType,!0),u=dt(s)[i];if(!l||!l.data){dt(s)[i]=null;return}u?u._mergeOption(l,n,!0):(o&&ct(l),D(l.data,function(c){c instanceof Array?(ct(c[0]),ct(c[1])):ct(c)}),u=this.createMarkerModelFromSeries(l,this,n),J(u,{mainType:this.mainType,seriesIndex:s.seriesIndex,name:s.name,createdBySelf:!0}),u.__hostSeries=s),dt(s)[i]=u},this)},e.prototype.formatTooltip=function(a,n,r){var o=this.getData(),i=this.getRawValue(a),s=o.getName(a);return aa("section",{header:this.name,blocks:[aa("nameValue",{name:s,value:i,noName:!s,noValue:i==null})]})},e.prototype.getData=function(){return this._data},e.prototype.setData=function(a){this._data=a},e.getMarkerModelFromSeries=function(a,n){return dt(a)[n]},e.type="marker",e.dependencies=["series","grid","polar","geo"],e}(st);Ta(ot,wr.prototype);function mc(t){return!(isNaN(parseFloat(t.x))&&isNaN(parseFloat(t.y)))}function yc(t){return!isNaN(parseFloat(t.x))&&!isNaN(parseFloat(t.y))}function ft(t,e,a,n,r,o){var i=[],s=fi(e,n),l=s?e.getCalculationInfo("stackResultDimension"):n,u=$a(e,l,t),c=e.indicesOfNearest(l,u)[0];i[r]=e.get(a,c),i[o]=e.get(l,c);var d=e.get(n,c),f=pi(e.get(n,c));return f=Math.min(f,20),f>=0&&(i[o]=+i[o].toFixed(f)),[i,d]}var Ht={min:q(ft,"min"),max:q(ft,"max"),average:q(ft,"average"),median:q(ft,"median")};function Kn(t,e){if(e){var a=t.getData(),n=t.coordinateSystem,r=n&&n.dimensions;if(!yc(e)&&!W(e.coord)&&W(r)){var o=to(e,a,n,t);if(e=ve(e),e.type&&Ht[e.type]&&o.baseAxis&&o.valueAxis){var i=ue(r,o.baseAxis.dim),s=ue(r,o.valueAxis.dim),l=Ht[e.type](a,o.baseDataDim,o.valueDataDim,i,s);e.coord=l[0],e.value=l[1]}else e.coord=[e.xAxis!=null?e.xAxis:e.radiusAxis,e.yAxis!=null?e.yAxis:e.angleAxis]}if(e.coord==null||!W(r))e.coord=[];else for(var u=e.coord,c=0;c<2;c++)Ht[u[c]]&&(u[c]=$a(a,a.mapDimension(r[c]),u[c]));return e}}function to(t,e,a,n){var r={};return t.valueIndex!=null||t.valueDim!=null?(r.valueDataDim=t.valueIndex!=null?e.getDimension(t.valueIndex):t.valueDim,r.valueAxis=a.getAxis(_c(n,r.valueDataDim)),r.baseAxis=a.getOtherAxis(r.valueAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim)):(r.baseAxis=n.getBaseAxis(),r.valueAxis=a.getOtherAxis(r.baseAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim),r.valueDataDim=e.mapDimension(r.valueAxis.dim)),r}function _c(t,e){var a=t.getData().getDimensionInfo(e);return a&&a.coordDim}function qn(t,e){return t&&t.containData&&e.coord&&!mc(e)?t.containData(e.coord):!0}function bc(t,e){return t?function(a,n,r,o){var i=o<2?a.coord&&a.coord[o]:a.value;return tn(i,e[o])}:function(a,n,r,o){return tn(a.value,e[o])}}function $a(t,e,a){if(a==="average"){var n=0,r=0;return t.each(e,function(o,i){isNaN(o)||(n+=o,r++)}),n/r}else return a==="median"?t.getMedian(e):t.getDataExtent(e)[a==="max"?1:0]}var jt=Ae(),xc=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.init=function(){this.markerGroupMap=mt()},e.prototype.render=function(a,n,r){var o=this,i=this.markerGroupMap;i.each(function(s){jt(s).keep=!1}),n.eachSeries(function(s){var l=ot.getMarkerModelFromSeries(s,o.type);l&&o.renderSeries(s,l,n,r)}),i.each(function(s){!jt(s).keep&&o.group.remove(s.group)})},e.prototype.markKeep=function(a){jt(a).keep=!0},e.prototype.toggleBlurSeries=function(a,n){var r=this;D(a,function(o){var i=ot.getMarkerModelFromSeries(o,r.type);if(i){var s=i.getData();s.eachItemGraphicEl(function(l){l&&(n?hi(l):vi(l))})}})},e.type="marker",e}(lt),Sc=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.createMarkerModelFromSeries=function(a,n,r){return new e(a,n,r)},e.type="markLine",e.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"},e}(ot),pt=Ae(),Cc=function(t,e,a,n){var r=t.getData(),o;if(W(n))o=n;else{var i=n.type;if(i==="min"||i==="max"||i==="average"||i==="median"||n.xAxis!=null||n.yAxis!=null){var s=void 0,l=void 0;if(n.yAxis!=null||n.xAxis!=null)s=e.getAxis(n.yAxis!=null?"y":"x"),l=mi(n.yAxis,n.xAxis);else{var u=to(n,r,e,t);s=u.valueAxis;var c=yi(r,u.valueDataDim);l=$a(r,c,i)}var d=s.dim==="x"?0:1,f=1-d,p=ve(n),h={coord:[]};p.type=null,p.coord=[],p.coord[f]=-1/0,h.coord[f]=1/0;var g=a.get("precision");g>=0&&_i(l)&&(l=+l.toFixed(Math.min(g,20))),p.coord[d]=h.coord[d]=l,o=[p,h,{type:i,valueIndex:n.valueIndex,value:l}]}else o=[]}var m=[Kn(t,o[0]),Kn(t,o[1]),J({},o[2])];return m[2].type=m[2].type||null,le(m[2],m[0]),le(m[2],m[1]),m};function bt(t){return!isNaN(t)&&!isFinite(t)}function Xn(t,e,a,n){var r=1-t,o=n.dimensions[t];return bt(e[r])&&bt(a[r])&&e[t]===a[t]&&n.getAxis(o).containData(e[t])}function wc(t,e){if(t.type==="cartesian2d"){var a=e[0].coord,n=e[1].coord;if(a&&n&&(Xn(1,a,n,t)||Xn(0,a,n,t)))return!0}return qn(t,e[0])&&qn(t,e[1])}function Yt(t,e,a,n,r){var o=n.coordinateSystem,i=t.getItemModel(e),s,l=Se(i.get("x"),r.getWidth()),u=Se(i.get("y"),r.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(n.getMarkerPosition)s=n.getMarkerPosition(t.getValues(t.dimensions,e));else{var c=o.dimensions,d=t.get(c[0],e),f=t.get(c[1],e);s=o.dataToPoint([d,f])}if(Ri(o,"cartesian2d")){var p=o.getAxis("x"),h=o.getAxis("y"),c=o.dimensions;bt(t.get(c[0],e))?s[0]=p.toGlobalCoord(p.getExtent()[a?0:1]):bt(t.get(c[1],e))&&(s[1]=h.toGlobalCoord(h.getExtent()[a?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}t.setItemLayout(e,s)}var Ac=function(t){V(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.updateTransform=function(a,n,r){n.eachSeries(function(o){var i=ot.getMarkerModelFromSeries(o,"markLine");if(i){var s=i.getData(),l=pt(i).from,u=pt(i).to;l.each(function(c){Yt(l,c,!0,o,r),Yt(u,c,!1,o,r)}),s.each(function(c){s.setItemLayout(c,[l.getItemLayout(c),u.getItemLayout(c)])}),this.markerGroupMap.get(o.id).updateLayout()}},this)},e.prototype.renderSeries=function(a,n,r,o){var i=a.coordinateSystem,s=a.id,l=a.getData(),u=this.markerGroupMap,c=u.get(s)||u.set(s,new gl);this.group.add(c.group);var d=Tc(i,a,n),f=d.from,p=d.to,h=d.line;pt(n).from=f,pt(n).to=p,n.setData(h);var g=n.get("symbol"),m=n.get("symbolSize"),_=n.get("symbolRotate"),b=n.get("symbolOffset");W(g)||(g=[g,g]),W(m)||(m=[m,m]),W(_)||(_=[_,_]),W(b)||(b=[b,b]),d.from.each(function(v){y(f,v,!0),y(p,v,!1)}),h.each(function(v){var S=h.getItemModel(v).getModel("lineStyle").getLineStyle();h.setItemLayout(v,[f.getItemLayout(v),p.getItemLayout(v)]),S.stroke==null&&(S.stroke=f.getItemVisual(v,"style").fill),h.setItemVisual(v,{fromSymbolKeepAspect:f.getItemVisual(v,"symbolKeepAspect"),fromSymbolOffset:f.getItemVisual(v,"symbolOffset"),fromSymbolRotate:f.getItemVisual(v,"symbolRotate"),fromSymbolSize:f.getItemVisual(v,"symbolSize"),fromSymbol:f.getItemVisual(v,"symbol"),toSymbolKeepAspect:p.getItemVisual(v,"symbolKeepAspect"),toSymbolOffset:p.getItemVisual(v,"symbolOffset"),toSymbolRotate:p.getItemVisual(v,"symbolRotate"),toSymbolSize:p.getItemVisual(v,"symbolSize"),toSymbol:p.getItemVisual(v,"symbol"),style:S})}),c.updateData(h),d.line.eachItemGraphicEl(function(v){Re(v).dataModel=n,v.traverse(function(S){Re(S).dataModel=n})});function y(v,S,x){var w=v.getItemModel(S);Yt(v,S,x,a,o);var P=w.getModel("itemStyle").getItemStyle();P.fill==null&&(P.fill=gi(l,"color")),v.setItemVisual(S,{symbolKeepAspect:w.get("symbolKeepAspect"),symbolOffset:xe(w.get("symbolOffset",!0),b[x?0:1]),symbolRotate:xe(w.get("symbolRotate",!0),_[x?0:1]),symbolSize:xe(w.get("symbolSize"),m[x?0:1]),symbol:xe(w.get("symbol",!0),g[x?0:1]),style:P})}this.markKeep(c),c.group.silent=n.get("silent")||a.get("silent")},e.type="markLine",e}(xc);function Tc(t,e,a){var n;t?n=Y(t&&t.dimensions,function(u){var c=e.getData().getDimensionInfo(e.getData().mapDimension(u))||{};return J(J({},c),{name:u,ordinalMeta:null})}):n=[{name:"value",type:"float"}];var r=new vt(n,a),o=new vt(n,a),i=new vt([],a),s=Y(a.get("data"),q(Cc,e,t,a));t&&(s=St(s,q(wc,t)));var l=bc(!!t,n);return r.initData(Y(s,function(u){return u[0]}),null,l),o.initData(Y(s,function(u){return u[1]}),null,l),i.initData(Y(s,function(u){return u[2]})),i.hasItemOption=!0,{from:r,to:o,line:i}}function Dc(t){t.registerComponentModel(Sc),t.registerComponentView(Ac),t.registerPreprocessor(function(e){gc(e.series,"markLine")&&(e.markLine=e.markLine||{})})}function Pc(t,e,a){const n=uo("Shift",{events:["keydown","keyup"]}),r=Z(()=>e.value==="dark"?"white":"black"),o=U({x:0,y:0}),i=144,s=U(!0),l=U(!1),u=U(!1),c=35,d=170;function f(b){return Le(a.value,b,Lr)}function p(b){s.value&&!u.value&&_(b.offsetX,b.offsetY)}function h(b){var y,v,S,x,w,P,A,I;if(l.value)u.value?(m(),u.value=!1,(P=(w=t.value)==null?void 0:w.chart)==null||P.getZr().off("mousemove",p),(I=(A=t.value)==null?void 0:A.chart)==null||I.getZr().off("mousedown",h)):u.value=!0;else{const T=((y=t.value)==null?void 0:y.convertFromPixel({seriesIndex:0},[b.offsetX,b.offsetY]))??[0,0];T[0]=f(Number(T[0]));const L=((v=t.value)==null?void 0:v.convertToPixel({seriesIndex:0},T))??[0,0];o.value={x:L[0],y:L[1]},(x=(S=t.value)==null?void 0:S.chart)==null||x.getZr().on("mousemove",p),g()}}function g(){var b;l.value=!0,(b=t.value)==null||b.setOption({dataZoom:[{disabled:!0}],graphic:[{type:"rect",shape:{x:o.value.x,width:0,y:o.value.y,height:0},style:{fill:r.value,opacity:.1}},{type:"rect",z:5,shape:{x:o.value.x,width:d,y:o.value.y,height:c,r:5},style:{fill:"#002fff",opacity:.8},textContent:{z:10,style:{text:"0 bars - 0%",font:"14px sans-serif",fill:"white"}},textConfig:{position:"inside"}}]})}function m(){var b;l.value=!1,(b=t.value)==null||b.setOption({dataZoom:[{disabled:!1}],graphic:[{$action:"remove"},{$action:"remove"}]})}function _(b,y){var G,j,ge,F;const v=((G=t.value)==null?void 0:G.convertFromPixel({seriesIndex:0},[o.value.x,o.value.y]))??[0,0],S=((j=t.value)==null?void 0:j.convertFromPixel({seriesIndex:0},[b,y]))??[0,0],x=Number(v[1]),w=Number(S[1]),P=f(Number(v[0])),A=f(Number(S[0])),I=Math.abs(P-A),T=((ge=t.value)==null?void 0:ge.convertToPixel({seriesIndex:0},[A,0])[0])??0,L=co(I,{units:["d","h","m"]}),E=Math.abs((x-w)/x*100).toFixed(2);(F=t.value)==null||F.setOption({graphic:[{shape:{width:T>o.value.x?-1*(o.value.x-T):T-o.value.x,height:y>o.value.y?-1*(o.value.y-y):y-o.value.y}},{shape:{x:o.value.x+(T-o.value.x)/2-d/2,y:y>o.value.y?y-(c+5):y+9},textContent:{style:{textAlign:b<o.value.x?"left":"right",text:`${I/a.value} bars (${x<w?E:"-"+E}%) 
 ${L}`}}}]}),s.value=!1,setTimeout(()=>{s.value=!0},1e3/i)}X(()=>n.value,()=>{var b,y;n.value&&!l.value&&((y=(b=t.value)==null?void 0:b.chart)==null||y.getZr().on("mousedown",h))})}const Lc={class:"chart-wrapper"},Kt="5.5%",qt="1%",Jn=55,Qe=8,kc="#00ff26",Ic="#00ff26",Oc="#faba25",Mc="#faba25",Bc=Ue({__name:"CandleChart",props:{trades:{required:!1,default:()=>[],type:Array},dataset:{required:!0,type:Object},heikinAshi:{required:!1,default:!1,type:Boolean},useUTC:{required:!1,default:!0,type:Boolean},plotConfig:{required:!0,type:Object},theme:{default:"dark",type:String},sliderPosition:{required:!1,type:Object,default:()=>{}},colorUp:{required:!1,type:String,default:"#12bb7b"},colorDown:{required:!1,type:String,default:"#ef5350"}},setup(t){Sa([bi,mu,xi,zi,$i,Si,vc,Ci,oc,wi,Fi,Wi,Dc,Ql,Ui,Gi,cl,Ai,Tu]);const e=t,a=e.colorUp,n=e.colorUp,r=e.colorDown,o=e.colorDown,i=U(),s=U({}),l=Z(()=>e.dataset?e.dataset.strategy:""),u=Z(()=>e.dataset?e.dataset.pair:""),c=Z(()=>e.dataset?e.dataset.timeframe:""),d=Z(()=>e.dataset!==null&&typeof e.dataset=="object"),f=Z(()=>e.trades.filter(b=>b.pair===u.value)),p=Z(()=>`${l.value} - ${u.value} - ${c.value}`),h=Z(()=>rl(e.plotConfig));Pc(i,Ya(()=>e.theme),Ya(()=>e.dataset.timeframe_ms));function g(b=!1){var me,R,ce,ae;if(!d.value)return;(me=s.value)!=null&&me.title&&(s.value.title[0].text=p.value);const y=e.dataset.columns.slice(),v=y.findIndex(k=>k==="__date_ts"),S=y.findIndex(k=>k==="open"),x=y.findIndex(k=>k==="high"),w=y.findIndex(k=>k==="low"),P=y.findIndex(k=>k==="close"),A=y.findIndex(k=>k==="volume"),I=y.findIndex(k=>k==="_buy_signal_close"||k==="_enter_long_signal_close"),T=y.findIndex(k=>k==="_sell_signal_close"||k==="_exit_long_signal_close"),L=y.findIndex(k=>k==="_enter_short_signal_close"),E=y.findIndex(k=>k==="_exit_short_signal_close"),G="subplots"in e.plotConfig?Object.keys(e.plotConfig.subplots).length+1:1;if(Array.isArray((R=s.value)==null?void 0:R.dataZoom))if(b){const k=(1-250/e.dataset.length)*100;s.value.dataZoom.forEach((H,Be)=>{s.value&&s.value.dataZoom&&(s.value.dataZoom[Be].start=k)})}else s.value.dataZoom.forEach((k,H)=>{s.value&&s.value.dataZoom&&(delete s.value.dataZoom[H].start,delete s.value.dataZoom[H].end)});let j=e.heikinAshi?qs(y,e.dataset.data):e.dataset.data.slice();h.value.forEach(([k,H])=>{j=nl(y,j,k,H)});const ge=Array(j.length>0?j[j.length-2].length:0);ge[v]=j[j.length-1][v]+e.dataset.timeframe_ms*3,j.push(ge);const F={dataset:{source:j},grid:[{left:Kt,right:qt,bottom:`${G*Qe+2}%`},{left:Kt,right:qt,bottom:`${G*Qe}%`,height:`${Qe}%`}],series:[{name:"Candles",type:"candlestick",barWidth:"80%",itemStyle:{color:a,color0:r,borderColor:n,borderColor0:o},encode:{x:v,y:[S,P,w,x]}},{name:"Volume",type:"bar",xAxisIndex:1,yAxisIndex:1,itemStyle:{color:"#777777"},large:!0,encode:{x:v,y:A}},{name:"Entry",type:"scatter",symbol:"triangle",symbolSize:10,xAxisIndex:0,yAxisIndex:0,itemStyle:{color:kc},encode:{x:v,y:I}}]};if(T>=0&&Array.isArray(F.series)&&F.series.push({name:"Exit",type:"scatter",symbol:"diamond",symbolSize:8,xAxisIndex:0,yAxisIndex:0,itemStyle:{color:Oc},encode:{x:v,y:T}}),Array.isArray(F.series)&&(L>=0&&F.series.push({name:"Entry",type:"scatter",symbol:"triangle",symbolRotate:180,symbolSize:10,xAxisIndex:0,yAxisIndex:0,itemStyle:{color:Ic},tooltip:{},encode:{x:v,y:L}}),E>=0&&F.series.push({name:"Exit",type:"scatter",symbol:"pin",symbolSize:8,xAxisIndex:0,yAxisIndex:0,itemStyle:{color:Mc},tooltip:{},encode:{x:v,y:E}})),Object.assign(s.value,F),"main_plot"in e.plotConfig&&Object.entries(e.plotConfig.main_plot).forEach(([k,H])=>{var je,Q,De,Ye,Ke,qe,Xe;const Be=y.findIndex(Pe=>Pe===k);if(Be>0){if(!Array.isArray((je=s.value)==null?void 0:je.legend)&&((De=(Q=s.value)==null?void 0:Q.legend)!=null&&De.data)&&s.value.legend.data.push(k),Array.isArray((Ye=s.value)==null?void 0:Ye.series)){if((Ke=s.value)==null||Ke.series.push(na(v,Be,k,H)),H.fill_to){const Pe=`${k}-${H.fill_to}`,At=y.findIndex(Pt=>Pt===Pe),Tt={color:H.color,type:be.line},Dt=sn(v,At,k,Tt,0);s.value.series[s.value.series.length-1].stack=k,s.value.series.push(Dt)}(Xe=s.value)==null||Xe.series.splice(((qe=s.value)==null?void 0:qe.series.length)-1,0)}}else console.log(`element ${k} for main plot not found in columns.`)}),"subplots"in e.plotConfig){let k=2;Object.entries(e.plotConfig.subplots).forEach(([H,Be])=>{const je=k;Array.isArray(s.value.yAxis)&&s.value.yAxis.length<=k&&s.value.yAxis.push({scale:!0,gridIndex:je,name:H,nameLocation:"middle",nameGap:Jn,axisLabel:{show:!0},axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1}}),Array.isArray(s.value.xAxis)&&s.value.xAxis.length<=k&&s.value.xAxis.push({type:"time",gridIndex:je,axisLine:{onZero:!1},axisTick:{show:!1},axisLabel:{show:!1},axisPointer:{label:{show:!1}},splitLine:{show:!1},splitNumber:20}),Array.isArray(s.value.dataZoom)&&s.value.dataZoom.forEach(Q=>Q.xAxisIndex&&Array.isArray(Q.xAxisIndex)?Q.xAxisIndex.push(k):null),s.value.grid&&Array.isArray(s.value.grid)&&s.value.grid.push({left:Kt,right:qt,bottom:`${(G-k+1)*Qe}%`,height:`${Qe}%`}),Object.entries(Be).forEach(([Q,De])=>{var Ke,qe,Xe;const Ye=y.findIndex(Pe=>Pe===Q);if(Ye>0){if(!Array.isArray(s.value.legend)&&((Ke=s.value.legend)!=null&&Ke.data)&&s.value.legend.data.push(Q),s.value.series&&Array.isArray(s.value.series)){if(s.value.series.push(na(v,Ye,Q,De,k)),De.fill_to){const Pe=`${Q}-${De.fill_to}`,At=y.findIndex(Pt=>Pt===Pe),Tt={color:De.color,type:be.line},Dt=sn(v,At,Q,Tt,k);s.value.series[s.value.series.length-1].stack=Q,s.value.series.push(Dt)}(Xe=s.value)==null||Xe.series.splice(((qe=s.value)==null?void 0:qe.series.length)-1,0)}}else console.log(`element ${Q} was not found in the columns.`)}),k+=1})}Array.isArray(s.value.grid)&&(s.value.grid[s.value.grid.length-1].bottom="50px",delete s.value.grid[s.value.grid.length-1].top);const Te="Trades";!Array.isArray(s.value.legend)&&((ce=s.value.legend)!=null&&ce.data)&&s.value.legend.data.push(Te);const He=al(Te,e.theme,e.dataset,f.value);Array.isArray(s.value.series)&&s.value.series.push(He),(ae=i.value)==null||ae.setOption(s.value,{replaceMerge:["series","grid","yAxis","xAxis","legend"],notMerge:b})}function m(){var b;(b=i.value)==null||b.setOption({},{notMerge:!0}),s.value={title:[{show:!1}],backgroundColor:"rgba(0, 0, 0, 0)",useUTC:e.useUTC,animation:!1,legend:{data:["Candles","Volume","Entry","Exit"],right:"1%",type:"scroll",pageTextStyle:{color:e.theme==="dark"?"#dedede":"#333"},pageIconColor:e.theme==="dark"?"#aaa":"#2f4554",pageIconInactiveColor:e.theme==="dark"?"#2f4554":"#aaa"},tooltip:{show:!0,trigger:"axis",renderMode:"richText",backgroundColor:"rgba(80,80,80,0.7)",borderWidth:0,textStyle:{color:"#fff"},axisPointer:{type:"cross",lineStyle:{color:"#cccccc",width:1,opacity:1}},position(y,v,S,x,w){const P={top:60},A=y[0]<w.viewSize[0]/2;return P[["left","right"][+A]]=A?5:60,P}},axisPointer:{link:[{xAxisIndex:"all"}],label:{backgroundColor:"#777"}},xAxis:[{type:"time",axisLine:{onZero:!1},axisTick:{show:!0},axisLabel:{show:!0},axisPointer:{label:{show:!1}},position:"top",splitLine:{show:!1},splitNumber:20,min:"dataMin",max:"dataMax"},{type:"time",gridIndex:1,axisLine:{onZero:!1},axisTick:{show:!1},axisLabel:{show:!1},axisPointer:{label:{show:!1}},splitLine:{show:!1},splitNumber:20,min:"dataMin",max:"dataMax"}],yAxis:[{scale:!0,max:y=>y.max+(y.max-y.min)*.02,min:y=>y.min-(y.max-y.min)*.04},{scale:!0,gridIndex:1,splitNumber:2,name:"volume",nameLocation:"middle",nameGap:Jn,axisLabel:{show:!1},axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1}}],dataZoom:[{type:"inside",xAxisIndex:[0,1],start:80,end:100},{xAxisIndex:[0,1],bottom:10,start:80,end:100,...Zi}]},console.log("Initialized"),g(!0)}function _(){if(!e.sliderPosition)return;const b=Ka(e.sliderPosition.startValue-e.dataset.timeframe_ms*40),y=Ka(e.sliderPosition.endValue?e.sliderPosition.endValue+e.dataset.timeframe_ms*40:e.sliderPosition.startValue+e.dataset.timeframe_ms*80);i.value&&i.value.dispatchAction({type:"dataZoom",dataZoomIndex:0,startValue:b,endValue:y})}return va(()=>{m()}),X(()=>e.useUTC,()=>m()),X(()=>e.dataset,()=>g()),X(()=>e.plotConfig,()=>m()),X(()=>e.heikinAshi,()=>g()),X(()=>e.sliderPosition,()=>_()),X(()=>e.theme,()=>{m()}),(b,y)=>(O(),$("div",Lc,[C(d)?(O(),ee(C(Ti),{key:0,ref_key:"candleChart",ref:i,theme:t.theme,autoresize:"","manual-update":""},null,8,["theme"])):K("",!0)]))}}),Vc=_a(Bc,[["__scopeId","data-v-806edda1"]]),Ec=t=>(tr("data-v-93ea43d8"),t=t(),ar(),t),Nc={class:"d-flex h-100"},Rc={class:"flex-fill w-100 flex-column align-items-stretch d-flex h-100"},zc={class:"d-flex me-0"},$c={class:"ms-1 ms-md-2 d-flex flex-wrap flex-md-nowrap align-items-center w-auto"},Fc={class:"ms-md-2 text-nowrap"},Wc={class:"d-flex flex-column"},Uc={class:"d-flex flex-row flex-wrap"},Gc={key:0,class:"ms-2 text-nowrap",title:"Long entry signals"},Zc={key:1,class:"ms-2 text-nowrap",title:"Long exit signals"},Hc={class:"d-flex flex-row flex-wrap"},jc={key:0,class:"ms-2 text-nowrap"},Yc={key:1,class:"ms-2 text-nowrap"},Kc={class:"ms-auto d-flex align-items-center w-auto"},qc=Ec(()=>N("small",{class:"text-nowrap"},"Heikin Ashi",-1)),Xc={class:"ms-2"},Jc={class:"ms-2 me-0 me-md-1"},Qc={class:"h-100 w-100 d-flex"},ed={class:"flex-grow-1"},td={key:1,class:"m-auto"},ad={key:1,style:{"font-size":"1.5rem"}},nd={key:2},rd={key:0,class:"w-25"},od=Ue({__name:"CandleChartContainer",props:{trades:{required:!1,default:()=>[],type:Array},availablePairs:{required:!0,type:Array},timeframe:{required:!0,type:String},historicView:{required:!1,default:!1,type:Boolean},plotConfigModal:{required:!1,default:!0,type:Boolean},timerange:{required:!1,default:"",type:String},strategy:{required:!1,default:"",type:String},freqaiModel:{required:!1,default:void 0,type:String},sliderPosition:{required:!1,type:Object,default:()=>{}}},setup(t){const e=t,a=fo(),n=po(),r=er(),o=ya(),i=U(),s=Z(()=>{var m,_;return e.historicView?(m=r.activeBot.history[`${r.activeBot.plotPair}__${e.timeframe}`])==null?void 0:m.data:(_=r.activeBot.candleData[`${r.activeBot.plotPair}__${e.timeframe}`])==null?void 0:_.data}),l=Z(()=>{var m;return e.strategy||((m=s.value)==null?void 0:m.strategy)||""}),u=Z(()=>s.value?s.value.all_columns??s.value.columns:[]),c=Z(()=>s.value&&s.value.data.length>0),d=Z(()=>e.historicView?r.activeBot.historyStatus===Ve.loading:r.activeBot.candleDataStatus===Ve.loading),f=Z(()=>{switch(e.historicView?r.activeBot.historyStatus:r.activeBot.candleDataStatus){case Ve.not_loaded:return"Not loaded yet.";case Ve.loading:return"Loading...";case Ve.success:return"No data available";case Ve.error:return"Failed to load data";default:return"Unknown"}}),p=U(!1);function h(){e.plotConfigModal?p.value=!p.value:i.value=!i.value}function g(){r.activeBot.plotPair&&e.timeframe&&(e.historicView?r.activeBot.getPairHistory({pair:r.activeBot.plotPair,timeframe:e.timeframe,timerange:e.timerange,strategy:e.strategy,freqaimodel:e.freqaiModel,columns:o.usedColumns}):r.activeBot.getPairCandles({pair:r.activeBot.plotPair,timeframe:e.timeframe,columns:o.usedColumns}))}return X(()=>e.availablePairs,()=>{e.availablePairs.find(m=>m===r.activeBot.plotPair)||([r.activeBot.plotPair]=e.availablePairs,g())}),X(()=>r.activeBot.selectedPair,()=>{r.activeBot.plotPair=r.activeBot.selectedPair}),X(()=>r.activeBot.plotPair,()=>{e.historicView||g()}),X(()=>o.plotConfig,()=>{const m=o.usedColumns.every(_=>u.value.includes(_));a.useReducedPairCalls&&!m&&(console.log("triggering refresh"),g())}),va(()=>{i.value=e.plotConfigModal,r.activeBot.selectedPair?r.activeBot.plotPair=r.activeBot.selectedPair:e.availablePairs.length>0&&([r.activeBot.plotPair]=e.availablePairs),o.plotConfigChanged(),c.value||g()}),(m,_)=>{const b=ho,y=xt,v=vo,S=go,x=Pr,w=mo,P=Vc,A=Ks,I=yo;return O(),$("div",Nc,[N("div",Rc,[N("div",zc,[N("div",$c,[N("span",Fc,he(C(l))+" | "+he(t.timeframe||""),1),M(C(Tr),{modelValue:C(r).activeBot.plotPair,"onUpdate:modelValue":_[0]||(_[0]=T=>C(r).activeBot.plotPair=T),class:"ms-md-2",options:t.availablePairs,style:{"min-width":"7em"},size:"sm",clearable:!1,onInput:g},null,8,["modelValue","options"]),M(y,{title:"Refresh chart",class:"ms-2",disabled:!C(r).activeBot.plotPair||C(d),size:"sm",onClick:g},{default:z(()=>[M(b)]),_:1},8,["disabled"]),C(d)?(O(),ee(v,{key:0,small:"",class:"ms-2",label:"Spinning"})):K("",!0),N("div",Wc,[N("div",Uc,[C(s)?(O(),$("small",Gc,"Long entries: "+he(C(s).enter_long_signals||C(s).buy_signals),1)):K("",!0),C(s)?(O(),$("small",Zc,"Long exit: "+he(C(s).exit_long_signals||C(s).sell_signals),1)):K("",!0)]),N("div",Hc,[C(s)&&C(s).enter_short_signals?(O(),$("small",jc,"Short entries: "+he(C(s).enter_short_signals),1)):K("",!0),C(s)&&C(s).exit_short_signals?(O(),$("small",Yc,"Short exits: "+he(C(s).exit_short_signals),1)):K("",!0)])])]),N("div",Kc,[M(S,{modelValue:C(a).useHeikinAshiCandles,"onUpdate:modelValue":_[1]||(_[1]=T=>C(a).useHeikinAshiCandles=T)},{default:z(()=>[qc]),_:1},8,["modelValue"]),N("div",Xc,[M(x)]),N("div",Jc,[M(y,{size:"sm",title:"Plot configurator",onClick:h},{default:z(()=>[M(w,{width:"12",height:"12"})]),_:1})])])]),N("div",Qc,[N("div",ed,[C(c)?(O(),ee(P,{key:0,dataset:C(s),trades:t.trades,"plot-config":C(o).plotConfig,"heikin-ashi":C(a).useHeikinAshiCandles,"use-u-t-c":C(a).timezone==="UTC",theme:C(a).chartTheme,"slider-position":t.sliderPosition,"color-up":C(n).colorUp,"color-down":C(n).colorDown},null,8,["dataset","trades","plot-config","heikin-ashi","use-u-t-c","theme","slider-position","color-up","color-down"])):(O(),$("div",td,[C(d)?(O(),ee(v,{key:0,label:"Spinning"})):(O(),$("div",ad,he(C(f)),1)),C(r).activeBot.historyTakesLonger?(O(),$("p",nd," This is taking longer than expected ... Hold on ... ")):K("",!0)]))]),M(Qn,{name:"fade"},{default:z(()=>[t.plotConfigModal?K("",!0):ht((O(),$("div",rd,[M(A,{columns:C(u),"is-visible":C(i)??!1},null,8,["columns","is-visible"])],512)),[[Xt,C(i)]])]),_:1})])]),t.plotConfigModal?(O(),ee(I,{key:0,id:"plotConfiguratorModal",modelValue:C(p),"onUpdate:modelValue":_[2]||(_[2]=T=>se(p)?p.value=T:null),title:"Plot Configurator","ok-only":"","hide-backdrop":""},{default:z(()=>[M(A,{"is-visible":C(p),columns:C(u)},null,8,["is-visible","columns"])]),_:1},8,["modelValue"])):K("",!0)])}}}),dd=_a(od,[["__scopeId","data-v-93ea43d8"]]);export{dd as _};
//# sourceMappingURL=CandleChartContainer-DXl2ryLC.js.map
