{"version": 3, "file": "PairListLive-CbIbYasi.js", "sources": ["../../src/components/ftbot/PairListLive.vue"], "sourcesContent": ["<template>\n  <div>\n    <div>\n      <h3>Whitelist Methods</h3>\n\n      <div v-if=\"botStore.activeBot.pairlistMethods.length\" class=\"list wide\">\n        <div\n          v-for=\"(method, key) in botStore.activeBot.pairlistMethods\"\n          :key=\"key\"\n          class=\"pair white align-middle border border-secondary\"\n        >\n          {{ method }}\n        </div>\n      </div>\n    </div>\n    <!-- Show Whitelist -->\n    <h3 :title=\"`${botStore.activeBot.whitelist.length} pairs`\">Whitelist</h3>\n    <div v-if=\"botStore.activeBot.whitelist.length\" class=\"list\">\n      <div\n        v-for=\"(pair, key) in botStore.activeBot.whitelist\"\n        :key=\"key\"\n        class=\"pair white align-middle border border-secondary text-small\"\n      >\n        {{ pair }}\n      </div>\n    </div>\n    <p v-else>List Unavailable. Please Login and make sure server is running.</p>\n    <hr />\n\n    <!-- Blacklsit -->\n    <div>\n      <label\n        class=\"me-auto h3\"\n        title=\"Blacklist - Select (followed by a click on '-') to remove pairs\"\n        >Blacklist</label\n      >\n      <div class=\"float-end d-flex d-flex-columns pe-1\">\n        <b-button\n          id=\"blacklist-add-btn\"\n          class=\"me-1\"\n          :class=\"botStore.activeBot.botApiVersion >= 1.12 ? 'col-6' : ''\"\n          size=\"sm\"\n          ><i-mdi-plus-box-outline />\n        </b-button>\n        <b-button\n          v-if=\"botStore.activeBot.botApiVersion >= 1.12\"\n          size=\"sm\"\n          class=\"col-6\"\n          title=\"Select pairs to delete pairs from your blacklist.\"\n          :disabled=\"blacklistSelect.length === 0\"\n          @click=\"deletePairs\"\n        >\n          <i-mdi-delete />\n        </b-button>\n      </div>\n      <BPopover\n        title=\"Add to blacklist\"\n        target=\"blacklist-add-btn\"\n        triggers=\"click\"\n        teleport-to=\"body\"\n        :show=\"blackListShow\"\n      >\n        <form ref=\"form\" @submit.prevent=\"addBlacklistPair\">\n          <div>\n            <b-form-group label-cols=\"2\" label=\"Pair\" label-for=\"pair-input\">\n              <b-form-input\n                id=\"pair-input\"\n                v-model=\"newblacklistpair\"\n                required\n                autofocus\n              ></b-form-input>\n            </b-form-group>\n            <b-button id=\"blacklist-submit\" class=\"float-end mb-2\" size=\"sm\" type=\"submit\">\n              Add\n            </b-button>\n          </div>\n        </form>\n      </BPopover>\n    </div>\n    <div v-if=\"botStore.activeBot.blacklist.length\" class=\"list\">\n      <div\n        v-for=\"(pair, key) in botStore.activeBot.blacklist\"\n        :key=\"key\"\n        class=\"pair black border border-secondary\"\n        :class=\"blacklistSelect.indexOf(key) > -1 ? 'active' : ''\"\n        @click=\"blacklistSelectClick(key)\"\n      >\n        <span class=\"check\"><i-mdi-check-circle /></span>{{ pair }}\n      </div>\n    </div>\n    <p v-else>BlackList Unavailable. Please Login and make sure server is running.</p>\n    <!-- Pagination -->\n    <!-- TODO Add pagination support -->\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst newblacklistpair = ref('');\nconst blackListShow = ref(false);\nconst blacklistSelect = ref<number[]>([]);\nconst botStore = useBotStore();\n\nconst initBlacklist = () => {\n  if (botStore.activeBot.whitelist.length === 0) {\n    botStore.activeBot.getWhitelist();\n  }\n  if (botStore.activeBot.blacklist.length === 0) {\n    botStore.activeBot.getBlacklist();\n  }\n};\n\nconst addBlacklistPair = () => {\n  if (newblacklistpair.value) {\n    blackListShow.value = false;\n\n    botStore.activeBot.addBlacklist({ blacklist: [newblacklistpair.value] });\n    newblacklistpair.value = '';\n  }\n};\n\nconst blacklistSelectClick = (key) => {\n  const index = blacklistSelect.value.indexOf(key);\n  if (index > -1) {\n    blacklistSelect.value.splice(index, 1);\n  } else {\n    blacklistSelect.value.push(key);\n  }\n};\n\nconst deletePairs = () => {\n  if (blacklistSelect.value.length === 0) {\n    console.log('nothing to delete');\n    return;\n  }\n  // const pairlist = blacklistSelect.value;\n  const pairlist = botStore.activeBot.blacklist.filter(\n    (value, index) => blacklistSelect.value.indexOf(index) > -1,\n  );\n  console.log('Deleting pairs: ', pairlist);\n  botStore.activeBot.deleteBlacklist(pairlist);\n  blacklistSelect.value = [];\n};\nonMounted(() => {\n  initBlacklist();\n});\n</script>\n\n<style scoped lang=\"scss\">\n.check {\n  // Hidden checkbox on blacklist selection\n  // background: white;\n  color: #41b883;\n  opacity: 0;\n  // border-radius: 50%;\n  z-index: 5;\n  width: 1.3em;\n  height: 1.3em;\n  top: -0.3em;\n  left: -0.3em;\n  position: absolute;\n  transition: opacity 0.2s;\n}\n\n.pair.active .check {\n  opacity: 1;\n}\n\n.list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));\n  grid-gap: 0.5rem;\n  padding-bottom: 1rem;\n}\n.wide {\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n}\n\n.pair {\n  background: #41b883;\n  padding: 0.5rem;\n  border-radius: 5px;\n  cursor: pointer;\n  position: relative;\n}\n\n.white {\n  background: white;\n  color: black;\n}\n\n.black {\n  background: black;\n  color: white;\n}\n</style>\n"], "names": ["newblacklistpair", "ref", "blackListShow", "blacklistSelect", "botStore", "useBotStore", "initBlacklist", "addBlacklistPair", "blacklistSelectClick", "key", "index", "deletePairs", "pairlist", "value", "onMounted"], "mappings": "oiCAmGM,MAAAA,EAAmBC,EAAI,EAAE,EACzBC,EAAgBD,EAAI,EAAK,EACzBE,EAAkBF,EAAc,CAAA,CAAE,EAClCG,EAAWC,IAEXC,EAAgB,IAAM,CACtBF,EAAS,UAAU,UAAU,SAAW,GAC1CA,EAAS,UAAU,eAEjBA,EAAS,UAAU,UAAU,SAAW,GAC1CA,EAAS,UAAU,cACrB,EAGIG,EAAmB,IAAM,CACzBP,EAAiB,QACnBE,EAAc,MAAQ,GAEbE,EAAA,UAAU,aAAa,CAAE,UAAW,CAACJ,EAAiB,KAAK,EAAG,EACvEA,EAAiB,MAAQ,GAC3B,EAGIQ,EAAwBC,GAAQ,CACpC,MAAMC,EAAQP,EAAgB,MAAM,QAAQM,CAAG,EAC3CC,EAAQ,GACMP,EAAA,MAAM,OAAOO,EAAO,CAAC,EAErBP,EAAA,MAAM,KAAKM,CAAG,CAChC,EAGIE,EAAc,IAAM,CACpB,GAAAR,EAAgB,MAAM,SAAW,EAAG,CACtC,QAAQ,IAAI,mBAAmB,EAC/B,MACF,CAEM,MAAAS,EAAWR,EAAS,UAAU,UAAU,OAC5C,CAACS,EAAOH,IAAUP,EAAgB,MAAM,QAAQO,CAAK,EAAI,EAAA,EAEnD,QAAA,IAAI,mBAAoBE,CAAQ,EAC/BR,EAAA,UAAU,gBAAgBQ,CAAQ,EAC3CT,EAAgB,MAAQ,EAAC,EAE3B,OAAAW,EAAU,IAAM,CACAR,GAAA,CACf"}