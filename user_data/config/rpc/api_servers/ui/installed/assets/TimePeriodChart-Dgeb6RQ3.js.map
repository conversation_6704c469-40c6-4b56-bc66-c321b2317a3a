{"version": 3, "file": "TimePeriodChart-Dgeb6RQ3.js", "sources": ["../../src/components/charts/TimePeriodChart.vue"], "sourcesContent": ["<template>\n  <ECharts\n    v-if=\"dailyStats.data\"\n    ref=\"dailyChart\"\n    :option=\"dailyChartOptions\"\n    :theme=\"settingsStore.chartTheme\"\n    :style=\"{ height: width * 0.6 + 'px' }\"\n    autoresize\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport ECharts from 'vue-echarts';\n// import { EChartsOption } from 'echarts';\n\nimport { use } from 'echarts/core';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport { LineChart, BarChart } from 'echarts/charts';\nimport {\n  DatasetComponent,\n  GridComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n  VisualMapComponent,\n} from 'echarts/components';\n\nimport { TimeSummaryReturnValue } from '@/types';\nimport { useSettingsStore } from '@/stores/settings';\nimport { EChartsOption } from 'echarts';\nimport { useElementSize } from '@vueuse/core';\nimport { useColorStore } from '@/stores/colors';\n\nuse([\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>hart,\n  <PERSON>vas<PERSON><PERSON><PERSON>,\n  GridComponent,\n  DatasetComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n  VisualMapComponent,\n]);\n\n// Define Column labels here to avoid typos\nconst CHART_ABS_PROFIT = 'Absolute profit';\nconst CHART_TRADE_COUNT = 'Trade Count';\n\nconst props = defineProps({\n  dailyStats: {\n    type: Object as () => TimeSummaryReturnValue,\n    required: true,\n  },\n  showTitle: {\n    type: Boolean,\n    default: true,\n  },\n});\n\nconst settingsStore = useSettingsStore();\nconst colorStore = useColorStore();\n\nconst dailyChart = ref(null);\nconst { width } = useElementSize(dailyChart);\n\nconst absoluteMin = computed(() =>\n  props.dailyStats.data.reduce(\n    (min, p) => (p.abs_profit < min ? p.abs_profit : min),\n    props.dailyStats.data[0]?.abs_profit,\n  ),\n);\nconst absoluteMax = computed(() =>\n  props.dailyStats.data.reduce(\n    (max, p) => (p.abs_profit > max ? p.abs_profit : max),\n    props.dailyStats.data[0]?.abs_profit,\n  ),\n);\nconst dailyChartOptions: ComputedRef<EChartsOption> = computed(() => {\n  return {\n    title: {\n      text: 'Daily profit',\n      show: props.showTitle,\n    },\n    backgroundColor: 'rgba(0, 0, 0, 0)',\n    dataset: {\n      dimensions: ['date', 'abs_profit', 'trade_count'],\n      source: props.dailyStats.data,\n    },\n    tooltip: {\n      trigger: 'axis',\n      axisPointer: {\n        type: 'line',\n        label: {\n          backgroundColor: '#6a7985',\n        },\n      },\n    },\n    legend: {\n      data: [CHART_ABS_PROFIT, CHART_TRADE_COUNT],\n      right: '5%',\n    },\n    xAxis: [\n      {\n        type: 'category',\n      },\n    ],\n    visualMap: [\n      {\n        dimension: 1,\n        seriesIndex: 0,\n        show: false,\n        pieces: [\n          {\n            max: 0.0,\n            min: absoluteMin.value,\n            color: colorStore.colorLoss,\n          },\n          {\n            min: 0.0,\n            max: absoluteMax.value,\n            color: colorStore.colorProfit,\n          },\n        ],\n      },\n    ],\n    yAxis: [\n      {\n        type: 'value',\n        name: CHART_ABS_PROFIT,\n        splitLine: {\n          show: false,\n        },\n        nameRotate: 90,\n        nameLocation: 'middle',\n        nameGap: 35,\n      },\n      {\n        type: 'value',\n        name: CHART_TRADE_COUNT,\n        nameRotate: 90,\n        nameLocation: 'middle',\n        nameGap: 30,\n      },\n    ],\n    series: [\n      {\n        type: 'line',\n        name: CHART_ABS_PROFIT,\n        // Color is induced by visualMap\n      },\n      {\n        type: 'bar',\n        name: CHART_TRADE_COUNT,\n        itemStyle: {\n          color: 'rgba(150,150,150,0.3)',\n        },\n        yAxisIndex: 1,\n      },\n    ],\n  };\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.echarts {\n  min-height: 240px;\n}\n</style>\n"], "names": ["CHART_ABS_PROFIT", "CHART_TRADE_COUNT", "use", "<PERSON><PERSON><PERSON>", "Line<PERSON>hart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GridComponent", "DatasetComponent", "LegendComponent", "TitleComponent", "TooltipComponent", "VisualMapComponent", "props", "__props", "settingsStore", "useSettingsStore", "colorStore", "useColorStore", "dailyChart", "ref", "width", "useElementSize", "absoluteMin", "computed", "min", "p", "_a", "absoluteMax", "max", "dailyChartOptions"], "mappings": "otBA8CMA,EAAmB,kBACnBC,EAAoB,sIAdtBC,EAAA,CACFC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,CAAA,CACD,EAMD,MAAMC,EAAQC,EAWRC,EAAgBC,IAChBC,EAAaC,IAEbC,EAAaC,EAAI,IAAI,EACrB,CAAE,MAAAC,CAAA,EAAUC,EAAeH,CAAU,EAErCI,EAAcC,EAAS,WAC3B,OAAAX,EAAM,WAAW,KAAK,OACpB,CAACY,EAAKC,IAAOA,EAAE,WAAaD,EAAMC,EAAE,WAAaD,GACjDE,EAAAd,EAAM,WAAW,KAAK,CAAC,IAAvB,YAAAc,EAA0B,UAC5B,EAAA,EAEIC,EAAcJ,EAAS,WAC3B,OAAAX,EAAM,WAAW,KAAK,OACpB,CAACgB,EAAKH,IAAOA,EAAE,WAAaG,EAAMH,EAAE,WAAaG,GACjDF,EAAAd,EAAM,WAAW,KAAK,CAAC,IAAvB,YAAAc,EAA0B,UAC5B,EAAA,EAEIG,EAAgDN,EAAS,KACtD,CACL,MAAO,CACL,KAAM,eACN,KAAMX,EAAM,SACd,EACA,gBAAiB,mBACjB,QAAS,CACP,WAAY,CAAC,OAAQ,aAAc,aAAa,EAChD,OAAQA,EAAM,WAAW,IAC3B,EACA,QAAS,CACP,QAAS,OACT,YAAa,CACX,KAAM,OACN,MAAO,CACL,gBAAiB,SACnB,CACF,CACF,EACA,OAAQ,CACN,KAAM,CAACZ,EAAkBC,CAAiB,EAC1C,MAAO,IACT,EACA,MAAO,CACL,CACE,KAAM,UACR,CACF,EACA,UAAW,CACT,CACE,UAAW,EACX,YAAa,EACb,KAAM,GACN,OAAQ,CACN,CACE,IAAK,EACL,IAAKqB,EAAY,MACjB,MAAON,EAAW,SACpB,EACA,CACE,IAAK,EACL,IAAKW,EAAY,MACjB,MAAOX,EAAW,WACpB,CACF,CACF,CACF,EACA,MAAO,CACL,CACE,KAAM,QACN,KAAMhB,EACN,UAAW,CACT,KAAM,EACR,EACA,WAAY,GACZ,aAAc,SACd,QAAS,EACX,EACA,CACE,KAAM,QACN,KAAMC,EACN,WAAY,GACZ,aAAc,SACd,QAAS,EACX,CACF,EACA,OAAQ,CACN,CACE,KAAM,OACN,KAAMD,CAER,EACA,CACE,KAAM,MACN,KAAMC,EACN,UAAW,CACT,MAAO,uBACT,EACA,WAAY,CACd,CACF,CAAA,EAEH"}