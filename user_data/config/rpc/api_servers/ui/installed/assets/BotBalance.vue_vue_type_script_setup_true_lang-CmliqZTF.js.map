{"version": 3, "file": "BotBalance.vue_vue_type_script_setup_true_lang-CmliqZTF.js", "sources": ["../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/label/labelGuideHelper.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/label/LabelManager.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/label/installLabelLayout.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/chart/pie/pieLayout.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/processor/dataFilter.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/chart/pie/labelLayout.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/chart/pie/PieView.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/visual/LegendVisualProvider.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/chart/pie/PieSeries.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/processor/negativeDataFilter.js", "../../node_modules/.pnpm/echarts@5.5.0/node_modules/echarts/lib/chart/pie/install.js", "../../src/components/charts/BalanceChart.vue", "../../src/components/ftbot/BotBalance.vue"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { Point, Path, Polyline } from '../util/graphic.js';\nimport PathProxy from 'zrender/lib/core/PathProxy.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nimport { cubicProjectPoint, quadraticProjectPoint } from 'zrender/lib/core/curve.js';\nimport { defaults, retrieve2 } from 'zrender/lib/core/util.js';\nimport { invert } from 'zrender/lib/core/matrix.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport { DISPLAY_STATES, SPECIAL_STATES } from '../util/states.js';\nvar PI2 = Math.PI * 2;\nvar CMD = PathProxy.CMD;\nvar DEFAULT_SEARCH_SPACE = ['top', 'right', 'bottom', 'left'];\nfunction getCandidateAnchor(pos, distance, rect, outPt, outDir) {\n  var width = rect.width;\n  var height = rect.height;\n  switch (pos) {\n    case 'top':\n      outPt.set(rect.x + width / 2, rect.y - distance);\n      outDir.set(0, -1);\n      break;\n    case 'bottom':\n      outPt.set(rect.x + width / 2, rect.y + height + distance);\n      outDir.set(0, 1);\n      break;\n    case 'left':\n      outPt.set(rect.x - distance, rect.y + height / 2);\n      outDir.set(-1, 0);\n      break;\n    case 'right':\n      outPt.set(rect.x + width + distance, rect.y + height / 2);\n      outDir.set(1, 0);\n      break;\n  }\n}\nfunction projectPointToArc(cx, cy, r, startAngle, endAngle, anticlockwise, x, y, out) {\n  x -= cx;\n  y -= cy;\n  var d = Math.sqrt(x * x + y * y);\n  x /= d;\n  y /= d;\n  // Intersect point.\n  var ox = x * r + cx;\n  var oy = y * r + cy;\n  if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n    // Is a circle\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n  if (anticlockwise) {\n    var tmp = startAngle;\n    startAngle = normalizeRadian(endAngle);\n    endAngle = normalizeRadian(tmp);\n  } else {\n    startAngle = normalizeRadian(startAngle);\n    endAngle = normalizeRadian(endAngle);\n  }\n  if (startAngle > endAngle) {\n    endAngle += PI2;\n  }\n  var angle = Math.atan2(y, x);\n  if (angle < 0) {\n    angle += PI2;\n  }\n  if (angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle) {\n    // Project point is on the arc.\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n  var x1 = r * Math.cos(startAngle) + cx;\n  var y1 = r * Math.sin(startAngle) + cy;\n  var x2 = r * Math.cos(endAngle) + cx;\n  var y2 = r * Math.sin(endAngle) + cy;\n  var d1 = (x1 - x) * (x1 - x) + (y1 - y) * (y1 - y);\n  var d2 = (x2 - x) * (x2 - x) + (y2 - y) * (y2 - y);\n  if (d1 < d2) {\n    out[0] = x1;\n    out[1] = y1;\n    return Math.sqrt(d1);\n  } else {\n    out[0] = x2;\n    out[1] = y2;\n    return Math.sqrt(d2);\n  }\n}\nfunction projectPointToLine(x1, y1, x2, y2, x, y, out, limitToEnds) {\n  var dx = x - x1;\n  var dy = y - y1;\n  var dx1 = x2 - x1;\n  var dy1 = y2 - y1;\n  var lineLen = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  dx1 /= lineLen;\n  dy1 /= lineLen;\n  // dot product\n  var projectedLen = dx * dx1 + dy * dy1;\n  var t = projectedLen / lineLen;\n  if (limitToEnds) {\n    t = Math.min(Math.max(t, 0), 1);\n  }\n  t *= lineLen;\n  var ox = out[0] = x1 + t * dx1;\n  var oy = out[1] = y1 + t * dy1;\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\nfunction projectPointToRect(x1, y1, width, height, x, y, out) {\n  if (width < 0) {\n    x1 = x1 + width;\n    width = -width;\n  }\n  if (height < 0) {\n    y1 = y1 + height;\n    height = -height;\n  }\n  var x2 = x1 + width;\n  var y2 = y1 + height;\n  var ox = out[0] = Math.min(Math.max(x, x1), x2);\n  var oy = out[1] = Math.min(Math.max(y, y1), y2);\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\nvar tmpPt = [];\nfunction nearestPointOnRect(pt, rect, out) {\n  var dist = projectPointToRect(rect.x, rect.y, rect.width, rect.height, pt.x, pt.y, tmpPt);\n  out.set(tmpPt[0], tmpPt[1]);\n  return dist;\n}\n/**\n * Calculate min distance corresponding point.\n * This method won't evaluate if point is in the path.\n */\nfunction nearestPointOnPath(pt, path, out) {\n  var xi = 0;\n  var yi = 0;\n  var x0 = 0;\n  var y0 = 0;\n  var x1;\n  var y1;\n  var minDist = Infinity;\n  var data = path.data;\n  var x = pt.x;\n  var y = pt.y;\n  for (var i = 0; i < data.length;) {\n    var cmd = data[i++];\n    if (i === 1) {\n      xi = data[i];\n      yi = data[i + 1];\n      x0 = xi;\n      y0 = yi;\n    }\n    var d = minDist;\n    switch (cmd) {\n      case CMD.M:\n        // moveTo 命令重新创建一个新的 subpath, 并且更新新的起点\n        // 在 closePath 的时候使用\n        x0 = data[i++];\n        y0 = data[i++];\n        xi = x0;\n        yi = y0;\n        break;\n      case CMD.L:\n        d = projectPointToLine(xi, yi, data[i], data[i + 1], x, y, tmpPt, true);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.C:\n        d = cubicProjectPoint(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.Q:\n        d = quadraticProjectPoint(xi, yi, data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.A:\n        // TODO Arc 判断的开销比较大\n        var cx = data[i++];\n        var cy = data[i++];\n        var rx = data[i++];\n        var ry = data[i++];\n        var theta = data[i++];\n        var dTheta = data[i++];\n        // TODO Arc 旋转\n        i += 1;\n        var anticlockwise = !!(1 - data[i++]);\n        x1 = Math.cos(theta) * rx + cx;\n        y1 = Math.sin(theta) * ry + cy;\n        // 不是直接使用 arc 命令\n        if (i <= 1) {\n          // 第一个命令起点还未定义\n          x0 = x1;\n          y0 = y1;\n        }\n        // zr 使用scale来模拟椭圆, 这里也对x做一定的缩放\n        var _x = (x - cx) * ry / rx + cx;\n        d = projectPointToArc(cx, cy, ry, theta, theta + dTheta, anticlockwise, _x, y, tmpPt);\n        xi = Math.cos(theta + dTheta) * rx + cx;\n        yi = Math.sin(theta + dTheta) * ry + cy;\n        break;\n      case CMD.R:\n        x0 = xi = data[i++];\n        y0 = yi = data[i++];\n        var width = data[i++];\n        var height = data[i++];\n        d = projectPointToRect(x0, y0, width, height, x, y, tmpPt);\n        break;\n      case CMD.Z:\n        d = projectPointToLine(xi, yi, x0, y0, x, y, tmpPt, true);\n        xi = x0;\n        yi = y0;\n        break;\n    }\n    if (d < minDist) {\n      minDist = d;\n      out.set(tmpPt[0], tmpPt[1]);\n    }\n  }\n  return minDist;\n}\n// Temporal variable for intermediate usage.\nvar pt0 = new Point();\nvar pt1 = new Point();\nvar pt2 = new Point();\nvar dir = new Point();\nvar dir2 = new Point();\n/**\n * Calculate a proper guide line based on the label position and graphic element definition\n * @param label\n * @param labelRect\n * @param target\n * @param targetRect\n */\nexport function updateLabelLinePoints(target, labelLineModel) {\n  if (!target) {\n    return;\n  }\n  var labelLine = target.getTextGuideLine();\n  var label = target.getTextContent();\n  // Needs to create text guide in each charts.\n  if (!(label && labelLine)) {\n    return;\n  }\n  var labelGuideConfig = target.textGuideLineConfig || {};\n  var points = [[0, 0], [0, 0], [0, 0]];\n  var searchSpace = labelGuideConfig.candidates || DEFAULT_SEARCH_SPACE;\n  var labelRect = label.getBoundingRect().clone();\n  labelRect.applyTransform(label.getComputedTransform());\n  var minDist = Infinity;\n  var anchorPoint = labelGuideConfig.anchor;\n  var targetTransform = target.getComputedTransform();\n  var targetInversedTransform = targetTransform && invert([], targetTransform);\n  var len = labelLineModel.get('length2') || 0;\n  if (anchorPoint) {\n    pt2.copy(anchorPoint);\n  }\n  for (var i = 0; i < searchSpace.length; i++) {\n    var candidate = searchSpace[i];\n    getCandidateAnchor(candidate, 0, labelRect, pt0, dir);\n    Point.scaleAndAdd(pt1, pt0, dir, len);\n    // Transform to target coord space.\n    pt1.transform(targetInversedTransform);\n    // Note: getBoundingRect will ensure the `path` being created.\n    var boundingRect = target.getBoundingRect();\n    var dist = anchorPoint ? anchorPoint.distance(pt1) : target instanceof Path ? nearestPointOnPath(pt1, target.path, pt2) : nearestPointOnRect(pt1, boundingRect, pt2);\n    // TODO pt2 is in the path\n    if (dist < minDist) {\n      minDist = dist;\n      // Transform back to global space.\n      pt1.transform(targetTransform);\n      pt2.transform(targetTransform);\n      pt2.toArray(points[0]);\n      pt1.toArray(points[1]);\n      pt0.toArray(points[2]);\n    }\n  }\n  limitTurnAngle(points, labelLineModel.get('minTurnAngle'));\n  labelLine.setShape({\n    points: points\n  });\n}\n// Temporal variable for the limitTurnAngle function\nvar tmpArr = [];\nvar tmpProjPoint = new Point();\n/**\n * Reduce the line segment attached to the label to limit the turn angle between two segments.\n * @param linePoints\n * @param minTurnAngle Radian of minimum turn angle. 0 - 180\n */\nexport function limitTurnAngle(linePoints, minTurnAngle) {\n  if (!(minTurnAngle <= 180 && minTurnAngle > 0)) {\n    return;\n  }\n  minTurnAngle = minTurnAngle / 180 * Math.PI;\n  // The line points can be\n  //      /pt1----pt2 (label)\n  //     /\n  // pt0/\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt0, pt1);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(dir2);\n  var minTurnAngleCos = Math.cos(minTurnAngle);\n  if (minTurnAngleCos < angleCos) {\n    // Smaller than minTurnAngle\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr);\n    // Calculate new projected length with limited minTurnAngle and get the new connect point\n    tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI - minTurnAngle));\n    // Limit the new calculated connect point between pt1 and pt2.\n    var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n    if (isNaN(t)) {\n      return;\n    }\n    if (t < 0) {\n      Point.copy(tmpProjPoint, pt1);\n    } else if (t > 1) {\n      Point.copy(tmpProjPoint, pt2);\n    }\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\n/**\n * Limit the angle of line and the surface\n * @param maxSurfaceAngle Radian of minimum turn angle. 0 - 180. 0 is same direction to normal. 180 is opposite\n */\nexport function limitSurfaceAngle(linePoints, surfaceNormal, maxSurfaceAngle) {\n  if (!(maxSurfaceAngle <= 180 && maxSurfaceAngle > 0)) {\n    return;\n  }\n  maxSurfaceAngle = maxSurfaceAngle / 180 * Math.PI;\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt1, pt0);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(surfaceNormal);\n  var maxSurfaceAngleCos = Math.cos(maxSurfaceAngle);\n  if (angleCos < maxSurfaceAngleCos) {\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr);\n    var HALF_PI = Math.PI / 2;\n    var angle2 = Math.acos(dir2.dot(surfaceNormal));\n    var newAngle = HALF_PI + angle2 - maxSurfaceAngle;\n    if (newAngle >= HALF_PI) {\n      // parallel\n      Point.copy(tmpProjPoint, pt2);\n    } else {\n      // Calculate new projected length with limited minTurnAngle and get the new connect point\n      tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI / 2 - newAngle));\n      // Limit the new calculated connect point between pt1 and pt2.\n      var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n      if (isNaN(t)) {\n        return;\n      }\n      if (t < 0) {\n        Point.copy(tmpProjPoint, pt1);\n      } else if (t > 1) {\n        Point.copy(tmpProjPoint, pt2);\n      }\n    }\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\nfunction setLabelLineState(labelLine, ignore, stateName, stateModel) {\n  var isNormal = stateName === 'normal';\n  var stateObj = isNormal ? labelLine : labelLine.ensureState(stateName);\n  // Make sure display.\n  stateObj.ignore = ignore;\n  // Set smooth\n  var smooth = stateModel.get('smooth');\n  if (smooth && smooth === true) {\n    smooth = 0.3;\n  }\n  stateObj.shape = stateObj.shape || {};\n  if (smooth > 0) {\n    stateObj.shape.smooth = smooth;\n  }\n  var styleObj = stateModel.getModel('lineStyle').getLineStyle();\n  isNormal ? labelLine.useStyle(styleObj) : stateObj.style = styleObj;\n}\nfunction buildLabelLinePath(path, shape) {\n  var smooth = shape.smooth;\n  var points = shape.points;\n  if (!points) {\n    return;\n  }\n  path.moveTo(points[0][0], points[0][1]);\n  if (smooth > 0 && points.length >= 3) {\n    var len1 = vector.dist(points[0], points[1]);\n    var len2 = vector.dist(points[1], points[2]);\n    if (!len1 || !len2) {\n      path.lineTo(points[1][0], points[1][1]);\n      path.lineTo(points[2][0], points[2][1]);\n      return;\n    }\n    var moveLen = Math.min(len1, len2) * smooth;\n    var midPoint0 = vector.lerp([], points[1], points[0], moveLen / len1);\n    var midPoint2 = vector.lerp([], points[1], points[2], moveLen / len2);\n    var midPoint1 = vector.lerp([], midPoint0, midPoint2, 0.5);\n    path.bezierCurveTo(midPoint0[0], midPoint0[1], midPoint0[0], midPoint0[1], midPoint1[0], midPoint1[1]);\n    path.bezierCurveTo(midPoint2[0], midPoint2[1], midPoint2[0], midPoint2[1], points[2][0], points[2][1]);\n  } else {\n    for (var i = 1; i < points.length; i++) {\n      path.lineTo(points[i][0], points[i][1]);\n    }\n  }\n}\n/**\n * Create a label line if necessary and set it's style.\n */\nexport function setLabelLineStyle(targetEl, statesModels, defaultStyle) {\n  var labelLine = targetEl.getTextGuideLine();\n  var label = targetEl.getTextContent();\n  if (!label) {\n    // Not show label line if there is no label.\n    if (labelLine) {\n      targetEl.removeTextGuideLine();\n    }\n    return;\n  }\n  var normalModel = statesModels.normal;\n  var showNormal = normalModel.get('show');\n  var labelIgnoreNormal = label.ignore;\n  for (var i = 0; i < DISPLAY_STATES.length; i++) {\n    var stateName = DISPLAY_STATES[i];\n    var stateModel = statesModels[stateName];\n    var isNormal = stateName === 'normal';\n    if (stateModel) {\n      var stateShow = stateModel.get('show');\n      var isLabelIgnored = isNormal ? labelIgnoreNormal : retrieve2(label.states[stateName] && label.states[stateName].ignore, labelIgnoreNormal);\n      if (isLabelIgnored // Not show when label is not shown in this state.\n      || !retrieve2(stateShow, showNormal) // Use normal state by default if not set.\n      ) {\n        var stateObj = isNormal ? labelLine : labelLine && labelLine.states[stateName];\n        if (stateObj) {\n          stateObj.ignore = true;\n        }\n        if (!!labelLine) {\n          setLabelLineState(labelLine, true, stateName, stateModel);\n        }\n        continue;\n      }\n      // Create labelLine if not exists\n      if (!labelLine) {\n        labelLine = new Polyline();\n        targetEl.setTextGuideLine(labelLine);\n        // Reset state of normal because it's new created.\n        // NOTE: NORMAL should always been the first!\n        if (!isNormal && (labelIgnoreNormal || !showNormal)) {\n          setLabelLineState(labelLine, true, 'normal', statesModels.normal);\n        }\n        // Use same state proxy.\n        if (targetEl.stateProxy) {\n          labelLine.stateProxy = targetEl.stateProxy;\n        }\n      }\n      setLabelLineState(labelLine, false, stateName, stateModel);\n    }\n  }\n  if (labelLine) {\n    defaults(labelLine.style, defaultStyle);\n    // Not fill.\n    labelLine.style.fill = null;\n    var showAbove = normalModel.get('showAbove');\n    var labelLineConfig = targetEl.textGuideLineConfig = targetEl.textGuideLineConfig || {};\n    labelLineConfig.showAbove = showAbove || false;\n    // Custom the buildPath.\n    labelLine.buildPath = buildLabelLinePath;\n  }\n}\nexport function getLabelLineStatesModels(itemModel, labelLineName) {\n  labelLineName = labelLineName || 'labelLine';\n  var statesModels = {\n    normal: itemModel.getModel(labelLineName)\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    statesModels[stateName] = itemModel.getModel([stateName, labelLineName]);\n  }\n  return statesModels;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// TODO: move labels out of viewport.\nimport { BoundingRect, updateProps, initProps, isElementRemoved } from '../util/graphic.js';\nimport { getECData } from '../util/innerStore.js';\nimport { parsePercent } from '../util/number.js';\nimport Transformable from 'zrender/lib/core/Transformable.js';\nimport { updateLabelLinePoints, setLabelLineStyle, getLabelLineStatesModels } from './labelGuideHelper.js';\nimport { makeInner } from '../util/model.js';\nimport { retrieve2, each, keys, isFunction, filter, indexOf } from 'zrender/lib/core/util.js';\nimport { prepareLayoutList, hideOverlap, shiftLayoutOnX, shiftLayoutOnY } from './labelLayoutHelper.js';\nimport { labelInner, animateLabelValue } from './labelStyle.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nfunction cloneArr(points) {\n  if (points) {\n    var newPoints = [];\n    for (var i = 0; i < points.length; i++) {\n      newPoints.push(points[i].slice());\n    }\n    return newPoints;\n  }\n}\nfunction prepareLayoutCallbackParams(labelItem, hostEl) {\n  var label = labelItem.label;\n  var labelLine = hostEl && hostEl.getTextGuideLine();\n  return {\n    dataIndex: labelItem.dataIndex,\n    dataType: labelItem.dataType,\n    seriesIndex: labelItem.seriesModel.seriesIndex,\n    text: labelItem.label.style.text,\n    rect: labelItem.hostRect,\n    labelRect: labelItem.rect,\n    // x: labelAttr.x,\n    // y: labelAttr.y,\n    align: label.style.align,\n    verticalAlign: label.style.verticalAlign,\n    labelLinePoints: cloneArr(labelLine && labelLine.shape.points)\n  };\n}\nvar LABEL_OPTION_TO_STYLE_KEYS = ['align', 'verticalAlign', 'width', 'height', 'fontSize'];\nvar dummyTransformable = new Transformable();\nvar labelLayoutInnerStore = makeInner();\nvar labelLineAnimationStore = makeInner();\nfunction extendWithKeys(target, source, keys) {\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (source[key] != null) {\n      target[key] = source[key];\n    }\n  }\n}\nvar LABEL_LAYOUT_PROPS = ['x', 'y', 'rotation'];\nvar LabelManager = /** @class */function () {\n  function LabelManager() {\n    this._labelList = [];\n    this._chartViewList = [];\n  }\n  LabelManager.prototype.clearLabels = function () {\n    this._labelList = [];\n    this._chartViewList = [];\n  };\n  /**\n   * Add label to manager\n   */\n  LabelManager.prototype._addLabel = function (dataIndex, dataType, seriesModel, label, layoutOption) {\n    var labelStyle = label.style;\n    var hostEl = label.__hostTarget;\n    var textConfig = hostEl.textConfig || {};\n    // TODO: If label is in other state.\n    var labelTransform = label.getComputedTransform();\n    var labelRect = label.getBoundingRect().plain();\n    BoundingRect.applyTransform(labelRect, labelRect, labelTransform);\n    if (labelTransform) {\n      dummyTransformable.setLocalTransform(labelTransform);\n    } else {\n      // Identity transform.\n      dummyTransformable.x = dummyTransformable.y = dummyTransformable.rotation = dummyTransformable.originX = dummyTransformable.originY = 0;\n      dummyTransformable.scaleX = dummyTransformable.scaleY = 1;\n    }\n    dummyTransformable.rotation = normalizeRadian(dummyTransformable.rotation);\n    var host = label.__hostTarget;\n    var hostRect;\n    if (host) {\n      hostRect = host.getBoundingRect().plain();\n      var transform = host.getComputedTransform();\n      BoundingRect.applyTransform(hostRect, hostRect, transform);\n    }\n    var labelGuide = hostRect && host.getTextGuideLine();\n    this._labelList.push({\n      label: label,\n      labelLine: labelGuide,\n      seriesModel: seriesModel,\n      dataIndex: dataIndex,\n      dataType: dataType,\n      layoutOption: layoutOption,\n      computedLayoutOption: null,\n      rect: labelRect,\n      hostRect: hostRect,\n      // Label with lower priority will be hidden when overlapped\n      // Use rect size as default priority\n      priority: hostRect ? hostRect.width * hostRect.height : 0,\n      // Save default label attributes.\n      // For restore if developers want get back to default value in callback.\n      defaultAttr: {\n        ignore: label.ignore,\n        labelGuideIgnore: labelGuide && labelGuide.ignore,\n        x: dummyTransformable.x,\n        y: dummyTransformable.y,\n        scaleX: dummyTransformable.scaleX,\n        scaleY: dummyTransformable.scaleY,\n        rotation: dummyTransformable.rotation,\n        style: {\n          x: labelStyle.x,\n          y: labelStyle.y,\n          align: labelStyle.align,\n          verticalAlign: labelStyle.verticalAlign,\n          width: labelStyle.width,\n          height: labelStyle.height,\n          fontSize: labelStyle.fontSize\n        },\n        cursor: label.cursor,\n        attachedPos: textConfig.position,\n        attachedRot: textConfig.rotation\n      }\n    });\n  };\n  LabelManager.prototype.addLabelsOfSeries = function (chartView) {\n    var _this = this;\n    this._chartViewList.push(chartView);\n    var seriesModel = chartView.__model;\n    var layoutOption = seriesModel.get('labelLayout');\n    /**\n     * Ignore layouting if it's not specified anything.\n     */\n    if (!(isFunction(layoutOption) || keys(layoutOption).length)) {\n      return;\n    }\n    chartView.group.traverse(function (child) {\n      if (child.ignore) {\n        return true; // Stop traverse descendants.\n      }\n      // Only support label being hosted on graphic elements.\n      var textEl = child.getTextContent();\n      var ecData = getECData(child);\n      // Can only attach the text on the element with dataIndex\n      if (textEl && !textEl.disableLabelLayout) {\n        _this._addLabel(ecData.dataIndex, ecData.dataType, seriesModel, textEl, layoutOption);\n      }\n    });\n  };\n  LabelManager.prototype.updateLayoutConfig = function (api) {\n    var width = api.getWidth();\n    var height = api.getHeight();\n    function createDragHandler(el, labelLineModel) {\n      return function () {\n        updateLabelLinePoints(el, labelLineModel);\n      };\n    }\n    for (var i = 0; i < this._labelList.length; i++) {\n      var labelItem = this._labelList[i];\n      var label = labelItem.label;\n      var hostEl = label.__hostTarget;\n      var defaultLabelAttr = labelItem.defaultAttr;\n      var layoutOption = void 0;\n      // TODO A global layout option?\n      if (isFunction(labelItem.layoutOption)) {\n        layoutOption = labelItem.layoutOption(prepareLayoutCallbackParams(labelItem, hostEl));\n      } else {\n        layoutOption = labelItem.layoutOption;\n      }\n      layoutOption = layoutOption || {};\n      labelItem.computedLayoutOption = layoutOption;\n      var degreeToRadian = Math.PI / 180;\n      // TODO hostEl should always exists.\n      // Or label should not have parent because the x, y is all in global space.\n      if (hostEl) {\n        hostEl.setTextConfig({\n          // Force to set local false.\n          local: false,\n          // Ignore position and rotation config on the host el if x or y is changed.\n          position: layoutOption.x != null || layoutOption.y != null ? null : defaultLabelAttr.attachedPos,\n          // Ignore rotation config on the host el if rotation is changed.\n          rotation: layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.attachedRot,\n          offset: [layoutOption.dx || 0, layoutOption.dy || 0]\n        });\n      }\n      var needsUpdateLabelLine = false;\n      if (layoutOption.x != null) {\n        // TODO width of chart view.\n        label.x = parsePercent(layoutOption.x, width);\n        label.setStyle('x', 0); // Ignore movement in style. TODO: origin.\n        needsUpdateLabelLine = true;\n      } else {\n        label.x = defaultLabelAttr.x;\n        label.setStyle('x', defaultLabelAttr.style.x);\n      }\n      if (layoutOption.y != null) {\n        // TODO height of chart view.\n        label.y = parsePercent(layoutOption.y, height);\n        label.setStyle('y', 0); // Ignore movement in style.\n        needsUpdateLabelLine = true;\n      } else {\n        label.y = defaultLabelAttr.y;\n        label.setStyle('y', defaultLabelAttr.style.y);\n      }\n      if (layoutOption.labelLinePoints) {\n        var guideLine = hostEl.getTextGuideLine();\n        if (guideLine) {\n          guideLine.setShape({\n            points: layoutOption.labelLinePoints\n          });\n          // Not update\n          needsUpdateLabelLine = false;\n        }\n      }\n      var labelLayoutStore = labelLayoutInnerStore(label);\n      labelLayoutStore.needsUpdateLabelLine = needsUpdateLabelLine;\n      label.rotation = layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.rotation;\n      label.scaleX = defaultLabelAttr.scaleX;\n      label.scaleY = defaultLabelAttr.scaleY;\n      for (var k = 0; k < LABEL_OPTION_TO_STYLE_KEYS.length; k++) {\n        var key = LABEL_OPTION_TO_STYLE_KEYS[k];\n        label.setStyle(key, layoutOption[key] != null ? layoutOption[key] : defaultLabelAttr.style[key]);\n      }\n      if (layoutOption.draggable) {\n        label.draggable = true;\n        label.cursor = 'move';\n        if (hostEl) {\n          var hostModel = labelItem.seriesModel;\n          if (labelItem.dataIndex != null) {\n            var data = labelItem.seriesModel.getData(labelItem.dataType);\n            hostModel = data.getItemModel(labelItem.dataIndex);\n          }\n          label.on('drag', createDragHandler(hostEl, hostModel.getModel('labelLine')));\n        }\n      } else {\n        // TODO Other drag functions?\n        label.off('drag');\n        label.cursor = defaultLabelAttr.cursor;\n      }\n    }\n  };\n  LabelManager.prototype.layout = function (api) {\n    var width = api.getWidth();\n    var height = api.getHeight();\n    var labelList = prepareLayoutList(this._labelList);\n    var labelsNeedsAdjustOnX = filter(labelList, function (item) {\n      return item.layoutOption.moveOverlap === 'shiftX';\n    });\n    var labelsNeedsAdjustOnY = filter(labelList, function (item) {\n      return item.layoutOption.moveOverlap === 'shiftY';\n    });\n    shiftLayoutOnX(labelsNeedsAdjustOnX, 0, width);\n    shiftLayoutOnY(labelsNeedsAdjustOnY, 0, height);\n    var labelsNeedsHideOverlap = filter(labelList, function (item) {\n      return item.layoutOption.hideOverlap;\n    });\n    hideOverlap(labelsNeedsHideOverlap);\n  };\n  /**\n   * Process all labels. Not only labels with layoutOption.\n   */\n  LabelManager.prototype.processLabelsOverall = function () {\n    var _this = this;\n    each(this._chartViewList, function (chartView) {\n      var seriesModel = chartView.__model;\n      var ignoreLabelLineUpdate = chartView.ignoreLabelLineUpdate;\n      var animationEnabled = seriesModel.isAnimationEnabled();\n      chartView.group.traverse(function (child) {\n        if (child.ignore && !child.forceLabelAnimation) {\n          return true; // Stop traverse descendants.\n        }\n\n        var needsUpdateLabelLine = !ignoreLabelLineUpdate;\n        var label = child.getTextContent();\n        if (!needsUpdateLabelLine && label) {\n          needsUpdateLabelLine = labelLayoutInnerStore(label).needsUpdateLabelLine;\n        }\n        if (needsUpdateLabelLine) {\n          _this._updateLabelLine(child, seriesModel);\n        }\n        if (animationEnabled) {\n          _this._animateLabels(child, seriesModel);\n        }\n      });\n    });\n  };\n  LabelManager.prototype._updateLabelLine = function (el, seriesModel) {\n    // Only support label being hosted on graphic elements.\n    var textEl = el.getTextContent();\n    // Update label line style.\n    var ecData = getECData(el);\n    var dataIndex = ecData.dataIndex;\n    // Only support labelLine on the labels represent data.\n    if (textEl && dataIndex != null) {\n      var data = seriesModel.getData(ecData.dataType);\n      var itemModel = data.getItemModel(dataIndex);\n      var defaultStyle = {};\n      var visualStyle = data.getItemVisual(dataIndex, 'style');\n      if (visualStyle) {\n        var visualType = data.getVisual('drawType');\n        // Default to be same with main color\n        defaultStyle.stroke = visualStyle[visualType];\n      }\n      var labelLineModel = itemModel.getModel('labelLine');\n      setLabelLineStyle(el, getLabelLineStatesModels(itemModel), defaultStyle);\n      updateLabelLinePoints(el, labelLineModel);\n    }\n  };\n  LabelManager.prototype._animateLabels = function (el, seriesModel) {\n    var textEl = el.getTextContent();\n    var guideLine = el.getTextGuideLine();\n    // Animate\n    if (textEl\n    // `forceLabelAnimation` has the highest priority\n    && (el.forceLabelAnimation || !textEl.ignore && !textEl.invisible && !el.disableLabelAnimation && !isElementRemoved(el))) {\n      var layoutStore = labelLayoutInnerStore(textEl);\n      var oldLayout = layoutStore.oldLayout;\n      var ecData = getECData(el);\n      var dataIndex = ecData.dataIndex;\n      var newProps = {\n        x: textEl.x,\n        y: textEl.y,\n        rotation: textEl.rotation\n      };\n      var data = seriesModel.getData(ecData.dataType);\n      if (!oldLayout) {\n        textEl.attr(newProps);\n        // Disable fade in animation if value animation is enabled.\n        if (!labelInner(textEl).valueAnimation) {\n          var oldOpacity = retrieve2(textEl.style.opacity, 1);\n          // Fade in animation\n          textEl.style.opacity = 0;\n          initProps(textEl, {\n            style: {\n              opacity: oldOpacity\n            }\n          }, seriesModel, dataIndex);\n        }\n      } else {\n        textEl.attr(oldLayout);\n        // Make sure the animation from is in the right status.\n        var prevStates = el.prevStates;\n        if (prevStates) {\n          if (indexOf(prevStates, 'select') >= 0) {\n            textEl.attr(layoutStore.oldLayoutSelect);\n          }\n          if (indexOf(prevStates, 'emphasis') >= 0) {\n            textEl.attr(layoutStore.oldLayoutEmphasis);\n          }\n        }\n        updateProps(textEl, newProps, seriesModel, dataIndex);\n      }\n      layoutStore.oldLayout = newProps;\n      if (textEl.states.select) {\n        var layoutSelect = layoutStore.oldLayoutSelect = {};\n        extendWithKeys(layoutSelect, newProps, LABEL_LAYOUT_PROPS);\n        extendWithKeys(layoutSelect, textEl.states.select, LABEL_LAYOUT_PROPS);\n      }\n      if (textEl.states.emphasis) {\n        var layoutEmphasis = layoutStore.oldLayoutEmphasis = {};\n        extendWithKeys(layoutEmphasis, newProps, LABEL_LAYOUT_PROPS);\n        extendWithKeys(layoutEmphasis, textEl.states.emphasis, LABEL_LAYOUT_PROPS);\n      }\n      animateLabelValue(textEl, dataIndex, data, seriesModel, seriesModel);\n    }\n    if (guideLine && !guideLine.ignore && !guideLine.invisible) {\n      var layoutStore = labelLineAnimationStore(guideLine);\n      var oldLayout = layoutStore.oldLayout;\n      var newLayout = {\n        points: guideLine.shape.points\n      };\n      if (!oldLayout) {\n        guideLine.setShape(newLayout);\n        guideLine.style.strokePercent = 0;\n        initProps(guideLine, {\n          style: {\n            strokePercent: 1\n          }\n        }, seriesModel);\n      } else {\n        guideLine.attr({\n          shape: oldLayout\n        });\n        updateProps(guideLine, {\n          shape: newLayout\n        }, seriesModel);\n      }\n      layoutStore.oldLayout = newLayout;\n    }\n  };\n  return LabelManager;\n}();\nexport default LabelManager;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { makeInner } from '../util/model.js';\nimport LabelManager from './LabelManager.js';\nvar getLabelManager = makeInner();\nexport function installLabelLayout(registers) {\n  registers.registerUpdateLifecycle('series:beforeupdate', function (ecModel, api, params) {\n    // TODO api provide an namespace that can save stuff per instance\n    var labelManager = getLabelManager(api).labelManager;\n    if (!labelManager) {\n      labelManager = getLabelManager(api).labelManager = new LabelManager();\n    }\n    labelManager.clearLabels();\n  });\n  registers.registerUpdateLifecycle('series:layoutlabels', function (ecModel, api, params) {\n    var labelManager = getLabelManager(api).labelManager;\n    params.updatedSeries.forEach(function (series) {\n      labelManager.addLabelsOfSeries(api.getViewOfSeriesModel(series));\n    });\n    labelManager.updateLayoutConfig(api);\n    labelManager.layout(api);\n    labelManager.processLabelsOverall();\n  });\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { parsePercent, linearMap } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { normalizeArcAngles } from 'zrender/lib/core/PathProxy.js';\nvar PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nexport function getBasicPieLayout(seriesModel, api) {\n  var viewRect = getViewRect(seriesModel, api);\n  // center can be string or number when coordinateSystem is specified\n  var center = seriesModel.get('center');\n  var radius = seriesModel.get('radius');\n  if (!zrUtil.isArray(radius)) {\n    radius = [0, radius];\n  }\n  var width = parsePercent(viewRect.width, api.getWidth());\n  var height = parsePercent(viewRect.height, api.getHeight());\n  var size = Math.min(width, height);\n  var r0 = parsePercent(radius[0], size / 2);\n  var r = parsePercent(radius[1], size / 2);\n  var cx;\n  var cy;\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys) {\n    // percentage is not allowed when coordinate system is specified\n    var point = coordSys.dataToPoint(center);\n    cx = point[0] || 0;\n    cy = point[1] || 0;\n  } else {\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n    cx = parsePercent(center[0], width) + viewRect.x;\n    cy = parsePercent(center[1], height) + viewRect.y;\n  }\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r\n  };\n}\nexport default function pieLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var viewRect = getViewRect(seriesModel, api);\n    var _a = getBasicPieLayout(seriesModel, api),\n      cx = _a.cx,\n      cy = _a.cy,\n      r = _a.r,\n      r0 = _a.r0;\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var endAngle = seriesModel.get('endAngle');\n    var padAngle = seriesModel.get('padAngle') * RADIAN;\n    endAngle = endAngle === 'auto' ? startAngle - PI2 : -endAngle * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var minAndPadAngle = minAngle + padAngle;\n    var validDataCount = 0;\n    data.each(valueDim, function (value) {\n      !isNaN(value) && validDataCount++;\n    });\n    var sum = data.getSum(valueDim);\n    // Sum may be 0\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var clockwise = seriesModel.get('clockwise');\n    var roseType = seriesModel.get('roseType');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum');\n    // [0...max]\n    var extent = data.getDataExtent(valueDim);\n    extent[0] = 0;\n    var dir = clockwise ? 1 : -1;\n    var angles = [startAngle, endAngle];\n    var halfPadAngle = dir * padAngle / 2;\n    normalizeArcAngles(angles, !clockwise);\n    startAngle = angles[0], endAngle = angles[1];\n    var angleRange = Math.abs(endAngle - startAngle);\n    // In the case some sector angle is smaller than minAngle\n    var restAngle = angleRange;\n    var valueSumLargerThanMinAngle = 0;\n    var currentAngle = startAngle;\n    data.setLayout({\n      viewRect: viewRect,\n      r: r\n    });\n    data.each(valueDim, function (value, idx) {\n      var angle;\n      if (isNaN(value)) {\n        data.setItemLayout(idx, {\n          angle: NaN,\n          startAngle: NaN,\n          endAngle: NaN,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: r0,\n          r: roseType ? NaN : r\n        });\n        return;\n      }\n      // FIXME 兼容 2.0 但是 roseType 是 area 的时候才是这样？\n      if (roseType !== 'area') {\n        angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n      } else {\n        angle = angleRange / validDataCount;\n      }\n      if (angle < minAndPadAngle) {\n        angle = minAndPadAngle;\n        restAngle -= minAndPadAngle;\n      } else {\n        valueSumLargerThanMinAngle += value;\n      }\n      var endAngle = currentAngle + dir * angle;\n      // calculate display angle\n      var actualStartAngle = 0;\n      var actualEndAngle = 0;\n      if (padAngle > angle) {\n        actualStartAngle = currentAngle + dir * angle / 2;\n        actualEndAngle = actualStartAngle;\n      } else {\n        actualStartAngle = currentAngle + halfPadAngle;\n        actualEndAngle = endAngle - halfPadAngle;\n      }\n      data.setItemLayout(idx, {\n        angle: angle,\n        startAngle: actualStartAngle,\n        endAngle: actualEndAngle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: roseType ? linearMap(value, extent, [r0, r]) : r\n      });\n      currentAngle = endAngle;\n    });\n    // Some sector is constrained by minAngle and padAngle\n    // Rest sectors needs recalculate angle\n    if (restAngle < PI2 && validDataCount) {\n      // Average the angle if rest angle is not enough after all angles is\n      // Constrained by minAngle and padAngle\n      if (restAngle <= 1e-3) {\n        var angle_1 = angleRange / validDataCount;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_1 = data.getItemLayout(idx);\n            layout_1.angle = angle_1;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle_1 < padAngle) {\n              actualStartAngle = startAngle + dir * (idx + 1 / 2) * angle_1;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = startAngle + dir * idx * angle_1 + halfPadAngle;\n              actualEndAngle = startAngle + dir * (idx + 1) * angle_1 - halfPadAngle;\n            }\n            layout_1.startAngle = actualStartAngle;\n            layout_1.endAngle = actualEndAngle;\n          }\n        });\n      } else {\n        unitRadian = restAngle / valueSumLargerThanMinAngle;\n        currentAngle = startAngle;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_2 = data.getItemLayout(idx);\n            var angle = layout_2.angle === minAndPadAngle ? minAndPadAngle : value * unitRadian;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle < padAngle) {\n              actualStartAngle = currentAngle + dir * angle / 2;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = currentAngle + halfPadAngle;\n              actualEndAngle = currentAngle + dir * angle - halfPadAngle;\n            }\n            layout_2.startAngle = actualStartAngle;\n            layout_2.endAngle = actualEndAngle;\n            currentAngle += dir * angle;\n          }\n        });\n      }\n    }\n  });\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport default function dataFilter(seriesType) {\n  return {\n    seriesType: seriesType,\n    reset: function (seriesModel, ecModel) {\n      var legendModels = ecModel.findComponents({\n        mainType: 'legend'\n      });\n      if (!legendModels || !legendModels.length) {\n        return;\n      }\n      var data = seriesModel.getData();\n      data.filterSelf(function (idx) {\n        var name = data.getName(idx);\n        // If in any legend component the status is not selected.\n        for (var i = 0; i < legendModels.length; i++) {\n          // @ts-ignore FIXME: LegendModel\n          if (!legendModels[i].isSelected(name)) {\n            return false;\n          }\n        }\n        return true;\n      });\n    }\n  };\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// FIXME emphasis label position is not same with normal label position\nimport { parsePercent } from '../../util/number.js';\nimport { Point } from '../../util/graphic.js';\nimport { each, isNumber } from 'zrender/lib/core/util.js';\nimport { limitTurnAngle, limitSurfaceAngle } from '../../label/labelGuideHelper.js';\nimport { shiftLayoutOnY } from '../../label/labelLayoutHelper.js';\nvar RADIAN = Math.PI / 180;\nfunction adjustSingleSide(list, cx, cy, r, dir, viewWidth, viewHeight, viewLeft, viewTop, farthestX) {\n  if (list.length < 2) {\n    return;\n  }\n  ;\n  function recalculateXOnSemiToAlignOnEllipseCurve(semi) {\n    var rB = semi.rB;\n    var rB2 = rB * rB;\n    for (var i = 0; i < semi.list.length; i++) {\n      var item = semi.list[i];\n      var dy = Math.abs(item.label.y - cy);\n      // horizontal r is always same with original r because x is not changed.\n      var rA = r + item.len;\n      var rA2 = rA * rA;\n      // Use ellipse implicit function to calculate x\n      var dx = Math.sqrt((1 - Math.abs(dy * dy / rB2)) * rA2);\n      var newX = cx + (dx + item.len2) * dir;\n      var deltaX = newX - item.label.x;\n      var newTargetWidth = item.targetTextWidth - deltaX * dir;\n      // text x is changed, so need to recalculate width.\n      constrainTextWidth(item, newTargetWidth, true);\n      item.label.x = newX;\n    }\n  }\n  // Adjust X based on the shifted y. Make tight labels aligned on an ellipse curve.\n  function recalculateX(items) {\n    // Extremes of\n    var topSemi = {\n      list: [],\n      maxY: 0\n    };\n    var bottomSemi = {\n      list: [],\n      maxY: 0\n    };\n    for (var i = 0; i < items.length; i++) {\n      if (items[i].labelAlignTo !== 'none') {\n        continue;\n      }\n      var item = items[i];\n      var semi = item.label.y > cy ? bottomSemi : topSemi;\n      var dy = Math.abs(item.label.y - cy);\n      if (dy >= semi.maxY) {\n        var dx = item.label.x - cx - item.len2 * dir;\n        // horizontal r is always same with original r because x is not changed.\n        var rA = r + item.len;\n        // Canculate rB based on the topest / bottemest label.\n        var rB = Math.abs(dx) < rA ? Math.sqrt(dy * dy / (1 - dx * dx / rA / rA)) : rA;\n        semi.rB = rB;\n        semi.maxY = dy;\n      }\n      semi.list.push(item);\n    }\n    recalculateXOnSemiToAlignOnEllipseCurve(topSemi);\n    recalculateXOnSemiToAlignOnEllipseCurve(bottomSemi);\n  }\n  var len = list.length;\n  for (var i = 0; i < len; i++) {\n    if (list[i].position === 'outer' && list[i].labelAlignTo === 'labelLine') {\n      var dx = list[i].label.x - farthestX;\n      list[i].linePoints[1][0] += dx;\n      list[i].label.x = farthestX;\n    }\n  }\n  if (shiftLayoutOnY(list, viewTop, viewTop + viewHeight)) {\n    recalculateX(list);\n  }\n}\nfunction avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop) {\n  var leftList = [];\n  var rightList = [];\n  var leftmostX = Number.MAX_VALUE;\n  var rightmostX = -Number.MAX_VALUE;\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var label = labelLayoutList[i].label;\n    if (isPositionCenter(labelLayoutList[i])) {\n      continue;\n    }\n    if (label.x < cx) {\n      leftmostX = Math.min(leftmostX, label.x);\n      leftList.push(labelLayoutList[i]);\n    } else {\n      rightmostX = Math.max(rightmostX, label.x);\n      rightList.push(labelLayoutList[i]);\n    }\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      if (layout.labelStyleWidth != null) {\n        continue;\n      }\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var targetTextWidth = void 0;\n      if (layout.labelAlignTo === 'edge') {\n        if (label.x < cx) {\n          targetTextWidth = linePoints[2][0] - layout.labelDistance - viewLeft - layout.edgeDistance;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - layout.edgeDistance - linePoints[2][0] - layout.labelDistance;\n        }\n      } else if (layout.labelAlignTo === 'labelLine') {\n        if (label.x < cx) {\n          targetTextWidth = leftmostX - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - rightmostX - layout.bleedMargin;\n        }\n      } else {\n        if (label.x < cx) {\n          targetTextWidth = label.x - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - label.x - layout.bleedMargin;\n        }\n      }\n      layout.targetTextWidth = targetTextWidth;\n      constrainTextWidth(layout, targetTextWidth);\n    }\n  }\n  adjustSingleSide(rightList, cx, cy, r, 1, viewWidth, viewHeight, viewLeft, viewTop, rightmostX);\n  adjustSingleSide(leftList, cx, cy, r, -1, viewWidth, viewHeight, viewLeft, viewTop, leftmostX);\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var isAlignToEdge = layout.labelAlignTo === 'edge';\n      var padding = label.style.padding;\n      var paddingH = padding ? padding[1] + padding[3] : 0;\n      // textRect.width already contains paddingH if bgColor is set\n      var extraPaddingH = label.style.backgroundColor ? 0 : paddingH;\n      var realTextWidth = layout.rect.width + extraPaddingH;\n      var dist = linePoints[1][0] - linePoints[2][0];\n      if (isAlignToEdge) {\n        if (label.x < cx) {\n          linePoints[2][0] = viewLeft + layout.edgeDistance + realTextWidth + layout.labelDistance;\n        } else {\n          linePoints[2][0] = viewLeft + viewWidth - layout.edgeDistance - realTextWidth - layout.labelDistance;\n        }\n      } else {\n        if (label.x < cx) {\n          linePoints[2][0] = label.x + layout.labelDistance;\n        } else {\n          linePoints[2][0] = label.x - layout.labelDistance;\n        }\n        linePoints[1][0] = linePoints[2][0] + dist;\n      }\n      linePoints[1][1] = linePoints[2][1] = label.y;\n    }\n  }\n}\n/**\n * Set max width of each label, and then wrap each label to the max width.\n *\n * @param layout label layout\n * @param availableWidth max width for the label to display\n * @param forceRecalculate recaculate the text layout even if the current width\n * is smaller than `availableWidth`. This is useful when the text was previously\n * wrapped by calling `constrainTextWidth` but now `availableWidth` changed, in\n * which case, previous wrapping should be redo.\n */\nfunction constrainTextWidth(layout, availableWidth, forceRecalculate) {\n  if (forceRecalculate === void 0) {\n    forceRecalculate = false;\n  }\n  if (layout.labelStyleWidth != null) {\n    // User-defined style.width has the highest priority.\n    return;\n  }\n  var label = layout.label;\n  var style = label.style;\n  var textRect = layout.rect;\n  var bgColor = style.backgroundColor;\n  var padding = style.padding;\n  var paddingH = padding ? padding[1] + padding[3] : 0;\n  var overflow = style.overflow;\n  // textRect.width already contains paddingH if bgColor is set\n  var oldOuterWidth = textRect.width + (bgColor ? 0 : paddingH);\n  if (availableWidth < oldOuterWidth || forceRecalculate) {\n    var oldHeight = textRect.height;\n    if (overflow && overflow.match('break')) {\n      // Temporarily set background to be null to calculate\n      // the bounding box without background.\n      label.setStyle('backgroundColor', null);\n      // Set constraining width\n      label.setStyle('width', availableWidth - paddingH);\n      // This is the real bounding box of the text without padding.\n      var innerRect = label.getBoundingRect();\n      label.setStyle('width', Math.ceil(innerRect.width));\n      label.setStyle('backgroundColor', bgColor);\n    } else {\n      var availableInnerWidth = availableWidth - paddingH;\n      var newWidth = availableWidth < oldOuterWidth\n      // Current text is too wide, use `availableWidth` as max width.\n      ? availableInnerWidth :\n      // Current available width is enough, but the text may have\n      // already been wrapped with a smaller available width.\n      forceRecalculate ? availableInnerWidth > layout.unconstrainedWidth\n      // Current available is larger than text width,\n      // so don't constrain width (otherwise it may have\n      // empty space in the background).\n      ? null\n      // Current available is smaller than text width, so\n      // use the current available width as constraining\n      // width.\n      : availableInnerWidth\n      // Current available width is enough, so no need to\n      // constrain.\n      : null;\n      label.setStyle('width', newWidth);\n    }\n    var newRect = label.getBoundingRect();\n    textRect.width = newRect.width;\n    var margin = (label.style.margin || 0) + 2.1;\n    textRect.height = newRect.height + margin;\n    textRect.y -= (textRect.height - oldHeight) / 2;\n  }\n}\nfunction isPositionCenter(sectorShape) {\n  // Not change x for center label\n  return sectorShape.position === 'center';\n}\nexport default function pieLabelLayout(seriesModel) {\n  var data = seriesModel.getData();\n  var labelLayoutList = [];\n  var cx;\n  var cy;\n  var hasLabelRotate = false;\n  var minShowLabelRadian = (seriesModel.get('minShowLabelAngle') || 0) * RADIAN;\n  var viewRect = data.getLayout('viewRect');\n  var r = data.getLayout('r');\n  var viewWidth = viewRect.width;\n  var viewLeft = viewRect.x;\n  var viewTop = viewRect.y;\n  var viewHeight = viewRect.height;\n  function setNotShow(el) {\n    el.ignore = true;\n  }\n  function isLabelShown(label) {\n    if (!label.ignore) {\n      return true;\n    }\n    for (var key in label.states) {\n      if (label.states[key].ignore === false) {\n        return true;\n      }\n    }\n    return false;\n  }\n  data.each(function (idx) {\n    var sector = data.getItemGraphicEl(idx);\n    var sectorShape = sector.shape;\n    var label = sector.getTextContent();\n    var labelLine = sector.getTextGuideLine();\n    var itemModel = data.getItemModel(idx);\n    var labelModel = itemModel.getModel('label');\n    // Use position in normal or emphasis\n    var labelPosition = labelModel.get('position') || itemModel.get(['emphasis', 'label', 'position']);\n    var labelDistance = labelModel.get('distanceToLabelLine');\n    var labelAlignTo = labelModel.get('alignTo');\n    var edgeDistance = parsePercent(labelModel.get('edgeDistance'), viewWidth);\n    var bleedMargin = labelModel.get('bleedMargin');\n    var labelLineModel = itemModel.getModel('labelLine');\n    var labelLineLen = labelLineModel.get('length');\n    labelLineLen = parsePercent(labelLineLen, viewWidth);\n    var labelLineLen2 = labelLineModel.get('length2');\n    labelLineLen2 = parsePercent(labelLineLen2, viewWidth);\n    if (Math.abs(sectorShape.endAngle - sectorShape.startAngle) < minShowLabelRadian) {\n      each(label.states, setNotShow);\n      label.ignore = true;\n      if (labelLine) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      }\n      return;\n    }\n    if (!isLabelShown(label)) {\n      return;\n    }\n    var midAngle = (sectorShape.startAngle + sectorShape.endAngle) / 2;\n    var nx = Math.cos(midAngle);\n    var ny = Math.sin(midAngle);\n    var textX;\n    var textY;\n    var linePoints;\n    var textAlign;\n    cx = sectorShape.cx;\n    cy = sectorShape.cy;\n    var isLabelInside = labelPosition === 'inside' || labelPosition === 'inner';\n    if (labelPosition === 'center') {\n      textX = sectorShape.cx;\n      textY = sectorShape.cy;\n      textAlign = 'center';\n    } else {\n      var x1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * nx : sectorShape.r * nx) + cx;\n      var y1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * ny : sectorShape.r * ny) + cy;\n      textX = x1 + nx * 3;\n      textY = y1 + ny * 3;\n      if (!isLabelInside) {\n        // For roseType\n        var x2 = x1 + nx * (labelLineLen + r - sectorShape.r);\n        var y2 = y1 + ny * (labelLineLen + r - sectorShape.r);\n        var x3 = x2 + (nx < 0 ? -1 : 1) * labelLineLen2;\n        var y3 = y2;\n        if (labelAlignTo === 'edge') {\n          // Adjust textX because text align of edge is opposite\n          textX = nx < 0 ? viewLeft + edgeDistance : viewLeft + viewWidth - edgeDistance;\n        } else {\n          textX = x3 + (nx < 0 ? -labelDistance : labelDistance);\n        }\n        textY = y3;\n        linePoints = [[x1, y1], [x2, y2], [x3, y3]];\n      }\n      textAlign = isLabelInside ? 'center' : labelAlignTo === 'edge' ? nx > 0 ? 'right' : 'left' : nx > 0 ? 'left' : 'right';\n    }\n    var PI = Math.PI;\n    var labelRotate = 0;\n    var rotate = labelModel.get('rotate');\n    if (isNumber(rotate)) {\n      labelRotate = rotate * (PI / 180);\n    } else if (labelPosition === 'center') {\n      labelRotate = 0;\n    } else if (rotate === 'radial' || rotate === true) {\n      var radialAngle = nx < 0 ? -midAngle + PI : -midAngle;\n      labelRotate = radialAngle;\n    } else if (rotate === 'tangential' && labelPosition !== 'outside' && labelPosition !== 'outer') {\n      var rad = Math.atan2(nx, ny);\n      if (rad < 0) {\n        rad = PI * 2 + rad;\n      }\n      var isDown = ny > 0;\n      if (isDown) {\n        rad = PI + rad;\n      }\n      labelRotate = rad - PI;\n    }\n    hasLabelRotate = !!labelRotate;\n    label.x = textX;\n    label.y = textY;\n    label.rotation = labelRotate;\n    label.setStyle({\n      verticalAlign: 'middle'\n    });\n    // Not sectorShape the inside label\n    if (!isLabelInside) {\n      var textRect = label.getBoundingRect().clone();\n      textRect.applyTransform(label.getComputedTransform());\n      // Text has a default 1px stroke. Exclude this.\n      var margin = (label.style.margin || 0) + 2.1;\n      textRect.y -= margin / 2;\n      textRect.height += margin;\n      labelLayoutList.push({\n        label: label,\n        labelLine: labelLine,\n        position: labelPosition,\n        len: labelLineLen,\n        len2: labelLineLen2,\n        minTurnAngle: labelLineModel.get('minTurnAngle'),\n        maxSurfaceAngle: labelLineModel.get('maxSurfaceAngle'),\n        surfaceNormal: new Point(nx, ny),\n        linePoints: linePoints,\n        textAlign: textAlign,\n        labelDistance: labelDistance,\n        labelAlignTo: labelAlignTo,\n        edgeDistance: edgeDistance,\n        bleedMargin: bleedMargin,\n        rect: textRect,\n        unconstrainedWidth: textRect.width,\n        labelStyleWidth: label.style.width\n      });\n    } else {\n      label.setStyle({\n        align: textAlign\n      });\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    sector.setTextConfig({\n      inside: isLabelInside\n    });\n  });\n  if (!hasLabelRotate && seriesModel.get('avoidLabelOverlap')) {\n    avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop);\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    var label = layout.label;\n    var labelLine = layout.labelLine;\n    var notShowLabel = isNaN(label.x) || isNaN(label.y);\n    if (label) {\n      label.setStyle({\n        align: layout.textAlign\n      });\n      if (notShowLabel) {\n        each(label.states, setNotShow);\n        label.ignore = true;\n      }\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    if (labelLine) {\n      var linePoints = layout.linePoints;\n      if (notShowLabel || !linePoints) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      } else {\n        limitTurnAngle(linePoints, layout.minTurnAngle);\n        limitSurfaceAngle(linePoints, layout.surfaceNormal, layout.maxSurfaceAngle);\n        labelLine.setShape({\n          points: linePoints\n        });\n        // Set the anchor to the midpoint of sector\n        label.__hostTarget.textGuideLineConfig = {\n          anchor: new Point(linePoints[0][0], linePoints[0][1])\n        };\n      }\n    }\n  }\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { extend, retrieve3 } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport ChartView from '../../view/Chart.js';\nimport labelLayout from './labelLayout.js';\nimport { setLabelLineStyle, getLabelLineStatesModels } from '../../label/labelGuideHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getSectorCornerRadius } from '../helper/sectorHelper.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nimport { getBasicPieLayout } from './pieLayout.js';\n/**\n * Piece of pie including Sector, Label, LabelLine\n */\nvar PiePiece = /** @class */function (_super) {\n  __extends(PiePiece, _super);\n  function PiePiece(data, idx, startAngle) {\n    var _this = _super.call(this) || this;\n    _this.z2 = 2;\n    var text = new graphic.Text();\n    _this.setTextContent(text);\n    _this.updateData(data, idx, startAngle, true);\n    return _this;\n  }\n  PiePiece.prototype.updateData = function (data, idx, startAngle, firstCreate) {\n    var sector = this;\n    var seriesModel = data.hostModel;\n    var itemModel = data.getItemModel(idx);\n    var emphasisModel = itemModel.getModel('emphasis');\n    var layout = data.getItemLayout(idx);\n    // cornerRadius & innerCornerRadius doesn't exist in the item layout. Use `0` if null value is specified.\n    // see `setItemLayout` in `pieLayout.ts`.\n    var sectorShape = extend(getSectorCornerRadius(itemModel.getModel('itemStyle'), layout, true), layout);\n    // Ignore NaN data.\n    if (isNaN(sectorShape.startAngle)) {\n      // Use NaN shape to avoid drawing shape.\n      sector.setShape(sectorShape);\n      return;\n    }\n    if (firstCreate) {\n      sector.setShape(sectorShape);\n      var animationType = seriesModel.getShallow('animationType');\n      if (seriesModel.ecModel.ssr) {\n        // Use scale animation in SSR mode(opacity?)\n        // Because CSS SVG animation doesn't support very customized shape animation.\n        graphic.initProps(sector, {\n          scaleX: 0,\n          scaleY: 0\n        }, seriesModel, {\n          dataIndex: idx,\n          isFrom: true\n        });\n        sector.originX = sectorShape.cx;\n        sector.originY = sectorShape.cy;\n      } else if (animationType === 'scale') {\n        sector.shape.r = layout.r0;\n        graphic.initProps(sector, {\n          shape: {\n            r: layout.r\n          }\n        }, seriesModel, idx);\n      }\n      // Expansion\n      else {\n        if (startAngle != null) {\n          sector.setShape({\n            startAngle: startAngle,\n            endAngle: startAngle\n          });\n          graphic.initProps(sector, {\n            shape: {\n              startAngle: layout.startAngle,\n              endAngle: layout.endAngle\n            }\n          }, seriesModel, idx);\n        } else {\n          sector.shape.endAngle = layout.startAngle;\n          graphic.updateProps(sector, {\n            shape: {\n              endAngle: layout.endAngle\n            }\n          }, seriesModel, idx);\n        }\n      }\n    } else {\n      saveOldStyle(sector);\n      // Transition animation from the old shape\n      graphic.updateProps(sector, {\n        shape: sectorShape\n      }, seriesModel, idx);\n    }\n    sector.useStyle(data.getItemVisual(idx, 'style'));\n    setStatesStylesFromModel(sector, itemModel);\n    var midAngle = (layout.startAngle + layout.endAngle) / 2;\n    var offset = seriesModel.get('selectedOffset');\n    var dx = Math.cos(midAngle) * offset;\n    var dy = Math.sin(midAngle) * offset;\n    var cursorStyle = itemModel.getShallow('cursor');\n    cursorStyle && sector.attr('cursor', cursorStyle);\n    this._updateLabel(seriesModel, data, idx);\n    sector.ensureState('emphasis').shape = extend({\n      r: layout.r + (emphasisModel.get('scale') ? emphasisModel.get('scaleSize') || 0 : 0)\n    }, getSectorCornerRadius(emphasisModel.getModel('itemStyle'), layout));\n    extend(sector.ensureState('select'), {\n      x: dx,\n      y: dy,\n      shape: getSectorCornerRadius(itemModel.getModel(['select', 'itemStyle']), layout)\n    });\n    extend(sector.ensureState('blur'), {\n      shape: getSectorCornerRadius(itemModel.getModel(['blur', 'itemStyle']), layout)\n    });\n    var labelLine = sector.getTextGuideLine();\n    var labelText = sector.getTextContent();\n    labelLine && extend(labelLine.ensureState('select'), {\n      x: dx,\n      y: dy\n    });\n    // TODO: needs dx, dy in zrender?\n    extend(labelText.ensureState('select'), {\n      x: dx,\n      y: dy\n    });\n    toggleHoverEmphasis(this, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  PiePiece.prototype._updateLabel = function (seriesModel, data, idx) {\n    var sector = this;\n    var itemModel = data.getItemModel(idx);\n    var labelLineModel = itemModel.getModel('labelLine');\n    var style = data.getItemVisual(idx, 'style');\n    var visualColor = style && style.fill;\n    var visualOpacity = style && style.opacity;\n    setLabelStyle(sector, getLabelStatesModels(itemModel), {\n      labelFetcher: data.hostModel,\n      labelDataIndex: idx,\n      inheritColor: visualColor,\n      defaultOpacity: visualOpacity,\n      defaultText: seriesModel.getFormattedLabel(idx, 'normal') || data.getName(idx)\n    });\n    var labelText = sector.getTextContent();\n    // Set textConfig on sector.\n    sector.setTextConfig({\n      // reset position, rotation\n      position: null,\n      rotation: null\n    });\n    // Make sure update style on labelText after setLabelStyle.\n    // Because setLabelStyle will replace a new style on it.\n    labelText.attr({\n      z2: 10\n    });\n    var labelPosition = seriesModel.get(['label', 'position']);\n    if (labelPosition !== 'outside' && labelPosition !== 'outer') {\n      sector.removeTextGuideLine();\n    } else {\n      var polyline = this.getTextGuideLine();\n      if (!polyline) {\n        polyline = new graphic.Polyline();\n        this.setTextGuideLine(polyline);\n      }\n      // Default use item visual color\n      setLabelLineStyle(this, getLabelLineStatesModels(itemModel), {\n        stroke: visualColor,\n        opacity: retrieve3(labelLineModel.get(['lineStyle', 'opacity']), visualOpacity, 1)\n      });\n    }\n  };\n  return PiePiece;\n}(graphic.Sector);\n// Pie view\nvar PieView = /** @class */function (_super) {\n  __extends(PieView, _super);\n  function PieView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.ignoreLabelLineUpdate = true;\n    return _this;\n  }\n  PieView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var group = this.group;\n    var startAngle;\n    // First render\n    if (!oldData && data.count() > 0) {\n      var shape = data.getItemLayout(0);\n      for (var s = 1; isNaN(shape && shape.startAngle) && s < data.count(); ++s) {\n        shape = data.getItemLayout(s);\n      }\n      if (shape) {\n        startAngle = shape.startAngle;\n      }\n    }\n    // remove empty-circle if it exists\n    if (this._emptyCircleSector) {\n      group.remove(this._emptyCircleSector);\n    }\n    // when all data are filtered, show lightgray empty circle\n    if (data.count() === 0 && seriesModel.get('showEmptyCircle')) {\n      var sector = new graphic.Sector({\n        shape: getBasicPieLayout(seriesModel, api)\n      });\n      sector.useStyle(seriesModel.getModel('emptyCircleStyle').getItemStyle());\n      this._emptyCircleSector = sector;\n      group.add(sector);\n    }\n    data.diff(oldData).add(function (idx) {\n      var piePiece = new PiePiece(data, idx, startAngle);\n      data.setItemGraphicEl(idx, piePiece);\n      group.add(piePiece);\n    }).update(function (newIdx, oldIdx) {\n      var piePiece = oldData.getItemGraphicEl(oldIdx);\n      piePiece.updateData(data, newIdx, startAngle);\n      piePiece.off('click');\n      group.add(piePiece);\n      data.setItemGraphicEl(newIdx, piePiece);\n    }).remove(function (idx) {\n      var piePiece = oldData.getItemGraphicEl(idx);\n      graphic.removeElementWithFadeOut(piePiece, seriesModel, idx);\n    }).execute();\n    labelLayout(seriesModel);\n    // Always use initial animation.\n    if (seriesModel.get('animationTypeUpdate') !== 'expansion') {\n      this._data = data;\n    }\n  };\n  PieView.prototype.dispose = function () {};\n  PieView.prototype.containPoint = function (point, seriesModel) {\n    var data = seriesModel.getData();\n    var itemLayout = data.getItemLayout(0);\n    if (itemLayout) {\n      var dx = point[0] - itemLayout.cx;\n      var dy = point[1] - itemLayout.cy;\n      var radius = Math.sqrt(dx * dx + dy * dy);\n      return radius <= itemLayout.r && radius >= itemLayout.r0;\n    }\n  };\n  PieView.type = 'pie';\n  return PieView;\n}(ChartView);\nexport default PieView;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * LegendVisualProvider is an bridge that pick encoded color from data and\n * provide to the legend component.\n */\nvar LegendVisualProvider = /** @class */function () {\n  function LegendVisualProvider(\n  // Function to get data after filtered. It stores all the encoding info\n  getDataWithEncodedVisual,\n  // Function to get raw data before filtered.\n  getRawData) {\n    this._getDataWithEncodedVisual = getDataWithEncodedVisual;\n    this._getRawData = getRawData;\n  }\n  LegendVisualProvider.prototype.getAllNames = function () {\n    var rawData = this._getRawData();\n    // We find the name from the raw data. In case it's filtered by the legend component.\n    // Normally, the name can be found in rawData, but can't be found in filtered data will display as gray.\n    return rawData.mapArray(rawData.getName);\n  };\n  LegendVisualProvider.prototype.containName = function (name) {\n    var rawData = this._getRawData();\n    return rawData.indexOfName(name) >= 0;\n  };\n  LegendVisualProvider.prototype.indexOfName = function (name) {\n    // Only get data when necessary.\n    // Because LegendVisualProvider constructor may be new in the stage that data is not prepared yet.\n    // Invoking Series#getData immediately will throw an error.\n    var dataWithEncodedVisual = this._getDataWithEncodedVisual();\n    return dataWithEncodedVisual.indexOfName(name);\n  };\n  LegendVisualProvider.prototype.getItemVisual = function (dataIndex, key) {\n    // Get encoded visual properties from final filtered data.\n    var dataWithEncodedVisual = this._getDataWithEncodedVisual();\n    return dataWithEncodedVisual.getItemVisual(dataIndex, key);\n  };\n  return LegendVisualProvider;\n}();\nexport default LegendVisualProvider;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport { getPercentSeats } from '../../util/number.js';\nimport { makeSeriesEncodeForNameBased } from '../../data/helper/sourceHelper.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport SeriesModel from '../../model/Series.js';\nvar innerData = modelUtil.makeInner();\nvar PieSeriesModel = /** @class */function (_super) {\n  __extends(PieSeriesModel, _super);\n  function PieSeriesModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\n   * @overwrite\n   */\n  PieSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n    this._defaultLabelLine(option);\n  };\n  /**\n   * @overwrite\n   */\n  PieSeriesModel.prototype.mergeOption = function () {\n    _super.prototype.mergeOption.apply(this, arguments);\n  };\n  /**\n   * @overwrite\n   */\n  PieSeriesModel.prototype.getInitialData = function () {\n    return createSeriesDataSimply(this, {\n      coordDimensions: ['value'],\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForNameBased, this)\n    });\n  };\n  /**\n   * @overwrite\n   */\n  PieSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var data = this.getData();\n    // update seats when data is changed\n    var dataInner = innerData(data);\n    var seats = dataInner.seats;\n    if (!seats) {\n      var valueList_1 = [];\n      data.each(data.mapDimension('value'), function (value) {\n        valueList_1.push(value);\n      });\n      seats = dataInner.seats = getPercentSeats(valueList_1, data.hostModel.get('percentPrecision'));\n    }\n    var params = _super.prototype.getDataParams.call(this, dataIndex);\n    // seats may be empty when sum is 0\n    params.percent = seats[dataIndex] || 0;\n    params.$vars.push('percent');\n    return params;\n  };\n  PieSeriesModel.prototype._defaultLabelLine = function (option) {\n    // Extend labelLine emphasis\n    modelUtil.defaultEmphasis(option, 'labelLine', ['show']);\n    var labelLineNormalOpt = option.labelLine;\n    var labelLineEmphasisOpt = option.emphasis.labelLine;\n    // Not show label line if `label.normal.show = false`\n    labelLineNormalOpt.show = labelLineNormalOpt.show && option.label.show;\n    labelLineEmphasisOpt.show = labelLineEmphasisOpt.show && option.emphasis.label.show;\n  };\n  PieSeriesModel.type = 'series.pie';\n  PieSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    colorBy: 'data',\n    // 默认全局居中\n    center: ['50%', '50%'],\n    radius: [0, '75%'],\n    // 默认顺时针\n    clockwise: true,\n    startAngle: 90,\n    endAngle: 'auto',\n    padAngle: 0,\n    // 最小角度改为0\n    minAngle: 0,\n    // If the angle of a sector less than `minShowLabelAngle`,\n    // the label will not be displayed.\n    minShowLabelAngle: 0,\n    // 选中时扇区偏移量\n    selectedOffset: 10,\n    // 选择模式，默认关闭，可选single，multiple\n    // selectedMode: false,\n    // 南丁格尔玫瑰图模式，'radius'（半径） | 'area'（面积）\n    // roseType: null,\n    percentPrecision: 2,\n    // If still show when all data zero.\n    stillShowZeroSum: true,\n    // cursor: null,\n    left: 0,\n    top: 0,\n    right: 0,\n    bottom: 0,\n    width: null,\n    height: null,\n    label: {\n      // color: 'inherit',\n      // If rotate around circle\n      rotate: 0,\n      show: true,\n      overflow: 'truncate',\n      // 'outer', 'inside', 'center'\n      position: 'outer',\n      // 'none', 'labelLine', 'edge'. Works only when position is 'outer'\n      alignTo: 'none',\n      // Closest distance between label and chart edge.\n      // Works only position is 'outer' and alignTo is 'edge'.\n      edgeDistance: '25%',\n      // Works only position is 'outer' and alignTo is not 'edge'.\n      bleedMargin: 10,\n      // Distance between text and label line.\n      distanceToLabelLine: 5\n      // formatter: 标签文本格式器，同 tooltip.formatter，不支持异步回调\n      // 默认使用全局文本样式，详见 textStyle\n      // distance: 当position为inner时有效，为label位置到圆心的距离与圆半径(环状图为内外半径和)的比例系数\n    },\n\n    // Enabled when label.normal.position is 'outer'\n    labelLine: {\n      show: true,\n      // 引导线两段中的第一段长度\n      length: 15,\n      // 引导线两段中的第二段长度\n      length2: 15,\n      smooth: false,\n      minTurnAngle: 90,\n      maxSurfaceAngle: 90,\n      lineStyle: {\n        // color: 各异,\n        width: 1,\n        type: 'solid'\n      }\n    },\n    itemStyle: {\n      borderWidth: 1,\n      borderJoin: 'round'\n    },\n    showEmptyCircle: true,\n    emptyCircleStyle: {\n      color: 'lightgray',\n      opacity: 1\n    },\n    labelLayout: {\n      // Hide the overlapped label.\n      hideOverlap: true\n    },\n    emphasis: {\n      scale: true,\n      scaleSize: 5\n    },\n    // If use strategy to avoid label overlapping\n    avoidLabelOverlap: true,\n    // Animation type. Valid values: expansion, scale\n    animationType: 'expansion',\n    animationDuration: 1000,\n    // Animation type when update. Valid values: transition, expansion\n    animationTypeUpdate: 'transition',\n    animationEasingUpdate: 'cubicInOut',\n    animationDurationUpdate: 500,\n    animationEasing: 'cubicInOut'\n  };\n  return PieSeriesModel;\n}(SeriesModel);\nexport default PieSeriesModel;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isNumber } from 'zrender/lib/core/util.js';\nexport default function negativeDataFilter(seriesType) {\n  return {\n    seriesType: seriesType,\n    reset: function (seriesModel, ecModel) {\n      var data = seriesModel.getData();\n      data.filterSelf(function (idx) {\n        // handle negative value condition\n        var valueDim = data.mapDimension('value');\n        var curValue = data.get(valueDim, idx);\n        if (isNumber(curValue) && !isNaN(curValue) && curValue < 0) {\n          return false;\n        }\n        return true;\n      });\n    }\n  };\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createLegacyDataSelectAction } from '../../legacy/dataSelectAction.js';\nimport pieLayout from '../pie/pieLayout.js';\nimport dataFilter from '../../processor/dataFilter.js';\nimport { curry } from 'zrender/lib/core/util.js';\nimport PieView from './PieView.js';\nimport PieSeriesModel from './PieSeries.js';\nimport negativeDataFilter from '../../processor/negativeDataFilter.js';\nexport function install(registers) {\n  registers.registerChartView(PieView);\n  registers.registerSeriesModel(PieSeriesModel);\n  createLegacyDataSelectAction('pie', registers.registerAction);\n  registers.registerLayout(curry(pieLayout, 'pie'));\n  registers.registerProcessor(dataFilter('pie'));\n  registers.registerProcessor(negativeDataFilter('pie'));\n}", "<template>\n  <ECharts\n    v-if=\"currencies\"\n    ref=\"balanceChart\"\n    :option=\"balanceChartOptions\"\n    :theme=\"settingsStore.chartTheme\"\n    :style=\"{ height: width * 0.6 + 'px' }\"\n    autoresize\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport { EChartsOption } from 'echarts';\nimport ECharts from 'vue-echarts';\n\nimport { PieChart } from 'echarts/charts';\nimport {\n  DatasetComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n} from 'echarts/components';\nimport { use } from 'echarts/core';\nimport { LabelLayout } from 'echarts/features';\nimport { CanvasRenderer } from 'echarts/renderers';\n\nimport { formatPriceCurrency } from '@/shared/formatters';\nimport { useSettingsStore } from '@/stores/settings';\nimport { BalanceValues } from '@/types';\nimport { useElementSize } from '@vueuse/core';\n\nuse([\n  <PERSON><PERSON><PERSON>,\n  Canvas<PERSON>enderer,\n  DatasetComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n  LabelLayout,\n]);\n\nconst balanceChart = ref(null);\nconst { width } = useElementSize(balanceChart);\n\nconst props = defineProps({\n  currencies: { required: true, type: Array as () => BalanceValues[] },\n  showTitle: { required: false, type: Boolean },\n});\nconst settingsStore = useSettingsStore();\n\nconst balanceChartOptions = computed((): EChartsOption => {\n  return {\n    title: {\n      text: 'Balance',\n      show: props.showTitle,\n    },\n    center: ['50%', '50%'],\n    backgroundColor: 'rgba(0, 0, 0, 0)',\n    dataset: {\n      dimensions: ['balance', 'currency', 'est_stake', 'free', 'used', 'stake'],\n      source: props.currencies,\n    },\n    tooltip: {\n      trigger: 'item',\n      formatter: (params) => {\n        return `${formatPriceCurrency(params.value.balance, params.value.currency, 8)}<br />${\n          params.percent\n        }% (${formatPriceCurrency(params.value.est_stake, params.value.stake)})`;\n      },\n    },\n    // legend: {\n    //   orient: 'vertical',\n    //   right: 10,\n    //   top: 20,\n    //   bottom: 20,\n    // },\n    series: [\n      {\n        type: 'pie',\n        radius: ['40%', '70%'],\n\n        encode: {\n          value: 'est_stake',\n          itemName: 'currency',\n          tooltip: ['balance', 'currency'],\n        },\n        label: {\n          formatter: '{b} - {d}%',\n        },\n        tooltip: {\n          show: true,\n        },\n      },\n    ],\n  };\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.echarts {\n  min-height: 20px;\n}\n</style>\n", "<template>\n  <div>\n    <div class=\"mb-2\">\n      <label class=\"me-auto h3\">Balance</label>\n      <div class=\"float-end d-flex flex-row\">\n        <b-button\n          v-if=\"canUseBotBalance\"\n          size=\"sm\"\n          :title=\"!showBotOnly ? 'Showing Account balance' : 'Showing Bot balance'\"\n          @click=\"showBotOnly = !showBotOnly\"\n        >\n          <i-mdi-robot v-if=\"showBotOnly\" />\n          <i-mdi-bank v-else />\n        </b-button>\n        <b-button\n          size=\"sm\"\n          :title=\"!hideSmallBalances ? 'Hide small balances' : 'Show all balances'\"\n          @click=\"hideSmallBalances = !hideSmallBalances\"\n        >\n          <i-mdi-eye-off v-if=\"hideSmallBalances\" />\n          <i-mdi-eye v-else />\n        </b-button>\n\n        <b-button class=\"float-end\" size=\"sm\" @click=\"refreshBalance\">\n          <i-mdi-refresh />\n        </b-button>\n      </div>\n    </div>\n    <BalanceChart v-if=\"balanceCurrencies\" :currencies=\"chartValues\" />\n    <div>\n      <p v-if=\"botStore.activeBot.balance.note\">\n        <strong>{{ botStore.activeBot.balance.note }}</strong>\n      </p>\n      <b-table class=\"table-sm\" :items=\"balanceCurrencies\" :fields=\"tableFields\">\n        <template #custom-foot>\n          <td class=\"pt-1\"><strong>Total</strong></td>\n          <td class=\"pt-1\">\n            <span\n              class=\"font-italic\"\n              :title=\"`Increase over initial capital of ${formatCurrency(\n                botStore.activeBot.balance.starting_capital,\n              )} ${botStore.activeBot.balance.stake}`\"\n            >\n              {{ formatPercent(botStore.activeBot.balance.starting_capital_ratio) }}\n            </span>\n          </td>\n          <!-- this is a computed prop that adds up all the expenses in the visible rows -->\n          <td class=\"pt-1\">\n            <strong>\n              {{\n                showBotOnly && canUseBotBalance\n                  ? formatCurrency(botStore.activeBot.balance.total_bot)\n                  : formatCurrency(botStore.activeBot.balance.total)\n              }}\n            </strong>\n          </td>\n        </template>\n      </b-table>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { formatPercent, formatPrice } from '@/shared/formatters';\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { BalanceValues } from '@/types';\nimport { TableField } from 'bootstrap-vue-next';\n\nconst botStore = useBotStore();\nconst hideSmallBalances = ref(true);\nconst showBotOnly = ref(true);\n\nconst smallBalance = computed<number>(() => {\n  return Number((1.1 ** botStore.activeBot.stakeCurrencyDecimals).toFixed(8));\n});\n\nconst canUseBotBalance = computed(() => {\n  return botStore.activeBot.botApiVersion >= 2.26;\n});\n\nconst balanceCurrencies = computed(() => {\n  return botStore.activeBot.balance.currencies?.filter(\n    (v) =>\n      (!hideSmallBalances.value || v.est_stake >= smallBalance.value) &&\n      (!canUseBotBalance.value || !showBotOnly.value || (v.is_bot_managed ?? true) === true),\n  );\n});\n\nconst formatCurrency = (value) => {\n  return value ? formatPrice(value, botStore.activeBot.stakeCurrencyDecimals) : '';\n};\n\nconst chartValues = computed<BalanceValues[]>(() => {\n  return balanceCurrencies.value?.map((v) => {\n    return {\n      balance:\n        showBotOnly.value && canUseBotBalance.value && v.bot_owned != undefined\n          ? v.bot_owned\n          : v.balance,\n      currency: v.currency,\n      est_stake:\n        showBotOnly.value && canUseBotBalance.value ? v.est_stake_bot ?? v.est_stake : v.est_stake,\n      free: showBotOnly.value && canUseBotBalance.value ? v.bot_owned ?? v.free : v.free,\n      used: v.used,\n      stake: v.stake,\n    };\n  });\n});\n\nconst tableFields = computed<TableField[]>(() => {\n  return [\n    { key: 'currency', label: 'Currency' },\n    {\n      key: showBotOnly.value && canUseBotBalance.value ? 'bot_owned' : 'free',\n      label: 'Available',\n      formatter: formatCurrency,\n    },\n    {\n      key: showBotOnly.value && canUseBotBalance.value ? 'est_stake_bot' : 'est_stake',\n      label: `in ${botStore.activeBot.balance.stake}`,\n      formatter: formatCurrency,\n    },\n  ];\n});\n\nasync function refreshBalance() {\n  botStore.activeBot.getBalance();\n}\n\nonMounted(() => {\n  refreshBalance();\n});\n</script>\n"], "names": ["PI2", "CMD", "PathProxy", "DEFAULT_SEARCH_SPACE", "getCandidateAnchor", "pos", "distance", "rect", "outPt", "outDir", "width", "height", "projectPointToArc", "cx", "cy", "r", "startAngle", "endAngle", "anticlockwise", "x", "y", "out", "d", "ox", "oy", "tmp", "normalizeRadian", "angle", "x1", "y1", "x2", "y2", "d1", "d2", "projectPointToLine", "limitToEnds", "dx", "dy", "dx1", "dy1", "lineLen", "projectedLen", "t", "projectPointToRect", "tmpPt", "nearestPointOnRect", "pt", "dist", "nearestPointOnPath", "path", "xi", "yi", "x0", "y0", "minDist", "data", "i", "cmd", "cubicProjectPoint", "quadraticProjectPoint", "rx", "ry", "theta", "d<PERSON><PERSON><PERSON>", "_x", "pt0", "Point", "pt1", "pt2", "dir", "dir2", "updateLabelLinePoints", "target", "labelLineModel", "labelLine", "label", "labelGuideConfig", "points", "searchSpace", "labelRect", "anchorPoint", "targetTransform", "targetInversedTransform", "invert", "len", "candidate", "boundingRect", "Path", "limitTurnAngle", "tmpArr", "tmpProjPoint", "linePoints", "minTurnAngle", "len1", "len2", "angleCos", "minTurnAngleCos", "limitSurfaceAngle", "surfaceNormal", "maxSurfaceAngle", "maxSurfaceAngleCos", "HALF_PI", "angle2", "newAngle", "setLabelLineState", "ignore", "stateName", "stateModel", "isNormal", "stateObj", "smooth", "styleObj", "buildLabelLinePath", "shape", "vector.dist", "moveLen", "midPoint0", "vector.lerp", "midPoint2", "midPoint1", "setLabelLineStyle", "targetEl", "statesModels", "defaultStyle", "normalModel", "showNormal", "labelIgnoreNormal", "DISPLAY_STATES", "stateShow", "isLabelIgnored", "retrieve2", "Polyline", "defaults", "showAbove", "labelLineConfig", "getLabelLineStatesModels", "itemModel", "labelLineName", "SPECIAL_STATES", "cloneArr", "newPoints", "prepareLayoutCallbackParams", "labelItem", "hostEl", "LABEL_OPTION_TO_STYLE_KEYS", "dummyTransformable", "Transformable", "labelLayoutInnerStore", "makeInner", "labelLineAnimationStore", "extendWithKeys", "source", "keys", "key", "LABEL_LAYOUT_PROPS", "LabelManager", "dataIndex", "dataType", "seriesModel", "layoutOption", "labelStyle", "textConfig", "labelTransform", "BoundingRect", "host", "hostRect", "transform", "labelGuide", "chartView", "_this", "isFunction", "child", "textEl", "ecData", "getECData", "api", "createDragHandler", "el", "defaultLabelAttr", "degreeToRadian", "needsUpdateLabelLine", "parsePercent", "guideLine", "labelLayoutStore", "k", "hostModel", "labelList", "prepareLayoutList", "labelsNeedsAdjustOnX", "filter", "item", "labelsNeedsAdjustOnY", "shiftLayoutOnX", "shiftLayoutOnY", "labelsNeedsHideOverlap", "hideOverlap", "each", "ignoreLabelLineUpdate", "animationEnabled", "visualStyle", "visualType", "isElementRemoved", "layoutStore", "oldLayout", "newProps", "prevStates", "indexOf", "updateProps", "labelInner", "oldOpacity", "initProps", "layoutSelect", "layoutEmphasis", "animateLabelValue", "newLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "installLabelLayout", "registers", "ecModel", "params", "labelManager", "series", "RADIAN", "getViewRect", "layout.getLayoutRect", "getBasicPieLayout", "viewRect", "center", "radius", "zrUtil.isArray", "size", "r0", "coordSys", "point", "pieLayout", "seriesType", "valueDim", "_a", "padAngle", "minAngle", "minAndPadAngle", "validDataCount", "value", "sum", "unitRadian", "clockwise", "roseType", "stillShowZeroSum", "extent", "angles", "halfPadAngle", "normalizeArcAngles", "angleRange", "restAngle", "valueSumLargerThanMinAngle", "currentAngle", "idx", "actualStartAngle", "actualEndAngle", "linearMap", "angle_1", "layout_1", "layout_2", "dataFilter", "legend<PERSON><PERSON><PERSON>", "name", "adjustSingleSide", "list", "viewWidth", "viewHeight", "viewLeft", "viewTop", "farthestX", "recalculateXOnSemiToAlignOnEllipseCurve", "semi", "rB", "rB2", "rA", "rA2", "newX", "deltaX", "newTargetWidth", "constrainTextWidth", "recalculateX", "items", "topSemi", "bottomSemi", "avoidOverlap", "labelLayoutList", "leftList", "rightList", "leftmostX", "rightmostX", "isPositionCenter", "layout", "targetTextWidth", "isAlignToEdge", "padding", "paddingH", "extraPaddingH", "realTextWidth", "availableWidth", "forceRecalculate", "style", "textRect", "bgColor", "overflow", "oldOuterWidth", "oldHeight", "innerRect", "availableInnerWidth", "newWidth", "newRect", "margin", "sectorShape", "pieLabelLayout", "hasLabelRotate", "minShowLabelRadian", "setNotShow", "isLabelShown", "sector", "labelModel", "labelPosition", "labelDistance", "labelAlignTo", "edgeDistance", "<PERSON><PERSON><PERSON><PERSON>", "labelLineLen", "labelLineLen2", "midAngle", "nx", "ny", "textX", "textY", "textAlign", "isLabelInside", "x3", "y3", "PI", "labelRotate", "rotate", "isNumber", "radialAngle", "rad", "isDown", "selectState", "notShowLabel", "<PERSON><PERSON><PERSON><PERSON>", "_super", "__extends", "text", "graphic.Text", "firstCreate", "emphasisModel", "extend", "getSectorCornerRadius", "animationType", "graphic.initProps", "graphic.updateProps", "saveOldStyle", "setStatesStylesFromModel", "offset", "cursorStyle", "labelText", "toggleHoverEmphasis", "visualColor", "visualOpacity", "setLabelStyle", "getLabelStatesModels", "polyline", "graphic.<PERSON><PERSON><PERSON>", "retrieve3", "graphic.Sector", "<PERSON><PERSON>ie<PERSON>", "payload", "oldData", "group", "s", "<PERSON><PERSON><PERSON><PERSON>", "newIdx", "oldIdx", "graphic.removeElementWithFadeOut", "labelLayout", "itemLayout", "ChartView", "LegendVisualProvider", "getDataWithEncodedVisual", "getRawData", "rawData", "dataWithEncodedVisual", "innerData", "modelUtil.makeInner", "PieSeriesModel", "option", "zrUtil.bind", "createSeriesDataSimply", "zrUtil.curry", "makeSeriesEncodeForNameBased", "dataInner", "seats", "valueList_1", "getPercentSeats", "modelUtil.defaultEmphasis", "labelLineNormalOpt", "labelLineEmphasisOpt", "SeriesModel", "negativeDataFilter", "curValue", "install", "createLegacyDataSelectAction", "curry", "use", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DatasetComponent", "LegendComponent", "TitleComponent", "TooltipComponent", "LabelLayout", "balanceChart", "ref", "useElementSize", "props", "__props", "settingsStore", "useSettingsStore", "balanceChartOptions", "computed", "formatPriceCurrency", "botStore", "useBotStore", "hideSmallBalances", "showBotOnly", "smallBalance", "canUseBotBalance", "balanceCurrencies", "v", "formatCurrency", "formatPrice", "chartValues", "tableFields", "refreshBalance", "onMounted"], "mappings": "o4BAmDA,IAAIA,GAAM,KAAK,GAAK,EAChBC,EAAMC,GAAU,IAChBC,GAAuB,CAAC,MAAO,QAAS,SAAU,MAAM,EAC5D,SAASC,GAAmBC,EAAKC,EAAUC,EAAMC,EAAOC,EAAQ,CAC9D,IAAIC,EAAQH,EAAK,MACbI,EAASJ,EAAK,OAClB,OAAQF,EAAG,CACT,IAAK,MACHG,EAAM,IAAID,EAAK,EAAIG,EAAQ,EAAGH,EAAK,EAAID,CAAQ,EAC/CG,EAAO,IAAI,EAAG,EAAE,EAChB,MACF,IAAK,SACHD,EAAM,IAAID,EAAK,EAAIG,EAAQ,EAAGH,EAAK,EAAII,EAASL,CAAQ,EACxDG,EAAO,IAAI,EAAG,CAAC,EACf,MACF,IAAK,OACHD,EAAM,IAAID,EAAK,EAAID,EAAUC,EAAK,EAAII,EAAS,CAAC,EAChDF,EAAO,IAAI,GAAI,CAAC,EAChB,MACF,IAAK,QACHD,EAAM,IAAID,EAAK,EAAIG,EAAQJ,EAAUC,EAAK,EAAII,EAAS,CAAC,EACxDF,EAAO,IAAI,EAAG,CAAC,EACf,KACH,CACH,CACA,SAASG,GAAkBC,EAAIC,EAAIC,EAAGC,EAAYC,EAAUC,EAAeC,EAAGC,EAAGC,EAAK,CACpFF,GAAKN,EACLO,GAAKN,EACL,IAAIQ,EAAI,KAAK,KAAKH,EAAIA,EAAIC,EAAIA,CAAC,EAC/BD,GAAKG,EACLF,GAAKE,EAEL,IAAIC,EAAKJ,EAAIJ,EAAIF,EACbW,EAAKJ,EAAIL,EAAID,EACjB,GAAI,KAAK,IAAIE,EAAaC,CAAQ,EAAIjB,GAAM,KAE1C,OAAAqB,EAAI,CAAC,EAAIE,EACTF,EAAI,CAAC,EAAIG,EACFF,EAAIP,EAEb,GAAIG,EAAe,CACjB,IAAIO,EAAMT,EACVA,EAAaU,GAAgBT,CAAQ,EACrCA,EAAWS,GAAgBD,CAAG,CAClC,MACIT,EAAaU,GAAgBV,CAAU,EACvCC,EAAWS,GAAgBT,CAAQ,EAEjCD,EAAaC,IACfA,GAAYjB,IAEd,IAAI2B,EAAQ,KAAK,MAAMP,EAAGD,CAAC,EAI3B,GAHIQ,EAAQ,IACVA,GAAS3B,IAEP2B,GAASX,GAAcW,GAASV,GAAYU,EAAQ3B,IAAOgB,GAAcW,EAAQ3B,IAAOiB,EAE1F,OAAAI,EAAI,CAAC,EAAIE,EACTF,EAAI,CAAC,EAAIG,EACFF,EAAIP,EAEb,IAAIa,EAAKb,EAAI,KAAK,IAAIC,CAAU,EAAIH,EAChCgB,EAAKd,EAAI,KAAK,IAAIC,CAAU,EAAIF,EAChCgB,EAAKf,EAAI,KAAK,IAAIE,CAAQ,EAAIJ,EAC9BkB,EAAKhB,EAAI,KAAK,IAAIE,CAAQ,EAAIH,EAC9BkB,GAAMJ,EAAKT,IAAMS,EAAKT,IAAMU,EAAKT,IAAMS,EAAKT,GAC5Ca,GAAMH,EAAKX,IAAMW,EAAKX,IAAMY,EAAKX,IAAMW,EAAKX,GAChD,OAAIY,EAAKC,GACPZ,EAAI,CAAC,EAAIO,EACTP,EAAI,CAAC,EAAIQ,EACF,KAAK,KAAKG,CAAE,IAEnBX,EAAI,CAAC,EAAIS,EACTT,EAAI,CAAC,EAAIU,EACF,KAAK,KAAKE,CAAE,EAEvB,CACA,SAASC,GAAmBN,EAAIC,EAAIC,EAAIC,EAAIZ,EAAGC,EAAGC,EAAKc,EAAa,CAClE,IAAIC,EAAKjB,EAAIS,EACTS,EAAKjB,EAAIS,EACTS,EAAMR,EAAKF,EACXW,EAAMR,EAAKF,EACXW,EAAU,KAAK,KAAKF,EAAMA,EAAMC,EAAMA,CAAG,EAC7CD,GAAOE,EACPD,GAAOC,EAEP,IAAIC,EAAeL,EAAKE,EAAMD,EAAKE,EAC/BG,EAAID,EAAeD,EACnBL,IACFO,EAAI,KAAK,IAAI,KAAK,IAAIA,EAAG,CAAC,EAAG,CAAC,GAEhCA,GAAKF,EACL,IAAIjB,EAAKF,EAAI,CAAC,EAAIO,EAAKc,EAAIJ,EACvBd,EAAKH,EAAI,CAAC,EAAIQ,EAAKa,EAAIH,EAC3B,OAAO,KAAK,MAAMhB,EAAKJ,IAAMI,EAAKJ,IAAMK,EAAKJ,IAAMI,EAAKJ,EAAE,CAC5D,CACA,SAASuB,GAAmBf,EAAIC,EAAInB,EAAOC,EAAQQ,EAAGC,EAAGC,EAAK,CACxDX,EAAQ,IACVkB,EAAKA,EAAKlB,EACVA,EAAQ,CAACA,GAEPC,EAAS,IACXkB,EAAKA,EAAKlB,EACVA,EAAS,CAACA,GAEZ,IAAImB,EAAKF,EAAKlB,EACVqB,EAAKF,EAAKlB,EACVY,EAAKF,EAAI,CAAC,EAAI,KAAK,IAAI,KAAK,IAAIF,EAAGS,CAAE,EAAGE,CAAE,EAC1CN,EAAKH,EAAI,CAAC,EAAI,KAAK,IAAI,KAAK,IAAID,EAAGS,CAAE,EAAGE,CAAE,EAC9C,OAAO,KAAK,MAAMR,EAAKJ,IAAMI,EAAKJ,IAAMK,EAAKJ,IAAMI,EAAKJ,EAAE,CAC5D,CACA,IAAIwB,EAAQ,CAAA,EACZ,SAASC,GAAmBC,EAAIvC,EAAMc,EAAK,CACzC,IAAI0B,EAAOJ,GAAmBpC,EAAK,EAAGA,EAAK,EAAGA,EAAK,MAAOA,EAAK,OAAQuC,EAAG,EAAGA,EAAG,EAAGF,CAAK,EACxF,OAAAvB,EAAI,IAAIuB,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EACnBG,CACT,CAKA,SAASC,GAAmBF,EAAIG,EAAM5B,EAAK,CAWzC,QAVI6B,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLzB,EACAC,EACAyB,EAAU,IACVC,EAAON,EAAK,KACZ9B,EAAI2B,EAAG,EACP1B,EAAI0B,EAAG,EACFU,EAAI,EAAGA,EAAID,EAAK,QAAS,CAChC,IAAIE,EAAMF,EAAKC,GAAG,EACdA,IAAM,IACRN,EAAKK,EAAKC,CAAC,EACXL,EAAKI,EAAKC,EAAI,CAAC,EACfJ,EAAKF,EACLG,EAAKF,GAEP,IAAI,EAAIG,EACR,OAAQG,EAAG,CACT,KAAKxD,EAAI,EAGPmD,EAAKG,EAAKC,GAAG,EACbH,EAAKE,EAAKC,GAAG,EACbN,EAAKE,EACLD,EAAKE,EACL,MACF,KAAKpD,EAAI,EACP,EAAIiC,GAAmBgB,EAAIC,EAAII,EAAKC,CAAC,EAAGD,EAAKC,EAAI,CAAC,EAAGrC,EAAGC,EAAGwB,EAAO,EAAI,EACtEM,EAAKK,EAAKC,GAAG,EACbL,EAAKI,EAAKC,GAAG,EACb,MACF,KAAKvD,EAAI,EACP,EAAIyD,GAAkBR,EAAIC,EAAII,EAAKC,GAAG,EAAGD,EAAKC,GAAG,EAAGD,EAAKC,GAAG,EAAGD,EAAKC,GAAG,EAAGD,EAAKC,CAAC,EAAGD,EAAKC,EAAI,CAAC,EAAGrC,EAAGC,EAAGwB,CAAK,EAC3GM,EAAKK,EAAKC,GAAG,EACbL,EAAKI,EAAKC,GAAG,EACb,MACF,KAAKvD,EAAI,EACP,EAAI0D,GAAsBT,EAAIC,EAAII,EAAKC,GAAG,EAAGD,EAAKC,GAAG,EAAGD,EAAKC,CAAC,EAAGD,EAAKC,EAAI,CAAC,EAAGrC,EAAGC,EAAGwB,CAAK,EACzFM,EAAKK,EAAKC,GAAG,EACbL,EAAKI,EAAKC,GAAG,EACb,MACF,KAAKvD,EAAI,EAEP,IAAIY,EAAK0C,EAAKC,GAAG,EACb1C,EAAKyC,EAAKC,GAAG,EACbI,EAAKL,EAAKC,GAAG,EACbK,EAAKN,EAAKC,GAAG,EACbM,EAAQP,EAAKC,GAAG,EAChBO,EAASR,EAAKC,GAAG,EAErBA,GAAK,EACL,IAAItC,EAAgB,CAAC,EAAE,EAAIqC,EAAKC,GAAG,GACnC5B,EAAK,KAAK,IAAIkC,CAAK,EAAIF,EAAK/C,EAC5BgB,EAAK,KAAK,IAAIiC,CAAK,EAAID,EAAK/C,EAExB0C,GAAK,IAEPJ,EAAKxB,EACLyB,EAAKxB,GAGP,IAAImC,GAAM7C,EAAIN,GAAMgD,EAAKD,EAAK/C,EAC9B,EAAID,GAAkBC,EAAIC,EAAI+C,EAAIC,EAAOA,EAAQC,EAAQ7C,EAAe8C,EAAI5C,EAAGwB,CAAK,EACpFM,EAAK,KAAK,IAAIY,EAAQC,CAAM,EAAIH,EAAK/C,EACrCsC,EAAK,KAAK,IAAIW,EAAQC,CAAM,EAAIF,EAAK/C,EACrC,MACF,KAAKb,EAAI,EACPmD,EAAKF,EAAKK,EAAKC,GAAG,EAClBH,EAAKF,EAAKI,EAAKC,GAAG,EAClB,IAAI9C,EAAQ6C,EAAKC,GAAG,EAChB7C,EAAS4C,EAAKC,GAAG,EACrB,EAAIb,GAAmBS,EAAIC,EAAI3C,EAAOC,EAAQQ,EAAGC,EAAGwB,CAAK,EACzD,MACF,KAAK3C,EAAI,EACP,EAAIiC,GAAmBgB,EAAIC,EAAIC,EAAIC,EAAIlC,EAAGC,EAAGwB,EAAO,EAAI,EACxDM,EAAKE,EACLD,EAAKE,EACL,KACH,CACG,EAAIC,IACNA,EAAU,EACVjC,EAAI,IAAIuB,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EAE7B,CACD,OAAOU,CACT,CAEA,IAAIW,EAAM,IAAIC,EACVC,EAAM,IAAID,EACVE,EAAM,IAAIF,EACVG,EAAM,IAAIH,EACVI,EAAO,IAAIJ,EAQR,SAASK,GAAsBC,EAAQC,EAAgB,CAC5D,GAAKD,EAGL,KAAIE,EAAYF,EAAO,mBACnBG,EAAQH,EAAO,iBAEnB,GAAMG,GAASD,EAGf,KAAIE,EAAmBJ,EAAO,qBAAuB,GACjDK,EAAS,CAAC,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,CAAC,EAChCC,EAAcF,EAAiB,YAAczE,GAC7C4E,EAAYJ,EAAM,gBAAiB,EAAC,MAAK,EAC7CI,EAAU,eAAeJ,EAAM,qBAAsB,CAAA,EACrD,IAAIrB,EAAU,IACV0B,EAAcJ,EAAiB,OAC/BK,EAAkBT,EAAO,uBACzBU,EAA0BD,GAAmBE,GAAO,CAAE,EAAEF,CAAe,EACvEG,EAAMX,EAAe,IAAI,SAAS,GAAK,EACvCO,GACFZ,EAAI,KAAKY,CAAW,EAEtB,QAASxB,EAAI,EAAGA,EAAIsB,EAAY,OAAQtB,IAAK,CAC3C,IAAI6B,EAAYP,EAAYtB,CAAC,EAC7BpD,GAAmBiF,EAAW,EAAGN,EAAWd,EAAKI,CAAG,EACpDH,EAAM,YAAYC,EAAKF,EAAKI,EAAKe,CAAG,EAEpCjB,EAAI,UAAUe,CAAuB,EAErC,IAAII,EAAed,EAAO,kBACtBzB,EAAOiC,EAAcA,EAAY,SAASb,CAAG,EAAIK,aAAkBe,GAAOvC,GAAmBmB,EAAKK,EAAO,KAAMJ,CAAG,EAAIvB,GAAmBsB,EAAKmB,EAAclB,CAAG,EAE/JrB,EAAOO,IACTA,EAAUP,EAEVoB,EAAI,UAAUc,CAAe,EAC7Bb,EAAI,UAAUa,CAAe,EAC7Bb,EAAI,QAAQS,EAAO,CAAC,CAAC,EACrBV,EAAI,QAAQU,EAAO,CAAC,CAAC,EACrBZ,EAAI,QAAQY,EAAO,CAAC,CAAC,EAExB,CACDW,GAAeX,EAAQJ,EAAe,IAAI,cAAc,CAAC,EACzDC,EAAU,SAAS,CACjB,OAAQG,CACZ,CAAG,GACH,CAEA,IAAIY,GAAS,CAAA,EACTC,EAAe,IAAIxB,EAMhB,SAASsB,GAAeG,EAAYC,EAAc,CACvD,GAAMA,GAAgB,KAAOA,EAAe,EAG5C,CAAAA,EAAeA,EAAe,IAAM,KAAK,GAKzC3B,EAAI,UAAU0B,EAAW,CAAC,CAAC,EAC3BxB,EAAI,UAAUwB,EAAW,CAAC,CAAC,EAC3BvB,EAAI,UAAUuB,EAAW,CAAC,CAAC,EAC3BzB,EAAM,IAAIG,EAAKJ,EAAKE,CAAG,EACvBD,EAAM,IAAII,EAAMF,EAAKD,CAAG,EACxB,IAAI0B,EAAOxB,EAAI,MACXyB,EAAOxB,EAAK,MAChB,GAAI,EAAAuB,EAAO,MAAQC,EAAO,MAG1B,CAAAzB,EAAI,MAAM,EAAIwB,CAAI,EAClBvB,EAAK,MAAM,EAAIwB,CAAI,EACnB,IAAIC,EAAW1B,EAAI,IAAIC,CAAI,EACvB0B,EAAkB,KAAK,IAAIJ,CAAY,EAC3C,GAAII,EAAkBD,EAAU,CAG9B,IAAIzE,EAAIY,GAAmBiC,EAAI,EAAGA,EAAI,EAAGC,EAAI,EAAGA,EAAI,EAAGH,EAAI,EAAGA,EAAI,EAAGwB,GAAQ,EAAK,EAClFC,EAAa,UAAUD,EAAM,EAE7BC,EAAa,YAAYpB,EAAMhD,EAAI,KAAK,IAAI,KAAK,GAAKsE,CAAY,CAAC,EAEnE,IAAIlD,EAAI0B,EAAI,IAAMD,EAAI,GAAKuB,EAAa,EAAIvB,EAAI,IAAMC,EAAI,EAAID,EAAI,IAAMuB,EAAa,EAAIvB,EAAI,IAAMC,EAAI,EAAID,EAAI,GAC/G,GAAI,MAAMzB,CAAC,EACT,OAEEA,EAAI,EACNwB,EAAM,KAAKwB,EAAcvB,CAAG,EACnBzB,EAAI,GACbwB,EAAM,KAAKwB,EAActB,CAAG,EAE9BsB,EAAa,QAAQC,EAAW,CAAC,CAAC,CACnC,GACH,CAKO,SAASM,GAAkBN,EAAYO,EAAeC,EAAiB,CAC5E,GAAMA,GAAmB,KAAOA,EAAkB,EAGlD,CAAAA,EAAkBA,EAAkB,IAAM,KAAK,GAC/ClC,EAAI,UAAU0B,EAAW,CAAC,CAAC,EAC3BxB,EAAI,UAAUwB,EAAW,CAAC,CAAC,EAC3BvB,EAAI,UAAUuB,EAAW,CAAC,CAAC,EAC3BzB,EAAM,IAAIG,EAAKF,EAAKF,CAAG,EACvBC,EAAM,IAAII,EAAMF,EAAKD,CAAG,EACxB,IAAI0B,EAAOxB,EAAI,MACXyB,EAAOxB,EAAK,MAChB,GAAI,EAAAuB,EAAO,MAAQC,EAAO,MAG1B,CAAAzB,EAAI,MAAM,EAAIwB,CAAI,EAClBvB,EAAK,MAAM,EAAIwB,CAAI,EACnB,IAAIC,EAAW1B,EAAI,IAAI6B,CAAa,EAChCE,EAAqB,KAAK,IAAID,CAAe,EACjD,GAAIJ,EAAWK,EAAoB,CAEjC,IAAI9E,EAAIY,GAAmBiC,EAAI,EAAGA,EAAI,EAAGC,EAAI,EAAGA,EAAI,EAAGH,EAAI,EAAGA,EAAI,EAAGwB,GAAQ,EAAK,EAClFC,EAAa,UAAUD,EAAM,EAC7B,IAAIY,EAAU,KAAK,GAAK,EACpBC,EAAS,KAAK,KAAKhC,EAAK,IAAI4B,CAAa,CAAC,EAC1CK,EAAWF,EAAUC,EAASH,EAClC,GAAII,GAAYF,EAEdnC,EAAM,KAAKwB,EAActB,CAAG,MACvB,CAELsB,EAAa,YAAYpB,EAAMhD,EAAI,KAAK,IAAI,KAAK,GAAK,EAAIiF,CAAQ,CAAC,EAEnE,IAAI7D,EAAI0B,EAAI,IAAMD,EAAI,GAAKuB,EAAa,EAAIvB,EAAI,IAAMC,EAAI,EAAID,EAAI,IAAMuB,EAAa,EAAIvB,EAAI,IAAMC,EAAI,EAAID,EAAI,GAC/G,GAAI,MAAMzB,CAAC,EACT,OAEEA,EAAI,EACNwB,EAAM,KAAKwB,EAAcvB,CAAG,EACnBzB,EAAI,GACbwB,EAAM,KAAKwB,EAActB,CAAG,CAE/B,CACDsB,EAAa,QAAQC,EAAW,CAAC,CAAC,CACnC,GACH,CACA,SAASa,GAAkB9B,EAAW+B,EAAQC,EAAWC,EAAY,CACnE,IAAIC,EAAWF,IAAc,SACzBG,EAAWD,EAAWlC,EAAYA,EAAU,YAAYgC,CAAS,EAErEG,EAAS,OAASJ,EAElB,IAAIK,EAASH,EAAW,IAAI,QAAQ,EAChCG,GAAUA,IAAW,KACvBA,EAAS,IAEXD,EAAS,MAAQA,EAAS,OAAS,CAAA,EAC/BC,EAAS,IACXD,EAAS,MAAM,OAASC,GAE1B,IAAIC,EAAWJ,EAAW,SAAS,WAAW,EAAE,aAAY,EAC5DC,EAAWlC,EAAU,SAASqC,CAAQ,EAAIF,EAAS,MAAQE,CAC7D,CACA,SAASC,GAAmB/D,EAAMgE,EAAO,CACvC,IAAIH,EAASG,EAAM,OACfpC,EAASoC,EAAM,OACnB,GAAKpC,EAIL,GADA5B,EAAK,OAAO4B,EAAO,CAAC,EAAE,CAAC,EAAGA,EAAO,CAAC,EAAE,CAAC,CAAC,EAClCiC,EAAS,GAAKjC,EAAO,QAAU,EAAG,CACpC,IAAIgB,EAAOqB,GAAYrC,EAAO,CAAC,EAAGA,EAAO,CAAC,CAAC,EACvCiB,EAAOoB,GAAYrC,EAAO,CAAC,EAAGA,EAAO,CAAC,CAAC,EAC3C,GAAI,CAACgB,GAAQ,CAACC,EAAM,CAClB7C,EAAK,OAAO4B,EAAO,CAAC,EAAE,CAAC,EAAGA,EAAO,CAAC,EAAE,CAAC,CAAC,EACtC5B,EAAK,OAAO4B,EAAO,CAAC,EAAE,CAAC,EAAGA,EAAO,CAAC,EAAE,CAAC,CAAC,EACtC,MACD,CACD,IAAIsC,EAAU,KAAK,IAAItB,EAAMC,CAAI,EAAIgB,EACjCM,EAAYC,GAAY,CAAE,EAAExC,EAAO,CAAC,EAAGA,EAAO,CAAC,EAAGsC,EAAUtB,CAAI,EAChEyB,EAAYD,GAAY,CAAE,EAAExC,EAAO,CAAC,EAAGA,EAAO,CAAC,EAAGsC,EAAUrB,CAAI,EAChEyB,EAAYF,GAAY,CAAA,EAAID,EAAWE,EAAW,EAAG,EACzDrE,EAAK,cAAcmE,EAAU,CAAC,EAAGA,EAAU,CAAC,EAAGA,EAAU,CAAC,EAAGA,EAAU,CAAC,EAAGG,EAAU,CAAC,EAAGA,EAAU,CAAC,CAAC,EACrGtE,EAAK,cAAcqE,EAAU,CAAC,EAAGA,EAAU,CAAC,EAAGA,EAAU,CAAC,EAAGA,EAAU,CAAC,EAAGzC,EAAO,CAAC,EAAE,CAAC,EAAGA,EAAO,CAAC,EAAE,CAAC,CAAC,CACzG,KACI,SAASrB,EAAI,EAAGA,EAAIqB,EAAO,OAAQrB,IACjCP,EAAK,OAAO4B,EAAOrB,CAAC,EAAE,CAAC,EAAGqB,EAAOrB,CAAC,EAAE,CAAC,CAAC,CAG5C,CAIO,SAASgE,GAAkBC,EAAUC,EAAcC,EAAc,CACtE,IAAIjD,EAAY+C,EAAS,mBACrB9C,EAAQ8C,EAAS,iBACrB,GAAI,CAAC9C,EAAO,CAEND,GACF+C,EAAS,oBAAmB,EAE9B,MACD,CAID,QAHIG,EAAcF,EAAa,OAC3BG,EAAaD,EAAY,IAAI,MAAM,EACnCE,EAAoBnD,EAAM,OACrBnB,EAAI,EAAGA,EAAIuE,GAAe,OAAQvE,IAAK,CAC9C,IAAIkD,EAAYqB,GAAevE,CAAC,EAC5BmD,EAAae,EAAahB,CAAS,EACnCE,EAAWF,IAAc,SAC7B,GAAIC,EAAY,CACd,IAAIqB,EAAYrB,EAAW,IAAI,MAAM,EACjCsB,EAAiBrB,EAAWkB,EAAoBI,GAAUvD,EAAM,OAAO+B,CAAS,GAAK/B,EAAM,OAAO+B,CAAS,EAAE,OAAQoB,CAAiB,EAC1I,GAAIG,GACD,CAACC,GAAUF,EAAWH,CAAU,EACjC,CACA,IAAIhB,EAAWD,EAAWlC,EAAYA,GAAaA,EAAU,OAAOgC,CAAS,EACzEG,IACFA,EAAS,OAAS,IAEdnC,GACJ8B,GAAkB9B,EAAW,GAAMgC,EAAWC,CAAU,EAE1D,QACD,CAEIjC,IACHA,EAAY,IAAIyD,GAChBV,EAAS,iBAAiB/C,CAAS,EAG/B,CAACkC,IAAakB,GAAqB,CAACD,IACtCrB,GAAkB9B,EAAW,GAAM,SAAUgD,EAAa,MAAM,EAG9DD,EAAS,aACX/C,EAAU,WAAa+C,EAAS,aAGpCjB,GAAkB9B,EAAW,GAAOgC,EAAWC,CAAU,CAC1D,CACF,CACD,GAAIjC,EAAW,CACb0D,GAAS1D,EAAU,MAAOiD,CAAY,EAEtCjD,EAAU,MAAM,KAAO,KACvB,IAAI2D,EAAYT,EAAY,IAAI,WAAW,EACvCU,EAAkBb,EAAS,oBAAsBA,EAAS,qBAAuB,CAAA,EACrFa,EAAgB,UAAYD,GAAa,GAEzC3D,EAAU,UAAYsC,EACvB,CACH,CACO,SAASuB,GAAyBC,EAAWC,EAAe,CACjEA,EAAgBA,GAAiB,YAIjC,QAHIf,EAAe,CACjB,OAAQc,EAAU,SAASC,CAAa,CAC5C,EACWjF,EAAI,EAAGA,EAAIkF,GAAe,OAAQlF,IAAK,CAC9C,IAAIkD,EAAYgC,GAAelF,CAAC,EAChCkE,EAAahB,CAAS,EAAI8B,EAAU,SAAS,CAAC9B,EAAW+B,CAAa,CAAC,CACxE,CACD,OAAOf,CACT,CCteA,SAASiB,GAAS9D,EAAQ,CACxB,GAAIA,EAAQ,CAEV,QADI+D,EAAY,CAAA,EACPpF,EAAI,EAAGA,EAAIqB,EAAO,OAAQrB,IACjCoF,EAAU,KAAK/D,EAAOrB,CAAC,EAAE,MAAO,CAAA,EAElC,OAAOoF,CACR,CACH,CACA,SAASC,GAA4BC,EAAWC,EAAQ,CACtD,IAAIpE,EAAQmE,EAAU,MAClBpE,EAAYqE,GAAUA,EAAO,iBAAgB,EACjD,MAAO,CACL,UAAWD,EAAU,UACrB,SAAUA,EAAU,SACpB,YAAaA,EAAU,YAAY,YACnC,KAAMA,EAAU,MAAM,MAAM,KAC5B,KAAMA,EAAU,SAChB,UAAWA,EAAU,KAGrB,MAAOnE,EAAM,MAAM,MACnB,cAAeA,EAAM,MAAM,cAC3B,gBAAiBgE,GAASjE,GAAaA,EAAU,MAAM,MAAM,CACjE,CACA,CACA,IAAIsE,GAA6B,CAAC,QAAS,gBAAiB,QAAS,SAAU,UAAU,EACrFC,EAAqB,IAAIC,GACzBC,GAAwBC,GAAS,EACjCC,GAA0BD,GAAS,EACvC,SAASE,GAAe9E,EAAQ+E,EAAQC,EAAM,CAC5C,QAAShG,EAAI,EAAGA,EAAIgG,EAAK,OAAQhG,IAAK,CACpC,IAAIiG,EAAMD,EAAKhG,CAAC,EACZ+F,EAAOE,CAAG,GAAK,OACjBjF,EAAOiF,CAAG,EAAIF,EAAOE,CAAG,EAE3B,CACH,CACA,IAAIC,GAAqB,CAAC,IAAK,IAAK,UAAU,EAC1CC,GAA4B,UAAY,CAC1C,SAASA,GAAe,CACtB,KAAK,WAAa,GAClB,KAAK,eAAiB,EACvB,CACD,OAAAA,EAAa,UAAU,YAAc,UAAY,CAC/C,KAAK,WAAa,GAClB,KAAK,eAAiB,EAC1B,EAIEA,EAAa,UAAU,UAAY,SAAUC,EAAWC,EAAUC,EAAanF,EAAOoF,EAAc,CAClG,IAAIC,EAAarF,EAAM,MACnBoE,EAASpE,EAAM,aACfsF,EAAalB,EAAO,YAAc,GAElCmB,EAAiBvF,EAAM,uBACvBI,EAAYJ,EAAM,gBAAiB,EAAC,MAAK,EAC7CwF,GAAa,eAAepF,EAAWA,EAAWmF,CAAc,EAC5DA,EACFjB,EAAmB,kBAAkBiB,CAAc,GAGnDjB,EAAmB,EAAIA,EAAmB,EAAIA,EAAmB,SAAWA,EAAmB,QAAUA,EAAmB,QAAU,EACtIA,EAAmB,OAASA,EAAmB,OAAS,GAE1DA,EAAmB,SAAWvH,GAAgBuH,EAAmB,QAAQ,EACzE,IAAImB,EAAOzF,EAAM,aACb0F,EACJ,GAAID,EAAM,CACRC,EAAWD,EAAK,gBAAiB,EAAC,MAAK,EACvC,IAAIE,EAAYF,EAAK,uBACrBD,GAAa,eAAeE,EAAUA,EAAUC,CAAS,CAC1D,CACD,IAAIC,EAAaF,GAAYD,EAAK,iBAAgB,EAClD,KAAK,WAAW,KAAK,CACnB,MAAOzF,EACP,UAAW4F,EACX,YAAaT,EACb,UAAWF,EACX,SAAUC,EACV,aAAcE,EACd,qBAAsB,KACtB,KAAMhF,EACN,SAAUsF,EAGV,SAAUA,EAAWA,EAAS,MAAQA,EAAS,OAAS,EAGxD,YAAa,CACX,OAAQ1F,EAAM,OACd,iBAAkB4F,GAAcA,EAAW,OAC3C,EAAGtB,EAAmB,EACtB,EAAGA,EAAmB,EACtB,OAAQA,EAAmB,OAC3B,OAAQA,EAAmB,OAC3B,SAAUA,EAAmB,SAC7B,MAAO,CACL,EAAGe,EAAW,EACd,EAAGA,EAAW,EACd,MAAOA,EAAW,MAClB,cAAeA,EAAW,cAC1B,MAAOA,EAAW,MAClB,OAAQA,EAAW,OACnB,SAAUA,EAAW,QACtB,EACD,OAAQrF,EAAM,OACd,YAAasF,EAAW,SACxB,YAAaA,EAAW,QACzB,CACP,CAAK,CACL,EACEN,EAAa,UAAU,kBAAoB,SAAUa,EAAW,CAC9D,IAAIC,EAAQ,KACZ,KAAK,eAAe,KAAKD,CAAS,EAClC,IAAIV,EAAcU,EAAU,QACxBT,EAAeD,EAAY,IAAI,aAAa,GAI1CY,GAAWX,CAAY,GAAKP,GAAKO,CAAY,EAAE,SAGrDS,EAAU,MAAM,SAAS,SAAUG,EAAO,CACxC,GAAIA,EAAM,OACR,MAAO,GAGT,IAAIC,EAASD,EAAM,iBACfE,EAASC,GAAUH,CAAK,EAExBC,GAAU,CAACA,EAAO,oBACpBH,EAAM,UAAUI,EAAO,UAAWA,EAAO,SAAUf,EAAac,EAAQb,CAAY,CAE5F,CAAK,CACL,EACEJ,EAAa,UAAU,mBAAqB,SAAUoB,EAAK,CACzD,IAAIrK,EAAQqK,EAAI,WACZpK,EAASoK,EAAI,YACjB,SAASC,EAAkBC,EAAIxG,EAAgB,CAC7C,OAAO,UAAY,CACjBF,GAAsB0G,EAAIxG,CAAc,CAChD,CACK,CACD,QAASjB,EAAI,EAAGA,EAAI,KAAK,WAAW,OAAQA,IAAK,CAC/C,IAAIsF,EAAY,KAAK,WAAWtF,CAAC,EAC7BmB,EAAQmE,EAAU,MAClBC,EAASpE,EAAM,aACfuG,EAAmBpC,EAAU,YAC7BiB,EAAe,OAEfW,GAAW5B,EAAU,YAAY,EACnCiB,EAAejB,EAAU,aAAaD,GAA4BC,EAAWC,CAAM,CAAC,EAEpFgB,EAAejB,EAAU,aAE3BiB,EAAeA,GAAgB,GAC/BjB,EAAU,qBAAuBiB,EACjC,IAAIoB,EAAiB,KAAK,GAAK,IAG3BpC,GACFA,EAAO,cAAc,CAEnB,MAAO,GAEP,SAAUgB,EAAa,GAAK,MAAQA,EAAa,GAAK,KAAO,KAAOmB,EAAiB,YAErF,SAAUnB,EAAa,QAAU,KAAOA,EAAa,OAASoB,EAAiBD,EAAiB,YAChG,OAAQ,CAACnB,EAAa,IAAM,EAAGA,EAAa,IAAM,CAAC,CAC7D,CAAS,EAEH,IAAIqB,EAAuB,GAmB3B,GAlBIrB,EAAa,GAAK,MAEpBpF,EAAM,EAAI0G,EAAatB,EAAa,EAAGrJ,CAAK,EAC5CiE,EAAM,SAAS,IAAK,CAAC,EACrByG,EAAuB,KAEvBzG,EAAM,EAAIuG,EAAiB,EAC3BvG,EAAM,SAAS,IAAKuG,EAAiB,MAAM,CAAC,GAE1CnB,EAAa,GAAK,MAEpBpF,EAAM,EAAI0G,EAAatB,EAAa,EAAGpJ,CAAM,EAC7CgE,EAAM,SAAS,IAAK,CAAC,EACrByG,EAAuB,KAEvBzG,EAAM,EAAIuG,EAAiB,EAC3BvG,EAAM,SAAS,IAAKuG,EAAiB,MAAM,CAAC,GAE1CnB,EAAa,gBAAiB,CAChC,IAAIuB,EAAYvC,EAAO,mBACnBuC,IACFA,EAAU,SAAS,CACjB,OAAQvB,EAAa,eACjC,CAAW,EAEDqB,EAAuB,GAE1B,CACD,IAAIG,EAAmBpC,GAAsBxE,CAAK,EAClD4G,EAAiB,qBAAuBH,EACxCzG,EAAM,SAAWoF,EAAa,QAAU,KAAOA,EAAa,OAASoB,EAAiBD,EAAiB,SACvGvG,EAAM,OAASuG,EAAiB,OAChCvG,EAAM,OAASuG,EAAiB,OAChC,QAASM,EAAI,EAAGA,EAAIxC,GAA2B,OAAQwC,IAAK,CAC1D,IAAI/B,EAAMT,GAA2BwC,CAAC,EACtC7G,EAAM,SAAS8E,EAAKM,EAAaN,CAAG,GAAK,KAAOM,EAAaN,CAAG,EAAIyB,EAAiB,MAAMzB,CAAG,CAAC,CAChG,CACD,GAAIM,EAAa,WAGf,GAFApF,EAAM,UAAY,GAClBA,EAAM,OAAS,OACXoE,EAAQ,CACV,IAAI0C,EAAY3C,EAAU,YAC1B,GAAIA,EAAU,WAAa,KAAM,CAC/B,IAAIvF,EAAOuF,EAAU,YAAY,QAAQA,EAAU,QAAQ,EAC3D2C,EAAYlI,EAAK,aAAauF,EAAU,SAAS,CAClD,CACDnE,EAAM,GAAG,OAAQqG,EAAkBjC,EAAQ0C,EAAU,SAAS,WAAW,CAAC,CAAC,CAC5E,OAGD9G,EAAM,IAAI,MAAM,EAChBA,EAAM,OAASuG,EAAiB,MAEnC,CACL,EACEvB,EAAa,UAAU,OAAS,SAAUoB,EAAK,CAC7C,IAAIrK,EAAQqK,EAAI,WACZpK,EAASoK,EAAI,YACbW,EAAYC,GAAkB,KAAK,UAAU,EAC7CC,EAAuBC,GAAOH,EAAW,SAAUI,EAAM,CAC3D,OAAOA,EAAK,aAAa,cAAgB,QAC/C,CAAK,EACGC,EAAuBF,GAAOH,EAAW,SAAUI,EAAM,CAC3D,OAAOA,EAAK,aAAa,cAAgB,QAC/C,CAAK,EACDE,GAAeJ,EAAsB,EAAGlL,CAAK,EAC7CuL,GAAeF,EAAsB,EAAGpL,CAAM,EAC9C,IAAIuL,EAAyBL,GAAOH,EAAW,SAAUI,EAAM,CAC7D,OAAOA,EAAK,aAAa,WAC/B,CAAK,EACDK,GAAYD,CAAsB,CACtC,EAIEvC,EAAa,UAAU,qBAAuB,UAAY,CACxD,IAAIc,EAAQ,KACZ2B,GAAK,KAAK,eAAgB,SAAU5B,EAAW,CAC7C,IAAIV,EAAcU,EAAU,QACxB6B,EAAwB7B,EAAU,sBAClC8B,EAAmBxC,EAAY,qBACnCU,EAAU,MAAM,SAAS,SAAUG,EAAO,CACxC,GAAIA,EAAM,QAAU,CAACA,EAAM,oBACzB,MAAO,GAGT,IAAIS,EAAuB,CAACiB,EACxB1H,EAAQgG,EAAM,iBACd,CAACS,GAAwBzG,IAC3ByG,EAAuBjC,GAAsBxE,CAAK,EAAE,sBAElDyG,GACFX,EAAM,iBAAiBE,EAAOb,CAAW,EAEvCwC,GACF7B,EAAM,eAAeE,EAAOb,CAAW,CAEjD,CAAO,CACP,CAAK,CACL,EACEH,EAAa,UAAU,iBAAmB,SAAUsB,EAAInB,EAAa,CAEnE,IAAIc,EAASK,EAAG,iBAEZJ,EAASC,GAAUG,CAAE,EACrBrB,EAAYiB,EAAO,UAEvB,GAAID,GAAUhB,GAAa,KAAM,CAC/B,IAAIrG,EAAOuG,EAAY,QAAQe,EAAO,QAAQ,EAC1CrC,EAAYjF,EAAK,aAAaqG,CAAS,EACvCjC,EAAe,CAAA,EACf4E,EAAchJ,EAAK,cAAcqG,EAAW,OAAO,EACvD,GAAI2C,EAAa,CACf,IAAIC,EAAajJ,EAAK,UAAU,UAAU,EAE1CoE,EAAa,OAAS4E,EAAYC,CAAU,CAC7C,CACD,IAAI/H,EAAiB+D,EAAU,SAAS,WAAW,EACnDhB,GAAkByD,EAAI1C,GAAyBC,CAAS,EAAGb,CAAY,EACvEpD,GAAsB0G,EAAIxG,CAAc,CACzC,CACL,EACEkF,EAAa,UAAU,eAAiB,SAAUsB,EAAInB,EAAa,CACjE,IAAIc,EAASK,EAAG,iBACZK,EAAYL,EAAG,mBAEnB,GAAIL,IAEAK,EAAG,qBAAuB,CAACL,EAAO,QAAU,CAACA,EAAO,WAAa,CAACK,EAAG,uBAAyB,CAACwB,GAAiBxB,CAAE,GAAI,CACxH,IAAIyB,EAAcvD,GAAsByB,CAAM,EAC1C+B,EAAYD,EAAY,UACxB7B,EAASC,GAAUG,CAAE,EACrBrB,EAAYiB,EAAO,UACnB+B,EAAW,CACb,EAAGhC,EAAO,EACV,EAAGA,EAAO,EACV,SAAUA,EAAO,QACzB,EACUrH,EAAOuG,EAAY,QAAQe,EAAO,QAAQ,EAC9C,GAAK8B,EAaE,CACL/B,EAAO,KAAK+B,CAAS,EAErB,IAAIE,EAAa5B,EAAG,WAChB4B,IACEC,GAAQD,EAAY,QAAQ,GAAK,GACnCjC,EAAO,KAAK8B,EAAY,eAAe,EAErCI,GAAQD,EAAY,UAAU,GAAK,GACrCjC,EAAO,KAAK8B,EAAY,iBAAiB,GAG7CK,GAAYnC,EAAQgC,EAAU9C,EAAaF,CAAS,CACrD,SAzBCgB,EAAO,KAAKgC,CAAQ,EAEhB,CAACI,GAAWpC,CAAM,EAAE,eAAgB,CACtC,IAAIqC,EAAa/E,GAAU0C,EAAO,MAAM,QAAS,CAAC,EAElDA,EAAO,MAAM,QAAU,EACvBsC,GAAUtC,EAAQ,CAChB,MAAO,CACL,QAASqC,CACV,CACb,EAAanD,EAAaF,CAAS,CAC1B,CAgBH,GADA8C,EAAY,UAAYE,EACpBhC,EAAO,OAAO,OAAQ,CACxB,IAAIuC,EAAeT,EAAY,gBAAkB,GACjDpD,GAAe6D,EAAcP,EAAUlD,EAAkB,EACzDJ,GAAe6D,EAAcvC,EAAO,OAAO,OAAQlB,EAAkB,CACtE,CACD,GAAIkB,EAAO,OAAO,SAAU,CAC1B,IAAIwC,EAAiBV,EAAY,kBAAoB,GACrDpD,GAAe8D,EAAgBR,EAAUlD,EAAkB,EAC3DJ,GAAe8D,EAAgBxC,EAAO,OAAO,SAAUlB,EAAkB,CAC1E,CACD2D,GAAkBzC,EAAQhB,EAAWrG,EAAMuG,EAAaA,CAAW,CACpE,CACD,GAAIwB,GAAa,CAACA,EAAU,QAAU,CAACA,EAAU,UAAW,CAC1D,IAAIoB,EAAcrD,GAAwBiC,CAAS,EAC/CqB,EAAYD,EAAY,UACxBY,EAAY,CACd,OAAQhC,EAAU,MAAM,MAChC,EACWqB,GASHrB,EAAU,KAAK,CACb,MAAOqB,CACjB,CAAS,EACDI,GAAYzB,EAAW,CACrB,MAAOgC,CACR,EAAExD,CAAW,IAbdwB,EAAU,SAASgC,CAAS,EAC5BhC,EAAU,MAAM,cAAgB,EAChC4B,GAAU5B,EAAW,CACnB,MAAO,CACL,cAAe,CAChB,CACF,EAAExB,CAAW,GAShB4C,EAAY,UAAYY,CACzB,CACL,EACS3D,CACT,EAAG,ECpYC4D,GAAkBnE,GAAS,EACxB,SAASoE,GAAmBC,EAAW,CAC5CA,EAAU,wBAAwB,sBAAuB,SAAUC,EAAS3C,EAAK4C,EAAQ,CAEvF,IAAIC,EAAeL,GAAgBxC,CAAG,EAAE,aACnC6C,IACHA,EAAeL,GAAgBxC,CAAG,EAAE,aAAe,IAAIpB,IAEzDiE,EAAa,YAAW,CAC5B,CAAG,EACDH,EAAU,wBAAwB,sBAAuB,SAAUC,EAAS3C,EAAK4C,EAAQ,CACvF,IAAIC,EAAeL,GAAgBxC,CAAG,EAAE,aACxC4C,EAAO,cAAc,QAAQ,SAAUE,EAAQ,CAC7CD,EAAa,kBAAkB7C,EAAI,qBAAqB8C,CAAM,CAAC,CACrE,CAAK,EACDD,EAAa,mBAAmB7C,CAAG,EACnC6C,EAAa,OAAO7C,CAAG,EACvB6C,EAAa,qBAAoB,CACrC,CAAG,CACH,CCjBA,IAAI5N,GAAM,KAAK,GAAK,EAChB8N,GAAS,KAAK,GAAK,IACvB,SAASC,GAAYjE,EAAaiB,EAAK,CACrC,OAAOiD,GAAqBlE,EAAY,qBAAsB,CAC5D,MAAOiB,EAAI,SAAU,EACrB,OAAQA,EAAI,UAAW,CAC3B,CAAG,CACH,CACO,SAASkD,GAAkBnE,EAAaiB,EAAK,CAClD,IAAImD,EAAWH,GAAYjE,EAAaiB,CAAG,EAEvCoD,EAASrE,EAAY,IAAI,QAAQ,EACjCsE,EAAStE,EAAY,IAAI,QAAQ,EAChCuE,GAAeD,CAAM,IACxBA,EAAS,CAAC,EAAGA,CAAM,GAErB,IAAI1N,EAAQ2K,EAAa6C,EAAS,MAAOnD,EAAI,SAAQ,CAAE,EACnDpK,EAAS0K,EAAa6C,EAAS,OAAQnD,EAAI,UAAS,CAAE,EACtDuD,EAAO,KAAK,IAAI5N,EAAOC,CAAM,EAC7B4N,EAAKlD,EAAa+C,EAAO,CAAC,EAAGE,EAAO,CAAC,EACrCvN,EAAIsK,EAAa+C,EAAO,CAAC,EAAGE,EAAO,CAAC,EACpCzN,EACAC,EACA0N,EAAW1E,EAAY,iBAC3B,GAAI0E,EAAU,CAEZ,IAAIC,EAAQD,EAAS,YAAYL,CAAM,EACvCtN,EAAK4N,EAAM,CAAC,GAAK,EACjB3N,EAAK2N,EAAM,CAAC,GAAK,CACrB,MACSJ,GAAeF,CAAM,IACxBA,EAAS,CAACA,EAAQA,CAAM,GAE1BtN,EAAKwK,EAAa8C,EAAO,CAAC,EAAGzN,CAAK,EAAIwN,EAAS,EAC/CpN,EAAKuK,EAAa8C,EAAO,CAAC,EAAGxN,CAAM,EAAIuN,EAAS,EAElD,MAAO,CACL,GAAIrN,EACJ,GAAIC,EACJ,GAAIyN,EACJ,EAAGxN,CACP,CACA,CACe,SAAS2N,GAAUC,EAAYjB,EAAS3C,EAAK,CAC1D2C,EAAQ,iBAAiBiB,EAAY,SAAU7E,EAAa,CAC1D,IAAIvG,EAAOuG,EAAY,UACnB8E,EAAWrL,EAAK,aAAa,OAAO,EACpC2K,EAAWH,GAAYjE,EAAaiB,CAAG,EACvC8D,EAAKZ,GAAkBnE,EAAaiB,CAAG,EACzClK,EAAKgO,EAAG,GACR/N,EAAK+N,EAAG,GACR9N,EAAI8N,EAAG,EACPN,EAAKM,EAAG,GACN7N,EAAa,CAAC8I,EAAY,IAAI,YAAY,EAAIgE,GAC9C7M,EAAW6I,EAAY,IAAI,UAAU,EACrCgF,EAAWhF,EAAY,IAAI,UAAU,EAAIgE,GAC7C7M,EAAWA,IAAa,OAASD,EAAahB,GAAM,CAACiB,EAAW6M,GAChE,IAAIiB,EAAWjF,EAAY,IAAI,UAAU,EAAIgE,GACzCkB,EAAiBD,EAAWD,EAC5BG,EAAiB,EACrB1L,EAAK,KAAKqL,EAAU,SAAUM,EAAO,CACnC,CAAC,MAAMA,CAAK,GAAKD,GACvB,CAAK,EACD,IAAIE,EAAM5L,EAAK,OAAOqL,CAAQ,EAE1BQ,EAAa,KAAK,IAAMD,GAAOF,GAAkB,EACjDI,EAAYvF,EAAY,IAAI,WAAW,EACvCwF,EAAWxF,EAAY,IAAI,UAAU,EACrCyF,EAAmBzF,EAAY,IAAI,kBAAkB,EAErD0F,EAASjM,EAAK,cAAcqL,CAAQ,EACxCY,EAAO,CAAC,EAAI,EACZ,IAAInL,EAAMgL,EAAY,EAAI,GACtBI,EAAS,CAACzO,EAAYC,CAAQ,EAC9ByO,EAAerL,EAAMyK,EAAW,EACpCa,GAAmBF,EAAQ,CAACJ,CAAS,EACrCrO,EAAayO,EAAO,CAAC,EAAGxO,EAAWwO,EAAO,CAAC,EAC3C,IAAIG,EAAa,KAAK,IAAI3O,EAAWD,CAAU,EAE3C6O,EAAYD,EACZE,EAA6B,EAC7BC,EAAe/O,EAyDnB,GAxDAuC,EAAK,UAAU,CACb,SAAU2K,EACV,EAAGnN,CACT,CAAK,EACDwC,EAAK,KAAKqL,EAAU,SAAUM,EAAOc,EAAK,CACxC,IAAIrO,EACJ,GAAI,MAAMuN,CAAK,EAAG,CAChB3L,EAAK,cAAcyM,EAAK,CACtB,MAAO,IACP,WAAY,IACZ,SAAU,IACV,UAAWX,EACX,GAAIxO,EACJ,GAAIC,EACJ,GAAIyN,EACJ,EAAGe,EAAW,IAAMvO,CAC9B,CAAS,EACD,MACD,CAEGuO,IAAa,OACf3N,EAAQwN,IAAQ,GAAKI,EAAmBH,EAAaF,EAAQE,EAE7DzN,EAAQiO,EAAaX,EAEnBtN,EAAQqN,GACVrN,EAAQqN,EACRa,GAAab,GAEbc,GAA8BZ,EAEhC,IAAIjO,EAAW8O,EAAe1L,EAAM1C,EAEhCsO,EAAmB,EACnBC,EAAiB,EACjBpB,EAAWnN,GACbsO,EAAmBF,EAAe1L,EAAM1C,EAAQ,EAChDuO,EAAiBD,IAEjBA,EAAmBF,EAAeL,EAClCQ,EAAiBjP,EAAWyO,GAE9BnM,EAAK,cAAcyM,EAAK,CACtB,MAAOrO,EACP,WAAYsO,EACZ,SAAUC,EACV,UAAWb,EACX,GAAIxO,EACJ,GAAIC,EACJ,GAAIyN,EACJ,EAAGe,EAAWa,GAAUjB,EAAOM,EAAQ,CAACjB,EAAIxN,CAAC,CAAC,EAAIA,CAC1D,CAAO,EACDgP,EAAe9O,CACrB,CAAK,EAGG4O,EAAY7P,IAAOiP,EAGrB,GAAIY,GAAa,KAAM,CACrB,IAAIO,EAAUR,EAAaX,EAC3B1L,EAAK,KAAKqL,EAAU,SAAUM,EAAOc,EAAK,CACxC,GAAI,CAAC,MAAMd,CAAK,EAAG,CACjB,IAAImB,EAAW9M,EAAK,cAAcyM,CAAG,EACrCK,EAAS,MAAQD,EACjB,IAAIH,EAAmB,EACnBC,EAAiB,EACjBE,EAAUtB,GACZmB,EAAmBjP,EAAaqD,GAAO2L,EAAM,EAAI,GAAKI,EACtDF,EAAiBD,IAEjBA,EAAmBjP,EAAaqD,EAAM2L,EAAMI,EAAUV,EACtDQ,EAAiBlP,EAAaqD,GAAO2L,EAAM,GAAKI,EAAUV,GAE5DW,EAAS,WAAaJ,EACtBI,EAAS,SAAWH,CACrB,CACX,CAAS,CACT,MACQd,EAAaS,EAAYC,EACzBC,EAAe/O,EACfuC,EAAK,KAAKqL,EAAU,SAAUM,EAAOc,EAAK,CACxC,GAAI,CAAC,MAAMd,CAAK,EAAG,CACjB,IAAIoB,EAAW/M,EAAK,cAAcyM,CAAG,EACjCrO,EAAQ2O,EAAS,QAAUtB,EAAiBA,EAAiBE,EAAQE,EACrEa,EAAmB,EACnBC,EAAiB,EACjBvO,EAAQmN,GACVmB,EAAmBF,EAAe1L,EAAM1C,EAAQ,EAChDuO,EAAiBD,IAEjBA,EAAmBF,EAAeL,EAClCQ,EAAiBH,EAAe1L,EAAM1C,EAAQ+N,GAEhDY,EAAS,WAAaL,EACtBK,EAAS,SAAWJ,EACpBH,GAAgB1L,EAAM1C,CACvB,CACX,CAAS,CAGT,CAAG,CACH,CC5Le,SAAS4O,GAAW5B,EAAY,CAC7C,MAAO,CACL,WAAYA,EACZ,MAAO,SAAU7E,EAAa4D,EAAS,CACrC,IAAI8C,EAAe9C,EAAQ,eAAe,CACxC,SAAU,QAClB,CAAO,EACD,GAAI,GAAC8C,GAAgB,CAACA,EAAa,QAGnC,KAAIjN,EAAOuG,EAAY,UACvBvG,EAAK,WAAW,SAAUyM,EAAK,CAG7B,QAFIS,EAAOlN,EAAK,QAAQyM,CAAG,EAElBxM,EAAI,EAAGA,EAAIgN,EAAa,OAAQhN,IAEvC,GAAI,CAACgN,EAAahN,CAAC,EAAE,WAAWiN,CAAI,EAClC,MAAO,GAGX,MAAO,EACf,CAAO,EACF,CACL,CACA,CClBA,IAAI3C,GAAS,KAAK,GAAK,IACvB,SAAS4C,GAAiBC,EAAM9P,EAAIC,EAAIC,EAAGsD,EAAKuM,EAAWC,EAAYC,EAAUC,EAASC,EAAW,CACnG,GAAIL,EAAK,OAAS,EAChB,OAGF,SAASM,EAAwCC,EAAM,CAGrD,QAFIC,EAAKD,EAAK,GACVE,EAAMD,EAAKA,EACN3N,EAAI,EAAGA,EAAI0N,EAAK,KAAK,OAAQ1N,IAAK,CACzC,IAAIsI,EAAOoF,EAAK,KAAK1N,CAAC,EAClBnB,EAAK,KAAK,IAAIyJ,EAAK,MAAM,EAAIhL,CAAE,EAE/BuQ,EAAKtQ,EAAI+K,EAAK,IACdwF,EAAMD,EAAKA,EAEXjP,EAAK,KAAK,MAAM,EAAI,KAAK,IAAIC,EAAKA,EAAK+O,CAAG,GAAKE,CAAG,EAClDC,EAAO1Q,GAAMuB,EAAK0J,EAAK,MAAQzH,EAC/BmN,EAASD,EAAOzF,EAAK,MAAM,EAC3B2F,EAAiB3F,EAAK,gBAAkB0F,EAASnN,EAErDqN,GAAmB5F,EAAM2F,EAAgB,EAAI,EAC7C3F,EAAK,MAAM,EAAIyF,CAChB,CACF,CAED,SAASI,EAAaC,EAAO,CAU3B,QARIC,EAAU,CACZ,KAAM,CAAE,EACR,KAAM,CACZ,EACQC,EAAa,CACf,KAAM,CAAE,EACR,KAAM,CACZ,EACatO,EAAI,EAAGA,EAAIoO,EAAM,OAAQpO,IAChC,GAAIoO,EAAMpO,CAAC,EAAE,eAAiB,OAG9B,KAAIsI,EAAO8F,EAAMpO,CAAC,EACd0N,EAAOpF,EAAK,MAAM,EAAIhL,EAAKgR,EAAaD,EACxCxP,EAAK,KAAK,IAAIyJ,EAAK,MAAM,EAAIhL,CAAE,EACnC,GAAIuB,GAAM6O,EAAK,KAAM,CACnB,IAAI9O,EAAK0J,EAAK,MAAM,EAAIjL,EAAKiL,EAAK,KAAOzH,EAErCgN,EAAKtQ,EAAI+K,EAAK,IAEdqF,EAAK,KAAK,IAAI/O,CAAE,EAAIiP,EAAK,KAAK,KAAKhP,EAAKA,GAAM,EAAID,EAAKA,EAAKiP,EAAKA,EAAG,EAAIA,EAC5EH,EAAK,GAAKC,EACVD,EAAK,KAAO7O,CACb,CACD6O,EAAK,KAAK,KAAKpF,CAAI,EAErBmF,EAAwCY,CAAO,EAC/CZ,EAAwCa,CAAU,CACnD,CAED,QADI1M,EAAMuL,EAAK,OACNnN,EAAI,EAAGA,EAAI4B,EAAK5B,IACvB,GAAImN,EAAKnN,CAAC,EAAE,WAAa,SAAWmN,EAAKnN,CAAC,EAAE,eAAiB,YAAa,CACxE,IAAIpB,EAAKuO,EAAKnN,CAAC,EAAE,MAAM,EAAIwN,EAC3BL,EAAKnN,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,GAAKpB,EAC5BuO,EAAKnN,CAAC,EAAE,MAAM,EAAIwN,CACnB,CAEC/E,GAAe0E,EAAMI,EAASA,EAAUF,CAAU,GACpDc,EAAahB,CAAI,CAErB,CACA,SAASoB,GAAaC,EAAiBnR,EAAIC,EAAIC,EAAG6P,EAAWC,EAAYC,EAAUC,EAAS,CAK1F,QAJIkB,EAAW,CAAA,EACXC,EAAY,CAAA,EACZC,EAAY,OAAO,UACnBC,EAAa,CAAC,OAAO,UAChB5O,EAAI,EAAGA,EAAIwO,EAAgB,OAAQxO,IAAK,CAC/C,IAAImB,EAAQqN,EAAgBxO,CAAC,EAAE,MAC3B6O,GAAiBL,EAAgBxO,CAAC,CAAC,IAGnCmB,EAAM,EAAI9D,GACZsR,EAAY,KAAK,IAAIA,EAAWxN,EAAM,CAAC,EACvCsN,EAAS,KAAKD,EAAgBxO,CAAC,CAAC,IAEhC4O,EAAa,KAAK,IAAIA,EAAYzN,EAAM,CAAC,EACzCuN,EAAU,KAAKF,EAAgBxO,CAAC,CAAC,GAEpC,CACD,QAASA,EAAI,EAAGA,EAAIwO,EAAgB,OAAQxO,IAAK,CAC/C,IAAI8O,EAASN,EAAgBxO,CAAC,EAC9B,GAAI,CAAC6O,GAAiBC,CAAM,GAAKA,EAAO,WAAY,CAClD,GAAIA,EAAO,iBAAmB,KAC5B,SAEF,IAAI3N,EAAQ2N,EAAO,MACf3M,EAAa2M,EAAO,WACpBC,EAAkB,OAClBD,EAAO,eAAiB,OACtB3N,EAAM,EAAI9D,EACZ0R,EAAkB5M,EAAW,CAAC,EAAE,CAAC,EAAI2M,EAAO,cAAgBxB,EAAWwB,EAAO,aAE9EC,EAAkBzB,EAAWF,EAAY0B,EAAO,aAAe3M,EAAW,CAAC,EAAE,CAAC,EAAI2M,EAAO,cAElFA,EAAO,eAAiB,YAC7B3N,EAAM,EAAI9D,EACZ0R,EAAkBJ,EAAYrB,EAAWwB,EAAO,YAEhDC,EAAkBzB,EAAWF,EAAYwB,EAAaE,EAAO,YAG3D3N,EAAM,EAAI9D,EACZ0R,EAAkB5N,EAAM,EAAImM,EAAWwB,EAAO,YAE9CC,EAAkBzB,EAAWF,EAAYjM,EAAM,EAAI2N,EAAO,YAG9DA,EAAO,gBAAkBC,EACzBb,GAAmBY,EAAQC,CAAe,CAC3C,CACF,CACD7B,GAAiBwB,EAAWrR,EAAIC,EAAIC,EAAG,EAAG6P,EAAWC,EAAYC,EAAUC,EAASqB,CAAU,EAC9F1B,GAAiBuB,EAAUpR,EAAIC,EAAIC,EAAG,GAAI6P,EAAWC,EAAYC,EAAUC,EAASoB,CAAS,EAC7F,QAAS3O,EAAI,EAAGA,EAAIwO,EAAgB,OAAQxO,IAAK,CAC/C,IAAI8O,EAASN,EAAgBxO,CAAC,EAC9B,GAAI,CAAC6O,GAAiBC,CAAM,GAAKA,EAAO,WAAY,CAClD,IAAI3N,EAAQ2N,EAAO,MACf3M,EAAa2M,EAAO,WACpBE,EAAgBF,EAAO,eAAiB,OACxCG,EAAU9N,EAAM,MAAM,QACtB+N,EAAWD,EAAUA,EAAQ,CAAC,EAAIA,EAAQ,CAAC,EAAI,EAE/CE,EAAgBhO,EAAM,MAAM,gBAAkB,EAAI+N,EAClDE,EAAgBN,EAAO,KAAK,MAAQK,EACpC5P,EAAO4C,EAAW,CAAC,EAAE,CAAC,EAAIA,EAAW,CAAC,EAAE,CAAC,EACzC6M,EACE7N,EAAM,EAAI9D,EACZ8E,EAAW,CAAC,EAAE,CAAC,EAAImL,EAAWwB,EAAO,aAAeM,EAAgBN,EAAO,cAE3E3M,EAAW,CAAC,EAAE,CAAC,EAAImL,EAAWF,EAAY0B,EAAO,aAAeM,EAAgBN,EAAO,eAGrF3N,EAAM,EAAI9D,EACZ8E,EAAW,CAAC,EAAE,CAAC,EAAIhB,EAAM,EAAI2N,EAAO,cAEpC3M,EAAW,CAAC,EAAE,CAAC,EAAIhB,EAAM,EAAI2N,EAAO,cAEtC3M,EAAW,CAAC,EAAE,CAAC,EAAIA,EAAW,CAAC,EAAE,CAAC,EAAI5C,GAExC4C,EAAW,CAAC,EAAE,CAAC,EAAIA,EAAW,CAAC,EAAE,CAAC,EAAIhB,EAAM,CAC7C,CACF,CACH,CAWA,SAAS+M,GAAmBY,EAAQO,EAAgBC,EAAkB,CAIpE,GAHIA,IAAqB,SACvBA,EAAmB,IAEjBR,EAAO,iBAAmB,KAI9B,KAAI3N,EAAQ2N,EAAO,MACfS,EAAQpO,EAAM,MACdqO,EAAWV,EAAO,KAClBW,EAAUF,EAAM,gBAChBN,EAAUM,EAAM,QAChBL,EAAWD,EAAUA,EAAQ,CAAC,EAAIA,EAAQ,CAAC,EAAI,EAC/CS,EAAWH,EAAM,SAEjBI,EAAgBH,EAAS,OAASC,EAAU,EAAIP,GACpD,GAAIG,EAAiBM,GAAiBL,EAAkB,CACtD,IAAIM,EAAYJ,EAAS,OACzB,GAAIE,GAAYA,EAAS,MAAM,OAAO,EAAG,CAGvCvO,EAAM,SAAS,kBAAmB,IAAI,EAEtCA,EAAM,SAAS,QAASkO,EAAiBH,CAAQ,EAEjD,IAAIW,EAAY1O,EAAM,kBACtBA,EAAM,SAAS,QAAS,KAAK,KAAK0O,EAAU,KAAK,CAAC,EAClD1O,EAAM,SAAS,kBAAmBsO,CAAO,CAC/C,KAAW,CACL,IAAIK,EAAsBT,EAAiBH,EACvCa,EAAWV,EAAiBM,EAE9BG,EAGFR,EAAmBQ,EAAsBhB,EAAO,mBAI9C,KAIAgB,EAGA,KACF3O,EAAM,SAAS,QAAS4O,CAAQ,CACjC,CACD,IAAIC,EAAU7O,EAAM,kBACpBqO,EAAS,MAAQQ,EAAQ,MACzB,IAAIC,GAAU9O,EAAM,MAAM,QAAU,GAAK,IACzCqO,EAAS,OAASQ,EAAQ,OAASC,EACnCT,EAAS,IAAMA,EAAS,OAASI,GAAa,CAC/C,EACH,CACA,SAASf,GAAiBqB,EAAa,CAErC,OAAOA,EAAY,WAAa,QAClC,CACe,SAASC,GAAe7J,EAAa,CAClD,IAAIvG,EAAOuG,EAAY,UACnBkI,EAAkB,CAAA,EAClBnR,EACAC,EACA8S,EAAiB,GACjBC,GAAsB/J,EAAY,IAAI,mBAAmB,GAAK,GAAKgE,GACnEI,EAAW3K,EAAK,UAAU,UAAU,EACpCxC,EAAIwC,EAAK,UAAU,GAAG,EACtBqN,EAAY1C,EAAS,MACrB4C,EAAW5C,EAAS,EACpB6C,EAAU7C,EAAS,EACnB2C,EAAa3C,EAAS,OAC1B,SAAS4F,EAAW7I,EAAI,CACtBA,EAAG,OAAS,EACb,CACD,SAAS8I,EAAapP,EAAO,CAC3B,GAAI,CAACA,EAAM,OACT,MAAO,GAET,QAAS8E,KAAO9E,EAAM,OACpB,GAAIA,EAAM,OAAO8E,CAAG,EAAE,SAAW,GAC/B,MAAO,GAGX,MAAO,EACR,CACDlG,EAAK,KAAK,SAAUyM,EAAK,CACvB,IAAIgE,EAASzQ,EAAK,iBAAiByM,CAAG,EAClC0D,EAAcM,EAAO,MACrBrP,EAAQqP,EAAO,iBACftP,EAAYsP,EAAO,mBACnBxL,EAAYjF,EAAK,aAAayM,CAAG,EACjCiE,EAAazL,EAAU,SAAS,OAAO,EAEvC0L,EAAgBD,EAAW,IAAI,UAAU,GAAKzL,EAAU,IAAI,CAAC,WAAY,QAAS,UAAU,CAAC,EAC7F2L,EAAgBF,EAAW,IAAI,qBAAqB,EACpDG,EAAeH,EAAW,IAAI,SAAS,EACvCI,EAAehJ,EAAa4I,EAAW,IAAI,cAAc,EAAGrD,CAAS,EACrE0D,EAAcL,EAAW,IAAI,aAAa,EAC1CxP,EAAiB+D,EAAU,SAAS,WAAW,EAC/C+L,EAAe9P,EAAe,IAAI,QAAQ,EAC9C8P,EAAelJ,EAAakJ,EAAc3D,CAAS,EACnD,IAAI4D,EAAgB/P,EAAe,IAAI,SAAS,EAEhD,GADA+P,EAAgBnJ,EAAamJ,EAAe5D,CAAS,EACjD,KAAK,IAAI8C,EAAY,SAAWA,EAAY,UAAU,EAAIG,EAAoB,CAChFzH,GAAKzH,EAAM,OAAQmP,CAAU,EAC7BnP,EAAM,OAAS,GACXD,IACF0H,GAAK1H,EAAU,OAAQoP,CAAU,EACjCpP,EAAU,OAAS,IAErB,MACD,CACD,GAAKqP,EAAapP,CAAK,EAGvB,KAAI8P,GAAYf,EAAY,WAAaA,EAAY,UAAY,EAC7DgB,EAAK,KAAK,IAAID,CAAQ,EACtBE,EAAK,KAAK,IAAIF,CAAQ,EACtBG,GACAC,GACAlP,GACAmP,GACJjU,EAAK6S,EAAY,GACjB5S,EAAK4S,EAAY,GACjB,IAAIqB,GAAgBb,IAAkB,UAAYA,IAAkB,QACpE,GAAIA,IAAkB,SACpBU,GAAQlB,EAAY,GACpBmB,GAAQnB,EAAY,GACpBoB,GAAY,aACP,CACL,IAAIlT,IAAMmT,IAAiBrB,EAAY,EAAIA,EAAY,IAAM,EAAIgB,EAAKhB,EAAY,EAAIgB,GAAM7T,EACxFgB,IAAMkT,IAAiBrB,EAAY,EAAIA,EAAY,IAAM,EAAIiB,EAAKjB,EAAY,EAAIiB,GAAM7T,EAG5F,GAFA8T,GAAQhT,GAAK8S,EAAK,EAClBG,GAAQhT,GAAK8S,EAAK,EACd,CAACI,GAAe,CAElB,IAAIjT,GAAKF,GAAK8S,GAAMH,EAAexT,EAAI2S,EAAY,GAC/C3R,GAAKF,GAAK8S,GAAMJ,EAAexT,EAAI2S,EAAY,GAC/CsB,GAAKlT,IAAM4S,EAAK,EAAI,GAAK,GAAKF,EAC9BS,GAAKlT,GACLqS,IAAiB,OAEnBQ,GAAQF,EAAK,EAAI5D,EAAWuD,EAAevD,EAAWF,EAAYyD,EAElEO,GAAQI,IAAMN,EAAK,EAAI,CAACP,EAAgBA,GAE1CU,GAAQI,GACRtP,GAAa,CAAC,CAAC/D,GAAIC,EAAE,EAAG,CAACC,GAAIC,EAAE,EAAG,CAACiT,GAAIC,EAAE,CAAC,CAC3C,CACDH,GAAYC,GAAgB,SAAWX,IAAiB,OAASM,EAAK,EAAI,QAAU,OAASA,EAAK,EAAI,OAAS,OAChH,CACD,IAAIQ,GAAK,KAAK,GACVC,GAAc,EACdC,GAASnB,EAAW,IAAI,QAAQ,EACpC,GAAIoB,GAASD,EAAM,EACjBD,GAAcC,IAAUF,GAAK,aACpBhB,IAAkB,SAC3BiB,GAAc,UACLC,KAAW,UAAYA,KAAW,GAAM,CACjD,IAAIE,GAAcZ,EAAK,EAAI,CAACD,EAAWS,GAAK,CAACT,EAC7CU,GAAcG,EACpB,SAAeF,KAAW,cAAgBlB,IAAkB,WAAaA,IAAkB,QAAS,CAC9F,IAAIqB,GAAM,KAAK,MAAMb,EAAIC,CAAE,EACvBY,GAAM,IACRA,GAAML,GAAK,EAAIK,IAEjB,IAAIC,GAASb,EAAK,EACda,KACFD,GAAML,GAAKK,IAEbJ,GAAcI,GAAML,EACrB,CASD,GARAtB,EAAiB,CAAC,CAACuB,GACnBxQ,EAAM,EAAIiQ,GACVjQ,EAAM,EAAIkQ,GACVlQ,EAAM,SAAWwQ,GACjBxQ,EAAM,SAAS,CACb,cAAe,QACrB,CAAK,EAEIoQ,GA0BE,CACLpQ,EAAM,SAAS,CACb,MAAOmQ,EACf,CAAO,EACD,IAAIW,GAAc9Q,EAAM,OAAO,OAC3B8Q,KACFA,GAAY,GAAK9Q,EAAM,EACvB8Q,GAAY,GAAK9Q,EAAM,EAE1B,KAnCmB,CAClB,IAAIqO,GAAWrO,EAAM,gBAAiB,EAAC,MAAK,EAC5CqO,GAAS,eAAerO,EAAM,qBAAsB,CAAA,EAEpD,IAAI8O,IAAU9O,EAAM,MAAM,QAAU,GAAK,IACzCqO,GAAS,GAAKS,GAAS,EACvBT,GAAS,QAAUS,GACnBzB,EAAgB,KAAK,CACnB,MAAOrN,EACP,UAAWD,EACX,SAAUwP,EACV,IAAKK,EACL,KAAMC,EACN,aAAc/P,EAAe,IAAI,cAAc,EAC/C,gBAAiBA,EAAe,IAAI,iBAAiB,EACrD,cAAe,IAAIP,EAAMwQ,EAAIC,CAAE,EAC/B,WAAYhP,GACZ,UAAWmP,GACX,cAAeX,EACf,aAAcC,EACd,aAAcC,EACd,YAAaC,EACb,KAAMtB,GACN,mBAAoBA,GAAS,MAC7B,gBAAiBrO,EAAM,MAAM,KACrC,CAAO,CACP,CAUIqP,EAAO,cAAc,CACnB,OAAQe,EACd,CAAK,EACL,CAAG,EACG,CAACnB,GAAkB9J,EAAY,IAAI,mBAAmB,GACxDiI,GAAaC,EAAiBnR,EAAIC,EAAIC,EAAG6P,EAAWC,EAAYC,EAAUC,CAAO,EAEnF,QAASvN,EAAI,EAAGA,EAAIwO,EAAgB,OAAQxO,IAAK,CAC/C,IAAI8O,EAASN,EAAgBxO,CAAC,EAC1BmB,EAAQ2N,EAAO,MACf5N,EAAY4N,EAAO,UACnBoD,EAAe,MAAM/Q,EAAM,CAAC,GAAK,MAAMA,EAAM,CAAC,EAClD,GAAIA,EAAO,CACTA,EAAM,SAAS,CACb,MAAO2N,EAAO,SACtB,CAAO,EACGoD,IACFtJ,GAAKzH,EAAM,OAAQmP,CAAU,EAC7BnP,EAAM,OAAS,IAEjB,IAAI8Q,EAAc9Q,EAAM,OAAO,OAC3B8Q,IACFA,EAAY,GAAK9Q,EAAM,EACvB8Q,EAAY,GAAK9Q,EAAM,EAE1B,CACD,GAAID,EAAW,CACb,IAAIiB,EAAa2M,EAAO,WACpBoD,GAAgB,CAAC/P,GACnByG,GAAK1H,EAAU,OAAQoP,CAAU,EACjCpP,EAAU,OAAS,KAEnBc,GAAeG,EAAY2M,EAAO,YAAY,EAC9CrM,GAAkBN,EAAY2M,EAAO,cAAeA,EAAO,eAAe,EAC1E5N,EAAU,SAAS,CACjB,OAAQiB,CAClB,CAAS,EAEDhB,EAAM,aAAa,oBAAsB,CACvC,OAAQ,IAAIT,EAAMyB,EAAW,CAAC,EAAE,CAAC,EAAGA,EAAW,CAAC,EAAE,CAAC,CAAC,CAC9D,EAEK,CACF,CACH,CChaA,IAAIgQ,GAAwB,SAAUC,EAAQ,CAC5CC,GAAUF,EAAUC,CAAM,EAC1B,SAASD,EAASpS,EAAMyM,EAAKhP,EAAY,CACvC,IAAIyJ,EAAQmL,EAAO,KAAK,IAAI,GAAK,KACjCnL,EAAM,GAAK,EACX,IAAIqL,EAAO,IAAIC,GACf,OAAAtL,EAAM,eAAeqL,CAAI,EACzBrL,EAAM,WAAWlH,EAAMyM,EAAKhP,EAAY,EAAI,EACrCyJ,CACR,CACD,OAAAkL,EAAS,UAAU,WAAa,SAAUpS,EAAMyM,EAAKhP,EAAYgV,EAAa,CAC5E,IAAIhC,EAAS,KACTlK,EAAcvG,EAAK,UACnBiF,EAAYjF,EAAK,aAAayM,CAAG,EACjCiG,EAAgBzN,EAAU,SAAS,UAAU,EAC7C8J,EAAS/O,EAAK,cAAcyM,CAAG,EAG/B0D,EAAcwC,GAAOC,GAAsB3N,EAAU,SAAS,WAAW,EAAG8J,EAAQ,EAAI,EAAGA,CAAM,EAErG,GAAI,MAAMoB,EAAY,UAAU,EAAG,CAEjCM,EAAO,SAASN,CAAW,EAC3B,MACD,CACD,GAAIsC,EAAa,CACfhC,EAAO,SAASN,CAAW,EAC3B,IAAI0C,EAAgBtM,EAAY,WAAW,eAAe,EACtDA,EAAY,QAAQ,KAGtBuM,GAAkBrC,EAAQ,CACxB,OAAQ,EACR,OAAQ,CACT,EAAElK,EAAa,CACd,UAAWkG,EACX,OAAQ,EAClB,CAAS,EACDgE,EAAO,QAAUN,EAAY,GAC7BM,EAAO,QAAUN,EAAY,IACpB0C,IAAkB,SAC3BpC,EAAO,MAAM,EAAI1B,EAAO,GACxB+D,GAAkBrC,EAAQ,CACxB,MAAO,CACL,EAAG1B,EAAO,CACX,CACX,EAAWxI,EAAakG,CAAG,GAIfhP,GAAc,MAChBgT,EAAO,SAAS,CACd,WAAYhT,EACZ,SAAUA,CACtB,CAAW,EACDqV,GAAkBrC,EAAQ,CACxB,MAAO,CACL,WAAY1B,EAAO,WACnB,SAAUA,EAAO,QAClB,CACb,EAAaxI,EAAakG,CAAG,IAEnBgE,EAAO,MAAM,SAAW1B,EAAO,WAC/BgE,GAAoBtC,EAAQ,CAC1B,MAAO,CACL,SAAU1B,EAAO,QAClB,CACb,EAAaxI,EAAakG,CAAG,EAG7B,MACMuG,GAAavC,CAAM,EAEnBsC,GAAoBtC,EAAQ,CAC1B,MAAON,CACf,EAAS5J,EAAakG,CAAG,EAErBgE,EAAO,SAASzQ,EAAK,cAAcyM,EAAK,OAAO,CAAC,EAChDwG,GAAyBxC,EAAQxL,CAAS,EAC1C,IAAIiM,GAAYnC,EAAO,WAAaA,EAAO,UAAY,EACnDmE,EAAS3M,EAAY,IAAI,gBAAgB,EACzC1H,EAAK,KAAK,IAAIqS,CAAQ,EAAIgC,EAC1BpU,EAAK,KAAK,IAAIoS,CAAQ,EAAIgC,EAC1BC,EAAclO,EAAU,WAAW,QAAQ,EAC/CkO,GAAe1C,EAAO,KAAK,SAAU0C,CAAW,EAChD,KAAK,aAAa5M,EAAavG,EAAMyM,CAAG,EACxCgE,EAAO,YAAY,UAAU,EAAE,MAAQkC,GAAO,CAC5C,EAAG5D,EAAO,GAAK2D,EAAc,IAAI,OAAO,GAAIA,EAAc,IAAI,WAAW,GAAK,EACpF,EAAOE,GAAsBF,EAAc,SAAS,WAAW,EAAG3D,CAAM,CAAC,EACrE4D,GAAOlC,EAAO,YAAY,QAAQ,EAAG,CACnC,EAAG5R,EACH,EAAGC,EACH,MAAO8T,GAAsB3N,EAAU,SAAS,CAAC,SAAU,WAAW,CAAC,EAAG8J,CAAM,CACtF,CAAK,EACD4D,GAAOlC,EAAO,YAAY,MAAM,EAAG,CACjC,MAAOmC,GAAsB3N,EAAU,SAAS,CAAC,OAAQ,WAAW,CAAC,EAAG8J,CAAM,CACpF,CAAK,EACD,IAAI5N,EAAYsP,EAAO,mBACnB2C,EAAY3C,EAAO,iBACvBtP,GAAawR,GAAOxR,EAAU,YAAY,QAAQ,EAAG,CACnD,EAAGtC,EACH,EAAGC,CACT,CAAK,EAED6T,GAAOS,EAAU,YAAY,QAAQ,EAAG,CACtC,EAAGvU,EACH,EAAGC,CACT,CAAK,EACDuU,GAAoB,KAAMX,EAAc,IAAI,OAAO,EAAGA,EAAc,IAAI,WAAW,EAAGA,EAAc,IAAI,UAAU,CAAC,CACvH,EACEN,EAAS,UAAU,aAAe,SAAU7L,EAAavG,EAAMyM,EAAK,CAClE,IAAIgE,EAAS,KACTxL,EAAYjF,EAAK,aAAayM,CAAG,EACjCvL,EAAiB+D,EAAU,SAAS,WAAW,EAC/CuK,EAAQxP,EAAK,cAAcyM,EAAK,OAAO,EACvC6G,EAAc9D,GAASA,EAAM,KAC7B+D,EAAgB/D,GAASA,EAAM,QACnCgE,GAAc/C,EAAQgD,GAAqBxO,CAAS,EAAG,CACrD,aAAcjF,EAAK,UACnB,eAAgByM,EAChB,aAAc6G,EACd,eAAgBC,EAChB,YAAahN,EAAY,kBAAkBkG,EAAK,QAAQ,GAAKzM,EAAK,QAAQyM,CAAG,CACnF,CAAK,EACD,IAAI2G,EAAY3C,EAAO,iBAEvBA,EAAO,cAAc,CAEnB,SAAU,KACV,SAAU,IAChB,CAAK,EAGD2C,EAAU,KAAK,CACb,GAAI,EACV,CAAK,EACD,IAAIzC,EAAgBpK,EAAY,IAAI,CAAC,QAAS,UAAU,CAAC,EACzD,GAAIoK,IAAkB,WAAaA,IAAkB,QACnDF,EAAO,oBAAmB,MACrB,CACL,IAAIiD,EAAW,KAAK,mBACfA,IACHA,EAAW,IAAIC,GACf,KAAK,iBAAiBD,CAAQ,GAGhCzP,GAAkB,KAAMe,GAAyBC,CAAS,EAAG,CAC3D,OAAQqO,EACR,QAASM,GAAU1S,EAAe,IAAI,CAAC,YAAa,SAAS,CAAC,EAAGqS,EAAe,CAAC,CACzF,CAAO,CACF,CACL,EACSnB,CACT,EAAEyB,EAAc,EAEZC,GAAuB,SAAUzB,EAAQ,CAC3CC,GAAUwB,EAASzB,CAAM,EACzB,SAASyB,GAAU,CACjB,IAAI5M,EAAQmL,IAAW,MAAQA,EAAO,MAAM,KAAM,SAAS,GAAK,KAChE,OAAAnL,EAAM,sBAAwB,GACvBA,CACR,CACD,OAAA4M,EAAQ,UAAU,OAAS,SAAUvN,EAAa4D,EAAS3C,EAAKuM,EAAS,CACvE,IAAI/T,EAAOuG,EAAY,UACnByN,EAAU,KAAK,MACfC,EAAQ,KAAK,MACbxW,EAEJ,GAAI,CAACuW,GAAWhU,EAAK,MAAK,EAAK,EAAG,CAEhC,QADI0D,EAAQ1D,EAAK,cAAc,CAAC,EACvBkU,EAAI,EAAG,MAAMxQ,GAASA,EAAM,UAAU,GAAKwQ,EAAIlU,EAAK,MAAO,EAAE,EAAEkU,EACtExQ,EAAQ1D,EAAK,cAAckU,CAAC,EAE1BxQ,IACFjG,EAAaiG,EAAM,WAEtB,CAMD,GAJI,KAAK,oBACPuQ,EAAM,OAAO,KAAK,kBAAkB,EAGlCjU,EAAK,UAAY,GAAKuG,EAAY,IAAI,iBAAiB,EAAG,CAC5D,IAAIkK,EAAS,IAAIoD,GAAe,CAC9B,MAAOnJ,GAAkBnE,EAAaiB,CAAG,CACjD,CAAO,EACDiJ,EAAO,SAASlK,EAAY,SAAS,kBAAkB,EAAE,aAAY,CAAE,EACvE,KAAK,mBAAqBkK,EAC1BwD,EAAM,IAAIxD,CAAM,CACjB,CACDzQ,EAAK,KAAKgU,CAAO,EAAE,IAAI,SAAUvH,EAAK,CACpC,IAAI0H,EAAW,IAAI/B,GAASpS,EAAMyM,EAAKhP,CAAU,EACjDuC,EAAK,iBAAiByM,EAAK0H,CAAQ,EACnCF,EAAM,IAAIE,CAAQ,CACnB,CAAA,EAAE,OAAO,SAAUC,EAAQC,EAAQ,CAClC,IAAIF,EAAWH,EAAQ,iBAAiBK,CAAM,EAC9CF,EAAS,WAAWnU,EAAMoU,EAAQ3W,CAAU,EAC5C0W,EAAS,IAAI,OAAO,EACpBF,EAAM,IAAIE,CAAQ,EAClBnU,EAAK,iBAAiBoU,EAAQD,CAAQ,CAC5C,CAAK,EAAE,OAAO,SAAU1H,EAAK,CACvB,IAAI0H,EAAWH,EAAQ,iBAAiBvH,CAAG,EAC3C6H,GAAiCH,EAAU5N,EAAakG,CAAG,CACjE,CAAK,EAAE,QAAO,EACV8H,GAAYhO,CAAW,EAEnBA,EAAY,IAAI,qBAAqB,IAAM,cAC7C,KAAK,MAAQvG,EAEnB,EACE8T,EAAQ,UAAU,QAAU,UAAY,GACxCA,EAAQ,UAAU,aAAe,SAAU5I,EAAO3E,EAAa,CAC7D,IAAIvG,EAAOuG,EAAY,UACnBiO,EAAaxU,EAAK,cAAc,CAAC,EACrC,GAAIwU,EAAY,CACd,IAAI3V,EAAKqM,EAAM,CAAC,EAAIsJ,EAAW,GAC3B1V,EAAKoM,EAAM,CAAC,EAAIsJ,EAAW,GAC3B3J,EAAS,KAAK,KAAKhM,EAAKA,EAAKC,EAAKA,CAAE,EACxC,OAAO+L,GAAU2J,EAAW,GAAK3J,GAAU2J,EAAW,EACvD,CACL,EACEV,EAAQ,KAAO,MACRA,CACT,EAAEW,EAAS,ECzOPC,GAAoC,UAAY,CAClD,SAASA,EAETC,EAEAC,EAAY,CACV,KAAK,0BAA4BD,EACjC,KAAK,YAAcC,CACpB,CACD,OAAAF,EAAqB,UAAU,YAAc,UAAY,CACvD,IAAIG,EAAU,KAAK,cAGnB,OAAOA,EAAQ,SAASA,EAAQ,OAAO,CAC3C,EACEH,EAAqB,UAAU,YAAc,SAAUxH,EAAM,CAC3D,IAAI2H,EAAU,KAAK,cACnB,OAAOA,EAAQ,YAAY3H,CAAI,GAAK,CACxC,EACEwH,EAAqB,UAAU,YAAc,SAAUxH,EAAM,CAI3D,IAAI4H,EAAwB,KAAK,4BACjC,OAAOA,EAAsB,YAAY5H,CAAI,CACjD,EACEwH,EAAqB,UAAU,cAAgB,SAAUrO,EAAWH,EAAK,CAEvE,IAAI4O,EAAwB,KAAK,4BACjC,OAAOA,EAAsB,cAAczO,EAAWH,CAAG,CAC7D,EACSwO,CACT,EAAG,EC5BCK,GAAYC,GAAmB,EAC/BC,GAA8B,SAAU5C,EAAQ,CAClDC,GAAU2C,EAAgB5C,CAAM,EAChC,SAAS4C,GAAiB,CACxB,OAAO5C,IAAW,MAAQA,EAAO,MAAM,KAAM,SAAS,GAAK,IAC5D,CAID,OAAA4C,EAAe,UAAU,KAAO,SAAUC,EAAQ,CAChD7C,EAAO,UAAU,KAAK,MAAM,KAAM,SAAS,EAG3C,KAAK,qBAAuB,IAAIqC,GAAqBS,GAAY,KAAK,QAAS,IAAI,EAAGA,GAAY,KAAK,WAAY,IAAI,CAAC,EACxH,KAAK,kBAAkBD,CAAM,CACjC,EAIED,EAAe,UAAU,YAAc,UAAY,CACjD5C,EAAO,UAAU,YAAY,MAAM,KAAM,SAAS,CACtD,EAIE4C,EAAe,UAAU,eAAiB,UAAY,CACpD,OAAOG,GAAuB,KAAM,CAClC,gBAAiB,CAAC,OAAO,EACzB,gBAAiBC,GAAaC,GAA8B,IAAI,CACtE,CAAK,CACL,EAIEL,EAAe,UAAU,cAAgB,SAAU5O,EAAW,CAC5D,IAAIrG,EAAO,KAAK,UAEZuV,EAAYR,GAAU/U,CAAI,EAC1BwV,EAAQD,EAAU,MACtB,GAAI,CAACC,EAAO,CACV,IAAIC,EAAc,CAAA,EAClBzV,EAAK,KAAKA,EAAK,aAAa,OAAO,EAAG,SAAU2L,EAAO,CACrD8J,EAAY,KAAK9J,CAAK,CAC9B,CAAO,EACD6J,EAAQD,EAAU,MAAQG,GAAgBD,EAAazV,EAAK,UAAU,IAAI,kBAAkB,CAAC,CAC9F,CACD,IAAIoK,EAASiI,EAAO,UAAU,cAAc,KAAK,KAAMhM,CAAS,EAEhE,OAAA+D,EAAO,QAAUoL,EAAMnP,CAAS,GAAK,EACrC+D,EAAO,MAAM,KAAK,SAAS,EACpBA,CACX,EACE6K,EAAe,UAAU,kBAAoB,SAAUC,EAAQ,CAE7DS,GAA0BT,EAAQ,YAAa,CAAC,MAAM,CAAC,EACvD,IAAIU,EAAqBV,EAAO,UAC5BW,EAAuBX,EAAO,SAAS,UAE3CU,EAAmB,KAAOA,EAAmB,MAAQV,EAAO,MAAM,KAClEW,EAAqB,KAAOA,EAAqB,MAAQX,EAAO,SAAS,MAAM,IACnF,EACED,EAAe,KAAO,aACtBA,EAAe,cAAgB,CAE7B,EAAG,EACH,gBAAiB,GACjB,QAAS,OAET,OAAQ,CAAC,MAAO,KAAK,EACrB,OAAQ,CAAC,EAAG,KAAK,EAEjB,UAAW,GACX,WAAY,GACZ,SAAU,OACV,SAAU,EAEV,SAAU,EAGV,kBAAmB,EAEnB,eAAgB,GAKhB,iBAAkB,EAElB,iBAAkB,GAElB,KAAM,EACN,IAAK,EACL,MAAO,EACP,OAAQ,EACR,MAAO,KACP,OAAQ,KACR,MAAO,CAGL,OAAQ,EACR,KAAM,GACN,SAAU,WAEV,SAAU,QAEV,QAAS,OAGT,aAAc,MAEd,YAAa,GAEb,oBAAqB,CAItB,EAGD,UAAW,CACT,KAAM,GAEN,OAAQ,GAER,QAAS,GACT,OAAQ,GACR,aAAc,GACd,gBAAiB,GACjB,UAAW,CAET,MAAO,EACP,KAAM,OACP,CACF,EACD,UAAW,CACT,YAAa,EACb,WAAY,OACb,EACD,gBAAiB,GACjB,iBAAkB,CAChB,MAAO,YACP,QAAS,CACV,EACD,YAAa,CAEX,YAAa,EACd,EACD,SAAU,CACR,MAAO,GACP,UAAW,CACZ,EAED,kBAAmB,GAEnB,cAAe,YACf,kBAAmB,IAEnB,oBAAqB,aACrB,sBAAuB,aACvB,wBAAyB,IACzB,gBAAiB,YACrB,EACSA,CACT,EAAEa,EAAW,EC1KE,SAASC,GAAmB3K,EAAY,CACrD,MAAO,CACL,WAAYA,EACZ,MAAO,SAAU7E,EAAa4D,EAAS,CACrC,IAAInK,EAAOuG,EAAY,UACvBvG,EAAK,WAAW,SAAUyM,EAAK,CAE7B,IAAIpB,EAAWrL,EAAK,aAAa,OAAO,EACpCgW,EAAWhW,EAAK,IAAIqL,EAAUoB,CAAG,EACrC,MAAI,EAAAqF,GAASkE,CAAQ,GAAK,CAAC,MAAMA,CAAQ,GAAKA,EAAW,EAIjE,CAAO,CACF,CACL,CACA,CCVO,SAASC,GAAQ/L,EAAW,CACjCA,EAAU,kBAAkB4J,EAAO,EACnC5J,EAAU,oBAAoB+K,EAAc,EAC5CiB,GAA6B,MAAOhM,EAAU,cAAc,EAC5DA,EAAU,eAAeiM,GAAMhL,GAAW,KAAK,CAAC,EAChDjB,EAAU,kBAAkB8C,GAAW,KAAK,CAAC,EAC7C9C,EAAU,kBAAkB6L,GAAmB,KAAK,CAAC,CACvD,8HC1BIK,GAAA,CACFC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,EAAA,CACD,EAEK,MAAAC,EAAeC,GAAI,IAAI,EACvB,CAAE,MAAA1Z,CAAA,EAAU2Z,GAAeF,CAAY,EAEvCG,EAAQC,EAIRC,EAAgBC,KAEhBC,EAAsBC,GAAS,KAC5B,CACL,MAAO,CACL,KAAM,UACN,KAAML,EAAM,SACd,EACA,OAAQ,CAAC,MAAO,KAAK,EACrB,gBAAiB,mBACjB,QAAS,CACP,WAAY,CAAC,UAAW,WAAY,YAAa,OAAQ,OAAQ,OAAO,EACxE,OAAQA,EAAM,UAChB,EACA,QAAS,CACP,QAAS,OACT,UAAY3M,GACH,GAAGiN,GAAoBjN,EAAO,MAAM,QAASA,EAAO,MAAM,SAAU,CAAC,CAAC,SAC3EA,EAAO,OACT,MAAMiN,GAAoBjN,EAAO,MAAM,UAAWA,EAAO,MAAM,KAAK,CAAC,GAEzE,EAOA,OAAQ,CACN,CACE,KAAM,MACN,OAAQ,CAAC,MAAO,KAAK,EAErB,OAAQ,CACN,MAAO,YACP,SAAU,WACV,QAAS,CAAC,UAAW,UAAU,CACjC,EACA,MAAO,CACL,UAAW,YACb,EACA,QAAS,CACP,KAAM,EACR,CACF,CACF,CAAA,EAEH,goEC3BD,MAAMkN,EAAWC,KACXC,EAAoBX,GAAI,EAAI,EAC5BY,EAAcZ,GAAI,EAAI,EAEtBa,EAAeN,GAAiB,IAC7B,QAAQ,KAAOE,EAAS,UAAU,uBAAuB,QAAQ,CAAC,CAAC,CAC3E,EAEKK,EAAmBP,GAAS,IACzBE,EAAS,UAAU,eAAiB,IAC5C,EAEKM,EAAoBR,GAAS,IAAM,OAChC,OAAA9L,EAAAgM,EAAS,UAAU,QAAQ,aAA3B,YAAAhM,EAAuC,OAC3CuM,IACE,CAACL,EAAkB,OAASK,EAAE,WAAaH,EAAa,SACxD,CAACC,EAAiB,OAAS,CAACF,EAAY,QAAUI,EAAE,gBAAkB,MAAU,IACrF,CACD,EAEKC,EAAkBnM,GACfA,EAAQoM,GAAYpM,EAAO2L,EAAS,UAAU,qBAAqB,EAAI,GAG1EU,EAAcZ,GAA0B,IAAM,OAClD,OAAO9L,EAAAsM,EAAkB,QAAlB,YAAAtM,EAAyB,IAAKuM,IAC5B,CACL,QACEJ,EAAY,OAASE,EAAiB,OAASE,EAAE,WAAa,KAC1DA,EAAE,UACFA,EAAE,QACR,SAAUA,EAAE,SACZ,UACEJ,EAAY,OAASE,EAAiB,MAAQE,EAAE,eAAiBA,EAAE,UAAYA,EAAE,UACnF,KAAMJ,EAAY,OAASE,EAAiB,MAAQE,EAAE,WAAaA,EAAE,KAAOA,EAAE,KAC9E,KAAMA,EAAE,KACR,MAAOA,EAAE,KAAA,GAEZ,CACF,EAEKI,EAAcb,GAAuB,IAClC,CACL,CAAE,IAAK,WAAY,MAAO,UAAW,EACrC,CACE,IAAKK,EAAY,OAASE,EAAiB,MAAQ,YAAc,OACjE,MAAO,YACP,UAAWG,CACb,EACA,CACE,IAAKL,EAAY,OAASE,EAAiB,MAAQ,gBAAkB,YACrE,MAAO,MAAML,EAAS,UAAU,QAAQ,KAAK,GAC7C,UAAWQ,CACb,CAAA,CAEH,EAED,eAAeI,GAAiB,CAC9BZ,EAAS,UAAU,YACrB,CAEA,OAAAa,GAAU,IAAM,CACCD,GAAA,CAChB", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}