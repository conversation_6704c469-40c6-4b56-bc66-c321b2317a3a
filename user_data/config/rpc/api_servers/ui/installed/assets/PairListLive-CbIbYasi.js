import{o as t,c as l,a as i,g as I,r as v,u as N,q as O,h as s,F as b,K as f,A as w,b as a,w as p,L as x,y as U,a3 as W,z as q,e as S,v as D,j as F,Z as j,Y as E,ba as J,x as k,p as K,d as R,_ as T}from"./index-B2p78N-x.js";import{_ as Y}from"./plus-box-outline-DIIEsppr.js";const Z={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},G=i("path",{fill:"currentColor",d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10s10-4.5 10-10S17.5 2 12 2m-2 15l-5-5l1.41-1.41L10 14.17l7.59-7.59L19 8z"},null,-1),H=[G];function Q(u,n){return t(),l("svg",Z,[...H])}const X={name:"mdi-check-circle",render:Q},g=u=>(K("data-v-64acf6b9"),u=u(),R(),u),ee=g(()=>i("h3",null,"Whitelist Methods",-1)),te={key:0,class:"list wide"},se=["title"],le={key:0,class:"list"},ie={key:1},oe=g(()=>i("hr",null,null,-1)),ae=g(()=>i("label",{class:"me-auto h3",title:"Blacklist - Select (followed by a click on '-') to remove pairs"},"Blacklist",-1)),ne={class:"float-end d-flex d-flex-columns pe-1"},ce={key:2,class:"list"},de=["onClick"],re={class:"check"},_e={key:3},ue=I({__name:"PairListLive",setup(u){const n=v(""),B=v(!1),o=v([]),e=N(),y=()=>{e.activeBot.whitelist.length===0&&e.activeBot.getWhitelist(),e.activeBot.blacklist.length===0&&e.activeBot.getBlacklist()},L=()=>{n.value&&(B.value=!1,e.activeBot.addBlacklist({blacklist:[n.value]}),n.value="")},C=r=>{const _=o.value.indexOf(r);_>-1?o.value.splice(_,1):o.value.push(r)},V=()=>{if(o.value.length===0){console.log("nothing to delete");return}const r=e.activeBot.blacklist.filter((_,h)=>o.value.indexOf(h)>-1);console.log("Deleting pairs: ",r),e.activeBot.deleteBlacklist(r),o.value=[]};return O(()=>{y()}),(r,_)=>{const h=Y,m=D,P=F,z=j,M=E,A=J,$=X;return t(),l("div",null,[i("div",null,[ee,s(e).activeBot.pairlistMethods.length?(t(),l("div",te,[(t(!0),l(b,null,f(s(e).activeBot.pairlistMethods,(c,d)=>(t(),l("div",{key:d,class:"pair white align-middle border border-secondary"},k(c),1))),128))])):w("",!0)]),i("h3",{title:`${s(e).activeBot.whitelist.length} pairs`},"Whitelist",8,se),s(e).activeBot.whitelist.length?(t(),l("div",le,[(t(!0),l(b,null,f(s(e).activeBot.whitelist,(c,d)=>(t(),l("div",{key:d,class:"pair white align-middle border border-secondary text-small"},k(c),1))),128))])):(t(),l("p",ie,"List Unavailable. Please Login and make sure server is running.")),oe,i("div",null,[ae,i("div",ne,[a(m,{id:"blacklist-add-btn",class:x(["me-1",s(e).activeBot.botApiVersion>=1.12?"col-6":""]),size:"sm"},{default:p(()=>[a(h)]),_:1},8,["class"]),s(e).activeBot.botApiVersion>=1.12?(t(),U(m,{key:0,size:"sm",class:"col-6",title:"Select pairs to delete pairs from your blacklist.",disabled:s(o).length===0,onClick:V},{default:p(()=>[a(P)]),_:1},8,["disabled"])):w("",!0)]),a(A,{title:"Add to blacklist",target:"blacklist-add-btn",triggers:"click","teleport-to":"body",show:s(B)},{default:p(()=>[i("form",{ref:"form",onSubmit:W(L,["prevent"])},[i("div",null,[a(M,{"label-cols":"2",label:"Pair","label-for":"pair-input"},{default:p(()=>[a(z,{id:"pair-input",modelValue:s(n),"onUpdate:modelValue":_[0]||(_[0]=c=>q(n)?n.value=c:null),required:"",autofocus:""},null,8,["modelValue"])]),_:1}),a(m,{id:"blacklist-submit",class:"float-end mb-2",size:"sm",type:"submit"},{default:p(()=>[S(" Add ")]),_:1})])],544)]),_:1},8,["show"])]),s(e).activeBot.blacklist.length?(t(),l("div",ce,[(t(!0),l(b,null,f(s(e).activeBot.blacklist,(c,d)=>(t(),l("div",{key:d,class:x(["pair black border border-secondary",s(o).indexOf(d)>-1?"active":""]),onClick:pe=>C(d)},[i("span",re,[a($)]),S(k(c),1)],10,de))),128))])):(t(),l("p",_e,"BlackList Unavailable. Please Login and make sure server is running."))])}}}),ve=T(ue,[["__scopeId","data-v-64acf6b9"]]);export{ve as default};
//# sourceMappingURL=PairListLive-CbIbYasi.js.map
